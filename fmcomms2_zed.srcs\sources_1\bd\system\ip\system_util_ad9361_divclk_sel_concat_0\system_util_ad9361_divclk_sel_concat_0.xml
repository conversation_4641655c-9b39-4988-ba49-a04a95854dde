<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>customized_ip</spirit:library>
  <spirit:name>system_util_ad9361_divclk_sel_concat_0</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:model>
    <spirit:ports>
      <spirit:port>
        <spirit:name>In0</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN0_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In0" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 0)">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In1</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN1_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In1" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 1)">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In2</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN2_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In2" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 2)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In3</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN3_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In3" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 3)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In4</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN4_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In4" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 4)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In5</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN5_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In5" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 5)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In6</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN6_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In6" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 6)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In7</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN7_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In7" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 7)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In8</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN8_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In8" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 8)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In9</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN9_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In9" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 9)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In10</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN10_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In10" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 10)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In11</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN11_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In11" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 11)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In12</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN12_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In12" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 12)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In13</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN13_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In13" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 13)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In14</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN14_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In14" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 14)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In15</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN15_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In15" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 15)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In16</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN16_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In16" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 16)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In17</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN17_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In17" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 17)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In18</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN18_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In18" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 18)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In19</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN19_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In19" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 19)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In20</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN20_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In20" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 20)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In21</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN21_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In21" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 21)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In22</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN22_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In22" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 22)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In23</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN23_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In23" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 23)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In24</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN24_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In24" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 24)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In25</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN25_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In25" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 25)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In26</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN26_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In26" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 26)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In27</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN27_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In27" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 27)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In28</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN28_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In28" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 28)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In29</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN29_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In29" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 29)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In30</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN30_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In30" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 30)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>In31</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.IN31_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.In31" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.NUM_PORTS&apos;)) > 31)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>dout</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.dout_width&apos;)) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
    </spirit:ports>
    <spirit:modelParameters>
      <spirit:modelParameter xsi:type="spirit:nameValueTypeType" spirit:dataType="integer">
        <spirit:name>IN0_WIDTH</spirit:name>
        <spirit:displayName>In0 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN0_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN1_WIDTH</spirit:name>
        <spirit:displayName>In1 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN1_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN2_WIDTH</spirit:name>
        <spirit:displayName>In2 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN2_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN3_WIDTH</spirit:name>
        <spirit:displayName>In3 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN3_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN4_WIDTH</spirit:name>
        <spirit:displayName>In4 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN4_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN5_WIDTH</spirit:name>
        <spirit:displayName>In5 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN5_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN6_WIDTH</spirit:name>
        <spirit:displayName>In6 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN6_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN7_WIDTH</spirit:name>
        <spirit:displayName>In7 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN7_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN8_WIDTH</spirit:name>
        <spirit:displayName>In8 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN8_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN9_WIDTH</spirit:name>
        <spirit:displayName>In9 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN9_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN10_WIDTH</spirit:name>
        <spirit:displayName>In10 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN10_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN11_WIDTH</spirit:name>
        <spirit:displayName>In11 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN11_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN12_WIDTH</spirit:name>
        <spirit:displayName>In12 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN12_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN13_WIDTH</spirit:name>
        <spirit:displayName>In13 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN13_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN14_WIDTH</spirit:name>
        <spirit:displayName>In14 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN14_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN15_WIDTH</spirit:name>
        <spirit:displayName>In15 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN15_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN16_WIDTH</spirit:name>
        <spirit:displayName>In16 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN16_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN17_WIDTH</spirit:name>
        <spirit:displayName>In17 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN17_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN18_WIDTH</spirit:name>
        <spirit:displayName>In18 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN18_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN19_WIDTH</spirit:name>
        <spirit:displayName>In19 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN19_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN20_WIDTH</spirit:name>
        <spirit:displayName>In20 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN20_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN21_WIDTH</spirit:name>
        <spirit:displayName>In21 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN21_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN22_WIDTH</spirit:name>
        <spirit:displayName>In22 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN22_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN23_WIDTH</spirit:name>
        <spirit:displayName>In23 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN23_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN24_WIDTH</spirit:name>
        <spirit:displayName>In24 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN24_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN25_WIDTH</spirit:name>
        <spirit:displayName>In25 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN25_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN26_WIDTH</spirit:name>
        <spirit:displayName>In26 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN26_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN27_WIDTH</spirit:name>
        <spirit:displayName>In27 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN27_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN28_WIDTH</spirit:name>
        <spirit:displayName>In28 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN28_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN29_WIDTH</spirit:name>
        <spirit:displayName>In29 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN29_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN30_WIDTH</spirit:name>
        <spirit:displayName>In30 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN30_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>IN31_WIDTH</spirit:name>
        <spirit:displayName>In31 Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.IN31_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>dout_width</spirit:name>
        <spirit:displayName>Dout Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.dout_width">2</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>NUM_PORTS</spirit:name>
        <spirit:displayName>Number of Ports</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.NUM_PORTS">2</spirit:value>
      </spirit:modelParameter>
    </spirit:modelParameters>
  </spirit:model>
  <spirit:description>Concatenates up to 32 ports into a single port</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="2">system_util_ad9361_divclk_sel_concat_0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>NUM_PORTS</spirit:name>
      <spirit:displayName>Number of Ports</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.NUM_PORTS" spirit:order="3" spirit:minimum="1" spirit:maximum="32" spirit:rangeType="long">2</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN0_WIDTH</spirit:name>
      <spirit:displayName>In0 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN0_WIDTH" spirit:order="4" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN1_WIDTH</spirit:name>
      <spirit:displayName>In1 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN1_WIDTH" spirit:order="5" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN2_WIDTH</spirit:name>
      <spirit:displayName>In2 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN2_WIDTH" spirit:order="6" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN3_WIDTH</spirit:name>
      <spirit:displayName>In3 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN3_WIDTH" spirit:order="7" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN4_WIDTH</spirit:name>
      <spirit:displayName>In4 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN4_WIDTH" spirit:order="8" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN5_WIDTH</spirit:name>
      <spirit:displayName>In5 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN5_WIDTH" spirit:order="9" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN6_WIDTH</spirit:name>
      <spirit:displayName>In6 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN6_WIDTH" spirit:order="10" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN7_WIDTH</spirit:name>
      <spirit:displayName>In7 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN7_WIDTH" spirit:order="11" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN8_WIDTH</spirit:name>
      <spirit:displayName>In8 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN8_WIDTH" spirit:order="12" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN9_WIDTH</spirit:name>
      <spirit:displayName>In9 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN9_WIDTH" spirit:order="13" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN10_WIDTH</spirit:name>
      <spirit:displayName>In10 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN10_WIDTH" spirit:order="14" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN11_WIDTH</spirit:name>
      <spirit:displayName>In11 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN11_WIDTH" spirit:order="15" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN12_WIDTH</spirit:name>
      <spirit:displayName>In12 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN12_WIDTH" spirit:order="16" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN13_WIDTH</spirit:name>
      <spirit:displayName>In13 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN13_WIDTH" spirit:order="17" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN14_WIDTH</spirit:name>
      <spirit:displayName>In14 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN14_WIDTH" spirit:order="18" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN15_WIDTH</spirit:name>
      <spirit:displayName>In15 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN15_WIDTH" spirit:order="19" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN16_WIDTH</spirit:name>
      <spirit:displayName>In16 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN16_WIDTH" spirit:order="20" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN17_WIDTH</spirit:name>
      <spirit:displayName>In17 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN17_WIDTH" spirit:order="21" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN18_WIDTH</spirit:name>
      <spirit:displayName>In18 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN18_WIDTH" spirit:order="22" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN19_WIDTH</spirit:name>
      <spirit:displayName>In19 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN19_WIDTH" spirit:order="23" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN20_WIDTH</spirit:name>
      <spirit:displayName>In20 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN20_WIDTH" spirit:order="24" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN21_WIDTH</spirit:name>
      <spirit:displayName>In21 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN21_WIDTH" spirit:order="25" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN22_WIDTH</spirit:name>
      <spirit:displayName>In22 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN22_WIDTH" spirit:order="26" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN23_WIDTH</spirit:name>
      <spirit:displayName>In23 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN23_WIDTH" spirit:order="27" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN24_WIDTH</spirit:name>
      <spirit:displayName>In24 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN24_WIDTH" spirit:order="28" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN25_WIDTH</spirit:name>
      <spirit:displayName>In25 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN25_WIDTH" spirit:order="29" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN26_WIDTH</spirit:name>
      <spirit:displayName>In26 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN26_WIDTH" spirit:order="30" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN27_WIDTH</spirit:name>
      <spirit:displayName>In27 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN27_WIDTH" spirit:order="31" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN28_WIDTH</spirit:name>
      <spirit:displayName>In28 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN28_WIDTH" spirit:order="32" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN29_WIDTH</spirit:name>
      <spirit:displayName>In29 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN29_WIDTH" spirit:order="33" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN30_WIDTH</spirit:name>
      <spirit:displayName>In30 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN30_WIDTH" spirit:order="34" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN31_WIDTH</spirit:name>
      <spirit:displayName>In31 Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.IN31_WIDTH" spirit:order="35" spirit:minimum="1" spirit:maximum="4096" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>dout_width</spirit:name>
      <spirit:displayName>Dout Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.dout_width" spirit:order="36">2</spirit:value>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:displayName>Concat</xilinx:displayName>
      <xilinx:coreRevision>1</xilinx:coreRevision>
      <xilinx:configElementInfos>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.IN0_WIDTH" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.IN1_WIDTH" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_PORTS" xilinx:valueSource="user"/>
      </xilinx:configElementInfos>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2018.3</xilinx:xilinxVersion>
      <xilinx:checksum xilinx:scope="fileGroups" xilinx:value="10991481"/>
      <xilinx:checksum xilinx:scope="ports" xilinx:value="6312e661"/>
      <xilinx:checksum xilinx:scope="hdlParameters" xilinx:value="7e7c2789"/>
      <xilinx:checksum xilinx:scope="parameters" xilinx:value="516a1f24"/>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
