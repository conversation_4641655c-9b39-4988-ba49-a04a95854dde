Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:15 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'vio_0'

1. Summary
----------

SUCCESS in the upgrade of vio_0 (xilinx.com:ip:vio:3.0) from (Rev. 14) to (Rev. 19)






Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:13 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_sel_concat_0'

1. Summary
----------

SUCCESS in the upgrade of system_util_ad9361_divclk_sel_concat_0 (xilinx.com:ip:xlconcat:2.1) from (Rev. 2) to (Rev. 1)






Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:13 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_sel_0'

1. Summary
----------

SUCCESS in the upgrade of system_util_ad9361_divclk_sel_0 (xilinx.com:ip:util_reduced_logic:2.0) from (Rev. 2) to (Rev. 4)






Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:13 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_reset_0'

1. Summary
----------

SUCCESS in the upgrade of system_util_ad9361_divclk_reset_0 (xilinx.com:ip:proc_sys_reset:5.0) from (Rev. 10) to (Rev. 13)






Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:13 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_sys_rstgen_0'

1. Summary
----------

SUCCESS in the upgrade of system_sys_rstgen_0 (xilinx.com:ip:proc_sys_reset:5.0) from (Rev. 10) to (Rev. 13)






Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:13 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_sys_ps7_0'

1. Summary
----------

SUCCESS in the upgrade of system_sys_ps7_0 (xilinx.com:ip:processing_system7:5.5) from (Rev. 3) to (Rev. 6)

2. Upgrade messages
-------------------

WARNING: upgrade cannot add parameter PCW_TRACE_INTERNAL_WIDTH with default value 32 : a parameter called PCW_TRACE_INTERNAL_WIDTH already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_USE_AXI_NONSECURE with default value 0 : a parameter called PCW_USE_AXI_NONSECURE already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_EN_PTP_ENET0 with default value 1 : a parameter called PCW_EN_PTP_ENET0 already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_EN_PTP_ENET0 with default value 1 : a parameter called PCW_EN_PTP_ENET0 already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP0_NUM_WRITE_THREADS with default value 4 : a parameter called PCW_GP0_NUM_WRITE_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP0_NUM_READ_THREADS with default value 4 : a parameter called PCW_GP0_NUM_READ_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP1_NUM_WRITE_THREADS with default value 4 : a parameter called PCW_GP1_NUM_WRITE_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP1_NUM_READ_THREADS with default value 4 : a parameter called PCW_GP1_NUM_READ_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP0_EN_MODIFIABLE_TXN with default value 0 : a parameter called PCW_GP0_EN_MODIFIABLE_TXN already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP1_EN_MODIFIABLE_TXN with default value 0 : a parameter called PCW_GP1_EN_MODIFIABLE_TXN already exists in processing_system7_v5_5






Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:13 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_sys_concat_intc_0'

1. Summary
----------

SUCCESS in the upgrade of system_sys_concat_intc_0 (xilinx.com:ip:xlconcat:2.1) from (Rev. 2) to (Rev. 1)






Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:13 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_hp2_interconnect_0'

1. Summary
----------

SUCCESS in the upgrade of system_axi_hp2_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1) from (Rev. 12) to (Rev. 19)






Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:13 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_hp1_interconnect_0'

1. Summary
----------

SUCCESS in the upgrade of system_axi_hp1_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1) from (Rev. 12) to (Rev. 19)






Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:13 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_hp0_interconnect_0'

1. Summary
----------

SUCCESS in the upgrade of system_axi_hp0_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1) from (Rev. 12) to (Rev. 19)






Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul 26 12:37:13 2025
| Host         : voyager_nb running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_cpu_interconnect_0'

1. Summary
----------

SUCCESS in the upgrade of system_axi_cpu_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1) from (Rev. 12) to (Rev. 19)






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_tdd_sync_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_tdd_sync_0 (analog.com:user:util_tdd_sync:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_sel_concat_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_divclk_sel_concat_0 (xilinx.com:ip:xlconcat:2.1 (Rev. 2)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_sel_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_divclk_sel_0 (xilinx.com:ip:util_reduced_logic:2.0 (Rev. 2)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_reset_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_divclk_reset_0 (xilinx.com:ip:proc_sys_reset:5.0 (Rev. 10)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_divclk_0 (analog.com:user:util_clkdiv:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_dac_upack_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_dac_upack_0 (analog.com:user:util_upack:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_adc_pack_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_adc_pack_0 (analog.com:user:util_cpack:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_adc_fifo_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_adc_fifo_0 (analog.com:user:util_wfifo:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_sys_rstgen_0'

1. Summary
----------

SUCCESS in the update of system_sys_rstgen_0 (xilinx.com:ip:proc_sys_reset:5.0 (Rev. 10)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_sys_ps7_0'

1. Summary
----------

SUCCESS in the update of system_sys_ps7_0 (xilinx.com:ip:processing_system7:5.5 (Rev. 3)) to current project options.

2. Upgrade messages
-------------------

WARNING: upgrade cannot add parameter PCW_TRACE_INTERNAL_WIDTH with default value 32 : a parameter called PCW_TRACE_INTERNAL_WIDTH already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_USE_AXI_NONSECURE with default value 0 : a parameter called PCW_USE_AXI_NONSECURE already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_EN_PTP_ENET0 with default value 1 : a parameter called PCW_EN_PTP_ENET0 already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_EN_PTP_ENET0 with default value 1 : a parameter called PCW_EN_PTP_ENET0 already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP0_NUM_WRITE_THREADS with default value 4 : a parameter called PCW_GP0_NUM_WRITE_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP0_NUM_READ_THREADS with default value 4 : a parameter called PCW_GP0_NUM_READ_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP1_NUM_WRITE_THREADS with default value 4 : a parameter called PCW_GP1_NUM_WRITE_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP1_NUM_READ_THREADS with default value 4 : a parameter called PCW_GP1_NUM_READ_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP0_EN_MODIFIABLE_TXN with default value 0 : a parameter called PCW_GP0_EN_MODIFIABLE_TXN already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP1_EN_MODIFIABLE_TXN with default value 0 : a parameter called PCW_GP1_EN_MODIFIABLE_TXN already exists in processing_system7_v5_5






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_sys_concat_intc_0'

1. Summary
----------

SUCCESS in the update of system_sys_concat_intc_0 (xilinx.com:ip:xlconcat:2.1 (Rev. 2)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_hp2_interconnect_0'

1. Summary
----------

SUCCESS in the update of system_axi_hp2_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1 (Rev. 12)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_hp1_interconnect_0'

1. Summary
----------

SUCCESS in the update of system_axi_hp1_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1 (Rev. 12)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_hp0_interconnect_0'

1. Summary
----------

SUCCESS in the update of system_axi_hp0_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1 (Rev. 12)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_cpu_interconnect_0'

1. Summary
----------

SUCCESS in the update of system_axi_cpu_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1 (Rev. 12)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_ad9361_dac_fifo_0'

1. Summary
----------

SUCCESS in the update of system_axi_ad9361_dac_fifo_0 (analog.com:user:util_rfifo:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_ad9361_dac_dma_0'

1. Summary
----------

SUCCESS in the update of system_axi_ad9361_dac_dma_0 (analog.com:user:axi_dmac:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_ad9361_adc_dma_0'

1. Summary
----------

SUCCESS in the update of system_axi_ad9361_adc_dma_0 (analog.com:user:axi_dmac:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 13:30:47 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z035ffg676-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_ad9361_0'

1. Summary
----------

SUCCESS in the update of system_axi_ad9361_0 (analog.com:user:axi_ad9361:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_tdd_sync_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_tdd_sync_0 (analog.com:user:util_tdd_sync:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_sel_concat_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_divclk_sel_concat_0 (xilinx.com:ip:xlconcat:2.1 (Rev. 2)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_sel_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_divclk_sel_0 (xilinx.com:ip:util_reduced_logic:2.0 (Rev. 2)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_reset_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_divclk_reset_0 (xilinx.com:ip:proc_sys_reset:5.0 (Rev. 10)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_divclk_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_divclk_0 (analog.com:user:util_clkdiv:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_dac_upack_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_dac_upack_0 (analog.com:user:util_upack:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_adc_pack_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_adc_pack_0 (analog.com:user:util_cpack:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_util_ad9361_adc_fifo_0'

1. Summary
----------

SUCCESS in the update of system_util_ad9361_adc_fifo_0 (analog.com:user:util_wfifo:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_sys_rstgen_0'

1. Summary
----------

SUCCESS in the update of system_sys_rstgen_0 (xilinx.com:ip:proc_sys_reset:5.0 (Rev. 10)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_sys_ps7_0'

1. Summary
----------

SUCCESS in the update of system_sys_ps7_0 (xilinx.com:ip:processing_system7:5.5 (Rev. 3)) to current project options.

2. Upgrade messages
-------------------

WARNING: upgrade cannot add parameter PCW_TRACE_INTERNAL_WIDTH with default value 32 : a parameter called PCW_TRACE_INTERNAL_WIDTH already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_USE_AXI_NONSECURE with default value 0 : a parameter called PCW_USE_AXI_NONSECURE already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_EN_PTP_ENET0 with default value 1 : a parameter called PCW_EN_PTP_ENET0 already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_EN_PTP_ENET0 with default value 1 : a parameter called PCW_EN_PTP_ENET0 already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP0_NUM_WRITE_THREADS with default value 4 : a parameter called PCW_GP0_NUM_WRITE_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP0_NUM_READ_THREADS with default value 4 : a parameter called PCW_GP0_NUM_READ_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP1_NUM_WRITE_THREADS with default value 4 : a parameter called PCW_GP1_NUM_WRITE_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP1_NUM_READ_THREADS with default value 4 : a parameter called PCW_GP1_NUM_READ_THREADS already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP0_EN_MODIFIABLE_TXN with default value 0 : a parameter called PCW_GP0_EN_MODIFIABLE_TXN already exists in processing_system7_v5_5
WARNING: upgrade cannot add parameter PCW_GP1_EN_MODIFIABLE_TXN with default value 0 : a parameter called PCW_GP1_EN_MODIFIABLE_TXN already exists in processing_system7_v5_5






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_sys_concat_intc_0'

1. Summary
----------

SUCCESS in the update of system_sys_concat_intc_0 (xilinx.com:ip:xlconcat:2.1 (Rev. 2)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_hp2_interconnect_0'

1. Summary
----------

SUCCESS in the update of system_axi_hp2_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1 (Rev. 12)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_hp1_interconnect_0'

1. Summary
----------

SUCCESS in the update of system_axi_hp1_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1 (Rev. 12)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_hp0_interconnect_0'

1. Summary
----------

SUCCESS in the update of system_axi_hp0_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1 (Rev. 12)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_cpu_interconnect_0'

1. Summary
----------

SUCCESS in the update of system_axi_cpu_interconnect_0 (xilinx.com:ip:axi_interconnect:2.1 (Rev. 12)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_ad9361_dac_fifo_0'

1. Summary
----------

SUCCESS in the update of system_axi_ad9361_dac_fifo_0 (analog.com:user:util_rfifo:1.0 (Rev. 1)) to current project options.






Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_ad9361_dac_dma_0'

1. Summary
----------

CAUTION (success, with warnings) in the update of system_axi_ad9361_dac_dma_0 (analog.com:user:axi_dmac:1.0 (Rev. 1)) to current project options.

After upgrade, an IP may have parameter and port differences compared to the original customization. Please review the parameters within the IP customization GUI to ensure proper functionality. Also, please review the updated IP instantiation template to ensure proper connectivity, and update your design if required.

2. Interface Information
------------------------

Detected external interface differences while upgrading 'system_axi_ad9361_dac_dma_0'.



3. Connection Warnings
----------------------

Detected external port differences while upgrading 'system_axi_ad9361_dac_dma_0'. These changes may impact your design.



4. Customization warnings
-------------------------

An attempt to modify the value of disabled parameter 'DMA_AXI_PROTOCOL_DEST' from '0' to '1' has been ignored for IP 'system_axi_ad9361_dac_dma_0'


5. Debug Commands
-----------------

  The following debug information can be passed to Vivado as Tcl commands,
in order to validate or debug the output of the upgrade flow.
  You may consult any warnings from within this upgrade, and alter or remove
the configuration parameter(s) which caused the warning; then execute the Tcl
commands, and use the IP Customization GUI to verify the IP configuration.

create_ip -vlnv analog.com:user:axi_dmac:1.0 -user_name system_axi_ad9361_dac_dma_0
set_property -dict "\
  CONFIG.ASYNC_CLK_DEST_REQ {true} \
  CONFIG.ASYNC_CLK_REQ_SRC {false} \
  CONFIG.ASYNC_CLK_SRC_DEST {true} \
  CONFIG.AXI_SLICE_DEST {true} \
  CONFIG.AXI_SLICE_SRC {false} \
  CONFIG.CYCLIC {true} \
  CONFIG.Component_Name {system_axi_ad9361_dac_dma_0} \
  CONFIG.DISABLE_DEBUG_REGISTERS {false} \
  CONFIG.DMA_2D_TRANSFER {false} \
  CONFIG.DMA_AXI_ADDR_WIDTH {29} \
  CONFIG.DMA_AXI_PROTOCOL_DEST {1} \
  CONFIG.DMA_AXI_PROTOCOL_SRC {1} \
  CONFIG.DMA_DATA_WIDTH_DEST {64} \
  CONFIG.DMA_DATA_WIDTH_SRC {64} \
  CONFIG.DMA_LENGTH_WIDTH {24} \
  CONFIG.DMA_TYPE_DEST {2} \
  CONFIG.DMA_TYPE_SRC {0} \
  CONFIG.FIFO_SIZE {4} \
  CONFIG.ID {0} \
  CONFIG.MAX_BYTES_PER_BURST {128} \
  CONFIG.SYNC_TRANSFER_START {false} \
  CONFIG.fifo_rd_signal_clock.ASSOCIATED_BUSIF {fifo_rd} \
  CONFIG.fifo_wr_signal_clock.ASSOCIATED_BUSIF {fifo_wr} \
  CONFIG.irq.SENSITIVITY {LEVEL_HIGH} \
  CONFIG.m_axis_signal_clock.ASSOCIATED_BUSIF {m_axis} \
  CONFIG.m_dest_axi_aclk.ASSOCIATED_BUSIF {m_dest_axi} \
  CONFIG.m_dest_axi_aclk.ASSOCIATED_RESET {m_dest_axi_aresetn} \
  CONFIG.m_dest_axi_aresetn.POLARITY {ACTIVE_LOW} \
  CONFIG.m_src_axi.SUPPORTS_NARROW_BURST {0} \
  CONFIG.m_src_axi_aclk.ASSOCIATED_BUSIF {m_src_axi} \
  CONFIG.m_src_axi_aclk.ASSOCIATED_RESET {m_src_axi_aresetn} \
  CONFIG.m_src_axi_aresetn.POLARITY {ACTIVE_LOW} \
  CONFIG.s_axi_aclk.ASSOCIATED_BUSIF {s_axi} \
  CONFIG.s_axi_aclk.ASSOCIATED_RESET {s_axi_aresetn} \
  CONFIG.s_axi_aresetn.POLARITY {ACTIVE_LOW} \
  CONFIG.s_axis_signal_clock.ASSOCIATED_BUSIF {s_axis} " [get_ips system_axi_ad9361_dac_dma_0]







Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_ad9361_adc_dma_0'

1. Summary
----------

CAUTION (success, with warnings) in the update of system_axi_ad9361_adc_dma_0 (analog.com:user:axi_dmac:1.0 (Rev. 1)) to current project options.

After upgrade, an IP may have parameter and port differences compared to the original customization. Please review the parameters within the IP customization GUI to ensure proper functionality. Also, please review the updated IP instantiation template to ensure proper connectivity, and update your design if required.

2. Interface Information
------------------------

Detected external interface differences while upgrading 'system_axi_ad9361_adc_dma_0'.



3. Connection Warnings
----------------------

Detected external port differences while upgrading 'system_axi_ad9361_adc_dma_0'. These changes may impact your design.



4. Customization warnings
-------------------------

An attempt to modify the value of disabled parameter 'DMA_AXI_PROTOCOL_SRC' from '0' to '1' has been ignored for IP 'system_axi_ad9361_adc_dma_0'


5. Debug Commands
-----------------

  The following debug information can be passed to Vivado as Tcl commands,
in order to validate or debug the output of the upgrade flow.
  You may consult any warnings from within this upgrade, and alter or remove
the configuration parameter(s) which caused the warning; then execute the Tcl
commands, and use the IP Customization GUI to verify the IP configuration.

create_ip -vlnv analog.com:user:axi_dmac:1.0 -user_name system_axi_ad9361_adc_dma_0
set_property -dict "\
  CONFIG.ASYNC_CLK_DEST_REQ {false} \
  CONFIG.ASYNC_CLK_REQ_SRC {true} \
  CONFIG.ASYNC_CLK_SRC_DEST {true} \
  CONFIG.AXI_SLICE_DEST {false} \
  CONFIG.AXI_SLICE_SRC {false} \
  CONFIG.CYCLIC {false} \
  CONFIG.Component_Name {system_axi_ad9361_adc_dma_0} \
  CONFIG.DISABLE_DEBUG_REGISTERS {false} \
  CONFIG.DMA_2D_TRANSFER {false} \
  CONFIG.DMA_AXI_ADDR_WIDTH {29} \
  CONFIG.DMA_AXI_PROTOCOL_DEST {1} \
  CONFIG.DMA_AXI_PROTOCOL_SRC {1} \
  CONFIG.DMA_DATA_WIDTH_DEST {64} \
  CONFIG.DMA_DATA_WIDTH_SRC {64} \
  CONFIG.DMA_LENGTH_WIDTH {24} \
  CONFIG.DMA_TYPE_DEST {0} \
  CONFIG.DMA_TYPE_SRC {2} \
  CONFIG.FIFO_SIZE {4} \
  CONFIG.ID {0} \
  CONFIG.MAX_BYTES_PER_BURST {128} \
  CONFIG.SYNC_TRANSFER_START {true} \
  CONFIG.fifo_rd_signal_clock.ASSOCIATED_BUSIF {fifo_rd} \
  CONFIG.fifo_wr_signal_clock.ASSOCIATED_BUSIF {fifo_wr} \
  CONFIG.irq.SENSITIVITY {LEVEL_HIGH} \
  CONFIG.m_axis_signal_clock.ASSOCIATED_BUSIF {m_axis} \
  CONFIG.m_dest_axi.SUPPORTS_NARROW_BURST {0} \
  CONFIG.m_dest_axi_aclk.ASSOCIATED_BUSIF {m_dest_axi} \
  CONFIG.m_dest_axi_aclk.ASSOCIATED_RESET {m_dest_axi_aresetn} \
  CONFIG.m_dest_axi_aresetn.POLARITY {ACTIVE_LOW} \
  CONFIG.m_src_axi_aclk.ASSOCIATED_BUSIF {m_src_axi} \
  CONFIG.m_src_axi_aclk.ASSOCIATED_RESET {m_src_axi_aresetn} \
  CONFIG.m_src_axi_aresetn.POLARITY {ACTIVE_LOW} \
  CONFIG.s_axi_aclk.ASSOCIATED_BUSIF {s_axi} \
  CONFIG.s_axi_aclk.ASSOCIATED_RESET {s_axi_aresetn} \
  CONFIG.s_axi_aresetn.POLARITY {ACTIVE_LOW} \
  CONFIG.s_axis_signal_clock.ASSOCIATED_BUSIF {s_axis} " [get_ips system_axi_ad9361_adc_dma_0]







Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 08:23:04 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : upgrade_ip
| Device       : xc7z045ffg900-2
------------------------------------------------------------------------------------

Upgrade Log for IP 'system_axi_ad9361_0'

1. Summary
----------

SUCCESS in the update of system_axi_ad9361_0 (analog.com:user:axi_ad9361:1.0 (Rev. 1)) to current project options.

