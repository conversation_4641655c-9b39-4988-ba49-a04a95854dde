#-----------------------------------------------------------
# Vivado v2016.4 (64-bit)
# SW Build 1756540 on Mon Jan 23 19:11:23 MST 2017
# IP Build 1755317 on Mon Jan 23 20:30:07 MST 2017
# Start of session at: Tue Mar 17 16:59:49 2020
# Process ID: 17120
# Current directory: D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3
# Command line: vivado.exe -log system_top.vdi -applog -product Vivado -messageDb vivado.pb -mode batch -source system_top.tcl -notrace
# Log file: D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3/system_top.vdi
# Journal file: D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3\vivado.jou
#-----------------------------------------------------------
source system_top.tcl -notrace
Design is defaulting to srcset: sources_1
Design is defaulting to constrset: constrs_1
INFO: [Project 1-454] Reading design checkpoint 'd:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0.dcp' for cell 'test_vio_0'
INFO: [Netlist 29-17] Analyzing 882 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2016.4
INFO: [Device 21-403] Loading part xc7z035ffg676-2
INFO: [Project 1-570] Preparing netlist for logic optimization
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xdc] for cell 'i_system_wrapper/system_i/sys_ps7/inst'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xdc] for cell 'i_system_wrapper/system_i/sys_ps7/inst'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0_board.xdc] for cell 'i_system_wrapper/system_i/sys_rstgen/U0'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0_board.xdc] for cell 'i_system_wrapper/system_i/sys_rstgen/U0'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0.xdc] for cell 'i_system_wrapper/system_i/sys_rstgen/U0'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0.xdc] for cell 'i_system_wrapper/system_i/sys_rstgen/U0'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_xfer_cntrl'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_xfer_cntrl'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/i_xfer_cntrl'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/i_xfer_cntrl'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/i_xfer_cntrl'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/i_xfer_cntrl'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_control'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_control'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_counter_values'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_counter_values'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_xfer_cntrl'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_xfer_cntrl'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/i_xfer_cntrl'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_up_adc_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_up_adc_channel/i_xfer_cntrl'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_up_adc_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_up_adc_channel/i_xfer_cntrl'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_up_adc_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_up_adc_channel/i_xfer_cntrl'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_core_rst_reg'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_core_rst_reg'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_mmcm_rst_reg'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_mmcm_rst_reg'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_delay_cntrl/i_delay_rst_reg'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_delay_cntrl/i_delay_rst_reg'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_core_rst_reg'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_core_rst_reg'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_mmcm_rst_reg'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_mmcm_rst_reg'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_xfer_status'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_xfer_status'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_status'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_status'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_xfer_status'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_xfer_status'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/i_xfer_status'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/i_xfer_status'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_up_adc_channel/i_xfer_status'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_up_adc_channel/i_xfer_status'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_up_adc_channel/i_xfer_status'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_up_adc_channel/i_xfer_status'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_up_adc_channel/i_xfer_status'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_up_adc_channel/i_xfer_status'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_clock_mon'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_clock_mon'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_clock_mon'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_clock_mon'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/axi_ad9361_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/axi_ad9361_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_tdd_sync_0/util_tdd_sync_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_tdd_sync/inst'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_tdd_sync_0/util_tdd_sync_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_tdd_sync/inst'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0_board.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk_reset/U0'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0_board.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk_reset/U0'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk_reset/U0'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk_reset/U0'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_fifo_0/util_wfifo_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_adc_fifo/inst'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_fifo_0/util_wfifo_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_adc_fifo/inst'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_pack_0/util_cpack_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_adc_pack/inst'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_pack_0/util_cpack_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_adc_pack/inst'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_fifo_0/util_rfifo_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_fifo_0/util_rfifo_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_dac_upack_0/util_upack_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_dac_upack/inst'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_dac_upack_0/util_upack_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_dac_upack/inst'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0.xdc] for cell 'test_vio_0'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0.xdc] for cell 'test_vio_0'
Parsing XDC File [D:/FPGA/xiaoE/fmcomms2_zed.srcs/constrs_1/new/cdc.xdc]
Finished Parsing XDC File [D:/FPGA/xiaoE/fmcomms2_zed.srcs/constrs_1/new/cdc.xdc]
Parsing XDC File [D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc]
Finished Parsing XDC File [D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc]
Parsing XDC File [D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc]
Finished Parsing XDC File [D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc]
Sourcing Tcl File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/bd/bd.tcl]
Finished Sourcing Tcl File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/bd/bd.tcl]
Sourcing Tcl File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/bd/bd.tcl]
Finished Sourcing Tcl File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/bd/bd.tcl]
Parsing XDC File [D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3/.Xil/Vivado-17120-cduser1/dcp/system_top.xdc]
Finished Parsing XDC File [D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3/.Xil/Vivado-17120-cduser1/dcp/system_top.xdc]
INFO: [Project 1-538] Using original IP XDC constraints instead of the XDC constraints in dcp 'd:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0.dcp'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/system_axi_ad9361_0_pps_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst'
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/system_axi_ad9361_0_pps_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk/inst'
INFO: [Timing 38-35] Done setting XDC timing constraints. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc:1]
WARNING: [Vivado 12-627] No clocks matched 'clk_div_sel_0_s'. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc:1]
INFO: [Vivado 12-626] No clocks found. Please use 'create_clock' or 'create_generated_clock' command to create clocks. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc:1]
get_clocks: Time (s): cpu = 00:00:24 ; elapsed = 00:00:23 . Memory (MB): peak = 1271.273 ; gain = 578.977
WARNING: [Vivado 12-627] No clocks matched 'clk_div_sel_1_s'. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc:1]
INFO: [Vivado 12-626] No clocks found. Please use 'create_clock' or 'create_generated_clock' command to create clocks. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc:1]
CRITICAL WARNING: [Vivado 12-4739] set_clock_groups:No valid object(s) found for '-group [get_clocks clk_div_sel_0_s]'. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc:1]
Resolution: Check if the specified object(s) exists in the current design. If it does, ensure that the correct design hierarchy was specified for the object. If you are working with clocks, make sure create_clock was used to create the clock object before it is referenced.
CRITICAL WARNING: [Vivado 12-4739] set_clock_groups:No valid object(s) found for '-group [get_clocks clk_div_sel_1_s]'. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc:1]
Resolution: Check if the specified object(s) exists in the current design. If it does, ensure that the correct design hierarchy was specified for the object. If you are working with clocks, make sure create_clock was used to create the clock object before it is referenced.
CRITICAL WARNING: [Constraints 18-4644] set_clock_groups: All clock groups specified are empty. Please specify atleast one clock group which is not empty. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc:1]
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk/inst'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_adc_dma/inst'
WARNING: [Vivado 12-1008] No clocks found for command 'get_clocks -of_objects [get_ports -scoped_to_current_instance -quiet {fifo_wr_clk s_axis_aclk m_src_axi_aclk}]'. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc:3]
Resolution: Verify the create_clock command was called to create the clock object before it is referenced.
INFO: [Vivado 12-626] No clocks found. Please use 'create_clock' or 'create_generated_clock' command to create clocks. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc:3]
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc:32]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc:38]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc:44]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc:50]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc:56]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_adc_dma/inst'
Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_dac_dma/inst'
WARNING: [Vivado 12-1008] No clocks found for command 'get_clocks -of_objects [get_ports -scoped_to_current_instance -quiet {fifo_rd_clk m_axis_aclk m_dest_axi_aclk}]'. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc:4]
Resolution: Verify the create_clock command was called to create the clock object before it is referenced.
INFO: [Vivado 12-626] No clocks found. Please use 'create_clock' or 'create_generated_clock' command to create clocks. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc:4]
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc:10]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc:32]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc:38]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc:44]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc:61]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
CRITICAL WARNING: [Common 17-55] 'get_property' expects at least one object. [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc:79]
Resolution: If [get_<value>] was used to populate the object, check to make sure this command returns at least one valid object.
Finished Parsing XDC File [d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_dac_dma/inst'
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 23 instances were transformed.
  IOBUF => IOBUF (IBUF, OBUFT): 15 instances
  OBUFDS => OBUFDS: 8 instances

link_design: Time (s): cpu = 00:00:50 ; elapsed = 00:00:51 . Memory (MB): peak = 1272.488 ; gain = 1026.219
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7z035'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z035'
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Project 1-461] DRC finished with 0 Errors
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:03 ; elapsed = 00:00:02 . Memory (MB): peak = 1272.488 ; gain = 0.000
INFO: [Timing 38-35] Done setting XDC timing constraints.

Starting Logic Optimization Task

Phase 1 Generate And Synthesize Debug Cores
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1700] Loaded user IP repository 'd:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/library'.
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'D:/Xilinx2016/Vivado/2016.4/data/ip'.
INFO: [IP_Flow 19-3806] Processing IP xilinx.com:ip:xsdbm:2.0 for cell dbg_hub_CV.
INFO: [Chipscope 16-220] Re-using generated and synthesized IP, "xilinx.com:ip:xsdbm:2.0", from Vivado IP cache entry "9d1825ff749706ff".
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.120 . Memory (MB): peak = 1272.488 ; gain = 0.000
Phase 1 Generate And Synthesize Debug Cores | Checksum: 2594b46ee

Time (s): cpu = 00:00:07 ; elapsed = 00:00:50 . Memory (MB): peak = 1272.488 ; gain = 0.000
Implement Debug Cores | Checksum: 1d0ade25a

Phase 2 Retarget
INFO: [Opt 31-138] Pushed 2 inverter(s) to 68 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 2 Retarget | Checksum: 1ddf70367

Time (s): cpu = 00:00:11 ; elapsed = 00:00:53 . Memory (MB): peak = 1272.488 ; gain = 0.000

Phase 3 Constant propagation
INFO: [Opt 31-138] Pushed 11 inverter(s) to 61 load pin(s).
INFO: [Opt 31-10] Eliminated 9789 cells.
Phase 3 Constant propagation | Checksum: 1da7e5e49

Time (s): cpu = 00:00:22 ; elapsed = 00:01:05 . Memory (MB): peak = 1272.488 ; gain = 0.000

Phase 4 Sweep
INFO: [Opt 31-12] Eliminated 17472 unconnected nets.
INFO: [Opt 31-11] Eliminated 6909 unconnected cells.
Phase 4 Sweep | Checksum: 1b358ddfc

Time (s): cpu = 00:00:28 ; elapsed = 00:01:11 . Memory (MB): peak = 1272.488 ; gain = 0.000

Phase 5 BUFG optimization
INFO: [Opt 31-12] Eliminated 0 unconnected nets.
INFO: [Opt 31-11] Eliminated 0 unconnected cells.
Phase 5 BUFG optimization | Checksum: 1abe0543e

Time (s): cpu = 00:00:33 ; elapsed = 00:01:15 . Memory (MB): peak = 1272.488 ; gain = 0.000

Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.111 . Memory (MB): peak = 1272.488 ; gain = 0.000
Ending Logic Optimization Task | Checksum: 1abe0543e

Time (s): cpu = 00:00:33 ; elapsed = 00:01:16 . Memory (MB): peak = 1272.488 ; gain = 0.000

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
INFO: [Pwropt 34-9] Applying IDT optimizations ...
INFO: [Pwropt 34-10] Applying ODC optimizations ...
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...
WARNING: [Power 33-230] Invalid input clock frequency for i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf. Out of range!
Resolution: Please refer to Clocking Resources User Guide for valid input clock frequency.
WARNING: [Power 33-230] Invalid input clock frequency for i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf. Out of range!
Resolution: Please refer to Clocking Resources User Guide for valid input clock frequency.
WARNING: [Power 33-230] Invalid input clock frequency for i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf. Out of range!
Resolution: Please refer to Clocking Resources User Guide for valid input clock frequency.
WARNING: [Power 33-230] Invalid input clock frequency for i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf. Out of range!
Resolution: Please refer to Clocking Resources User Guide for valid input clock frequency.

Finished Running Vector-less Activity Propagation


Starting PowerOpt Patch Enables Task
INFO: [Pwropt 34-162] WRITE_MODE attribute of 0 BRAM(s) out of a total of 4 has been updated to save power. Run report_power_opt to get a complete listing of the BRAMs updated.
INFO: [Pwropt 34-201] Structural ODC has moved 0 WE to EN ports
Number of BRAM Ports augmented: 1 newly gated: 6 Total Ports: 8
Ending PowerOpt Patch Enables Task | Checksum: 23712635a

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.150 . Memory (MB): peak = 1478.734 ; gain = 0.000
Ending Power Optimization Task | Checksum: 23712635a

Time (s): cpu = 00:00:17 ; elapsed = 00:00:10 . Memory (MB): peak = 1478.734 ; gain = 206.246
INFO: [Common 17-83] Releasing license: Implementation
42 Infos, 8 Warnings, 14 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
opt_design: Time (s): cpu = 00:00:58 ; elapsed = 00:01:31 . Memory (MB): peak = 1478.734 ; gain = 206.246
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.150 . Memory (MB): peak = 1478.734 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3/system_top_opt.dcp' has been generated.
write_checkpoint: Time (s): cpu = 00:00:10 ; elapsed = 00:00:05 . Memory (MB): peak = 1478.734 ; gain = 0.000
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3/system_top_drc_opted.rpt.
report_drc: Time (s): cpu = 00:00:12 ; elapsed = 00:00:07 . Memory (MB): peak = 1478.734 ; gain = 0.000
INFO: [Chipscope 16-240] Debug cores have already been implemented
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7z035'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z035'
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC 23-20] Rule violation (CHECK-3) Report rule limit reached - REQP-1839 rule limit reached: 20 violations have been found.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[2] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[2] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors, 21 Warnings
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.

Starting Placer Task
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 2 CPUs

Phase 1 Placer Initialization
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.120 . Memory (MB): peak = 1478.734 ; gain = 0.000
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.070 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 1.1 IO Placement/ Clock Placement/ Build Placer Device
INFO: [Timing 38-35] Done setting XDC timing constraints.
Phase 1.1 IO Placement/ Clock Placement/ Build Placer Device | Checksum: a2da120c

Time (s): cpu = 00:00:23 ; elapsed = 00:00:21 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 1.2 Build Placer Netlist Model
Phase 1.2 Build Placer Netlist Model | Checksum: bf1d8ba8

Time (s): cpu = 00:00:39 ; elapsed = 00:00:32 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 1.3 Constrain Clocks/Macros
Phase 1.3 Constrain Clocks/Macros | Checksum: bf1d8ba8

Time (s): cpu = 00:00:39 ; elapsed = 00:00:32 . Memory (MB): peak = 1478.734 ; gain = 0.000
Phase 1 Placer Initialization | Checksum: bf1d8ba8

Time (s): cpu = 00:00:39 ; elapsed = 00:00:33 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 2 Global Placement
Phase 2 Global Placement | Checksum: e12a291f

Time (s): cpu = 00:01:12 ; elapsed = 00:00:53 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: e12a291f

Time (s): cpu = 00:01:13 ; elapsed = 00:00:53 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: 18a412a50

Time (s): cpu = 00:01:22 ; elapsed = 00:00:59 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: 1110f53f8

Time (s): cpu = 00:01:22 ; elapsed = 00:01:00 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 200b7b732

Time (s): cpu = 00:01:22 ; elapsed = 00:01:00 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 3.5 Timing Path Optimizer
Phase 3.5 Timing Path Optimizer | Checksum: 16c5bb665

Time (s): cpu = 00:01:24 ; elapsed = 00:01:01 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 3.6 Small Shape Detail Placement
Phase 3.6 Small Shape Detail Placement | Checksum: 193a40acd

Time (s): cpu = 00:01:39 ; elapsed = 00:01:15 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 3.7 Re-assign LUT pins
Phase 3.7 Re-assign LUT pins | Checksum: 1e8814b97

Time (s): cpu = 00:01:41 ; elapsed = 00:01:17 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 3.8 Pipeline Register Optimization
Phase 3.8 Pipeline Register Optimization | Checksum: 14de393c1

Time (s): cpu = 00:01:41 ; elapsed = 00:01:18 . Memory (MB): peak = 1478.734 ; gain = 0.000
Phase 3 Detail Placement | Checksum: 14de393c1

Time (s): cpu = 00:01:42 ; elapsed = 00:01:18 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization
INFO: [Timing 38-35] Done setting XDC timing constraints.

Phase 4.1.1 Post Placement Optimization
INFO: [Place 30-746] Post Placement Timing Summary WNS=4.360. For the most accurate timing information please run report_timing.
Phase 4.1.1 Post Placement Optimization | Checksum: 13b5599d4

Time (s): cpu = 00:01:57 ; elapsed = 00:01:28 . Memory (MB): peak = 1478.734 ; gain = 0.000
Phase 4.1 Post Commit Optimization | Checksum: 13b5599d4

Time (s): cpu = 00:01:58 ; elapsed = 00:01:29 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: 13b5599d4

Time (s): cpu = 00:01:59 ; elapsed = 00:01:29 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 4.3 Placer Reporting
Phase 4.3 Placer Reporting | Checksum: 13b5599d4

Time (s): cpu = 00:01:59 ; elapsed = 00:01:30 . Memory (MB): peak = 1478.734 ; gain = 0.000

Phase 4.4 Final Placement Cleanup
Phase 4.4 Final Placement Cleanup | Checksum: 8deda294

Time (s): cpu = 00:01:59 ; elapsed = 00:01:30 . Memory (MB): peak = 1478.734 ; gain = 0.000
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 8deda294

Time (s): cpu = 00:02:00 ; elapsed = 00:01:30 . Memory (MB): peak = 1478.734 ; gain = 0.000
Ending Placer Task | Checksum: 4ad14369

Time (s): cpu = 00:02:00 ; elapsed = 00:01:30 . Memory (MB): peak = 1478.734 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
60 Infos, 29 Warnings, 14 Critical Warnings and 0 Errors encountered.
place_design completed successfully
place_design: Time (s): cpu = 00:02:11 ; elapsed = 00:01:37 . Memory (MB): peak = 1478.734 ; gain = 0.000
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:16 ; elapsed = 00:00:06 . Memory (MB): peak = 1478.734 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3/system_top_placed.dcp' has been generated.
write_checkpoint: Time (s): cpu = 00:00:18 ; elapsed = 00:00:08 . Memory (MB): peak = 1478.734 ; gain = 0.000
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.219 . Memory (MB): peak = 1478.734 ; gain = 0.000
report_utilization: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.571 . Memory (MB): peak = 1478.734 ; gain = 0.000
report_control_sets: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.170 . Memory (MB): peak = 1478.734 ; gain = 0.000
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7z035'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z035'
Running DRC as a precondition to command route_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 2 CPUs
Checksum: PlaceDB: 394ceec0 ConstDB: 0 ShapeSum: 118454a9 RouteDB: 0

Phase 1 Build RT Design
Phase 1 Build RT Design | Checksum: d46eb931

Time (s): cpu = 00:02:50 ; elapsed = 00:02:22 . Memory (MB): peak = 1692.301 ; gain = 213.566

Phase 2 Router Initialization

Phase 2.1 Create Timer
Phase 2.1 Create Timer | Checksum: d46eb931

Time (s): cpu = 00:02:52 ; elapsed = 00:02:23 . Memory (MB): peak = 1692.301 ; gain = 213.566

Phase 2.2 Fix Topology Constraints
Phase 2.2 Fix Topology Constraints | Checksum: d46eb931

Time (s): cpu = 00:02:52 ; elapsed = 00:02:23 . Memory (MB): peak = 1692.301 ; gain = 213.566

Phase 2.3 Pre Route Cleanup
Phase 2.3 Pre Route Cleanup | Checksum: d46eb931

Time (s): cpu = 00:02:52 ; elapsed = 00:02:23 . Memory (MB): peak = 1692.301 ; gain = 213.566
 Number of Nodes with overlaps = 0

Phase 2.4 Update Timing
Phase 2.4 Update Timing | Checksum: 1ebe0a1f4

Time (s): cpu = 00:03:19 ; elapsed = 00:02:42 . Memory (MB): peak = 1822.168 ; gain = 343.434
INFO: [Route 35-416] Intermediate Timing Summary | WNS=4.150  | TNS=0.000  | WHS=-0.262 | THS=-563.440|

Phase 2 Router Initialization | Checksum: 1ea045f1d

Time (s): cpu = 00:03:29 ; elapsed = 00:02:48 . Memory (MB): peak = 1822.168 ; gain = 343.434

Phase 3 Initial Routing
Phase 3 Initial Routing | Checksum: 11d0334b0

Time (s): cpu = 00:03:37 ; elapsed = 00:02:52 . Memory (MB): peak = 1822.168 ; gain = 343.434

Phase 4 Rip-up And Reroute

Phase 4.1 Global Iteration 0
 Number of Nodes with overlaps = 1414
 Number of Nodes with overlaps = 55
 Number of Nodes with overlaps = 0

Phase 4.1.1 Update Timing
Phase 4.1.1 Update Timing | Checksum: 18242c275

Time (s): cpu = 00:03:56 ; elapsed = 00:03:02 . Memory (MB): peak = 1822.168 ; gain = 343.434
INFO: [Route 35-416] Intermediate Timing Summary | WNS=2.540  | TNS=0.000  | WHS=N/A    | THS=N/A    |

Phase 4.1 Global Iteration 0 | Checksum: 1cfdcbfd1

Time (s): cpu = 00:03:56 ; elapsed = 00:03:03 . Memory (MB): peak = 1822.168 ; gain = 343.434
Phase 4 Rip-up And Reroute | Checksum: 1cfdcbfd1

Time (s): cpu = 00:03:56 ; elapsed = 00:03:03 . Memory (MB): peak = 1822.168 ; gain = 343.434

Phase 5 Delay and Skew Optimization

Phase 5.1 Delay CleanUp
Phase 5.1 Delay CleanUp | Checksum: 1cfdcbfd1

Time (s): cpu = 00:03:56 ; elapsed = 00:03:03 . Memory (MB): peak = 1822.168 ; gain = 343.434

Phase 5.2 Clock Skew Optimization
Phase 5.2 Clock Skew Optimization | Checksum: 1cfdcbfd1

Time (s): cpu = 00:03:57 ; elapsed = 00:03:03 . Memory (MB): peak = 1822.168 ; gain = 343.434
Phase 5 Delay and Skew Optimization | Checksum: 1cfdcbfd1

Time (s): cpu = 00:03:57 ; elapsed = 00:03:03 . Memory (MB): peak = 1822.168 ; gain = 343.434

Phase 6 Post Hold Fix

Phase 6.1 Hold Fix Iter

Phase 6.1.1 Update Timing
Phase 6.1.1 Update Timing | Checksum: 205dd367e

Time (s): cpu = 00:04:00 ; elapsed = 00:03:06 . Memory (MB): peak = 1822.168 ; gain = 343.434
INFO: [Route 35-416] Intermediate Timing Summary | WNS=2.613  | TNS=0.000  | WHS=0.065  | THS=0.000  |

Phase 6.1 Hold Fix Iter | Checksum: 202907b0f

Time (s): cpu = 00:04:00 ; elapsed = 00:03:06 . Memory (MB): peak = 1822.168 ; gain = 343.434
Phase 6 Post Hold Fix | Checksum: 202907b0f

Time (s): cpu = 00:04:01 ; elapsed = 00:03:06 . Memory (MB): peak = 1822.168 ; gain = 343.434

Phase 7 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 1.37516 %
  Global Horizontal Routing Utilization  = 1.88332 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Phase 7 Route finalize | Checksum: 1c2ef86d6

Time (s): cpu = 00:04:01 ; elapsed = 00:03:06 . Memory (MB): peak = 1822.168 ; gain = 343.434

Phase 8 Verifying routed nets

 Verification completed successfully
Phase 8 Verifying routed nets | Checksum: 1c2ef86d6

Time (s): cpu = 00:04:01 ; elapsed = 00:03:06 . Memory (MB): peak = 1822.168 ; gain = 343.434

Phase 9 Depositing Routes
Phase 9 Depositing Routes | Checksum: 1a76da5a1

Time (s): cpu = 00:04:05 ; elapsed = 00:03:10 . Memory (MB): peak = 1822.168 ; gain = 343.434

Phase 10 Post Router Timing
INFO: [Route 35-57] Estimated Timing Summary | WNS=2.613  | TNS=0.000  | WHS=0.065  | THS=0.000  |

INFO: [Route 35-327] The final timing numbers are based on the router estimated timing analysis. For a complete and accurate timing signoff, please run report_timing_summary.
Phase 10 Post Router Timing | Checksum: 1a76da5a1

Time (s): cpu = 00:04:05 ; elapsed = 00:03:10 . Memory (MB): peak = 1822.168 ; gain = 343.434
INFO: [Route 35-16] Router Completed Successfully

Time (s): cpu = 00:04:08 ; elapsed = 00:03:11 . Memory (MB): peak = 1822.168 ; gain = 343.434

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
73 Infos, 29 Warnings, 14 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:04:20 ; elapsed = 00:03:18 . Memory (MB): peak = 1822.168 ; gain = 343.434
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:18 ; elapsed = 00:00:07 . Memory (MB): peak = 1822.168 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3/system_top_routed.dcp' has been generated.
write_checkpoint: Time (s): cpu = 00:00:19 ; elapsed = 00:00:09 . Memory (MB): peak = 1822.168 ; gain = 0.000
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3/system_top_drc_routed.rpt.
report_drc: Time (s): cpu = 00:00:21 ; elapsed = 00:00:11 . Memory (MB): peak = 1822.168 ; gain = 0.000
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 2 threads
INFO: [Coretcl 2-1520] The results of Report Methodology are in file D:/FPGA/xiaoE/fmcomms2_zed.runs/impl_3/system_top_methodology_drc_routed.rpt.
report_methodology: Time (s): cpu = 00:00:35 ; elapsed = 00:00:21 . Memory (MB): peak = 1822.168 ; gain = 0.000
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -2, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
INFO: [Timing 38-35] Done setting XDC timing constraints.
report_timing_summary: Time (s): cpu = 00:00:09 ; elapsed = 00:00:07 . Memory (MB): peak = 1822.168 ; gain = 0.000
Command: report_power -file system_top_power_routed.rpt -pb system_top_power_summary_routed.pb -rpx system_top_power_routed.rpx
Running Vector-less Activity Propagation...
WARNING: [Power 33-230] Invalid input clock frequency for i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf. Out of range!
Resolution: Please refer to Clocking Resources User Guide for valid input clock frequency.
WARNING: [Power 33-230] Invalid input clock frequency for i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf. Out of range!
Resolution: Please refer to Clocking Resources User Guide for valid input clock frequency.
WARNING: [Power 33-230] Invalid input clock frequency for i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf. Out of range!
Resolution: Please refer to Clocking Resources User Guide for valid input clock frequency.
WARNING: [Power 33-230] Invalid input clock frequency for i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf. Out of range!
Resolution: Please refer to Clocking Resources User Guide for valid input clock frequency.

Finished Running Vector-less Activity Propagation
WARNING: [Power 33-332] Found switching activity that implies high-fanout reset nets being asserted for excessive periods of time which may result in inaccurate power analysis.
Resolution: To review and fix problems, please run Power Constraints Advisor in the GUI from Tools > Power Constraints Advisor or run report_power with the -advisory option to generate a text report.
83 Infos, 34 Warnings, 14 Critical Warnings and 0 Errors encountered.
report_power completed successfully
report_power: Time (s): cpu = 00:00:17 ; elapsed = 00:00:09 . Memory (MB): peak = 1885.676 ; gain = 63.508
Command: write_bitstream -force -no_partial_bitfile system_top.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7z035'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z035'
Running DRC as a precondition to command write_bitstream
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC 23-20] Rule violation (CHECK-3) Report rule limit reached - REQP-1839 rule limit reached: 20 violations have been found.
WARNING: [DRC 23-20] Rule violation (DPOP-1) PREG Output pipelining - DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
WARNING: [DRC 23-20] Rule violation (DPOP-1) PREG Output pipelining - DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
WARNING: [DRC 23-20] Rule violation (DPOP-1) PREG Output pipelining - DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
WARNING: [DRC 23-20] Rule violation (DPOP-1) PREG Output pipelining - DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[2] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[2] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (RTSTAT-10) No routable loads - 27 net(s) have no routable loads. The problem bus(es) and/or net(s) are dbg_hub/inst/RUNTEST, dbg_hub/inst/TCK, dbg_hub/inst/TMS, dbg_hub/inst/UPDATE_temp_i, dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gr1.gr1_int.rfwft/aempty_fwft_i, dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD7_CTL/ctl_reg[2:0], dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD7_CTL/ctl_reg_en_2[1], dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_en_2[1], dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_capture[0], dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0], dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags[7:0], dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_runtest[0], dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_i, dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/ram_full_i, dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/rd_rst_reg[0] (the first 15 of 18 listed).
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 26 Warnings, 56 Advisories
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Bitstream compression saved 75128640 bits.
Writing bitstream ./system_top.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-118] WebTalk data collection is enabled (User setting is ON. Install Setting is ON.).
INFO: [Common 17-83] Releasing license: Implementation
146 Infos, 60 Warnings, 14 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:01:35 ; elapsed = 00:01:30 . Memory (MB): peak = 2408.051 ; gain = 522.375
INFO: [Common 17-206] Exiting Vivado at Tue Mar 17 17:10:09 2020...
