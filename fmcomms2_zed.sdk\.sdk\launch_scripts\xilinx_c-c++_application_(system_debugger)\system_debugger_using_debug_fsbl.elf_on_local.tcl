connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf
bpadd -addr &main
