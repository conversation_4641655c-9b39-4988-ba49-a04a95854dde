{
   "ExpandedHierarchyInLayout":"",
   "guistr":"# # String gsaved with Nlview 6.8.11  2018-08-07 bk=1.4403 VDI=40 GEI=35 GUI=JA:9.0 non-TLS
#  -string -flagsOSRD
preplace port enable -pg 1 -y 160 -defaultsOSRD
preplace port rx_clk_in_p -pg 1 -y 170 -defaultsOSRD
preplace port ps_intr_10 -pg 1 -y 2500 -defaultsOSRD
preplace port spi0_sdi_i -pg 1 -y 3420 -defaultsOSRD
preplace port spi0_csn_2_o -pg 1 -y 3550 -defaultsOSRD
preplace port fixed_io -pg 1 -y 3350 -defaultsOSRD
preplace port tdd_sync_o -pg 1 -y 2380 -defaultsOSRD
preplace port up_txnrx -pg 1 -y 470 -defaultsOSRD
preplace port tx_frame_out_n -pg 1 -y 100 -defaultsOSRD
preplace port rx_frame_in_n -pg 1 -y 230 -defaultsOSRD
preplace port ps_intr_00 -pg 1 -y 1700 -defaultsOS<PERSON>
preplace port up_enable -pg 1 -y 450 -defaults<PERSON><PERSON>
preplace port tx_clk_out_n -pg 1 -y 60 -defaultsOSRD
preplace port ps_intr_01 -pg 1 -y 1720 -defaultsOSRD
preplace port tx_frame_out_p -pg 1 -y 80 -defaultsOSRD
preplace port rx_frame_in_p -pg 1 -y 210 -defaultsOSRD
preplace port ps_intr_02 -pg 1 -y 1740 -defaultsOSRD
preplace port spi0_clk_o -pg 1 -y 3410 -defaultsOSRD
preplace port spi0_csn_i -pg 1 -y 3610 -defaultsOSRD
preplace port tx_clk_out_p -pg 1 -y 40 -defaultsOSRD
preplace port ps_intr_03 -pg 1 -y 1760 -defaultsOSRD
preplace port ps_intr_04 -pg 1 -y 1780 -defaultsOSRD
preplace port spi0_sdo_i -pg 1 -y 3400 -defaultsOSRD
preplace port tdd_sync_t -pg 1 -y 2080 -defaultsOSRD
preplace port ps_intr_05 -pg 1 -y 1800 -defaultsOSRD
preplace port ddr -pg 1 -y 3330 -defaultsOSRD
preplace port ps_intr_06 -pg 1 -y 2460 -defaultsOSRD
preplace port spi0_csn_1_o -pg 1 -y 3530 -defaultsOSRD
preplace port tdd_sync_i -pg 1 -y 2580 -defaultsOSRD
preplace port ps_intr_07 -pg 1 -y 2480 -defaultsOSRD
preplace port spi0_csn_0_o -pg 1 -y 3510 -defaultsOSRD
preplace port ps_intr_08 -pg 1 -y 1820 -defaultsOSRD
preplace port FCLK_CLK0 -pg 1 -y 3780 -defaultsOSRD
preplace port rx_clk_in_n -pg 1 -y 190 -defaultsOSRD
preplace port ps_intr_09 -pg 1 -y 1840 -defaultsOSRD
preplace port spi0_clk_i -pg 1 -y 3380 -defaultsOSRD
preplace port txnrx -pg 1 -y 180 -defaultsOSRD
preplace port spi0_sdo_o -pg 1 -y 3450 -defaultsOSRD
preplace portBus rx_data_in_p -pg 1 -y 250 -defaultsOSRD
preplace portBus gpio_o -pg 1 -y 3290 -defaultsOSRD
preplace portBus gpio_t -pg 1 -y 3310 -defaultsOSRD
preplace portBus tx_data_out_n -pg 1 -y 140 -defaultsOSRD
preplace portBus gpio_i -pg 1 -y 3360 -defaultsOSRD
preplace portBus tx_data_out_p -pg 1 -y 120 -defaultsOSRD
preplace portBus rx_data_in_n -pg 1 -y 270 -defaultsOSRD
preplace inst util_ad9361_tdd_sync -pg 1 -lvl 9 -y 2550 -defaultsOSRD
preplace inst axi_hp2_interconnect -pg 1 -lvl 8 -y 2950 -defaultsOSRD
preplace inst axi_ad9361 -pg 1 -lvl 7 -y 400 -defaultsOSRD
preplace inst axi_hp0_interconnect -pg 1 -lvl 8 -y 2460 -defaultsOSRD
preplace inst util_ad9361_divclk_sel_concat -pg 1 -lvl 1 -y 1090 -defaultsOSRD
preplace inst sys_concat_intc -pg 1 -lvl 8 -y 1600 -defaultsOSRD
preplace inst axi_ad9361_adc_dma -pg 1 -lvl 7 -y 1300 -defaultsOSRD
preplace inst util_ad9361_divclk_sel -pg 1 -lvl 2 -y 1090 -defaultsOSRD
preplace inst util_ad9361_divclk_reset -pg 1 -lvl 4 -y 1640 -defaultsOSRD
preplace inst sys_rstgen -pg 1 -lvl 3 -y 2680 -defaultsOSRD
preplace inst axi_ad9361_dac_dma -pg 1 -lvl 7 -y 1870 -defaultsOSRD
preplace inst util_ad9361_adc_fifo -pg 1 -lvl 5 -y 770 -defaultsOSRD
preplace inst util_ad9361_divclk -pg 1 -lvl 3 -y 1110 -defaultsOSRD
preplace inst sys_ps7 -pg 1 -lvl 9 -y 3600 -defaultsOSRD
preplace inst axi_cpu_interconnect -pg 1 -lvl 6 -y 2140 -defaultsOSRD
preplace inst util_ad9361_dac_upack -pg 1 -lvl 6 -y 1320 -defaultsOSRD
preplace inst util_ad9361_adc_pack -pg 1 -lvl 6 -y 770 -defaultsOSRD
preplace inst axi_ad9361_dac_fifo -pg 1 -lvl 5 -y 1410 -defaultsOSRD
preplace inst axi_hp1_interconnect -pg 1 -lvl 8 -y 2710 -defaultsOSRD
preplace netloc axi_ad9361_dac_valid_i1 1 4 4 1450 1660 NJ 1660 NJ 1660 3040
preplace netloc S00_AXI_3 1 7 1 3110
preplace netloc axi_ad9361_dac_enable_i0 1 4 4 1350 980 NJ 980 NJ 980 3060
preplace netloc S00_AXI_4 1 7 1 3020
preplace netloc axi_ad9361_dac_enable_i1 1 4 4 1320 990 NJ 990 NJ 990 2990
preplace netloc util_ad9361_adc_fifo_dout_enable_0 1 5 1 1940
preplace netloc ps_intr_06_1 1 0 8 20J 1770 NJ 1770 NJ 1770 NJ 1770 NJ 1770 1910J 1570 NJ 1570 NJ
preplace netloc sys_ps7_GPIO_O 1 9 1 4060J
preplace netloc util_ad9361_adc_fifo_dout_enable_1 1 5 1 1940
preplace netloc axi_ad9361_adc_data_q0 1 4 4 1380 400 1750J 970 NJ 970 3140
preplace netloc up_enable_1 1 0 7 NJ 450 NJ 450 NJ 450 NJ 450 1300J 460 1800J 570 2280J
preplace netloc util_ad9361_adc_fifo_dout_enable_2 1 5 1 1830
preplace netloc util_ad9361_adc_fifo_dout_valid_0 1 5 1 1940
preplace netloc axi_ad9361_adc_data_q1 1 4 4 1320 350 NJ 350 2560J 810 2930
preplace netloc ps_intr_02_1 1 0 8 -10J 1530 NJ 1530 NJ 1530 NJ 1530 1260J 1680 NJ 1680 NJ 1680 3090J
preplace netloc util_ad9361_adc_fifo_dout_enable_3 1 5 1 1760
preplace netloc util_ad9361_adc_fifo_dout_valid_1 1 5 1 1870
preplace netloc util_ad9361_divclk_clk_out 1 3 4 870 1110 1300 550 1810 1170 2370
preplace netloc axi_ad9361_l_clk 1 2 6 520 1040 NJ 1040 1290 380 NJ 380 2550 870 3130
preplace netloc ps_intr_01_1 1 0 8 -20J 1170 NJ 1170 NJ 1170 NJ 1170 1280J 1090 NJ 1090 NJ 1090 3140J
preplace netloc axi_ad9361_adc_valid_q0 1 4 4 1420 430 NJ 430 2480J 920 3100
preplace netloc axi_ad9361_dac_valid_q0 1 4 4 1440 1670 NJ 1670 NJ 1670 3070
preplace netloc axi_ad9361_adc_dma_fifo_wr_overflow 1 4 3 1450 1040 NJ 1040 2380J
preplace netloc util_ad9361_adc_fifo_dout_valid_2 1 5 1 1800
preplace netloc axi_ad9361_adc_valid_q1 1 4 4 1340 390 NJ 390 2310J 930 3010
preplace netloc axi_ad9361_tx_frame_out_n 1 7 3 NJ 100 NJ 100 NJ
preplace netloc ps_intr_09_1 1 0 8 NJ 1840 NJ 1840 NJ 1840 NJ 1840 NJ 1840 NJ 1840 2280J 2010 3120J
preplace netloc axi_ad9361_dac_valid_q1 1 4 4 1310 520 NJ 520 2490J 860 2920
preplace netloc util_ad9361_adc_fifo_dout_valid_3 1 5 1 1770
preplace netloc sys_ps7_GPIO_T 1 9 1 4070J
preplace netloc axi_ad9361_tx_frame_out_p 1 7 3 NJ 80 NJ 80 NJ
preplace netloc axi_ad9361_dac_dma_fifo_rd_dout 1 5 2 1940 1810 2290J
preplace netloc util_ad9361_dac_upack_dac_valid 1 6 1 2390
preplace netloc util_ad9361_dac_upack_dac_data_0 1 4 3 1450 1140 NJ 1140 2280
preplace netloc rx_frame_in_n_1 1 0 7 NJ 230 NJ 230 NJ 230 NJ 230 NJ 230 NJ 230 NJ
preplace netloc ps_intr_05_1 1 0 8 NJ 1800 NJ 1800 NJ 1800 NJ 1800 1280J 1700 1900J 1550 NJ 1550 NJ
preplace netloc sys_ps7_FCLK_RESET0_N 1 2 8 520 2570 NJ 2570 NJ 2570 NJ 2570 NJ 2570 2950J 2590 3490J 2630 4030
preplace netloc util_ad9361_divclk_sel_Res 1 2 1 510
preplace netloc util_ad9361_dac_upack_dac_data_1 1 4 3 1440 1120 NJ 1120 2290
preplace netloc util_ad9361_tdd_sync_sync_out 1 6 4 2570 2320 NJ 2320 NJ 2320 4090
preplace netloc util_ad9361_dac_upack_dac_data_2 1 4 3 1420 1130 NJ 1130 2320
preplace netloc util_ad9361_adc_fifo_din_ovf 1 5 2 1770 400 2390J
preplace netloc ps_intr_07_1 1 0 8 40J 1780 NJ 1780 NJ 1780 NJ 1780 NJ 1780 1920J 1590 NJ 1590 NJ
preplace netloc util_ad9361_dac_upack_dac_data_3 1 4 3 1430 1150 NJ 1150 2310
preplace netloc spi0_sdi_i_1 1 0 10 20J 3280 NJ 3280 NJ 3280 NJ 3280 NJ 3280 NJ 3280 NJ 3280 NJ 3280 NJ 3280 4020
preplace netloc ps_intr_00_1 1 0 8 -30J 1020 NJ 1020 NJ 1020 NJ 1020 1280J 1060 NJ 1060 NJ 1060 3150J
preplace netloc util_ad9361_divclk_reset_peripheral_aresetn 1 4 1 1250
preplace netloc rx_clk_in_p_1 1 0 7 NJ 170 NJ 170 NJ 170 NJ 170 NJ 170 NJ 170 NJ
preplace netloc axi_ad9361_dac_fifo_din_valid_0 1 5 1 N
preplace netloc sys_ps7_SPI0_SS1_O 1 9 1 4130J
preplace netloc axi_ad9361_dac_fifo_din_valid_1 1 5 1 N
preplace netloc tdd_sync_i_1 1 0 9 NJ 2580 NJ 2580 NJ 2580 NJ 2580 NJ 2580 NJ 2580 NJ 2580 NJ 2580 NJ
preplace netloc rx_clk_in_n_1 1 0 7 NJ 190 NJ 190 NJ 190 NJ 190 NJ 190 NJ 190 NJ
preplace netloc sys_ps7_DDR 1 9 1 4080J
preplace netloc axi_ad9361_dac_fifo_din_valid_2 1 5 1 N
preplace netloc spi0_csn_i_1 1 0 10 NJ 3610 NJ 3610 NJ 3610 NJ 3610 NJ 3610 NJ 3610 NJ 3610 NJ 3610 3460J 3900 3990
preplace netloc axi_hp0_interconnect_M00_AXI 1 8 1 3500
preplace netloc axi_ad9361_dac_fifo_din_valid_3 1 5 1 N
preplace netloc util_ad9361_divclk_reset_peripheral_reset 1 4 2 1270J 530 1760
preplace netloc axi_ad9361_rst 1 4 4 1330 540 NJ 540 2450J 880 3120
preplace netloc axi_ad9361_dac_r1_mode 1 0 8 -10 510 NJ 510 NJ 510 NJ 510 NJ 510 NJ 510 2430J 940 2940
preplace netloc ps_intr_08_1 1 0 8 30J 1790 NJ 1790 NJ 1790 NJ 1790 NJ 1790 1930J 1610 NJ 1610 NJ
preplace netloc axi_ad9361_tx_clk_out_n 1 7 3 NJ 60 NJ 60 NJ
preplace netloc sys_ps7_SPI0_MOSI_O 1 9 1 4110J
preplace netloc axi_ad9361_adc_r1_mode 1 0 8 -20 500 NJ 500 NJ 500 NJ 500 NJ 500 NJ 500 2520J 850 2950
preplace netloc axi_ad9361_tx_clk_out_p 1 7 3 NJ 40 NJ 40 NJ
preplace netloc axi_ad9361_adc_enable_i0 1 4 4 1400 360 1940J 560 2510J 820 3000
preplace netloc axi_ad9361_adc_dma_irq 1 7 1 3130
preplace netloc axi_ad9361_adc_enable_i1 1 4 4 1360 370 1890J 550 2440J 890 3020
preplace netloc util_ad9361_divclk_sel_concat_dout 1 1 1 NJ
preplace netloc axi_cpu_interconnect_M08_AXI 1 6 1 2430
preplace netloc gpio_i_1 1 0 10 0J 3300 NJ 3300 NJ 3300 NJ 3300 NJ 3300 NJ 3300 NJ 3300 NJ 3300 NJ 3300 3990
preplace netloc ps_intr_04_1 1 0 8 0J 1740 NJ 1740 NJ 1740 NJ 1740 NJ 1740 1890J 1530 NJ 1530 NJ
preplace netloc ps_intr_10_1 1 0 8 10J 1540 NJ 1540 NJ 1540 NJ 1540 1240J 1690 NJ 1690 NJ 1690 3140J
preplace netloc axi_ad9361_dac_dma_fifo_rd_underflow 1 4 3 1430 1800 NJ 1800 2320J
preplace netloc util_ad9361_adc_pack_adc_sync 1 6 1 2390
preplace netloc axi_ad9361_tdd_sync_cntr 1 7 3 NJ 220 3530 2080 NJ
preplace netloc axi_ad9361_txnrx 1 7 3 NJ 180 NJ 180 NJ
preplace netloc axi_ad9361_enable 1 7 3 NJ 160 NJ 160 NJ
preplace netloc axi_ad9361_dac_fifo_din_enable_0 1 5 1 N
preplace netloc up_txnrx_1 1 0 7 NJ 470 NJ 470 NJ 470 NJ 470 1300J 480 1780J 580 2500J
preplace netloc axi_hp1_interconnect_M00_AXI 1 8 1 3470
preplace netloc axi_ad9361_dac_fifo_din_enable_1 1 5 1 N
preplace netloc ps_intr_03_1 1 0 8 NJ 1760 NJ 1760 NJ 1760 NJ 1760 1360J 1720 NJ 1720 NJ 1720 3100J
preplace netloc axi_ad9361_dac_fifo_din_enable_2 1 5 1 N
preplace netloc util_ad9361_dac_upack_dac_valid_out_0 1 4 3 1400 1070 NJ 1070 2340
preplace netloc sys_200m_clk 1 6 4 2580 2310 NJ 2310 N 2310 4040
preplace netloc axi_ad9361_dac_fifo_din_enable_3 1 5 1 N
preplace netloc util_ad9361_dac_upack_dac_valid_out_1 1 4 3 1410 1100 NJ 1100 2330
preplace netloc util_ad9361_dac_upack_dac_valid_out_2 1 4 3 1390 1110 NJ 1110 2350
preplace netloc sys_concat_intc_dout 1 8 1 3510
preplace netloc axi_ad9361_adc_enable_q0 1 4 4 1410 410 NJ 410 2540J 830 2970
preplace netloc util_ad9361_dac_upack_dac_valid_out_3 1 4 3 1380 1080 NJ 1080 2360
preplace netloc axi_ad9361_dac_enable_q0 1 4 4 1360 1000 NJ 1000 NJ 1000 3050
preplace netloc axi_ad9361_adc_enable_q1 1 4 4 1370 420 NJ 420 2530J 840 2960
preplace netloc sys_ps7_FIXED_IO 1 9 1 4090J
preplace netloc axi_ad9361_dac_fifo_dout_unf 1 5 2 1880 530 NJ
preplace netloc axi_ad9361_dac_enable_q1 1 4 4 1340 1010 NJ 1010 NJ 1010 2980
preplace netloc util_ad9361_adc_pack_adc_data 1 6 1 2400
preplace netloc rx_frame_in_p_1 1 0 7 NJ 210 NJ 210 NJ 210 NJ 210 NJ 210 NJ 210 NJ
preplace netloc axi_ad9361_dac_fifo_dout_data_0 1 5 2 1790 440 2520J
preplace netloc axi_ad9361_tx_data_out_n 1 7 3 NJ 140 NJ 140 NJ
preplace netloc axi_ad9361_dac_fifo_dout_data_1 1 5 2 1820 460 2470J
preplace netloc axi_ad9361_dac_fifo_dout_data_2 1 5 2 1840 480 2470J
preplace netloc axi_ad9361_tx_data_out_p 1 7 3 NJ 120 NJ 120 NJ
preplace netloc spi0_clk_i_1 1 0 10 -20J 3270 NJ 3270 NJ 3270 NJ 3270 NJ 3270 NJ 3270 NJ 3270 NJ 3270 NJ 3270 4010
preplace netloc axi_ad9361_dac_fifo_dout_data_3 1 5 2 1860 490 NJ
preplace netloc rx_data_in_n_1 1 0 7 NJ 270 NJ 270 NJ 270 NJ 270 NJ 270 NJ 270 NJ
preplace netloc spi0_sdo_i_1 1 0 10 30J 3290 NJ 3290 NJ 3290 NJ 3290 NJ 3290 NJ 3290 NJ 3290 NJ 3290 NJ 3290 4000
preplace netloc util_ad9361_adc_pack_adc_valid 1 6 1 2410
preplace netloc util_ad9361_adc_fifo_dout_data_0 1 5 1 1940
preplace netloc sys_cpu_reset 1 3 1 N
preplace netloc axi_cpu_interconnect_M09_AXI 1 6 1 2560
preplace netloc axi_cpu_interconnect_M07_AXI 1 6 1 2420
preplace netloc util_ad9361_adc_fifo_dout_data_1 1 5 1 1850
preplace netloc sys_ps7_SPI0_SS2_O 1 9 1 4140J
preplace netloc util_ad9361_adc_fifo_dout_data_2 1 5 1 1770
preplace netloc axi_ad9361_adc_data_i0 1 4 4 1450 490 1730J 950 NJ 950 3150
preplace netloc sys_cpu_clk 1 2 8 510 2360 NJ 2360 NJ 2360 1870 1830 2470 2020 2930 2330 3480 3910 4130
preplace netloc util_ad9361_adc_fifo_dout_data_3 1 5 1 1760
preplace netloc sys_cpu_resetn 1 3 6 870 2370 NJ 2370 1780 1820 2300 2030 2920 2340 3520
preplace netloc axi_ad9361_adc_data_i1 1 4 4 1390 440 1740J 960 NJ 960 3090
preplace netloc sys_ps7_SPI0_SS_O 1 9 1 4120J
preplace netloc sys_ps7_SPI0_SCLK_O 1 9 1 4100J
preplace netloc axi_hp2_interconnect_M00_AXI 1 8 1 3460
preplace netloc axi_ad9361_adc_valid_i0 1 4 4 1440 450 NJ 450 2290J 900 3110
preplace netloc rx_data_in_p_1 1 0 7 NJ 250 NJ 250 NJ 250 NJ 250 NJ 250 NJ 250 NJ
preplace netloc S00_AXI_1 1 5 5 1920 2440 2420J 2300 NJ 2300 NJ 2300 4050
preplace netloc axi_ad9361_dac_dma_irq 1 7 1 3150
preplace netloc axi_ad9361_dac_valid_i0 1 4 4 1370 1050 NJ 1050 NJ 1050 3080
preplace netloc axi_ad9361_adc_valid_i1 1 4 4 1430 470 NJ 470 2460J 910 3030
levelinfo -pg 1 -50 140 380 700 1070 1590 2130 2750 3310 3760 4160 -top -20 -bot 4120
"
}
0
