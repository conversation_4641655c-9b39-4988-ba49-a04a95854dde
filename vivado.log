#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Sun Sep 28 10:45:55 2025
# Process ID: 28904
# Current directory: D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent26472 D:\WORK\fuyong\2025\AD9361_PRJ\AD9361_Relative\PL_PS\PS_Z7035\fmcomms2_zed.xpr
# Log file: D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/vivado.log
# Journal file: D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035\vivado.jou
#-----------------------------------------------------------
start_gui
open_project D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/fmcomms2_zed.xpr
Scanning sources...
Finished scanning sources
WARNING: [filemgmt 56-3] IP Repository Path: Could not find the directory 'D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/hdl-hdl_2017_r1/hdl-hdl_2017_r1/library'.
INFO: [IP_Flow 19-234] Refreshing IP repositories
WARNING: [IP_Flow 19-2248] Failed to load user IP repository 'd:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/hdl-hdl_2017_r1/hdl-hdl_2017_r1/library'; Can't find the specified path.
If this directory should no longer be in your list of user repositories, go to the IP Settings dialog and remove it.
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'C:/Xilinx/Vivado/2018.3/data/ip'.
open_project: Time (s): cpu = 00:00:12 ; elapsed = 00:00:11 . Memory (MB): peak = 814.547 ; gain = 151.641
update_compile_order -fileset sources_1
open_bd_design {D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/fmcomms2_zed.srcs/sources_1/bd/system/system.bd}
Adding cell -- analog.com:user:axi_ad9361:1.0 - axi_ad9361
Adding cell -- analog.com:user:axi_dmac:1.0 - axi_ad9361_adc_dma
CRITICAL WARNING: [BD 41-51] Could not find bus definition for the interface: fifo_wr 
CRITICAL WARNING: [BD 41-49] Could not find abstraction definition for the interface: fifo_wr 
CRITICAL WARNING: [BD 41-49] Could not find abstraction definition for the interface: fifo_wr 
Adding cell -- analog.com:user:axi_dmac:1.0 - axi_ad9361_dac_dma
CRITICAL WARNING: [BD 41-51] Could not find bus definition for the interface: fifo_rd 
CRITICAL WARNING: [BD 41-49] Could not find abstraction definition for the interface: fifo_rd 
CRITICAL WARNING: [BD 41-49] Could not find abstraction definition for the interface: fifo_rd 
Adding cell -- analog.com:user:util_rfifo:1.0 - axi_ad9361_dac_fifo
Adding cell -- analog.com:user:util_wfifo:1.0 - util_ad9361_adc_fifo
Adding cell -- analog.com:user:util_cpack:1.0 - util_ad9361_adc_pack
Adding cell -- analog.com:user:util_upack:1.0 - util_ad9361_dac_upack
Adding cell -- analog.com:user:util_clkdiv:1.0 - util_ad9361_divclk
Adding cell -- analog.com:user:util_tdd_sync:1.0 - util_ad9361_tdd_sync
Adding cell -- xilinx.com:ip:axi_interconnect:2.1 - axi_cpu_interconnect
Adding cell -- xilinx.com:ip:axi_crossbar:2.1 - xbar
Adding cell -- xilinx.com:ip:axi_interconnect:2.1 - axi_hp0_interconnect
Adding cell -- xilinx.com:ip:axi_interconnect:2.1 - axi_hp1_interconnect
Adding cell -- xilinx.com:ip:axi_interconnect:2.1 - axi_hp2_interconnect
Adding cell -- xilinx.com:ip:xlconcat:2.1 - sys_concat_intc
Adding cell -- xilinx.com:ip:processing_system7:5.5 - sys_ps7
Adding cell -- xilinx.com:ip:proc_sys_reset:5.0 - sys_rstgen
Adding cell -- xilinx.com:ip:proc_sys_reset:5.0 - util_ad9361_divclk_reset
Adding cell -- xilinx.com:ip:util_reduced_logic:2.0 - util_ad9361_divclk_sel
Adding cell -- xilinx.com:ip:xlconcat:2.1 - util_ad9361_divclk_sel_concat
WARNING: [BD 41-1731] Type mismatch between connected pins: /sys_ps7/FCLK_CLK0(clk) and /util_ad9361_tdd_sync/clk(undef)
WARNING: [BD 41-1731] Type mismatch between connected pins: /sys_ps7/FCLK_CLK1(clk) and /axi_ad9361/delay_clk(undef)
WARNING: [BD 41-1731] Type mismatch between connected pins: /sys_rstgen/peripheral_aresetn(rst) and /util_ad9361_tdd_sync/rstn(undef)
WARNING: [BD 41-1731] Type mismatch between connected pins: /util_ad9361_divclk/clk_out(clk) and /axi_ad9361_dac_fifo/din_clk(undef)
WARNING: [BD 41-1731] Type mismatch between connected pins: /util_ad9361_divclk/clk_out(clk) and /util_ad9361_adc_fifo/dout_clk(undef)
WARNING: [BD 41-1731] Type mismatch between connected pins: /util_ad9361_divclk/clk_out(clk) and /util_ad9361_adc_pack/adc_clk(undef)
WARNING: [BD 41-1731] Type mismatch between connected pins: /util_ad9361_divclk/clk_out(clk) and /util_ad9361_dac_upack/dac_clk(undef)
WARNING: [BD 41-1731] Type mismatch between connected pins: /util_ad9361_divclk_reset/peripheral_aresetn(rst) and /axi_ad9361_dac_fifo/din_rstn(undef)
WARNING: [BD 41-1731] Type mismatch between connected pins: /util_ad9361_divclk_reset/peripheral_aresetn(rst) and /util_ad9361_adc_fifo/dout_rstn(undef)
WARNING: [BD 41-1731] Type mismatch between connected pins: /util_ad9361_divclk_reset/peripheral_reset(rst) and /util_ad9361_adc_pack/adc_rst(undef)
Successfully read diagram <system> from BD file <D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/fmcomms2_zed.srcs/sources_1/bd/system/system.bd>
open_bd_design: Time (s): cpu = 00:00:04 ; elapsed = 00:00:06 . Memory (MB): peak = 886.191 ; gain = 12.148
ERROR: [Vivado 12-106] *** Exception: java.lang.NullPointerException (See D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/vivado_pid28904.debug)
ERROR: [Vivado 12-106] *** Exception: java.lang.NullPointerException (See D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/vivado_pid28904.debug)
CRITICAL WARNING: [PSU-1]  Parameter : PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_2 has negative value -0.009 . PS DDR interfaces might fail when entering negative DQS skew values. 
CRITICAL WARNING: [PSU-2]  Parameter : PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_3 has negative value -0.061 . PS DDR interfaces might fail when entering negative DQS skew values. 
CRITICAL WARNING: [PSU-1]  Parameter : PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_2 has negative value -0.009 . PS DDR interfaces might fail when entering negative DQS skew values. 
CRITICAL WARNING: [PSU-2]  Parameter : PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_3 has negative value -0.061 . PS DDR interfaces might fail when entering negative DQS skew values. 
WARNING: [IP_Flow 19-474] Invalid Parameter 'Component_Name'
CRITICAL WARNING: [PSU-1]  Parameter : PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_2 has negative value -0.009 . PS DDR interfaces might fail when entering negative DQS skew values. 
CRITICAL WARNING: [PSU-2]  Parameter : PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_3 has negative value -0.061 . PS DDR interfaces might fail when entering negative DQS skew values. 
exit
INFO: [Common 17-206] Exiting Vivado at Sun Sep 28 17:04:11 2025...
