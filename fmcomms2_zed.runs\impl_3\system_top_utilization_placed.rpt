Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
-----------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 17:04:18 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : report_utilization -file system_top_utilization_placed.rpt -pb system_top_utilization_placed.pb
| Design       : system_top
| Device       : 7z035ffg676-2
| Design State : Fully Placed
-----------------------------------------------------------------------------------------------------------------

Utilization Design Information

Table of Contents
-----------------
1. Slice Logic
1.1 Summary of Registers by Type
2. Slice Logic Distribution
3. Memory
4. DSP
5. IO and GT Specific
6. Clocking
7. Specific Feature
8. Primitives
9. Black Boxes
10. Instantiated Netlists

1. Slice Logic
--------------

+----------------------------+-------+-------+-----------+-------+
|          Site Type         |  Used | Fixed | Available | Util% |
+----------------------------+-------+-------+-----------+-------+
| Slice LUTs                 | 11764 |     0 |    171900 |  6.84 |
|   LUT as Logic             | 11520 |     0 |    171900 |  6.70 |
|   LUT as Memory            |   244 |     0 |     70400 |  0.35 |
|     LUT as Distributed RAM |    24 |     0 |           |       |
|     LUT as Shift Register  |   220 |     0 |           |       |
| Slice Registers            | 23001 |     0 |    343800 |  6.69 |
|   Register as Flip Flop    | 23001 |     0 |    343800 |  6.69 |
|   Register as Latch        |     0 |     0 |    343800 |  0.00 |
| F7 Muxes                   |   301 |     0 |    109300 |  0.28 |
| F8 Muxes                   |    85 |     0 |     54650 |  0.16 |
+----------------------------+-------+-------+-----------+-------+


1.1 Summary of Registers by Type
--------------------------------

+-------+--------------+-------------+--------------+
| Total | Clock Enable | Synchronous | Asynchronous |
+-------+--------------+-------------+--------------+
| 0     |            _ |           - |            - |
| 0     |            _ |           - |          Set |
| 0     |            _ |           - |        Reset |
| 0     |            _ |         Set |            - |
| 0     |            _ |       Reset |            - |
| 0     |          Yes |           - |            - |
| 53    |          Yes |           - |          Set |
| 7213  |          Yes |           - |        Reset |
| 333   |          Yes |         Set |            - |
| 15402 |          Yes |       Reset |            - |
+-------+--------------+-------------+--------------+


2. Slice Logic Distribution
---------------------------

+-------------------------------------------+-------+-------+-----------+-------+
|                 Site Type                 |  Used | Fixed | Available | Util% |
+-------------------------------------------+-------+-------+-----------+-------+
| Slice                                     |  6450 |     0 |     54650 | 11.80 |
|   SLICEL                                  |  4206 |     0 |           |       |
|   SLICEM                                  |  2244 |     0 |           |       |
| LUT as Logic                              | 11520 |     0 |    171900 |  6.70 |
|   using O5 output only                    |     0 |       |           |       |
|   using O6 output only                    | 11147 |       |           |       |
|   using O5 and O6                         |   373 |       |           |       |
| LUT as Memory                             |   244 |     0 |     70400 |  0.35 |
|   LUT as Distributed RAM                  |    24 |     0 |           |       |
|     using O5 output only                  |     0 |       |           |       |
|     using O6 output only                  |     0 |       |           |       |
|     using O5 and O6                       |    24 |       |           |       |
|   LUT as Shift Register                   |   220 |     0 |           |       |
|     using O5 output only                  |     8 |       |           |       |
|     using O6 output only                  |   156 |       |           |       |
|     using O5 and O6                       |    56 |       |           |       |
| LUT Flip Flop Pairs                       |  7265 |     0 |    171900 |  4.23 |
|   fully used LUT-FF pairs                 |    97 |       |           |       |
|   LUT-FF pairs with one unused LUT output |  6882 |       |           |       |
|   LUT-FF pairs with one unused Flip Flop  |  5561 |       |           |       |
| Unique Control Sets                       |   701 |       |           |       |
+-------------------------------------------+-------+-------+-----------+-------+
* Note: Review the Control Sets Report for more information regarding control sets.


3. Memory
---------

+-------------------+------+-------+-----------+-------+
|     Site Type     | Used | Fixed | Available | Util% |
+-------------------+------+-------+-----------+-------+
| Block RAM Tile    |    4 |     0 |       500 |  0.80 |
|   RAMB36/FIFO*    |    4 |     0 |       500 |  0.80 |
|     RAMB36E1 only |    4 |       |           |       |
|   RAMB18          |    0 |     0 |      1000 |  0.00 |
+-------------------+------+-------+-----------+-------+
* Note: Each Block RAM Tile only has one FIFO logic available and therefore can accommodate only one FIFO36E1 or one FIFO18E1. However, if a FIFO18E1 occupies a Block RAM Tile, that tile can still accommodate a RAMB18E1


4. DSP
------

+----------------+------+-------+-----------+-------+
|    Site Type   | Used | Fixed | Available | Util% |
+----------------+------+-------+-----------+-------+
| DSPs           |   60 |     0 |       900 |  6.67 |
|   DSP48E1 only |   60 |       |           |       |
+----------------+------+-------+-----------+-------+


5. IO and GT Specific
---------------------

+-----------------------------+------+-------+-----------+--------+
|          Site Type          | Used | Fixed | Available |  Util% |
+-----------------------------+------+-------+-----------+--------+
| Bonded IOB                  |   88 |    88 |       250 |  35.20 |
|   IOB Master Pads           |   46 |       |           |        |
|   IOB Slave Pads            |   42 |       |           |        |
| Bonded IPADs                |    0 |     0 |        26 |   0.00 |
| Bonded OPADs                |    0 |     0 |        16 |   0.00 |
| Bonded IOPADs               |  130 |   130 |       130 | 100.00 |
| PHY_CONTROL                 |    0 |     0 |         8 |   0.00 |
| PHASER_REF                  |    0 |     0 |         8 |   0.00 |
| OUT_FIFO                    |    0 |     0 |        32 |   0.00 |
| IN_FIFO                     |    0 |     0 |        32 |   0.00 |
| IDELAYCTRL                  |    1 |     0 |         8 |  12.50 |
| IBUFDS                      |    8 |     8 |       240 |   3.33 |
| GTXE2_COMMON                |    0 |     0 |         2 |   0.00 |
| GTXE2_CHANNEL               |    0 |     0 |         8 |   0.00 |
| PHASER_OUT/PHASER_OUT_PHY   |    0 |     0 |        32 |   0.00 |
| PHASER_IN/PHASER_IN_PHY     |    0 |     0 |        32 |   0.00 |
| IDELAYE2/IDELAYE2_FINEDELAY |    7 |     7 |       400 |   1.75 |
|   IDELAYE2 only             |    7 |     7 |           |        |
| ODELAYE2/ODELAYE2_FINEDELAY |    0 |     0 |       150 |   0.00 |
| IBUFDS_GTE2                 |    0 |     0 |         4 |   0.00 |
| ILOGIC                      |    7 |     7 |       250 |   2.80 |
|   IFF_IDDR_Register         |    7 |     7 |           |        |
| OLOGIC                      |   10 |    10 |       250 |   4.00 |
|   OUTFF_ODDR_Register       |   10 |    10 |           |        |
+-----------------------------+------+-------+-----------+--------+


6. Clocking
-----------

+------------+------+-------+-----------+-------+
|  Site Type | Used | Fixed | Available | Util% |
+------------+------+-------+-----------+-------+
| BUFGCTRL   |    5 |     0 |        32 | 15.63 |
| BUFIO      |    0 |     0 |        32 |  0.00 |
| MMCME2_ADV |    0 |     0 |         8 |  0.00 |
| PLLE2_ADV  |    0 |     0 |         8 |  0.00 |
| BUFMRCE    |    0 |     0 |        16 |  0.00 |
| BUFHCE     |    0 |     0 |       168 |  0.00 |
| BUFR       |    2 |     0 |        32 |  6.25 |
+------------+------+-------+-----------+-------+


7. Specific Feature
-------------------

+-------------+------+-------+-----------+-------+
|  Site Type  | Used | Fixed | Available | Util% |
+-------------+------+-------+-----------+-------+
| BSCANE2     |    1 |     0 |         4 | 25.00 |
| CAPTUREE2   |    0 |     0 |         1 |  0.00 |
| DNA_PORT    |    0 |     0 |         1 |  0.00 |
| EFUSE_USR   |    0 |     0 |         1 |  0.00 |
| FRAME_ECCE2 |    0 |     0 |         1 |  0.00 |
| ICAPE2      |    0 |     0 |         2 |  0.00 |
| PCIE_2_1    |    0 |     0 |         1 |  0.00 |
| STARTUPE2   |    0 |     0 |         1 |  0.00 |
| XADC        |    0 |     0 |         1 |  0.00 |
+-------------+------+-------+-----------+-------+


8. Primitives
-------------

+------------+-------+----------------------+
|  Ref Name  |  Used |  Functional Category |
+------------+-------+----------------------+
| FDRE       | 15402 |         Flop & Latch |
| FDCE       |  7213 |         Flop & Latch |
| LUT6       |  3460 |                  LUT |
| LUT3       |  2626 |                  LUT |
| LUT2       |  2250 |                  LUT |
| LUT5       |  1410 |                  LUT |
| LUT4       |  1287 |                  LUT |
| LUT1       |   860 |                  LUT |
| CARRY4     |   789 |           CarryLogic |
| FDSE       |   333 |         Flop & Latch |
| MUXF7      |   301 |                MuxFx |
| SRLC32E    |   141 |   Distributed Memory |
| SRL16E     |   135 |   Distributed Memory |
| BIBUF      |   130 |                   IO |
| MUXF8      |    85 |                MuxFx |
| DSP48E1    |    60 |     Block Arithmetic |
| FDPE       |    53 |         Flop & Latch |
| RAMD32     |    36 |   Distributed Memory |
| OBUF       |    34 |                   IO |
| IBUF       |    20 |                   IO |
| OBUFT      |    17 |                   IO |
| RAMS32     |    12 |   Distributed Memory |
| ODDR       |    10 |                   IO |
| OBUFDS     |     8 |                   IO |
| IBUFDS     |     8 |                   IO |
| IDELAYE2   |     7 |                   IO |
| IDDR       |     7 |                   IO |
| RAMB36E1   |     4 |         Block Memory |
| BUFG       |     4 |                Clock |
| BUFR       |     2 |                Clock |
| PS7        |     1 | Specialized Resource |
| IDELAYCTRL |     1 |                   IO |
| BUFGCTRL   |     1 |                Clock |
| BSCANE2    |     1 |               Others |
+------------+-------+----------------------+


9. Black Boxes
--------------

+----------+------+
| Ref Name | Used |
+----------+------+


10. Instantiated Netlists
-------------------------

+------------+------+
|  Ref Name  | Used |
+------------+------+
| vio_0      |    1 |
| dbg_hub_CV |    1 |
+------------+------+


