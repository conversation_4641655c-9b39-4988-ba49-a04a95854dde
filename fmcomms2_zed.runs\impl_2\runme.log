
*** Running vivado
    with args -log system_top.vdi -applog -m64 -product Vivado -messageDb vivado.pb -mode batch -source system_top.tcl -notrace


****** Vivado v2016.4 (64-bit)
  **** SW Build 1756540 on Mon Jan 23 19:11:23 MST 2017
  **** IP Build 1755317 on Mon Jan 23 20:30:07 MST 2017
    ** Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.

source system_top.tcl -notrace
Design is defaulting to srcset: sources_1
Design is defaulting to constrset: constrs_1
INFO: [Netlist 29-17] Analyzing 878 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2016.4
INFO: [Device 21-403] Loading part xc7z045ffg900-2
INFO: [Project 1-570] Preparing netlist for logic optimization
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xdc] for cell 'i_system_wrapper/system_i/sys_ps7/inst'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xdc] for cell 'i_system_wrapper/system_i/sys_ps7/inst'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0_board.xdc] for cell 'i_system_wrapper/system_i/sys_rstgen/U0'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0_board.xdc] for cell 'i_system_wrapper/system_i/sys_rstgen/U0'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0.xdc] for cell 'i_system_wrapper/system_i/sys_rstgen/U0'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0.xdc] for cell 'i_system_wrapper/system_i/sys_rstgen/U0'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_xfer_cntrl'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_xfer_cntrl'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/i_xfer_cntrl'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/i_xfer_cntrl'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/i_xfer_cntrl'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/i_xfer_cntrl'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_control'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_control'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_counter_values'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_counter_values'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_xfer_cntrl'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_xfer_cntrl'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/i_xfer_cntrl'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_up_adc_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_up_adc_channel/i_xfer_cntrl'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_up_adc_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_up_adc_channel/i_xfer_cntrl'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_up_adc_channel/i_xfer_cntrl'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_up_adc_channel/i_xfer_cntrl'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_core_rst_reg'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_core_rst_reg'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_mmcm_rst_reg'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_mmcm_rst_reg'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_delay_cntrl/i_delay_rst_reg'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_delay_cntrl/i_delay_rst_reg'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_core_rst_reg'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_core_rst_reg'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_mmcm_rst_reg'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_mmcm_rst_reg'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_xfer_status'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_xfer_status'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_status'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_status'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_xfer_status'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_xfer_status'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/i_xfer_status'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/i_xfer_status'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_up_adc_channel/i_xfer_status'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_up_adc_channel/i_xfer_status'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_up_adc_channel/i_xfer_status'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_up_adc_channel/i_xfer_status'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_up_adc_channel/i_xfer_status'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_up_adc_channel/i_xfer_status'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_clock_mon'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_clock_mon'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_clock_mon'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_clock_mon'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/axi_ad9361_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/axi_ad9361_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_tdd_sync_0/util_tdd_sync_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_tdd_sync/inst'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_tdd_sync_0/util_tdd_sync_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_tdd_sync/inst'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0_board.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk_reset/U0'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0_board.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk_reset/U0'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk_reset/U0'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk_reset/U0'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_fifo_0/util_wfifo_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_adc_fifo/inst'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_fifo_0/util_wfifo_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_adc_fifo/inst'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_pack_0/util_cpack_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_adc_pack/inst'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_pack_0/util_cpack_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_adc_pack/inst'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_fifo_0/util_rfifo_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_fifo_0/util_rfifo_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_dac_upack_0/util_upack_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_dac_upack/inst'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_dac_upack_0/util_upack_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_dac_upack/inst'
Parsing XDC File [D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/constrs_1/new/cdc.xdc]
Finished Parsing XDC File [D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/constrs_1/new/cdc.xdc]
Parsing XDC File [D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc]
Finished Parsing XDC File [D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc]
Parsing XDC File [D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc]
Finished Parsing XDC File [D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc]
Sourcing Tcl File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/bd/bd.tcl]
Finished Sourcing Tcl File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/bd/bd.tcl]
Sourcing Tcl File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/bd/bd.tcl]
Finished Sourcing Tcl File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/bd/bd.tcl]
Parsing XDC File [D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.runs/impl_2/.Xil/Vivado-9256-cduser1/dcp/system_top.xdc]
Finished Parsing XDC File [D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.runs/impl_2/.Xil/Vivado-9256-cduser1/dcp/system_top.xdc]
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/system_axi_ad9361_0_pps_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/system_axi_ad9361_0_pps_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361/inst'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk/inst'
INFO: [Timing 38-35] Done setting XDC timing constraints. [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc:1]
INFO: [Timing 38-2] Deriving generated clocks [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc:1]
get_clocks: Time (s): cpu = 00:00:20 ; elapsed = 00:00:17 . Memory (MB): peak = 1262.992 ; gain = 583.859
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc] for cell 'i_system_wrapper/system_i/util_ad9361_divclk/inst'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_adc_dma/inst'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_adc_dma/inst'
Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_dac_dma/inst'
Finished Parsing XDC File [d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc] for cell 'i_system_wrapper/system_i/axi_ad9361_dac_dma/inst'
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 23 instances were transformed.
  IOBUF => IOBUF (IBUF, OBUFT): 15 instances
  OBUFDS => OBUFDS: 8 instances

link_design: Time (s): cpu = 00:00:39 ; elapsed = 00:00:36 . Memory (MB): peak = 1263.000 ; gain = 1016.668
INFO: [Common 17-600] The following parameters have non-default value.
general.maxThreads
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7z045'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z045'
INFO: [Common 17-1223] The version limit for your license is '2018.06' and will expire in -486 days. A version limit expiration means that, although you may be able to continue to use the current version of tools or IP with this license, you will not be eligible for any updates or new releases.
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 8 threads
INFO: [Project 1-461] DRC finished with 0 Errors
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1263.000 ; gain = 0.000
INFO: [Timing 38-35] Done setting XDC timing constraints.

Starting Logic Optimization Task
Implement Debug Cores | Checksum: 19e79b025

Phase 1 Retarget
INFO: [Opt 31-138] Pushed 2 inverter(s) to 68 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 1 Retarget | Checksum: 1c8e2547c

Time (s): cpu = 00:00:03 ; elapsed = 00:00:03 . Memory (MB): peak = 1263.000 ; gain = 0.000

Phase 2 Constant propagation
INFO: [Opt 31-138] Pushed 11 inverter(s) to 61 load pin(s).
INFO: [Opt 31-10] Eliminated 9781 cells.
Phase 2 Constant propagation | Checksum: 1337da6f1

Time (s): cpu = 00:00:12 ; elapsed = 00:00:12 . Memory (MB): peak = 1263.000 ; gain = 0.000

Phase 3 Sweep
INFO: [Opt 31-12] Eliminated 17442 unconnected nets.
INFO: [Opt 31-11] Eliminated 6909 unconnected cells.
Phase 3 Sweep | Checksum: 1b3520484

Time (s): cpu = 00:00:16 ; elapsed = 00:00:16 . Memory (MB): peak = 1263.000 ; gain = 0.000

Phase 4 BUFG optimization
INFO: [Opt 31-12] Eliminated 0 unconnected nets.
INFO: [Opt 31-11] Eliminated 0 unconnected cells.
Phase 4 BUFG optimization | Checksum: 1414b1347

Time (s): cpu = 00:00:20 ; elapsed = 00:00:20 . Memory (MB): peak = 1263.000 ; gain = 0.000

Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.100 . Memory (MB): peak = 1263.000 ; gain = 0.000
Ending Logic Optimization Task | Checksum: 1414b1347

Time (s): cpu = 00:00:20 ; elapsed = 00:00:20 . Memory (MB): peak = 1263.000 ; gain = 0.000

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
INFO: [Pwropt 34-9] Applying IDT optimizations ...
INFO: [Pwropt 34-10] Applying ODC optimizations ...
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation


Starting PowerOpt Patch Enables Task
INFO: [Pwropt 34-162] WRITE_MODE attribute of 0 BRAM(s) out of a total of 4 has been updated to save power. Run report_power_opt to get a complete listing of the BRAMs updated.
INFO: [Pwropt 34-201] Structural ODC has moved 0 WE to EN ports
Number of BRAM Ports augmented: 1 newly gated: 6 Total Ports: 8
Ending PowerOpt Patch Enables Task | Checksum: 19ca42611

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.130 . Memory (MB): peak = 1485.836 ; gain = 0.000
Ending Power Optimization Task | Checksum: 19ca42611

Time (s): cpu = 00:00:16 ; elapsed = 00:00:07 . Memory (MB): peak = 1485.836 ; gain = 222.836
INFO: [Common 17-83] Releasing license: Implementation
31 Infos, 0 Warnings, 0 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
opt_design: Time (s): cpu = 00:00:45 ; elapsed = 00:00:31 . Memory (MB): peak = 1485.836 ; gain = 222.836
INFO: [Common 17-600] The following parameters have non-default value.
general.maxThreads
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.130 . Memory (MB): peak = 1485.836 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.runs/impl_2/system_top_opt.dcp' has been generated.
INFO: [DRC 23-27] Running DRC with 8 threads
INFO: [Coretcl 2-168] The results of DRC are in file D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.runs/impl_2/system_top_drc_opted.rpt.
INFO: [Chipscope 16-241] No debug cores found in the current design.
Before running the implement_debug_core command, either use the Set Up Debug wizard (GUI mode)
or use the create_debug_core and connect_debug_core Tcl commands to insert debug cores into the design.
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7z045'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z045'
INFO: [Common 17-1223] The version limit for your license is '2018.06' and will expire in -486 days. A version limit expiration means that, although you may be able to continue to use the current version of tools or IP with this license, you will not be eligible for any updates or new releases.
INFO: [DRC 23-27] Running DRC with 8 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 8 threads
WARNING: [DRC 23-20] Rule violation (CHECK-3) Report rule limit reached - REQP-1839 rule limit reached: 20 violations have been found.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[2] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[2] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors, 21 Warnings
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.

Starting Placer Task
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 8 CPUs

Phase 1 Placer Initialization
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.090 . Memory (MB): peak = 1485.836 ; gain = 0.000
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.060 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 1.1 IO Placement/ Clock Placement/ Build Placer Device
INFO: [Timing 38-35] Done setting XDC timing constraints.
Phase 1.1 IO Placement/ Clock Placement/ Build Placer Device | Checksum: fa40d9db

Time (s): cpu = 00:00:23 ; elapsed = 00:00:14 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 1.2 Build Placer Netlist Model
Phase 1.2 Build Placer Netlist Model | Checksum: 1bb7eb7bd

Time (s): cpu = 00:00:41 ; elapsed = 00:00:22 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 1.3 Constrain Clocks/Macros
Phase 1.3 Constrain Clocks/Macros | Checksum: 1bb7eb7bd

Time (s): cpu = 00:00:41 ; elapsed = 00:00:22 . Memory (MB): peak = 1485.836 ; gain = 0.000
Phase 1 Placer Initialization | Checksum: 1bb7eb7bd

Time (s): cpu = 00:00:42 ; elapsed = 00:00:23 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 2 Global Placement
Phase 2 Global Placement | Checksum: 168250003

Time (s): cpu = 00:01:30 ; elapsed = 00:00:41 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: 168250003

Time (s): cpu = 00:01:31 ; elapsed = 00:00:41 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: 1865ad69c

Time (s): cpu = 00:01:45 ; elapsed = 00:00:47 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: fc0bb190

Time (s): cpu = 00:01:46 ; elapsed = 00:00:47 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 164dd6bf6

Time (s): cpu = 00:01:46 ; elapsed = 00:00:47 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 3.5 Timing Path Optimizer
Phase 3.5 Timing Path Optimizer | Checksum: 117607c9c

Time (s): cpu = 00:01:51 ; elapsed = 00:00:49 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 3.6 Small Shape Detail Placement
Phase 3.6 Small Shape Detail Placement | Checksum: 12d46e776

Time (s): cpu = 00:02:02 ; elapsed = 00:00:59 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 3.7 Re-assign LUT pins
Phase 3.7 Re-assign LUT pins | Checksum: 190cfc25a

Time (s): cpu = 00:02:03 ; elapsed = 00:01:00 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 3.8 Pipeline Register Optimization
Phase 3.8 Pipeline Register Optimization | Checksum: 22930efe8

Time (s): cpu = 00:02:04 ; elapsed = 00:01:00 . Memory (MB): peak = 1485.836 ; gain = 0.000
Phase 3 Detail Placement | Checksum: 22930efe8

Time (s): cpu = 00:02:05 ; elapsed = 00:01:01 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization
INFO: [Timing 38-35] Done setting XDC timing constraints.

Phase 4.1.1 Post Placement Optimization
INFO: [Place 30-746] Post Placement Timing Summary WNS=0.610. For the most accurate timing information please run report_timing.
Phase 4.1.1 Post Placement Optimization | Checksum: 177311ffa

Time (s): cpu = 00:02:23 ; elapsed = 00:01:06 . Memory (MB): peak = 1485.836 ; gain = 0.000
Phase 4.1 Post Commit Optimization | Checksum: 177311ffa

Time (s): cpu = 00:02:24 ; elapsed = 00:01:06 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: 177311ffa

Time (s): cpu = 00:02:25 ; elapsed = 00:01:07 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 4.3 Placer Reporting
Phase 4.3 Placer Reporting | Checksum: 177311ffa

Time (s): cpu = 00:02:25 ; elapsed = 00:01:07 . Memory (MB): peak = 1485.836 ; gain = 0.000

Phase 4.4 Final Placement Cleanup
Phase 4.4 Final Placement Cleanup | Checksum: 156ff2268

Time (s): cpu = 00:02:25 ; elapsed = 00:01:07 . Memory (MB): peak = 1485.836 ; gain = 0.000
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 156ff2268

Time (s): cpu = 00:02:25 ; elapsed = 00:01:07 . Memory (MB): peak = 1485.836 ; gain = 0.000
Ending Placer Task | Checksum: 143c7b497

Time (s): cpu = 00:02:26 ; elapsed = 00:01:07 . Memory (MB): peak = 1485.836 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
51 Infos, 21 Warnings, 0 Critical Warnings and 0 Errors encountered.
place_design completed successfully
place_design: Time (s): cpu = 00:02:36 ; elapsed = 00:01:13 . Memory (MB): peak = 1485.836 ; gain = 0.000
INFO: [Common 17-600] The following parameters have non-default value.
general.maxThreads
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:13 ; elapsed = 00:00:05 . Memory (MB): peak = 1485.836 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.runs/impl_2/system_top_placed.dcp' has been generated.
write_checkpoint: Time (s): cpu = 00:00:14 ; elapsed = 00:00:06 . Memory (MB): peak = 1485.836 ; gain = 0.000
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.200 . Memory (MB): peak = 1485.836 ; gain = 0.000
report_utilization: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.450 . Memory (MB): peak = 1485.836 ; gain = 0.000
report_control_sets: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.140 . Memory (MB): peak = 1485.836 ; gain = 0.000
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7z045'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z045'
INFO: [Common 17-1223] The version limit for your license is '2018.06' and will expire in -486 days. A version limit expiration means that, although you may be able to continue to use the current version of tools or IP with this license, you will not be eligible for any updates or new releases.
Running DRC as a precondition to command route_design
INFO: [DRC 23-27] Running DRC with 8 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 8 CPUs
Checksum: PlaceDB: cec203e9 ConstDB: 0 ShapeSum: 7505b0ae RouteDB: 0

Phase 1 Build RT Design
Phase 1 Build RT Design | Checksum: 132a8f554

Time (s): cpu = 00:02:08 ; elapsed = 00:01:45 . Memory (MB): peak = 1667.781 ; gain = 181.945

Phase 2 Router Initialization

Phase 2.1 Create Timer
Phase 2.1 Create Timer | Checksum: 132a8f554

Time (s): cpu = 00:02:09 ; elapsed = 00:01:46 . Memory (MB): peak = 1667.781 ; gain = 181.945

Phase 2.2 Fix Topology Constraints
Phase 2.2 Fix Topology Constraints | Checksum: 132a8f554

Time (s): cpu = 00:02:09 ; elapsed = 00:01:46 . Memory (MB): peak = 1680.578 ; gain = 194.742

Phase 2.3 Pre Route Cleanup
Phase 2.3 Pre Route Cleanup | Checksum: 132a8f554

Time (s): cpu = 00:02:10 ; elapsed = 00:01:46 . Memory (MB): peak = 1680.578 ; gain = 194.742
 Number of Nodes with overlaps = 0

Phase 2.4 Update Timing
Phase 2.4 Update Timing | Checksum: 1d861234a

Time (s): cpu = 00:02:39 ; elapsed = 00:01:58 . Memory (MB): peak = 1825.234 ; gain = 339.398
INFO: [Route 35-416] Intermediate Timing Summary | WNS=0.565  | TNS=0.000  | WHS=-0.382 | THS=-1222.786|

Phase 2 Router Initialization | Checksum: 1ffffef07

Time (s): cpu = 00:02:56 ; elapsed = 00:02:03 . Memory (MB): peak = 1825.234 ; gain = 339.398

Phase 3 Initial Routing
Phase 3 Initial Routing | Checksum: 13526d889

Time (s): cpu = 00:03:06 ; elapsed = 00:02:05 . Memory (MB): peak = 1825.234 ; gain = 339.398

Phase 4 Rip-up And Reroute

Phase 4.1 Global Iteration 0
 Number of Nodes with overlaps = 1311
 Number of Nodes with overlaps = 52
 Number of Nodes with overlaps = 0

Phase 4.1.1 Update Timing
Phase 4.1.1 Update Timing | Checksum: 19c34c0ee

Time (s): cpu = 00:03:32 ; elapsed = 00:02:13 . Memory (MB): peak = 1825.234 ; gain = 339.398
INFO: [Route 35-416] Intermediate Timing Summary | WNS=0.545  | TNS=0.000  | WHS=N/A    | THS=N/A    |

Phase 4.1 Global Iteration 0 | Checksum: 114138f2f

Time (s): cpu = 00:03:33 ; elapsed = 00:02:13 . Memory (MB): peak = 1825.234 ; gain = 339.398
Phase 4 Rip-up And Reroute | Checksum: 114138f2f

Time (s): cpu = 00:03:33 ; elapsed = 00:02:13 . Memory (MB): peak = 1825.234 ; gain = 339.398

Phase 5 Delay and Skew Optimization

Phase 5.1 Delay CleanUp
Phase 5.1 Delay CleanUp | Checksum: 114138f2f

Time (s): cpu = 00:03:34 ; elapsed = 00:02:13 . Memory (MB): peak = 1825.234 ; gain = 339.398

Phase 5.2 Clock Skew Optimization
Phase 5.2 Clock Skew Optimization | Checksum: 114138f2f

Time (s): cpu = 00:03:34 ; elapsed = 00:02:13 . Memory (MB): peak = 1825.234 ; gain = 339.398
Phase 5 Delay and Skew Optimization | Checksum: 114138f2f

Time (s): cpu = 00:03:34 ; elapsed = 00:02:14 . Memory (MB): peak = 1825.234 ; gain = 339.398

Phase 6 Post Hold Fix

Phase 6.1 Hold Fix Iter

Phase 6.1.1 Update Timing
Phase 6.1.1 Update Timing | Checksum: e90301b1

Time (s): cpu = 00:03:39 ; elapsed = 00:02:15 . Memory (MB): peak = 1825.234 ; gain = 339.398
INFO: [Route 35-416] Intermediate Timing Summary | WNS=0.589  | TNS=0.000  | WHS=0.057  | THS=0.000  |

Phase 6.1 Hold Fix Iter | Checksum: e90301b1

Time (s): cpu = 00:03:39 ; elapsed = 00:02:15 . Memory (MB): peak = 1825.234 ; gain = 339.398
Phase 6 Post Hold Fix | Checksum: e90301b1

Time (s): cpu = 00:03:39 ; elapsed = 00:02:15 . Memory (MB): peak = 1825.234 ; gain = 339.398

Phase 7 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 1.352 %
  Global Horizontal Routing Utilization  = 1.86472 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Phase 7 Route finalize | Checksum: 16080bd30

Time (s): cpu = 00:03:40 ; elapsed = 00:02:16 . Memory (MB): peak = 1825.234 ; gain = 339.398

Phase 8 Verifying routed nets

 Verification completed successfully
Phase 8 Verifying routed nets | Checksum: 16080bd30

Time (s): cpu = 00:03:40 ; elapsed = 00:02:16 . Memory (MB): peak = 1825.234 ; gain = 339.398

Phase 9 Depositing Routes
Phase 9 Depositing Routes | Checksum: 162c82a2a

Time (s): cpu = 00:03:42 ; elapsed = 00:02:18 . Memory (MB): peak = 1825.234 ; gain = 339.398

Phase 10 Post Router Timing
INFO: [Route 35-57] Estimated Timing Summary | WNS=0.589  | TNS=0.000  | WHS=0.057  | THS=0.000  |

INFO: [Route 35-327] The final timing numbers are based on the router estimated timing analysis. For a complete and accurate timing signoff, please run report_timing_summary.
Phase 10 Post Router Timing | Checksum: 162c82a2a

Time (s): cpu = 00:03:43 ; elapsed = 00:02:18 . Memory (MB): peak = 1825.234 ; gain = 339.398
INFO: [Route 35-16] Router Completed Successfully

Time (s): cpu = 00:03:47 ; elapsed = 00:02:19 . Memory (MB): peak = 1825.234 ; gain = 339.398

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
66 Infos, 21 Warnings, 0 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:03:56 ; elapsed = 00:02:24 . Memory (MB): peak = 1825.234 ; gain = 339.398
INFO: [Common 17-600] The following parameters have non-default value.
general.maxThreads
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:15 ; elapsed = 00:00:06 . Memory (MB): peak = 1825.234 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.runs/impl_2/system_top_routed.dcp' has been generated.
write_checkpoint: Time (s): cpu = 00:00:16 ; elapsed = 00:00:07 . Memory (MB): peak = 1825.234 ; gain = 0.000
INFO: [DRC 23-27] Running DRC with 8 threads
INFO: [Coretcl 2-168] The results of DRC are in file D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.runs/impl_2/system_top_drc_routed.rpt.
report_drc: Time (s): cpu = 00:00:18 ; elapsed = 00:00:06 . Memory (MB): peak = 1825.234 ; gain = 0.000
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 8 threads
INFO: [Coretcl 2-1520] The results of Report Methodology are in file D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.runs/impl_2/system_top_methodology_drc_routed.rpt.
report_methodology: Time (s): cpu = 00:00:43 ; elapsed = 00:00:10 . Memory (MB): peak = 1833.656 ; gain = 8.422
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -2, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 8 CPUs
INFO: [Timing 38-35] Done setting XDC timing constraints.
Command: report_power -file system_top_power_routed.rpt -pb system_top_power_summary_routed.pb -rpx system_top_power_routed.rpx
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation
WARNING: [Power 33-332] Found switching activity that implies high-fanout reset nets being asserted for excessive periods of time which may result in inaccurate power analysis.
Resolution: To review and fix problems, please run Power Constraints Advisor in the GUI from Tools > Power Constraints Advisor or run report_power with the -advisory option to generate a text report.
77 Infos, 22 Warnings, 0 Critical Warnings and 0 Errors encountered.
report_power completed successfully
report_power: Time (s): cpu = 00:00:14 ; elapsed = 00:00:06 . Memory (MB): peak = 1949.242 ; gain = 93.309
Command: write_bitstream -force -no_partial_bitfile system_top.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7z045'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z045'
INFO: [Common 17-1223] The version limit for your license is '2018.06' and will expire in -486 days. A version limit expiration means that, although you may be able to continue to use the current version of tools or IP with this license, you will not be eligible for any updates or new releases.
Running DRC as a precondition to command write_bitstream
INFO: [DRC 23-27] Running DRC with 8 threads
WARNING: [DRC 23-20] Rule violation (CHECK-3) Report rule limit reached - REQP-1839 rule limit reached: 20 violations have been found.
WARNING: [DRC 23-20] Rule violation (DPOP-1) PREG Output pipelining - DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
WARNING: [DRC 23-20] Rule violation (DPOP-1) PREG Output pipelining - DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
WARNING: [DRC 23-20] Rule violation (DPOP-1) PREG Output pipelining - DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
WARNING: [DRC 23-20] Rule violation (DPOP-1) PREG Output pipelining - DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ADDRBWRADDR[10] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/din_waddr_reg[4][4]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_waddr_reg[4]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ADDRBWRADDR[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/din_waddr_reg[4][0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_waddr_reg[0]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ADDRBWRADDR[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/din_waddr_reg[4][1]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_waddr_reg[1]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ADDRBWRADDR[8] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/din_waddr_reg[4][2]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_waddr_reg[2]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ADDRBWRADDR[9] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/din_waddr_reg[4][3]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_waddr_reg[3]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg_ENARDEN_cooolgate_en_sig_2) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_control/d_data_cntrl_int_reg[14]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg_ENARDEN_cooolgate_en_sig_2) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_control/d_data_cntrl_int_reg[9]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg_ENARDEN_cooolgate_en_sig_2) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_xfer_cntrl/d_data_cntrl_int_reg[17]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ENBWREN (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[0] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[1] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[2] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC 23-20] Rule violation (REQP-1839) RAMB36 async control check - The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [DRC 23-20] Rule violation (AVAL-4) enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND - i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 25 Warnings, 56 Advisories
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Bitstream compression saved 75343360 bits.
Writing bitstream ./system_top.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-118] WebTalk data collection is enabled (User setting is ON. Install Setting is ON.).
INFO: [Common 17-186] 'D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.runs/impl_2/usage_statistics_webtalk.xml' has been successfully sent to Xilinx on Tue Oct 29 13:27:21 2019. For additional details about this file, please refer to the WebTalk help file at D:/Xilinx2016/Vivado/2016.4/doc/webtalk_introduction.html.
INFO: [Common 17-83] Releasing license: Implementation
142 Infos, 47 Warnings, 0 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:01:17 ; elapsed = 00:01:11 . Memory (MB): peak = 2439.625 ; gain = 490.383
INFO: [Common 17-206] Exiting Vivado at Tue Oct 29 13:27:21 2019...
