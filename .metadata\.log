!SESSION 2020-04-30 10:21:57.951 -----------------------------------------------
eclipse.buildId=2016.4
java.version=1.8.0_66
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64

!ENTRY org.eclipse.ui 2 0 2020-04-30 10:22:20.509
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2020-04-30 10:22:20.509
!MESSAGE Commands should really have a category: plug-in='com.xilinx.sdk.appwiz', id='com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences', categoryId='com.xilinx.sdk.app.commands.category'

!ENTRY org.eclipse.ui 2 0 2020-04-30 10:22:21.320
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2020-04-30 10:22:21.320
!MESSAGE Commands should really have a category: plug-in='com.xilinx.sdk.appwiz', id='com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences', categoryId='com.xilinx.sdk.app.commands.category'

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 10:22:36.408
!MESSAGE XSCT Command: [::hsi::utils::init_repo], Thread: Worker-6

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 10:22:36.408
!MESSAGE XSCT Command: [set sdk::sdk_chan tcfchan#0], Thread: Thread-15

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 10:22:36.860
!MESSAGE XSCT command with result: [::hsi::utils::init_repo], Result: [null, ]. Thread: Worker-6

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 10:22:36.860
!MESSAGE XSCT command with result: [set sdk::sdk_chan tcfchan#0], Result: [null, tcfchan#0]. Thread: Thread-15

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 10:22:36.860
!MESSAGE XSCT Command: [setws E:/wgfile/R75_Z7035_0320], Thread: Thread-15

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 10:22:36.876
!MESSAGE XSCT command with result: [setws E:/wgfile/R75_Z7035_0320], Result: [null, ]. Thread: Thread-15

!ENTRY org.eclipse.egit.ui 2 0 2020-04-30 10:22:42.305
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\Administrator'. If this is
not correct please set the HOME environment variable and restart Eclipse. Otherwise Git for Windows and
EGit might behave differently since they see different configuration options.
This warning can be switched off on the Team > Git > Confirmations and Warnings preference page.

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 11:17:08.600
!MESSAGE XSCT Command: [::hsi::utils::init_repo], Thread: Worker-2

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 11:17:08.600
!MESSAGE XSCT Command: [set sdk::sdk_chan tcfchan#0], Thread: Thread-15

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 11:17:08.945
!MESSAGE XSCT command with result: [::hsi::utils::init_repo], Result: [null, ]. Thread: Worker-2

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 11:17:08.945
!MESSAGE XSCT command with result: [set sdk::sdk_chan tcfchan#0], Result: [null, tcfchan#0]. Thread: Thread-15

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 11:17:08.945
!MESSAGE XSCT Command: [setws E:/wgfile/R75_Z7035_0320], Thread: Thread-15

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-30 11:17:08.945
!MESSAGE XSCT command with result: [setws E:/wgfile/R75_Z7035_0320], Result: [null, ]. Thread: Thread-15
