<?xml version="1.0" encoding="UTF-8" ?>
<document>
<!--The data in this file is primarily intended for consumption by Xilinx tools.
The structure and the elements are likely to change over the next few releases.
This means code written to parse this file will need to be revisited each subsequent release.-->
<application name="pa" timeStamp="Sun Sep 28 17:04:07 2025">
<section name="Project Information" visible="false">
<property name="ProjectID" value="1c142af950e54c1dbdf4e47e4c2ec105" type="ProjectID"/>
<property name="ProjectIteration" value="13" type="ProjectIteration"/>
</section>
<section name="PlanAhead Usage" visible="true">
<item name="Project Data">
<property name="SrcSetCount" value="1" type="SrcSetCount"/>
<property name="ConstraintSetCount" value="1" type="ConstraintSetCount"/>
<property name="DesignMode" value="RTL" type="DesignMode"/>
<property name="SynthesisStrategy" value="Flow_PerfOptimized_high" type="SynthesisStrategy"/>
<property name="ImplStrategy" value="Vivado Implementation Defaults" type="ImplStrategy"/>
</item>
<item name="Java Command Handlers">
<property name="AutoConnectTarget" value="20" type="JavaHandler"/>
<property name="CloseProject" value="3" type="JavaHandler"/>
<property name="CloseServer" value="6" type="JavaHandler"/>
<property name="CloseTarget" value="1" type="JavaHandler"/>
<property name="CoreView" value="2" type="JavaHandler"/>
<property name="CreateTopHDL" value="5" type="JavaHandler"/>
<property name="CustomizeCore" value="2" type="JavaHandler"/>
<property name="CustomizeRSBBlock" value="33" type="JavaHandler"/>
<property name="EditCopy" value="1" type="JavaHandler"/>
<property name="EditDelete" value="40" type="JavaHandler"/>
<property name="EditPaste" value="2" type="JavaHandler"/>
<property name="EditProperties" value="1" type="JavaHandler"/>
<property name="LaunchProgramFpga" value="9" type="JavaHandler"/>
<property name="ManageCompositeTargets" value="3" type="JavaHandler"/>
<property name="NewExportHardware" value="6" type="JavaHandler"/>
<property name="NewLaunchHardware" value="9" type="JavaHandler"/>
<property name="OpenBlockDesign" value="4" type="JavaHandler"/>
<property name="OpenHardwareManager" value="14" type="JavaHandler"/>
<property name="OpenProject" value="2" type="JavaHandler"/>
<property name="OpenRecentTarget" value="11" type="JavaHandler"/>
<property name="ProjectSettingsCmdHandler" value="4" type="JavaHandler"/>
<property name="ReportIPStatus" value="7" type="JavaHandler"/>
<property name="RunBitgen" value="12" type="JavaHandler"/>
<property name="RunImplementation" value="2" type="JavaHandler"/>
<property name="RunSynthesis" value="2" type="JavaHandler"/>
<property name="SaveDesign" value="5" type="JavaHandler"/>
<property name="SaveRSBDesign" value="6" type="JavaHandler"/>
<property name="ShowView" value="8" type="JavaHandler"/>
<property name="TimingConstraintsWizard" value="1" type="JavaHandler"/>
<property name="UpgradeIP" value="3" type="JavaHandler"/>
<property name="ValidateRSBDesign" value="8" type="JavaHandler"/>
<property name="ViewTaskImplementation" value="1" type="JavaHandler"/>
<property name="ui.views.c.aJ" value="1" type="JavaHandler"/>
</item>
<item name="Gui Handlers">
<property name="BaseDialog_CANCEL" value="1" type="GuiHandlerData"/>
<property name="BaseDialog_OK" value="3" type="GuiHandlerData"/>
<property name="ClkConfigTreeTablePanel_CLK_CONFIG_TREE_TABLE" value="1" type="GuiHandlerData"/>
<property name="CmdMsgDialog_OK" value="3" type="GuiHandlerData"/>
<property name="CustomizeCoreDialog_CHOOSE_IP_LOCATION" value="1" type="GuiHandlerData"/>
<property name="CustomizeCoreDialog_DOCUMENTATION" value="3" type="GuiHandlerData"/>
<property name="CustomizeCoreDialog_IP_LOCATION" value="2" type="GuiHandlerData"/>
<property name="FileSetPanel_FILE_SET_PANEL_TREE" value="13" type="GuiHandlerData"/>
<property name="FlowNavigatorTreePanel_FLOW_NAVIGATOR_TREE" value="4" type="GuiHandlerData"/>
<property name="HExceptionDialog_CONTINUE" value="1" type="GuiHandlerData"/>
<property name="IPStatusSectionPanel_UPGRADE_SELECTED" value="1" type="GuiHandlerData"/>
<property name="IPStatusTablePanel_IP_STATUS_TABLE" value="6" type="GuiHandlerData"/>
<property name="IPStatusTablePanel_MORE_INFO" value="3" type="GuiHandlerData"/>
<property name="IPStatusTablePanel_VIEW_CHANGE_LOG" value="1" type="GuiHandlerData"/>
<property name="MIOConfigTreeTablePanel_MIO_CONFIG_TREE_TABLE" value="4" type="GuiHandlerData"/>
<property name="MIOTablePagePanel_MIO_TABLE" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_UPDATE_HIER" value="8" type="GuiHandlerData"/>
<property name="PACommandNames_REPORT_IP_STATUS" value="2" type="GuiHandlerData"/>
<property name="PAViews_PROJECT_SUMMARY" value="2" type="GuiHandlerData"/>
<property name="ReportIPStatusInfoDialog_REPORT_IP_STATUS" value="1" type="GuiHandlerData"/>
<property name="SelectMenu_HIGHLIGHT" value="2" type="GuiHandlerData"/>
<property name="SrcMenu_IP_DOCUMENTATION" value="4" type="GuiHandlerData"/>
<property name="SrcMenu_IP_HIERARCHY" value="4" type="GuiHandlerData"/>
<property name="SrcMenu_REFRESH_HIERARCHY" value="1" type="GuiHandlerData"/>
<property name="SyntheticaGettingStartedView_RECENT_PROJECTS" value="1" type="GuiHandlerData"/>
<property name="SystemBuilderMenu_IP_DOCUMENTATION" value="5" type="GuiHandlerData"/>
<property name="SystemBuilderView_EXPAND_COLLAPSE" value="4" type="GuiHandlerData"/>
<property name="SystemBuilderView_ORIENTATION" value="5" type="GuiHandlerData"/>
<property name="SystemBuilderView_PINNING" value="4" type="GuiHandlerData"/>
<property name="SystemTab_UPGRADE_LATER" value="1" type="GuiHandlerData"/>
<property name="SystemTreeView_SYSTEM_TREE" value="3" type="GuiHandlerData"/>
</item>
<item name="Other">
<property name="GuiMode" value="42" type="GuiMode"/>
<property name="BatchMode" value="0" type="BatchMode"/>
<property name="TclMode" value="38" type="TclMode"/>
</item>
</section>
</application>
</document>
