<?xml version="1.0" encoding="UTF-8"?>
<section name="Workbench">
	<section name="org.eclipse.ui.texteditor.FindReplaceDialog">
		<item value="false" key="isRegEx"/>
		<item value="REG_RX_ENABLE_FILTER_CTRL" key="selection"/>
		<item value="false" key="casesensitive"/>
		<item value="false" key="incremental"/>
		<item value="true" key="wrap"/>
		<item value="false" key="wholeword"/>
		<list key="findhistory">
			<item value="REG_RX_ENABLE_FILTER_CTRL"/>
			<item value="XILINX_PLATFORM"/>
			<item value="received_cmd"/>
			<item value="spi_init"/>
			<item value="main"/>
			<item value="ad9361"/>
			<item value="init"/>
			<item value="tx-fb-clock-delay"/>
		</list>
		<list key="replacehistory">
		</list>
	</section>
	<section name="org.eclipse.ui.texteditor.FindReplaceDialog_dialogBounds">
		<item value="273" key="DIALOG_WIDTH"/>
		<item value="1|Microsoft YaHei UI|9.0|0|WINDOWS|1|-12|0|0|0|400|0|0|0|1|0|0|0|0|Microsoft YaHei UI" key="DIALOG_FONT_NAME"/>
		<item value="400" key="DIALOG_HEIGHT"/>
		<item value="1120" key="DIALOG_X_ORIGIN"/>
		<item value="226" key="DIALOG_Y_ORIGIN"/>
	</section>
</section>
