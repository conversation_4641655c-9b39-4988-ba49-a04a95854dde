<?xml version="1.0" encoding="UTF-8" ?>
<webTalkData  fileName='usage_statistics_webtalk.xml'  majorVersion='1' minorVersion='0' timeStamp='Tue Oct 29 13:27:16 2019'>
<section name="__ROOT__" level="0" order="1" description="">
 <section name="software_version_and_target_device" level="1" order="1" description="">
  <keyValuePair key="beta" value="FALSE" description="" />
  <keyValuePair key="build_version" value="1756540" description="" />
  <keyValuePair key="date_generated" value="Tue Oct 29 13:27:15 2019" description="" />
  <keyValuePair key="os_platform" value="WIN64" description="" />
  <keyValuePair key="product_version" value="Vivado v2016.4 (64-bit)" description="" />
  <keyValuePair key="project_id" value="d99595073c624624ae85d6bca6a02b61" description="" />
  <keyValuePair key="project_iteration" value="3" description="" />
  <keyValuePair key="random_id" value="d5aa0efbea9552bb80be422e35c8ba78" description="" />
  <keyValuePair key="registration_id" value="205589051_15690819_210558907_866" description="" />
  <keyValuePair key="route_design" value="TRUE" description="" />
  <keyValuePair key="target_device" value="xc7z045" description="" />
  <keyValuePair key="target_family" value="zynq" description="" />
  <keyValuePair key="target_package" value="ffg900" description="" />
  <keyValuePair key="target_speed" value="-2" description="" />
  <keyValuePair key="tool_flow" value="Vivado" description="" />
 </section>
 <section name="user_environment" level="1" order="2" description="">
  <keyValuePair key="cpu_name" value="AMD Ryzen 5 2400G with Radeon Vega Graphics    " description="" />
  <keyValuePair key="cpu_speed" value="3593 MHz" description="" />
  <keyValuePair key="os_name" value="Microsoft Windows 8 or later , 64-bit" description="" />
  <keyValuePair key="os_release" value="major release  (build 9200)" description="" />
  <keyValuePair key="system_ram" value="16.000 GB" description="" />
  <keyValuePair key="total_processors" value="1" description="" />
 </section>
 <section name="ip_statistics" level="1" order="3" description="">
  <section name="IP_Integrator/1" level="2" order="1" description="">
   <keyValuePair key="bdsource" value="USER" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="maxhierdepth" value="0" description="" />
   <keyValuePair key="numblks" value="38" description="" />
   <keyValuePair key="numhdlrefblks" value="0" description="" />
   <keyValuePair key="numhierblks" value="18" description="" />
   <keyValuePair key="numhlsblks" value="0" description="" />
   <keyValuePair key="numnonxlnxblks" value="9" description="" />
   <keyValuePair key="numpkgbdblks" value="0" description="" />
   <keyValuePair key="numreposblks" value="20" description="" />
   <keyValuePair key="numsysgenblks" value="0" description="" />
   <keyValuePair key="synth_mode" value="Global" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="BlockDiagram" description="" />
   <keyValuePair key="x_ipname" value="system" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.00.a" description="" />
  </section>
  <section name="axi_crossbar_v2_1_12_axi_crossbar/1" level="2" order="2" description="">
   <keyValuePair key="c_axi_addr_width" value="32" description="" />
   <keyValuePair key="c_axi_aruser_width" value="1" description="" />
   <keyValuePair key="c_axi_awuser_width" value="1" description="" />
   <keyValuePair key="c_axi_buser_width" value="1" description="" />
   <keyValuePair key="c_axi_data_width" value="32" description="" />
   <keyValuePair key="c_axi_id_width" value="12" description="" />
   <keyValuePair key="c_axi_protocol" value="0" description="" />
   <keyValuePair key="c_axi_ruser_width" value="1" description="" />
   <keyValuePair key="c_axi_supports_user_signals" value="0" description="" />
   <keyValuePair key="c_axi_wuser_width" value="1" description="" />
   <keyValuePair key="c_connectivity_mode" value="1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_m_axi_addr_width" value="0x0000000c0000000c0000001000000000000000000000000000000000000000000000000000000000" description="" />
   <keyValuePair key="c_m_axi_base_addr" value="0x000000007c420000000000007c4000000000000079020000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff" description="" />
   <keyValuePair key="c_m_axi_read_connectivity" value="0x00000001000000010000000100000001000000010000000100000001000000010000000100000001" description="" />
   <keyValuePair key="c_m_axi_read_issuing" value="0x00000008000000080000000200000008000000080000000800000008000000080000000800000008" description="" />
   <keyValuePair key="c_m_axi_secure" value="0x00000000000000000000000000000000000000000000000000000000000000000000000000000000" description="" />
   <keyValuePair key="c_m_axi_write_connectivity" value="0x00000001000000010000000100000001000000010000000100000001000000010000000100000001" description="" />
   <keyValuePair key="c_m_axi_write_issuing" value="0x00000008000000080000000200000008000000080000000800000008000000080000000800000008" description="" />
   <keyValuePair key="c_num_addr_ranges" value="1" description="" />
   <keyValuePair key="c_num_master_slots" value="10" description="" />
   <keyValuePair key="c_num_slave_slots" value="1" description="" />
   <keyValuePair key="c_r_register" value="0" description="" />
   <keyValuePair key="c_s_axi_arb_priority" value="0x00000000" description="" />
   <keyValuePair key="c_s_axi_base_id" value="0x00000000" description="" />
   <keyValuePair key="c_s_axi_read_acceptance" value="0x00000008" description="" />
   <keyValuePair key="c_s_axi_single_thread" value="0x00000000" description="" />
   <keyValuePair key="c_s_axi_thread_id_width" value="0x0000000c" description="" />
   <keyValuePair key="c_s_axi_write_acceptance" value="0x00000008" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="12" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="axi_crossbar" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.1" description="" />
  </section>
  <section name="axi_protocol_converter_v2_1_11_axi_protocol_converter/1" level="2" order="3" description="">
   <keyValuePair key="c_axi_addr_width" value="16" description="" />
   <keyValuePair key="c_axi_aruser_width" value="1" description="" />
   <keyValuePair key="c_axi_awuser_width" value="1" description="" />
   <keyValuePair key="c_axi_buser_width" value="1" description="" />
   <keyValuePair key="c_axi_data_width" value="32" description="" />
   <keyValuePair key="c_axi_id_width" value="12" description="" />
   <keyValuePair key="c_axi_ruser_width" value="1" description="" />
   <keyValuePair key="c_axi_supports_read" value="1" description="" />
   <keyValuePair key="c_axi_supports_user_signals" value="0" description="" />
   <keyValuePair key="c_axi_supports_write" value="1" description="" />
   <keyValuePair key="c_axi_wuser_width" value="1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_ignore_id" value="0" description="" />
   <keyValuePair key="c_m_axi_protocol" value="2" description="" />
   <keyValuePair key="c_s_axi_protocol" value="0" description="" />
   <keyValuePair key="c_translation_mode" value="2" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="11" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="axi_protocol_converter" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.1" description="" />
  </section>
  <section name="axi_protocol_converter_v2_1_11_axi_protocol_converter/2" level="2" order="4" description="">
   <keyValuePair key="c_axi_addr_width" value="12" description="" />
   <keyValuePair key="c_axi_aruser_width" value="1" description="" />
   <keyValuePair key="c_axi_awuser_width" value="1" description="" />
   <keyValuePair key="c_axi_buser_width" value="1" description="" />
   <keyValuePair key="c_axi_data_width" value="32" description="" />
   <keyValuePair key="c_axi_id_width" value="12" description="" />
   <keyValuePair key="c_axi_ruser_width" value="1" description="" />
   <keyValuePair key="c_axi_supports_read" value="1" description="" />
   <keyValuePair key="c_axi_supports_user_signals" value="0" description="" />
   <keyValuePair key="c_axi_supports_write" value="1" description="" />
   <keyValuePair key="c_axi_wuser_width" value="1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_ignore_id" value="0" description="" />
   <keyValuePair key="c_m_axi_protocol" value="2" description="" />
   <keyValuePair key="c_s_axi_protocol" value="0" description="" />
   <keyValuePair key="c_translation_mode" value="2" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="11" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="axi_protocol_converter" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.1" description="" />
  </section>
  <section name="axi_protocol_converter_v2_1_11_axi_protocol_converter/3" level="2" order="5" description="">
   <keyValuePair key="c_axi_addr_width" value="12" description="" />
   <keyValuePair key="c_axi_aruser_width" value="1" description="" />
   <keyValuePair key="c_axi_awuser_width" value="1" description="" />
   <keyValuePair key="c_axi_buser_width" value="1" description="" />
   <keyValuePair key="c_axi_data_width" value="32" description="" />
   <keyValuePair key="c_axi_id_width" value="12" description="" />
   <keyValuePair key="c_axi_ruser_width" value="1" description="" />
   <keyValuePair key="c_axi_supports_read" value="1" description="" />
   <keyValuePair key="c_axi_supports_user_signals" value="0" description="" />
   <keyValuePair key="c_axi_supports_write" value="1" description="" />
   <keyValuePair key="c_axi_wuser_width" value="1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_ignore_id" value="0" description="" />
   <keyValuePair key="c_m_axi_protocol" value="2" description="" />
   <keyValuePair key="c_s_axi_protocol" value="0" description="" />
   <keyValuePair key="c_translation_mode" value="2" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="11" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="axi_protocol_converter" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.1" description="" />
  </section>
  <section name="axi_protocol_converter_v2_1_11_axi_protocol_converter/4" level="2" order="6" description="">
   <keyValuePair key="c_axi_addr_width" value="32" description="" />
   <keyValuePair key="c_axi_aruser_width" value="1" description="" />
   <keyValuePair key="c_axi_awuser_width" value="1" description="" />
   <keyValuePair key="c_axi_buser_width" value="1" description="" />
   <keyValuePair key="c_axi_data_width" value="32" description="" />
   <keyValuePair key="c_axi_id_width" value="12" description="" />
   <keyValuePair key="c_axi_ruser_width" value="1" description="" />
   <keyValuePair key="c_axi_supports_read" value="1" description="" />
   <keyValuePair key="c_axi_supports_user_signals" value="0" description="" />
   <keyValuePair key="c_axi_supports_write" value="1" description="" />
   <keyValuePair key="c_axi_wuser_width" value="1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_ignore_id" value="0" description="" />
   <keyValuePair key="c_m_axi_protocol" value="0" description="" />
   <keyValuePair key="c_s_axi_protocol" value="1" description="" />
   <keyValuePair key="c_translation_mode" value="2" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="11" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="axi_protocol_converter" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.1" description="" />
  </section>
  <section name="proc_sys_reset/1" level="2" order="7" description="">
   <keyValuePair key="c_aux_reset_high" value="0" description="" />
   <keyValuePair key="c_aux_rst_width" value="4" description="" />
   <keyValuePair key="c_ext_reset_high" value="0" description="" />
   <keyValuePair key="c_ext_rst_width" value="1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_num_bus_rst" value="1" description="" />
   <keyValuePair key="c_num_interconnect_aresetn" value="1" description="" />
   <keyValuePair key="c_num_perp_aresetn" value="1" description="" />
   <keyValuePair key="c_num_perp_rst" value="1" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="proc_sys_reset" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="5.0" description="" />
  </section>
  <section name="proc_sys_reset/2" level="2" order="8" description="">
   <keyValuePair key="c_aux_reset_high" value="0" description="" />
   <keyValuePair key="c_aux_rst_width" value="4" description="" />
   <keyValuePair key="c_ext_reset_high" value="0" description="" />
   <keyValuePair key="c_ext_rst_width" value="4" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_num_bus_rst" value="1" description="" />
   <keyValuePair key="c_num_interconnect_aresetn" value="1" description="" />
   <keyValuePair key="c_num_perp_aresetn" value="1" description="" />
   <keyValuePair key="c_num_perp_rst" value="1" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="proc_sys_reset" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="5.0" description="" />
  </section>
  <section name="processing_system7_v5.5_user_configuration/1" level="2" order="9" description="">
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="pcw_apu_clk_ratio_enable" value="6:2:1" description="" />
   <keyValuePair key="pcw_apu_peripheral_freqmhz" value="666.666667" description="" />
   <keyValuePair key="pcw_armpll_ctrl_fbdiv" value="40" description="" />
   <keyValuePair key="pcw_can0_grp_clk_enable" value="0" description="" />
   <keyValuePair key="pcw_can0_peripheral_clksrc" value="External" description="" />
   <keyValuePair key="pcw_can0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_can0_peripheral_freqmhz" value="-1" description="" />
   <keyValuePair key="pcw_can1_grp_clk_enable" value="0" description="" />
   <keyValuePair key="pcw_can1_peripheral_clksrc" value="External" description="" />
   <keyValuePair key="pcw_can1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_can1_peripheral_freqmhz" value="-1" description="" />
   <keyValuePair key="pcw_can_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_can_peripheral_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_cpu_cpu_pll_freqmhz" value="1333.333" description="" />
   <keyValuePair key="pcw_cpu_peripheral_clksrc" value="ARM PLL" description="" />
   <keyValuePair key="pcw_crystal_peripheral_freqmhz" value="33.333333" description="" />
   <keyValuePair key="pcw_dci_peripheral_clksrc" value="DDR PLL" description="" />
   <keyValuePair key="pcw_dci_peripheral_freqmhz" value="10.159" description="" />
   <keyValuePair key="pcw_ddr_ddr_pll_freqmhz" value="1066.667" description="" />
   <keyValuePair key="pcw_ddr_hpr_to_critical_priority_level" value="15" description="" />
   <keyValuePair key="pcw_ddr_hprlpr_queue_partition" value="HPR(0)/LPR(32)" description="" />
   <keyValuePair key="pcw_ddr_lpr_to_critical_priority_level" value="2" description="" />
   <keyValuePair key="pcw_ddr_peripheral_clksrc" value="DDR PLL" description="" />
   <keyValuePair key="pcw_ddr_port0_hpr_enable" value="0" description="" />
   <keyValuePair key="pcw_ddr_port1_hpr_enable" value="0" description="" />
   <keyValuePair key="pcw_ddr_port2_hpr_enable" value="0" description="" />
   <keyValuePair key="pcw_ddr_port3_hpr_enable" value="0" description="" />
   <keyValuePair key="pcw_ddr_write_to_critical_priority_level" value="2" description="" />
   <keyValuePair key="pcw_ddrpll_ctrl_fbdiv" value="32" description="" />
   <keyValuePair key="pcw_enet0_grp_mdio_enable" value="0" description="" />
   <keyValuePair key="pcw_enet0_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_enet0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_enet0_peripheral_freqmhz" value="1000 Mbps" description="" />
   <keyValuePair key="pcw_enet0_reset_enable" value="1" description="" />
   <keyValuePair key="pcw_enet0_reset_io" value="MIO 40" description="" />
   <keyValuePair key="pcw_enet1_enet1_io" value="MIO 28 .. 39" description="" />
   <keyValuePair key="pcw_enet1_grp_mdio_enable" value="1" description="" />
   <keyValuePair key="pcw_enet1_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_enet1_peripheral_enable" value="1" description="" />
   <keyValuePair key="pcw_enet1_peripheral_freqmhz" value="1000 Mbps" description="" />
   <keyValuePair key="pcw_enet1_reset_enable" value="1" description="" />
   <keyValuePair key="pcw_enet1_reset_io" value="MIO 40" description="" />
   <keyValuePair key="pcw_enet_reset_polarity" value="Active Low" description="" />
   <keyValuePair key="pcw_fclk0_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_fclk1_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_fclk2_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_fclk3_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_fpga0_peripheral_freqmhz" value="100.0" description="" />
   <keyValuePair key="pcw_fpga1_peripheral_freqmhz" value="200.0" description="" />
   <keyValuePair key="pcw_fpga2_peripheral_freqmhz" value="50" description="" />
   <keyValuePair key="pcw_fpga3_peripheral_freqmhz" value="50" description="" />
   <keyValuePair key="pcw_fpga_fclk0_enable" value="1" description="" />
   <keyValuePair key="pcw_fpga_fclk1_enable" value="1" description="" />
   <keyValuePair key="pcw_fpga_fclk2_enable" value="0" description="" />
   <keyValuePair key="pcw_fpga_fclk3_enable" value="0" description="" />
   <keyValuePair key="pcw_gpio_emio_gpio_enable" value="1" description="" />
   <keyValuePair key="pcw_gpio_emio_gpio_io" value="64" description="" />
   <keyValuePair key="pcw_gpio_mio_gpio_enable" value="1" description="" />
   <keyValuePair key="pcw_gpio_mio_gpio_io" value="MIO" description="" />
   <keyValuePair key="pcw_gpio_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c0_grp_int_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c0_reset_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c1_grp_int_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c1_reset_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c_reset_polarity" value="Active Low" description="" />
   <keyValuePair key="pcw_io_io_pll_freqmhz" value="1000.000" description="" />
   <keyValuePair key="pcw_iopll_ctrl_fbdiv" value="30" description="" />
   <keyValuePair key="pcw_irq_f2p_mode" value="REVERSE" description="" />
   <keyValuePair key="pcw_m_axi_gp0_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_m_axi_gp1_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_nand_cycles_t_ar" value="1" description="" />
   <keyValuePair key="pcw_nand_cycles_t_clr" value="1" description="" />
   <keyValuePair key="pcw_nand_cycles_t_rc" value="11" description="" />
   <keyValuePair key="pcw_nand_cycles_t_rea" value="1" description="" />
   <keyValuePair key="pcw_nand_cycles_t_rr" value="1" description="" />
   <keyValuePair key="pcw_nand_cycles_t_wc" value="11" description="" />
   <keyValuePair key="pcw_nand_cycles_t_wp" value="1" description="" />
   <keyValuePair key="pcw_nand_grp_d8_enable" value="0" description="" />
   <keyValuePair key="pcw_nand_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_cs0_t_ceoe" value="1" description="" />
   <keyValuePair key="pcw_nor_cs0_t_pc" value="1" description="" />
   <keyValuePair key="pcw_nor_cs0_t_rc" value="11" description="" />
   <keyValuePair key="pcw_nor_cs0_t_tr" value="1" description="" />
   <keyValuePair key="pcw_nor_cs0_t_wc" value="11" description="" />
   <keyValuePair key="pcw_nor_cs0_t_wp" value="1" description="" />
   <keyValuePair key="pcw_nor_cs0_we_time" value="0" description="" />
   <keyValuePair key="pcw_nor_cs1_t_ceoe" value="1" description="" />
   <keyValuePair key="pcw_nor_cs1_t_pc" value="1" description="" />
   <keyValuePair key="pcw_nor_cs1_t_rc" value="11" description="" />
   <keyValuePair key="pcw_nor_cs1_t_tr" value="1" description="" />
   <keyValuePair key="pcw_nor_cs1_t_wc" value="11" description="" />
   <keyValuePair key="pcw_nor_cs1_t_wp" value="1" description="" />
   <keyValuePair key="pcw_nor_cs1_we_time" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_a25_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_cs0_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_cs1_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_sram_cs0_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_sram_cs1_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_sram_int_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_ceoe" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_pc" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_rc" value="11" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_tr" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_wc" value="11" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_wp" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_we_time" value="0" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_ceoe" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_pc" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_rc" value="11" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_tr" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_wc" value="11" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_wp" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_we_time" value="0" description="" />
   <keyValuePair key="pcw_override_basic_clock" value="0" description="" />
   <keyValuePair key="pcw_pcap_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_pcap_peripheral_freqmhz" value="200" description="" />
   <keyValuePair key="pcw_pjtag_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_preset_bank0_voltage" value="LVCMOS 3.3V" description="" />
   <keyValuePair key="pcw_preset_bank1_voltage" value="LVCMOS 2.5V" description="" />
   <keyValuePair key="pcw_qspi_grp_fbclk_enable" value="0" description="" />
   <keyValuePair key="pcw_qspi_grp_io1_enable" value="0" description="" />
   <keyValuePair key="pcw_qspi_grp_single_ss_enable" value="1" description="" />
   <keyValuePair key="pcw_qspi_grp_single_ss_io" value="MIO 1 .. 6" description="" />
   <keyValuePair key="pcw_qspi_grp_ss1_enable" value="0" description="" />
   <keyValuePair key="pcw_qspi_internal_highaddress" value="0xFCFFFFFF" description="" />
   <keyValuePair key="pcw_qspi_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_qspi_peripheral_enable" value="1" description="" />
   <keyValuePair key="pcw_qspi_peripheral_freqmhz" value="200" description="" />
   <keyValuePair key="pcw_qspi_qspi_io" value="MIO 1 .. 6" description="" />
   <keyValuePair key="pcw_s_axi_acp_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_s_axi_gp0_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_s_axi_gp1_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_s_axi_hp0_data_width" value="64" description="" />
   <keyValuePair key="pcw_s_axi_hp0_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_s_axi_hp1_data_width" value="64" description="" />
   <keyValuePair key="pcw_s_axi_hp1_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_s_axi_hp2_data_width" value="64" description="" />
   <keyValuePair key="pcw_s_axi_hp2_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_s_axi_hp3_data_width" value="64" description="" />
   <keyValuePair key="pcw_s_axi_hp3_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_sd0_grp_cd_enable" value="0" description="" />
   <keyValuePair key="pcw_sd0_grp_pow_enable" value="0" description="" />
   <keyValuePair key="pcw_sd0_grp_wp_enable" value="0" description="" />
   <keyValuePair key="pcw_sd0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_sd1_grp_cd_enable" value="0" description="" />
   <keyValuePair key="pcw_sd1_grp_pow_enable" value="0" description="" />
   <keyValuePair key="pcw_sd1_grp_wp_enable" value="0" description="" />
   <keyValuePair key="pcw_sd1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_sdio_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_sdio_peripheral_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_smc_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_smc_peripheral_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_spi0_grp_ss0_enable" value="1" description="" />
   <keyValuePair key="pcw_spi0_grp_ss0_io" value="EMIO" description="" />
   <keyValuePair key="pcw_spi0_grp_ss1_enable" value="1" description="" />
   <keyValuePair key="pcw_spi0_grp_ss1_io" value="EMIO" description="" />
   <keyValuePair key="pcw_spi0_grp_ss2_enable" value="1" description="" />
   <keyValuePair key="pcw_spi0_grp_ss2_io" value="EMIO" description="" />
   <keyValuePair key="pcw_spi0_peripheral_enable" value="1" description="" />
   <keyValuePair key="pcw_spi0_spi0_io" value="EMIO" description="" />
   <keyValuePair key="pcw_spi1_grp_ss0_enable" value="1" description="" />
   <keyValuePair key="pcw_spi1_grp_ss0_io" value="EMIO" description="" />
   <keyValuePair key="pcw_spi1_grp_ss1_enable" value="1" description="" />
   <keyValuePair key="pcw_spi1_grp_ss1_io" value="EMIO" description="" />
   <keyValuePair key="pcw_spi1_grp_ss2_enable" value="1" description="" />
   <keyValuePair key="pcw_spi1_grp_ss2_io" value="EMIO" description="" />
   <keyValuePair key="pcw_spi1_peripheral_enable" value="1" description="" />
   <keyValuePair key="pcw_spi1_spi1_io" value="EMIO" description="" />
   <keyValuePair key="pcw_spi_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_spi_peripheral_freqmhz" value="166.666666" description="" />
   <keyValuePair key="pcw_tpiu_peripheral_clksrc" value="External" description="" />
   <keyValuePair key="pcw_tpiu_peripheral_freqmhz" value="200" description="" />
   <keyValuePair key="pcw_trace_grp_16bit_enable" value="0" description="" />
   <keyValuePair key="pcw_trace_grp_2bit_enable" value="0" description="" />
   <keyValuePair key="pcw_trace_grp_32bit_enable" value="0" description="" />
   <keyValuePair key="pcw_trace_grp_4bit_enable" value="0" description="" />
   <keyValuePair key="pcw_trace_grp_8bit_enable" value="0" description="" />
   <keyValuePair key="pcw_trace_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_ttc0_clk0_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc0_clk0_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc0_clk1_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc0_clk1_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc0_clk2_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc0_clk2_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_ttc1_clk0_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc1_clk0_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc1_clk1_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc1_clk1_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc1_clk2_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc1_clk2_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_ttc_peripheral_freqmhz" value="50" description="" />
   <keyValuePair key="pcw_uart0_baud_rate" value="115200" description="" />
   <keyValuePair key="pcw_uart0_grp_full_enable" value="0" description="" />
   <keyValuePair key="pcw_uart0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_uart1_baud_rate" value="115200" description="" />
   <keyValuePair key="pcw_uart1_grp_full_enable" value="0" description="" />
   <keyValuePair key="pcw_uart1_peripheral_enable" value="1" description="" />
   <keyValuePair key="pcw_uart1_uart1_io" value="MIO 12 .. 13" description="" />
   <keyValuePair key="pcw_uart_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_uart_peripheral_freqmhz" value="50" description="" />
   <keyValuePair key="pcw_uiparam_ddr_adv_enable" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_al" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_bank_addr_count" value="3" description="" />
   <keyValuePair key="pcw_uiparam_ddr_bl" value="8" description="" />
   <keyValuePair key="pcw_uiparam_ddr_board_delay0" value="0.41" description="" />
   <keyValuePair key="pcw_uiparam_ddr_board_delay1" value="0.411" description="" />
   <keyValuePair key="pcw_uiparam_ddr_board_delay2" value="0.341" description="" />
   <keyValuePair key="pcw_uiparam_ddr_board_delay3" value="0.358" description="" />
   <keyValuePair key="pcw_uiparam_ddr_bus_width" value="32 Bit" description="" />
   <keyValuePair key="pcw_uiparam_ddr_cl" value="7" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_0_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_0_package_length" value="61.0905" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_0_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_1_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_1_package_length" value="61.0905" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_1_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_2_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_2_package_length" value="61.0905" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_2_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_3_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_3_package_length" value="61.0905" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_3_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_stop_en" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_col_addr_count" value="10" description="" />
   <keyValuePair key="pcw_uiparam_ddr_cwl" value="6" description="" />
   <keyValuePair key="pcw_uiparam_ddr_device_capacity" value="1024 MBits" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_0_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_0_package_length" value="64.1705" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_0_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_1_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_1_package_length" value="63.686" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_1_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_2_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_2_package_length" value="68.46" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_2_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_3_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_3_package_length" value="105.4895" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_3_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_0_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_0_package_length" value="68.4725" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_0_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_1_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_1_package_length" value="71.086" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_1_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_2_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_2_package_length" value="66.794" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_2_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_3_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_3_package_length" value="108.7385" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_3_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_to_clk_delay_0" value="0.025" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_to_clk_delay_1" value="0.028" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_to_clk_delay_2" value="-0.009" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_to_clk_delay_3" value="-0.061" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dram_width" value="16 Bits" description="" />
   <keyValuePair key="pcw_uiparam_ddr_ecc" value="Disabled" description="" />
   <keyValuePair key="pcw_uiparam_ddr_enable" value="1" description="" />
   <keyValuePair key="pcw_uiparam_ddr_freq_mhz" value="533.333313" description="" />
   <keyValuePair key="pcw_uiparam_ddr_high_temp" value="Normal (0-85)" description="" />
   <keyValuePair key="pcw_uiparam_ddr_memory_type" value="DDR 3" description="" />
   <keyValuePair key="pcw_uiparam_ddr_partno" value="MT41J64M16 JT-125G" description="" />
   <keyValuePair key="pcw_uiparam_ddr_row_addr_count" value="13" description="" />
   <keyValuePair key="pcw_uiparam_ddr_speed_bin" value="DDR3_1066F" description="" />
   <keyValuePair key="pcw_uiparam_ddr_t_faw" value="40.0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_t_ras_min" value="35.0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_t_rc" value="48.75" description="" />
   <keyValuePair key="pcw_uiparam_ddr_t_rcd" value="7" description="" />
   <keyValuePair key="pcw_uiparam_ddr_t_rp" value="7" description="" />
   <keyValuePair key="pcw_uiparam_ddr_train_data_eye" value="1" description="" />
   <keyValuePair key="pcw_uiparam_ddr_train_read_gate" value="1" description="" />
   <keyValuePair key="pcw_uiparam_ddr_train_write_level" value="1" description="" />
   <keyValuePair key="pcw_uiparam_ddr_use_internal_vref" value="1" description="" />
   <keyValuePair key="pcw_usb0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_usb0_peripheral_freqmhz" value="60" description="" />
   <keyValuePair key="pcw_usb0_reset_enable" value="0" description="" />
   <keyValuePair key="pcw_usb1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_usb1_peripheral_freqmhz" value="60" description="" />
   <keyValuePair key="pcw_usb1_reset_enable" value="0" description="" />
   <keyValuePair key="pcw_usb_reset_polarity" value="Active Low" description="" />
   <keyValuePair key="pcw_use_cross_trigger" value="0" description="" />
   <keyValuePair key="pcw_use_m_axi_gp0" value="1" description="" />
   <keyValuePair key="pcw_use_m_axi_gp1" value="0" description="" />
   <keyValuePair key="pcw_use_s_axi_acp" value="0" description="" />
   <keyValuePair key="pcw_use_s_axi_gp0" value="0" description="" />
   <keyValuePair key="pcw_use_s_axi_gp1" value="0" description="" />
   <keyValuePair key="pcw_use_s_axi_hp0" value="1" description="" />
   <keyValuePair key="pcw_use_s_axi_hp1" value="1" description="" />
   <keyValuePair key="pcw_use_s_axi_hp2" value="1" description="" />
   <keyValuePair key="pcw_use_s_axi_hp3" value="0" description="" />
   <keyValuePair key="pcw_wdt_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_wdt_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_wdt_peripheral_freqmhz" value="133.333333" description="" />
  </section>
  <section name="processing_system7_v5_5_processing_system7/1" level="2" order="10" description="">
   <keyValuePair key="c_dm_width" value="4" description="" />
   <keyValuePair key="c_dq_width" value="32" description="" />
   <keyValuePair key="c_dqs_width" value="4" description="" />
   <keyValuePair key="c_emio_gpio_width" value="64" description="" />
   <keyValuePair key="c_en_emio_enet0" value="0" description="" />
   <keyValuePair key="c_en_emio_enet1" value="0" description="" />
   <keyValuePair key="c_en_emio_pjtag" value="0" description="" />
   <keyValuePair key="c_en_emio_trace" value="0" description="" />
   <keyValuePair key="c_fclk_clk0_buf" value="TRUE" description="" />
   <keyValuePair key="c_fclk_clk1_buf" value="TRUE" description="" />
   <keyValuePair key="c_fclk_clk2_buf" value="FALSE" description="" />
   <keyValuePair key="c_fclk_clk3_buf" value="FALSE" description="" />
   <keyValuePair key="c_gp0_en_modifiable_txn" value="0" description="" />
   <keyValuePair key="c_gp1_en_modifiable_txn" value="0" description="" />
   <keyValuePair key="c_include_acp_trans_check" value="0" description="" />
   <keyValuePair key="c_include_trace_buffer" value="0" description="" />
   <keyValuePair key="c_irq_f2p_mode" value="REVERSE" description="" />
   <keyValuePair key="c_m_axi_gp0_enable_static_remap" value="0" description="" />
   <keyValuePair key="c_m_axi_gp0_id_width" value="12" description="" />
   <keyValuePair key="c_m_axi_gp0_thread_id_width" value="12" description="" />
   <keyValuePair key="c_m_axi_gp1_enable_static_remap" value="0" description="" />
   <keyValuePair key="c_m_axi_gp1_id_width" value="12" description="" />
   <keyValuePair key="c_m_axi_gp1_thread_id_width" value="12" description="" />
   <keyValuePair key="c_mio_primitive" value="54" description="" />
   <keyValuePair key="c_num_f2p_intr_inputs" value="16" description="" />
   <keyValuePair key="c_package_name" value="clg484" description="" />
   <keyValuePair key="c_ps7_si_rev" value="PRODUCTION" description="" />
   <keyValuePair key="c_s_axi_acp_aruser_val" value="31" description="" />
   <keyValuePair key="c_s_axi_acp_awuser_val" value="31" description="" />
   <keyValuePair key="c_s_axi_acp_id_width" value="3" description="" />
   <keyValuePair key="c_s_axi_gp0_id_width" value="6" description="" />
   <keyValuePair key="c_s_axi_gp1_id_width" value="6" description="" />
   <keyValuePair key="c_s_axi_hp0_data_width" value="64" description="" />
   <keyValuePair key="c_s_axi_hp0_id_width" value="6" description="" />
   <keyValuePair key="c_s_axi_hp1_data_width" value="64" description="" />
   <keyValuePair key="c_s_axi_hp1_id_width" value="6" description="" />
   <keyValuePair key="c_s_axi_hp2_data_width" value="64" description="" />
   <keyValuePair key="c_s_axi_hp2_id_width" value="6" description="" />
   <keyValuePair key="c_s_axi_hp3_data_width" value="64" description="" />
   <keyValuePair key="c_s_axi_hp3_id_width" value="6" description="" />
   <keyValuePair key="c_trace_buffer_clock_delay" value="12" description="" />
   <keyValuePair key="c_trace_buffer_fifo_size" value="128" description="" />
   <keyValuePair key="c_trace_internal_width" value="2" description="" />
   <keyValuePair key="c_trace_pipeline_width" value="8" description="" />
   <keyValuePair key="c_use_axi_nonsecure" value="0" description="" />
   <keyValuePair key="c_use_default_acp_user_val" value="0" description="" />
   <keyValuePair key="c_use_m_axi_gp0" value="1" description="" />
   <keyValuePair key="c_use_m_axi_gp1" value="0" description="" />
   <keyValuePair key="c_use_s_axi_acp" value="0" description="" />
   <keyValuePair key="c_use_s_axi_gp0" value="0" description="" />
   <keyValuePair key="c_use_s_axi_hp0" value="1" description="" />
   <keyValuePair key="c_use_s_axi_hp1" value="1" description="" />
   <keyValuePair key="c_use_s_axi_hp2" value="1" description="" />
   <keyValuePair key="c_use_s_axi_hp3" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="use_trace_data_edge_detector" value="0" description="" />
   <keyValuePair key="x_ipcorerevision" value="3" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="processing_system7" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="5.5" description="" />
  </section>
  <section name="util_reduced_logic/1" level="2" order="11" description="">
   <keyValuePair key="c_operation" value="and" description="" />
   <keyValuePair key="c_size" value="2" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="2" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="util_reduced_logic" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.0" description="" />
  </section>
  <section name="xlconcat/1" level="2" order="12" description="">
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="dout_width" value="16" description="" />
   <keyValuePair key="in0_width" value="1" description="" />
   <keyValuePair key="in10_width" value="1" description="" />
   <keyValuePair key="in11_width" value="1" description="" />
   <keyValuePair key="in12_width" value="1" description="" />
   <keyValuePair key="in13_width" value="1" description="" />
   <keyValuePair key="in14_width" value="1" description="" />
   <keyValuePair key="in15_width" value="1" description="" />
   <keyValuePair key="in16_width" value="1" description="" />
   <keyValuePair key="in17_width" value="1" description="" />
   <keyValuePair key="in18_width" value="1" description="" />
   <keyValuePair key="in19_width" value="1" description="" />
   <keyValuePair key="in1_width" value="1" description="" />
   <keyValuePair key="in20_width" value="1" description="" />
   <keyValuePair key="in21_width" value="1" description="" />
   <keyValuePair key="in22_width" value="1" description="" />
   <keyValuePair key="in23_width" value="1" description="" />
   <keyValuePair key="in24_width" value="1" description="" />
   <keyValuePair key="in25_width" value="1" description="" />
   <keyValuePair key="in26_width" value="1" description="" />
   <keyValuePair key="in27_width" value="1" description="" />
   <keyValuePair key="in28_width" value="1" description="" />
   <keyValuePair key="in29_width" value="1" description="" />
   <keyValuePair key="in2_width" value="1" description="" />
   <keyValuePair key="in30_width" value="1" description="" />
   <keyValuePair key="in31_width" value="1" description="" />
   <keyValuePair key="in3_width" value="1" description="" />
   <keyValuePair key="in4_width" value="1" description="" />
   <keyValuePair key="in5_width" value="1" description="" />
   <keyValuePair key="in6_width" value="1" description="" />
   <keyValuePair key="in7_width" value="1" description="" />
   <keyValuePair key="in8_width" value="1" description="" />
   <keyValuePair key="in9_width" value="1" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="num_ports" value="16" description="" />
   <keyValuePair key="x_ipcorerevision" value="2" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="xlconcat" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.1" description="" />
  </section>
  <section name="xlconcat/2" level="2" order="13" description="">
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="dout_width" value="2" description="" />
   <keyValuePair key="in0_width" value="1" description="" />
   <keyValuePair key="in10_width" value="1" description="" />
   <keyValuePair key="in11_width" value="1" description="" />
   <keyValuePair key="in12_width" value="1" description="" />
   <keyValuePair key="in13_width" value="1" description="" />
   <keyValuePair key="in14_width" value="1" description="" />
   <keyValuePair key="in15_width" value="1" description="" />
   <keyValuePair key="in16_width" value="1" description="" />
   <keyValuePair key="in17_width" value="1" description="" />
   <keyValuePair key="in18_width" value="1" description="" />
   <keyValuePair key="in19_width" value="1" description="" />
   <keyValuePair key="in1_width" value="1" description="" />
   <keyValuePair key="in20_width" value="1" description="" />
   <keyValuePair key="in21_width" value="1" description="" />
   <keyValuePair key="in22_width" value="1" description="" />
   <keyValuePair key="in23_width" value="1" description="" />
   <keyValuePair key="in24_width" value="1" description="" />
   <keyValuePair key="in25_width" value="1" description="" />
   <keyValuePair key="in26_width" value="1" description="" />
   <keyValuePair key="in27_width" value="1" description="" />
   <keyValuePair key="in28_width" value="1" description="" />
   <keyValuePair key="in29_width" value="1" description="" />
   <keyValuePair key="in2_width" value="1" description="" />
   <keyValuePair key="in30_width" value="1" description="" />
   <keyValuePair key="in31_width" value="1" description="" />
   <keyValuePair key="in3_width" value="1" description="" />
   <keyValuePair key="in4_width" value="1" description="" />
   <keyValuePair key="in5_width" value="1" description="" />
   <keyValuePair key="in6_width" value="1" description="" />
   <keyValuePair key="in7_width" value="1" description="" />
   <keyValuePair key="in8_width" value="1" description="" />
   <keyValuePair key="in9_width" value="1" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="num_ports" value="2" description="" />
   <keyValuePair key="x_ipcorerevision" value="2" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="xlconcat" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2016.4" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.1" description="" />
  </section>
 </section>
 <section name="power_opt_design" level="1" order="4" description="">
  <section name="command_line_options_spo" level="2" order="1" description="">
   <keyValuePair key="-cell_types" value="default::all" description="" />
   <keyValuePair key="-clocks" value="default::[not_specified]" description="" />
   <keyValuePair key="-exclude_cells" value="default::[not_specified]" description="" />
   <keyValuePair key="-include_cells" value="default::[not_specified]" description="" />
  </section>
  <section name="usage" level="2" order="2" description="">
   <keyValuePair key="bram_ports_augmented" value="1" description="" />
   <keyValuePair key="bram_ports_newly_gated" value="6" description="" />
   <keyValuePair key="bram_ports_total" value="8" description="" />
   <keyValuePair key="flow_state" value="default" description="" />
   <keyValuePair key="slice_registers_augmented" value="0" description="" />
   <keyValuePair key="slice_registers_newly_gated" value="0" description="" />
   <keyValuePair key="slice_registers_total" value="22149" description="" />
   <keyValuePair key="srls_augmented" value="0" description="" />
   <keyValuePair key="srls_newly_gated" value="0" description="" />
   <keyValuePair key="srls_total" value="276" description="" />
  </section>
 </section>
 <section name="report_drc" level="1" order="5" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-append" value="default::[not_specified]" description="" />
   <keyValuePair key="-checks" value="default::[not_specified]" description="" />
   <keyValuePair key="-fail_on" value="default::[not_specified]" description="" />
   <keyValuePair key="-force" value="default::[not_specified]" description="" />
   <keyValuePair key="-format" value="default::[not_specified]" description="" />
   <keyValuePair key="-messages" value="default::[not_specified]" description="" />
   <keyValuePair key="-name" value="default::[not_specified]" description="" />
   <keyValuePair key="-return_string" value="default::[not_specified]" description="" />
   <keyValuePair key="-ruledecks" value="default::[not_specified]" description="" />
   <keyValuePair key="-upgrade_cw" value="default::[not_specified]" description="" />
  </section>
  <section name="results" level="2" order="2" description="">
   <keyValuePair key="aval-4" value="56" description="" />
   <keyValuePair key="check-3" value="1" description="" />
   <keyValuePair key="dpop-1" value="4" description="" />
   <keyValuePair key="reqp-1839" value="20" description="" />
  </section>
 </section>
 <section name="report_methodology" level="1" order="6" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-append" value="default::[not_specified]" description="" />
   <keyValuePair key="-checks" value="default::[not_specified]" description="" />
   <keyValuePair key="-fail_on" value="default::[not_specified]" description="" />
   <keyValuePair key="-force" value="default::[not_specified]" description="" />
   <keyValuePair key="-format" value="default::[not_specified]" description="" />
   <keyValuePair key="-messages" value="default::[not_specified]" description="" />
   <keyValuePair key="-name" value="default::[not_specified]" description="" />
   <keyValuePair key="-return_string" value="default::[not_specified]" description="" />
  </section>
  <section name="results" level="2" order="2" description="">
   <keyValuePair key="pdrc-190" value="4" description="" />
   <keyValuePair key="timing-10" value="1" description="" />
   <keyValuePair key="timing-18" value="32" description="" />
   <keyValuePair key="timing-28" value="2" description="" />
   <keyValuePair key="timing-9" value="1" description="" />
  </section>
 </section>
 <section name="report_power" level="1" order="7" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-advisory" value="default::[not_specified]" description="" />
   <keyValuePair key="-append" value="default::[not_specified]" description="" />
   <keyValuePair key="-file" value="[specified]" description="" />
   <keyValuePair key="-format" value="default::text" description="" />
   <keyValuePair key="-hier" value="default::power" description="" />
   <keyValuePair key="-l" value="default::[not_specified]" description="" />
   <keyValuePair key="-name" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_propagation" value="default::[not_specified]" description="" />
   <keyValuePair key="-return_string" value="default::[not_specified]" description="" />
   <keyValuePair key="-rpx" value="[specified]" description="" />
   <keyValuePair key="-verbose" value="default::[not_specified]" description="" />
   <keyValuePair key="-vid" value="default::[not_specified]" description="" />
   <keyValuePair key="-xpe" value="default::[not_specified]" description="" />
  </section>
  <section name="usage" level="2" order="2" description="">
   <keyValuePair key="airflow" value="250 (LFM)" description="" />
   <keyValuePair key="ambient_temp" value="25.0 (C)" description="" />
   <keyValuePair key="bi-dir_toggle" value="12.500000" description="" />
   <keyValuePair key="bidir_output_enable" value="1.000000" description="" />
   <keyValuePair key="board_layers" value="12to15 (12 to 15 Layers)" description="" />
   <keyValuePair key="board_selection" value="medium (10&quot;x10&quot;)" description="" />
   <keyValuePair key="bram" value="0.009336" description="" />
   <keyValuePair key="clocks" value="0.184502" description="" />
   <keyValuePair key="confidence_level_clock_activity" value="High" description="" />
   <keyValuePair key="confidence_level_design_state" value="High" description="" />
   <keyValuePair key="confidence_level_device_models" value="High" description="" />
   <keyValuePair key="confidence_level_internal_activity" value="Medium" description="" />
   <keyValuePair key="confidence_level_io_activity" value="Low" description="" />
   <keyValuePair key="confidence_level_overall" value="Low" description="" />
   <keyValuePair key="customer" value="TBD" description="" />
   <keyValuePair key="customer_class" value="TBD" description="" />
   <keyValuePair key="devstatic" value="0.256536" description="" />
   <keyValuePair key="die" value="xc7z045ffg900-2" description="" />
   <keyValuePair key="dsp" value="0.130862" description="" />
   <keyValuePair key="dsp_output_toggle" value="12.500000" description="" />
   <keyValuePair key="dynamic" value="2.260907" description="" />
   <keyValuePair key="effective_thetaja" value="1.8" description="" />
   <keyValuePair key="enable_probability" value="0.990000" description="" />
   <keyValuePair key="family" value="zynq" description="" />
   <keyValuePair key="ff_toggle" value="12.500000" description="" />
   <keyValuePair key="flow_state" value="routed" description="" />
   <keyValuePair key="heatsink" value="medium (Medium Profile)" description="" />
   <keyValuePair key="i/o" value="0.213962" description="" />
   <keyValuePair key="input_toggle" value="12.500000" description="" />
   <keyValuePair key="junction_temp" value="29.5 (C)" description="" />
   <keyValuePair key="logic" value="0.071252" description="" />
   <keyValuePair key="mgtavcc_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="mgtavcc_static_current" value="0.000000" description="" />
   <keyValuePair key="mgtavcc_total_current" value="0.000000" description="" />
   <keyValuePair key="mgtavcc_voltage" value="1.000000" description="" />
   <keyValuePair key="mgtavtt_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="mgtavtt_static_current" value="0.000000" description="" />
   <keyValuePair key="mgtavtt_total_current" value="0.000000" description="" />
   <keyValuePair key="mgtavtt_voltage" value="1.200000" description="" />
   <keyValuePair key="mgtvccaux_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="mgtvccaux_static_current" value="0.000000" description="" />
   <keyValuePair key="mgtvccaux_total_current" value="0.000000" description="" />
   <keyValuePair key="mgtvccaux_voltage" value="1.800000" description="" />
   <keyValuePair key="netlist_net_matched" value="NA" description="" />
   <keyValuePair key="off-chip_power" value="0.009800" description="" />
   <keyValuePair key="on-chip_power" value="2.517444" description="" />
   <keyValuePair key="output_enable" value="1.000000" description="" />
   <keyValuePair key="output_load" value="5.000000" description="" />
   <keyValuePair key="output_toggle" value="12.500000" description="" />
   <keyValuePair key="package" value="ffg900" description="" />
   <keyValuePair key="pct_clock_constrained" value="17.000000" description="" />
   <keyValuePair key="pct_inputs_defined" value="4" description="" />
   <keyValuePair key="platform" value="nt64" description="" />
   <keyValuePair key="process" value="typical" description="" />
   <keyValuePair key="ps7" value="1.542892" description="" />
   <keyValuePair key="ram_enable" value="50.000000" description="" />
   <keyValuePair key="ram_write" value="50.000000" description="" />
   <keyValuePair key="read_saif" value="False" description="" />
   <keyValuePair key="set/reset_probability" value="0.000000" description="" />
   <keyValuePair key="signal_rate" value="False" description="" />
   <keyValuePair key="signals" value="0.128192" description="" />
   <keyValuePair key="simulation_file" value="None" description="" />
   <keyValuePair key="speedgrade" value="-2" description="" />
   <keyValuePair key="static_prob" value="False" description="" />
   <keyValuePair key="temp_grade" value="commercial" description="" />
   <keyValuePair key="thetajb" value="2.7 (C/W)" description="" />
   <keyValuePair key="thetasa" value="3.3 (C/W)" description="" />
   <keyValuePair key="toggle_rate" value="False" description="" />
   <keyValuePair key="user_board_temp" value="25.0 (C)" description="" />
   <keyValuePair key="user_effective_thetaja" value="1.8" description="" />
   <keyValuePair key="user_junc_temp" value="29.5 (C)" description="" />
   <keyValuePair key="user_thetajb" value="2.7 (C/W)" description="" />
   <keyValuePair key="user_thetasa" value="3.3 (C/W)" description="" />
   <keyValuePair key="vccadc_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vccadc_static_current" value="0.020000" description="" />
   <keyValuePair key="vccadc_total_current" value="0.020000" description="" />
   <keyValuePair key="vccadc_voltage" value="1.800000" description="" />
   <keyValuePair key="vccaux_dynamic_current" value="0.023912" description="" />
   <keyValuePair key="vccaux_io_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vccaux_io_static_current" value="0.000000" description="" />
   <keyValuePair key="vccaux_io_total_current" value="0.000000" description="" />
   <keyValuePair key="vccaux_io_voltage" value="1.800000" description="" />
   <keyValuePair key="vccaux_static_current" value="0.054019" description="" />
   <keyValuePair key="vccaux_total_current" value="0.077931" description="" />
   <keyValuePair key="vccaux_voltage" value="1.800000" description="" />
   <keyValuePair key="vccbram_dynamic_current" value="0.000348" description="" />
   <keyValuePair key="vccbram_static_current" value="0.002011" description="" />
   <keyValuePair key="vccbram_total_current" value="0.002359" description="" />
   <keyValuePair key="vccbram_voltage" value="1.000000" description="" />
   <keyValuePair key="vccint_dynamic_current" value="0.513368" description="" />
   <keyValuePair key="vccint_static_current" value="0.060857" description="" />
   <keyValuePair key="vccint_total_current" value="0.574225" description="" />
   <keyValuePair key="vccint_voltage" value="1.000000" description="" />
   <keyValuePair key="vcco12_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vcco12_static_current" value="0.000000" description="" />
   <keyValuePair key="vcco12_total_current" value="0.000000" description="" />
   <keyValuePair key="vcco12_voltage" value="1.200000" description="" />
   <keyValuePair key="vcco135_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vcco135_static_current" value="0.000000" description="" />
   <keyValuePair key="vcco135_total_current" value="0.000000" description="" />
   <keyValuePair key="vcco135_voltage" value="1.350000" description="" />
   <keyValuePair key="vcco15_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vcco15_static_current" value="0.001000" description="" />
   <keyValuePair key="vcco15_total_current" value="0.001000" description="" />
   <keyValuePair key="vcco15_voltage" value="1.500000" description="" />
   <keyValuePair key="vcco18_dynamic_current" value="0.095032" description="" />
   <keyValuePair key="vcco18_static_current" value="0.001000" description="" />
   <keyValuePair key="vcco18_total_current" value="0.096032" description="" />
   <keyValuePair key="vcco18_voltage" value="1.800000" description="" />
   <keyValuePair key="vcco25_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vcco25_static_current" value="0.001000" description="" />
   <keyValuePair key="vcco25_total_current" value="0.001000" description="" />
   <keyValuePair key="vcco25_voltage" value="2.500000" description="" />
   <keyValuePair key="vcco33_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vcco33_static_current" value="0.001000" description="" />
   <keyValuePair key="vcco33_total_current" value="0.001000" description="" />
   <keyValuePair key="vcco33_voltage" value="3.300000" description="" />
   <keyValuePair key="vcco_ddr_dynamic_current" value="0.456904" description="" />
   <keyValuePair key="vcco_ddr_static_current" value="0.002000" description="" />
   <keyValuePair key="vcco_ddr_total_current" value="0.458904" description="" />
   <keyValuePair key="vcco_ddr_voltage" value="1.500000" description="" />
   <keyValuePair key="vcco_mio0_dynamic_current" value="0.001500" description="" />
   <keyValuePair key="vcco_mio0_static_current" value="0.001000" description="" />
   <keyValuePair key="vcco_mio0_total_current" value="0.002500" description="" />
   <keyValuePair key="vcco_mio0_voltage" value="3.300000" description="" />
   <keyValuePair key="vcco_mio1_dynamic_current" value="0.002187" description="" />
   <keyValuePair key="vcco_mio1_static_current" value="0.001000" description="" />
   <keyValuePair key="vcco_mio1_total_current" value="0.003187" description="" />
   <keyValuePair key="vcco_mio1_voltage" value="2.500000" description="" />
   <keyValuePair key="vccpaux_dynamic_current" value="0.051022" description="" />
   <keyValuePair key="vccpaux_static_current" value="0.010330" description="" />
   <keyValuePair key="vccpaux_total_current" value="0.061352" description="" />
   <keyValuePair key="vccpaux_voltage" value="1.800000" description="" />
   <keyValuePair key="vccpint_dynamic_current" value="0.730298" description="" />
   <keyValuePair key="vccpint_static_current" value="0.018540" description="" />
   <keyValuePair key="vccpint_total_current" value="0.748838" description="" />
   <keyValuePair key="vccpint_voltage" value="1.000000" description="" />
   <keyValuePair key="vccpll_dynamic_current" value="0.013878" description="" />
   <keyValuePair key="vccpll_static_current" value="0.003000" description="" />
   <keyValuePair key="vccpll_total_current" value="0.016878" description="" />
   <keyValuePair key="vccpll_voltage" value="1.800000" description="" />
   <keyValuePair key="version" value="2016.4" description="" />
  </section>
 </section>
 <section name="report_utilization" level="1" order="8" description="">
  <section name="clocking" level="2" order="1" description="">
   <keyValuePair key="bufgctrl_available" value="32" description="" />
   <keyValuePair key="bufgctrl_fixed" value="0" description="" />
   <keyValuePair key="bufgctrl_used" value="4" description="" />
   <keyValuePair key="bufgctrl_util_percentage" value="12.50" description="" />
   <keyValuePair key="bufhce_available" value="168" description="" />
   <keyValuePair key="bufhce_fixed" value="0" description="" />
   <keyValuePair key="bufhce_used" value="0" description="" />
   <keyValuePair key="bufhce_util_percentage" value="0.00" description="" />
   <keyValuePair key="bufio_available" value="32" description="" />
   <keyValuePair key="bufio_fixed" value="0" description="" />
   <keyValuePair key="bufio_used" value="0" description="" />
   <keyValuePair key="bufio_util_percentage" value="0.00" description="" />
   <keyValuePair key="bufmrce_available" value="16" description="" />
   <keyValuePair key="bufmrce_fixed" value="0" description="" />
   <keyValuePair key="bufmrce_used" value="0" description="" />
   <keyValuePair key="bufmrce_util_percentage" value="0.00" description="" />
   <keyValuePair key="bufr_available" value="32" description="" />
   <keyValuePair key="bufr_fixed" value="0" description="" />
   <keyValuePair key="bufr_used" value="2" description="" />
   <keyValuePair key="bufr_util_percentage" value="6.25" description="" />
   <keyValuePair key="mmcme2_adv_available" value="8" description="" />
   <keyValuePair key="mmcme2_adv_fixed" value="0" description="" />
   <keyValuePair key="mmcme2_adv_used" value="0" description="" />
   <keyValuePair key="mmcme2_adv_util_percentage" value="0.00" description="" />
   <keyValuePair key="plle2_adv_available" value="8" description="" />
   <keyValuePair key="plle2_adv_fixed" value="0" description="" />
   <keyValuePair key="plle2_adv_used" value="0" description="" />
   <keyValuePair key="plle2_adv_util_percentage" value="0.00" description="" />
  </section>
  <section name="dsp" level="2" order="2" description="">
   <keyValuePair key="dsp48e1_only_used" value="60" description="" />
   <keyValuePair key="dsps_available" value="900" description="" />
   <keyValuePair key="dsps_fixed" value="0" description="" />
   <keyValuePair key="dsps_used" value="60" description="" />
   <keyValuePair key="dsps_util_percentage" value="6.67" description="" />
  </section>
  <section name="io_standard" level="2" order="3" description="">
   <keyValuePair key="blvds_25" value="0" description="" />
   <keyValuePair key="diff_hstl_i" value="0" description="" />
   <keyValuePair key="diff_hstl_i_18" value="0" description="" />
   <keyValuePair key="diff_hstl_i_dci" value="0" description="" />
   <keyValuePair key="diff_hstl_i_dci_18" value="0" description="" />
   <keyValuePair key="diff_hstl_ii" value="0" description="" />
   <keyValuePair key="diff_hstl_ii_18" value="0" description="" />
   <keyValuePair key="diff_hstl_ii_dci" value="0" description="" />
   <keyValuePair key="diff_hstl_ii_dci_18" value="0" description="" />
   <keyValuePair key="diff_hstl_ii_t_dci" value="0" description="" />
   <keyValuePair key="diff_hstl_ii_t_dci_18" value="0" description="" />
   <keyValuePair key="diff_hsul_12" value="0" description="" />
   <keyValuePair key="diff_hsul_12_dci" value="0" description="" />
   <keyValuePair key="diff_mobile_ddr" value="0" description="" />
   <keyValuePair key="diff_sstl12" value="0" description="" />
   <keyValuePair key="diff_sstl12_dci" value="0" description="" />
   <keyValuePair key="diff_sstl12_t_dci" value="0" description="" />
   <keyValuePair key="diff_sstl135" value="0" description="" />
   <keyValuePair key="diff_sstl135_dci" value="0" description="" />
   <keyValuePair key="diff_sstl135_r" value="0" description="" />
   <keyValuePair key="diff_sstl135_t_dci" value="0" description="" />
   <keyValuePair key="diff_sstl15" value="1" description="" />
   <keyValuePair key="diff_sstl15_dci" value="0" description="" />
   <keyValuePair key="diff_sstl15_r" value="0" description="" />
   <keyValuePair key="diff_sstl15_t_dci" value="1" description="" />
   <keyValuePair key="diff_sstl18_i" value="0" description="" />
   <keyValuePair key="diff_sstl18_i_dci" value="0" description="" />
   <keyValuePair key="diff_sstl18_ii" value="0" description="" />
   <keyValuePair key="diff_sstl18_ii_dci" value="0" description="" />
   <keyValuePair key="diff_sstl18_ii_t_dci" value="0" description="" />
   <keyValuePair key="hslvdci_15" value="0" description="" />
   <keyValuePair key="hslvdci_18" value="0" description="" />
   <keyValuePair key="hstl_i" value="0" description="" />
   <keyValuePair key="hstl_i_12" value="0" description="" />
   <keyValuePair key="hstl_i_18" value="0" description="" />
   <keyValuePair key="hstl_i_dci" value="0" description="" />
   <keyValuePair key="hstl_i_dci_18" value="0" description="" />
   <keyValuePair key="hstl_ii" value="0" description="" />
   <keyValuePair key="hstl_ii_18" value="0" description="" />
   <keyValuePair key="hstl_ii_dci" value="0" description="" />
   <keyValuePair key="hstl_ii_dci_18" value="0" description="" />
   <keyValuePair key="hstl_ii_t_dci" value="0" description="" />
   <keyValuePair key="hstl_ii_t_dci_18" value="0" description="" />
   <keyValuePair key="hsul_12" value="0" description="" />
   <keyValuePair key="hsul_12_dci" value="0" description="" />
   <keyValuePair key="lvcmos12" value="0" description="" />
   <keyValuePair key="lvcmos15" value="0" description="" />
   <keyValuePair key="lvcmos18" value="1" description="" />
   <keyValuePair key="lvcmos25" value="1" description="" />
   <keyValuePair key="lvcmos33" value="1" description="" />
   <keyValuePair key="lvdci_15" value="0" description="" />
   <keyValuePair key="lvdci_18" value="0" description="" />
   <keyValuePair key="lvdci_dv2_15" value="0" description="" />
   <keyValuePair key="lvdci_dv2_18" value="0" description="" />
   <keyValuePair key="lvds" value="1" description="" />
   <keyValuePair key="lvds_25" value="0" description="" />
   <keyValuePair key="lvttl" value="0" description="" />
   <keyValuePair key="mini_lvds_25" value="0" description="" />
   <keyValuePair key="mobile_ddr" value="0" description="" />
   <keyValuePair key="pci33_3" value="0" description="" />
   <keyValuePair key="ppds_25" value="0" description="" />
   <keyValuePair key="rsds_25" value="0" description="" />
   <keyValuePair key="sstl12" value="0" description="" />
   <keyValuePair key="sstl12_dci" value="0" description="" />
   <keyValuePair key="sstl12_t_dci" value="0" description="" />
   <keyValuePair key="sstl135" value="0" description="" />
   <keyValuePair key="sstl135_dci" value="0" description="" />
   <keyValuePair key="sstl135_r" value="0" description="" />
   <keyValuePair key="sstl135_t_dci" value="0" description="" />
   <keyValuePair key="sstl15" value="1" description="" />
   <keyValuePair key="sstl15_dci" value="0" description="" />
   <keyValuePair key="sstl15_r" value="0" description="" />
   <keyValuePair key="sstl15_t_dci" value="1" description="" />
   <keyValuePair key="sstl18_i" value="0" description="" />
   <keyValuePair key="sstl18_i_dci" value="0" description="" />
   <keyValuePair key="sstl18_ii" value="0" description="" />
   <keyValuePair key="sstl18_ii_dci" value="0" description="" />
   <keyValuePair key="sstl18_ii_t_dci" value="0" description="" />
   <keyValuePair key="tmds_33" value="0" description="" />
  </section>
  <section name="memory" level="2" order="4" description="">
   <keyValuePair key="block_ram_tile_available" value="545" description="" />
   <keyValuePair key="block_ram_tile_fixed" value="0" description="" />
   <keyValuePair key="block_ram_tile_used" value="4" description="" />
   <keyValuePair key="block_ram_tile_util_percentage" value="0.73" description="" />
   <keyValuePair key="ramb18_available" value="1090" description="" />
   <keyValuePair key="ramb18_fixed" value="0" description="" />
   <keyValuePair key="ramb18_used" value="0" description="" />
   <keyValuePair key="ramb18_util_percentage" value="0.00" description="" />
   <keyValuePair key="ramb36_fifo_available" value="545" description="" />
   <keyValuePair key="ramb36_fifo_fixed" value="0" description="" />
   <keyValuePair key="ramb36_fifo_used" value="4" description="" />
   <keyValuePair key="ramb36_fifo_util_percentage" value="0.73" description="" />
   <keyValuePair key="ramb36e1_only_used" value="4" description="" />
  </section>
  <section name="primitives" level="2" order="5" description="">
   <keyValuePair key="bibuf_functional_category" value="IO" description="" />
   <keyValuePair key="bibuf_used" value="130" description="" />
   <keyValuePair key="bufg_functional_category" value="Clock" description="" />
   <keyValuePair key="bufg_used" value="3" description="" />
   <keyValuePair key="bufgctrl_functional_category" value="Clock" description="" />
   <keyValuePair key="bufgctrl_used" value="1" description="" />
   <keyValuePair key="bufr_functional_category" value="Clock" description="" />
   <keyValuePair key="bufr_used" value="2" description="" />
   <keyValuePair key="carry4_functional_category" value="CarryLogic" description="" />
   <keyValuePair key="carry4_used" value="777" description="" />
   <keyValuePair key="dsp48e1_functional_category" value="Block Arithmetic" description="" />
   <keyValuePair key="dsp48e1_used" value="60" description="" />
   <keyValuePair key="fdce_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdce_used" value="7042" description="" />
   <keyValuePair key="fdpe_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdpe_used" value="13" description="" />
   <keyValuePair key="fdre_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdre_used" value="14768" description="" />
   <keyValuePair key="fdse_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdse_used" value="326" description="" />
   <keyValuePair key="ibuf_functional_category" value="IO" description="" />
   <keyValuePair key="ibuf_used" value="16" description="" />
   <keyValuePair key="ibufds_functional_category" value="IO" description="" />
   <keyValuePair key="ibufds_used" value="8" description="" />
   <keyValuePair key="iddr_functional_category" value="IO" description="" />
   <keyValuePair key="iddr_used" value="7" description="" />
   <keyValuePair key="idelayctrl_functional_category" value="IO" description="" />
   <keyValuePair key="idelayctrl_used" value="1" description="" />
   <keyValuePair key="idelaye2_functional_category" value="IO" description="" />
   <keyValuePair key="idelaye2_used" value="7" description="" />
   <keyValuePair key="lut1_functional_category" value="LUT" description="" />
   <keyValuePair key="lut1_used" value="835" description="" />
   <keyValuePair key="lut2_functional_category" value="LUT" description="" />
   <keyValuePair key="lut2_used" value="2177" description="" />
   <keyValuePair key="lut3_functional_category" value="LUT" description="" />
   <keyValuePair key="lut3_used" value="2490" description="" />
   <keyValuePair key="lut4_functional_category" value="LUT" description="" />
   <keyValuePair key="lut4_used" value="1225" description="" />
   <keyValuePair key="lut5_functional_category" value="LUT" description="" />
   <keyValuePair key="lut5_used" value="1295" description="" />
   <keyValuePair key="lut6_functional_category" value="LUT" description="" />
   <keyValuePair key="lut6_used" value="3260" description="" />
   <keyValuePair key="muxf7_functional_category" value="MuxFx" description="" />
   <keyValuePair key="muxf7_used" value="297" description="" />
   <keyValuePair key="muxf8_functional_category" value="MuxFx" description="" />
   <keyValuePair key="muxf8_used" value="85" description="" />
   <keyValuePair key="obuf_functional_category" value="IO" description="" />
   <keyValuePair key="obuf_used" value="7" description="" />
   <keyValuePair key="obufds_functional_category" value="IO" description="" />
   <keyValuePair key="obufds_used" value="8" description="" />
   <keyValuePair key="obuft_functional_category" value="IO" description="" />
   <keyValuePair key="obuft_used" value="15" description="" />
   <keyValuePair key="oddr_functional_category" value="IO" description="" />
   <keyValuePair key="oddr_used" value="10" description="" />
   <keyValuePair key="ps7_functional_category" value="Specialized Resource" description="" />
   <keyValuePair key="ps7_used" value="1" description="" />
   <keyValuePair key="ramb36e1_functional_category" value="Block Memory" description="" />
   <keyValuePair key="ramb36e1_used" value="4" description="" />
   <keyValuePair key="srl16e_functional_category" value="Distributed Memory" description="" />
   <keyValuePair key="srl16e_used" value="135" description="" />
   <keyValuePair key="srlc32e_functional_category" value="Distributed Memory" description="" />
   <keyValuePair key="srlc32e_used" value="141" description="" />
  </section>
  <section name="slice_logic" level="2" order="6" description="">
   <keyValuePair key="f7_muxes_available" value="109300" description="" />
   <keyValuePair key="f7_muxes_fixed" value="0" description="" />
   <keyValuePair key="f7_muxes_used" value="297" description="" />
   <keyValuePair key="f7_muxes_util_percentage" value="0.27" description="" />
   <keyValuePair key="f8_muxes_available" value="54650" description="" />
   <keyValuePair key="f8_muxes_fixed" value="0" description="" />
   <keyValuePair key="f8_muxes_used" value="85" description="" />
   <keyValuePair key="f8_muxes_util_percentage" value="0.16" description="" />
   <keyValuePair key="fully_used_lut_ff_pairs_fixed" value="5.07" description="" />
   <keyValuePair key="fully_used_lut_ff_pairs_used" value="56" description="" />
   <keyValuePair key="lut_as_distributed_ram_fixed" value="0" description="" />
   <keyValuePair key="lut_as_distributed_ram_fixed" value="0" description="" />
   <keyValuePair key="lut_as_distributed_ram_used" value="0" description="" />
   <keyValuePair key="lut_as_distributed_ram_used" value="0" description="" />
   <keyValuePair key="lut_as_logic_available" value="218600" description="" />
   <keyValuePair key="lut_as_logic_available" value="218600" description="" />
   <keyValuePair key="lut_as_logic_fixed" value="0" description="" />
   <keyValuePair key="lut_as_logic_fixed" value="0" description="" />
   <keyValuePair key="lut_as_logic_used" value="10981" description="" />
   <keyValuePair key="lut_as_logic_used" value="10981" description="" />
   <keyValuePair key="lut_as_logic_util_percentage" value="5.02" description="" />
   <keyValuePair key="lut_as_logic_util_percentage" value="5.02" description="" />
   <keyValuePair key="lut_as_memory_available" value="70400" description="" />
   <keyValuePair key="lut_as_memory_available" value="70400" description="" />
   <keyValuePair key="lut_as_memory_fixed" value="0" description="" />
   <keyValuePair key="lut_as_memory_fixed" value="0" description="" />
   <keyValuePair key="lut_as_memory_used" value="220" description="" />
   <keyValuePair key="lut_as_memory_used" value="220" description="" />
   <keyValuePair key="lut_as_memory_util_percentage" value="0.31" description="" />
   <keyValuePair key="lut_as_memory_util_percentage" value="0.31" description="" />
   <keyValuePair key="lut_as_shift_register_fixed" value="0" description="" />
   <keyValuePair key="lut_as_shift_register_fixed" value="0" description="" />
   <keyValuePair key="lut_as_shift_register_used" value="220" description="" />
   <keyValuePair key="lut_as_shift_register_used" value="220" description="" />
   <keyValuePair key="lut_ff_pairs_with_one_unused_flip_flop_fixed" value="220" description="" />
   <keyValuePair key="lut_ff_pairs_with_one_unused_flip_flop_used" value="5222" description="" />
   <keyValuePair key="lut_ff_pairs_with_one_unused_lut_output_fixed" value="5222" description="" />
   <keyValuePair key="lut_ff_pairs_with_one_unused_lut_output_used" value="6628" description="" />
   <keyValuePair key="lut_flip_flop_pairs_available" value="218600" description="" />
   <keyValuePair key="lut_flip_flop_pairs_fixed" value="0" description="" />
   <keyValuePair key="lut_flip_flop_pairs_used" value="6948" description="" />
   <keyValuePair key="lut_flip_flop_pairs_util_percentage" value="3.18" description="" />
   <keyValuePair key="register_as_flip_flop_available" value="437200" description="" />
   <keyValuePair key="register_as_flip_flop_fixed" value="0" description="" />
   <keyValuePair key="register_as_flip_flop_used" value="22149" description="" />
   <keyValuePair key="register_as_flip_flop_util_percentage" value="5.07" description="" />
   <keyValuePair key="register_as_latch_available" value="437200" description="" />
   <keyValuePair key="register_as_latch_fixed" value="0" description="" />
   <keyValuePair key="register_as_latch_used" value="0" description="" />
   <keyValuePair key="register_as_latch_util_percentage" value="0.00" description="" />
   <keyValuePair key="slice_available" value="54650" description="" />
   <keyValuePair key="slice_fixed" value="0" description="" />
   <keyValuePair key="slice_luts_available" value="218600" description="" />
   <keyValuePair key="slice_luts_fixed" value="0" description="" />
   <keyValuePair key="slice_luts_used" value="11201" description="" />
   <keyValuePair key="slice_luts_util_percentage" value="5.12" description="" />
   <keyValuePair key="slice_registers_available" value="437200" description="" />
   <keyValuePair key="slice_registers_fixed" value="0" description="" />
   <keyValuePair key="slice_registers_used" value="22149" description="" />
   <keyValuePair key="slice_registers_util_percentage" value="5.07" description="" />
   <keyValuePair key="slice_used" value="6389" description="" />
   <keyValuePair key="slice_util_percentage" value="11.69" description="" />
   <keyValuePair key="slicel_fixed" value="0" description="" />
   <keyValuePair key="slicel_used" value="4154" description="" />
   <keyValuePair key="slicem_fixed" value="0" description="" />
   <keyValuePair key="slicem_used" value="2235" description="" />
   <keyValuePair key="unique_control_sets_used" value="627" description="" />
   <keyValuePair key="using_o5_and_o6_fixed" value="627" description="" />
   <keyValuePair key="using_o5_and_o6_used" value="56" description="" />
   <keyValuePair key="using_o5_output_only_fixed" value="56" description="" />
   <keyValuePair key="using_o5_output_only_used" value="8" description="" />
   <keyValuePair key="using_o6_output_only_fixed" value="8" description="" />
   <keyValuePair key="using_o6_output_only_used" value="156" description="" />
  </section>
  <section name="specific_feature" level="2" order="7" description="">
   <keyValuePair key="bscane2_available" value="4" description="" />
   <keyValuePair key="bscane2_fixed" value="0" description="" />
   <keyValuePair key="bscane2_used" value="0" description="" />
   <keyValuePair key="bscane2_util_percentage" value="0.00" description="" />
   <keyValuePair key="capturee2_available" value="1" description="" />
   <keyValuePair key="capturee2_fixed" value="0" description="" />
   <keyValuePair key="capturee2_used" value="0" description="" />
   <keyValuePair key="capturee2_util_percentage" value="0.00" description="" />
   <keyValuePair key="dna_port_available" value="1" description="" />
   <keyValuePair key="dna_port_fixed" value="0" description="" />
   <keyValuePair key="dna_port_used" value="0" description="" />
   <keyValuePair key="dna_port_util_percentage" value="0.00" description="" />
   <keyValuePair key="efuse_usr_available" value="1" description="" />
   <keyValuePair key="efuse_usr_fixed" value="0" description="" />
   <keyValuePair key="efuse_usr_used" value="0" description="" />
   <keyValuePair key="efuse_usr_util_percentage" value="0.00" description="" />
   <keyValuePair key="frame_ecce2_available" value="1" description="" />
   <keyValuePair key="frame_ecce2_fixed" value="0" description="" />
   <keyValuePair key="frame_ecce2_used" value="0" description="" />
   <keyValuePair key="frame_ecce2_util_percentage" value="0.00" description="" />
   <keyValuePair key="icape2_available" value="2" description="" />
   <keyValuePair key="icape2_fixed" value="0" description="" />
   <keyValuePair key="icape2_used" value="0" description="" />
   <keyValuePair key="icape2_util_percentage" value="0.00" description="" />
   <keyValuePair key="pcie_2_1_available" value="1" description="" />
   <keyValuePair key="pcie_2_1_fixed" value="0" description="" />
   <keyValuePair key="pcie_2_1_used" value="0" description="" />
   <keyValuePair key="pcie_2_1_util_percentage" value="0.00" description="" />
   <keyValuePair key="startupe2_available" value="1" description="" />
   <keyValuePair key="startupe2_fixed" value="0" description="" />
   <keyValuePair key="startupe2_used" value="0" description="" />
   <keyValuePair key="startupe2_util_percentage" value="0.00" description="" />
   <keyValuePair key="xadc_available" value="1" description="" />
   <keyValuePair key="xadc_fixed" value="0" description="" />
   <keyValuePair key="xadc_used" value="0" description="" />
   <keyValuePair key="xadc_util_percentage" value="0.00" description="" />
  </section>
 </section>
 <section name="router" level="1" order="9" description="">
  <section name="usage" level="2" order="1" description="">
   <keyValuePair key="actual_expansions" value="18681140" description="" />
   <keyValuePair key="bogomips" value="0" description="" />
   <keyValuePair key="bram18" value="0" description="" />
   <keyValuePair key="bram36" value="4" description="" />
   <keyValuePair key="bufg" value="0" description="" />
   <keyValuePair key="bufr" value="2" description="" />
   <keyValuePair key="congestion_level" value="0" description="" />
   <keyValuePair key="ctrls" value="627" description="" />
   <keyValuePair key="dsp" value="60" description="" />
   <keyValuePair key="effort" value="2" description="" />
   <keyValuePair key="estimated_expansions" value="24001722" description="" />
   <keyValuePair key="ff" value="22149" description="" />
   <keyValuePair key="global_clocks" value="4" description="" />
   <keyValuePair key="high_fanout_nets" value="12" description="" />
   <keyValuePair key="iob" value="55" description="" />
   <keyValuePair key="lut" value="11281" description="" />
   <keyValuePair key="movable_instances" value="36035" description="" />
   <keyValuePair key="nets" value="45013" description="" />
   <keyValuePair key="pins" value="214415" description="" />
   <keyValuePair key="pll" value="0" description="" />
   <keyValuePair key="router_runtime" value="2.000000" description="" />
   <keyValuePair key="router_timing_driven" value="1" description="" />
   <keyValuePair key="threads" value="8" description="" />
   <keyValuePair key="timing_constraints_exist" value="1" description="" />
  </section>
 </section>
 <section name="synthesis" level="1" order="10" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-assert" value="default::[not_specified]" description="" />
   <keyValuePair key="-bufg" value="default::12" description="" />
   <keyValuePair key="-cascade_dsp" value="default::auto" description="" />
   <keyValuePair key="-constrset" value="default::[not_specified]" description="" />
   <keyValuePair key="-control_set_opt_threshold" value="default::auto" description="" />
   <keyValuePair key="-directive" value="default::default" description="" />
   <keyValuePair key="-fanout_limit" value="400" description="" />
   <keyValuePair key="-flatten_hierarchy" value="default::rebuilt" description="" />
   <keyValuePair key="-fsm_extraction" value="one_hot" description="" />
   <keyValuePair key="-gated_clock_conversion" value="default::off" description="" />
   <keyValuePair key="-generic" value="default::[not_specified]" description="" />
   <keyValuePair key="-include_dirs" value="default::[not_specified]" description="" />
   <keyValuePair key="-keep_equivalent_registers" value="[specified]" description="" />
   <keyValuePair key="-max_bram" value="default::-1" description="" />
   <keyValuePair key="-max_bram_cascade_height" value="default::-1" description="" />
   <keyValuePair key="-max_dsp" value="default::-1" description="" />
   <keyValuePair key="-max_uram" value="default::-1" description="" />
   <keyValuePair key="-max_uram_cascade_height" value="default::-1" description="" />
   <keyValuePair key="-mode" value="default::default" description="" />
   <keyValuePair key="-name" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_lc" value="[specified]" description="" />
   <keyValuePair key="-no_srlextract" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_timing_driven" value="default::[not_specified]" description="" />
   <keyValuePair key="-part" value="xc7z045ffg900-2" description="" />
   <keyValuePair key="-resource_sharing" value="off" description="" />
   <keyValuePair key="-retiming" value="default::[not_specified]" description="" />
   <keyValuePair key="-rtl" value="default::[not_specified]" description="" />
   <keyValuePair key="-rtl_skip_constraints" value="default::[not_specified]" description="" />
   <keyValuePair key="-rtl_skip_ip" value="default::[not_specified]" description="" />
   <keyValuePair key="-seu_protect" value="default::none" description="" />
   <keyValuePair key="-shreg_min_size" value="5" description="" />
   <keyValuePair key="-top" value="system_top" description="" />
   <keyValuePair key="-verilog_define" value="default::[not_specified]" description="" />
  </section>
  <section name="usage" level="2" order="2" description="">
   <keyValuePair key="elapsed" value="00:04:19s" description="" />
   <keyValuePair key="hls_ip" value="0" description="" />
   <keyValuePair key="memory_gain" value="1103.555MB" description="" />
   <keyValuePair key="memory_peak" value="1413.684MB" description="" />
  </section>
 </section>
 <section name="unisim_transformation" level="1" order="11" description="">
  <section name="post_unisim_transformation" level="2" order="1" description="">
   <keyValuePair key="bibuf" value="130" description="" />
   <keyValuePair key="bufg" value="3" description="" />
   <keyValuePair key="bufgctrl" value="1" description="" />
   <keyValuePair key="bufr" value="2" description="" />
   <keyValuePair key="carry4" value="777" description="" />
   <keyValuePair key="dsp48e1" value="60" description="" />
   <keyValuePair key="fdce" value="7232" description="" />
   <keyValuePair key="fdpe" value="16" description="" />
   <keyValuePair key="fdre" value="18915" description="" />
   <keyValuePair key="fdse" value="328" description="" />
   <keyValuePair key="gnd" value="395" description="" />
   <keyValuePair key="ibuf" value="16" description="" />
   <keyValuePair key="ibufds" value="8" description="" />
   <keyValuePair key="iddr" value="7" description="" />
   <keyValuePair key="idelayctrl" value="1" description="" />
   <keyValuePair key="idelaye2" value="7" description="" />
   <keyValuePair key="lut1" value="1219" description="" />
   <keyValuePair key="lut2" value="2402" description="" />
   <keyValuePair key="lut3" value="2946" description="" />
   <keyValuePair key="lut4" value="1953" description="" />
   <keyValuePair key="lut5" value="2450" description="" />
   <keyValuePair key="lut6" value="6167" description="" />
   <keyValuePair key="muxf7" value="426" description="" />
   <keyValuePair key="muxf8" value="102" description="" />
   <keyValuePair key="obuf" value="7" description="" />
   <keyValuePair key="obufds" value="8" description="" />
   <keyValuePair key="obuft" value="15" description="" />
   <keyValuePair key="oddr" value="10" description="" />
   <keyValuePair key="ps7" value="1" description="" />
   <keyValuePair key="ramb36e1" value="4" description="" />
   <keyValuePair key="srl16e" value="135" description="" />
   <keyValuePair key="srlc32e" value="141" description="" />
   <keyValuePair key="vcc" value="455" description="" />
  </section>
  <section name="pre_unisim_transformation" level="2" order="2" description="">
   <keyValuePair key="bibuf" value="130" description="" />
   <keyValuePair key="bufg" value="3" description="" />
   <keyValuePair key="bufgctrl" value="1" description="" />
   <keyValuePair key="bufr" value="2" description="" />
   <keyValuePair key="carry4" value="777" description="" />
   <keyValuePair key="dsp48e1" value="60" description="" />
   <keyValuePair key="fdce" value="7232" description="" />
   <keyValuePair key="fdpe" value="16" description="" />
   <keyValuePair key="fdre" value="18915" description="" />
   <keyValuePair key="fdse" value="328" description="" />
   <keyValuePair key="gnd" value="395" description="" />
   <keyValuePair key="ibuf" value="1" description="" />
   <keyValuePair key="ibufds" value="8" description="" />
   <keyValuePair key="iddr" value="7" description="" />
   <keyValuePair key="idelayctrl" value="1" description="" />
   <keyValuePair key="idelaye2" value="7" description="" />
   <keyValuePair key="iobuf" value="15" description="" />
   <keyValuePair key="lut1" value="1219" description="" />
   <keyValuePair key="lut2" value="2402" description="" />
   <keyValuePair key="lut3" value="2946" description="" />
   <keyValuePair key="lut4" value="1953" description="" />
   <keyValuePair key="lut5" value="2450" description="" />
   <keyValuePair key="lut6" value="6167" description="" />
   <keyValuePair key="muxf7" value="426" description="" />
   <keyValuePair key="muxf8" value="102" description="" />
   <keyValuePair key="obuf" value="7" description="" />
   <keyValuePair key="obufds" value="8" description="" />
   <keyValuePair key="oddr" value="10" description="" />
   <keyValuePair key="ps7" value="1" description="" />
   <keyValuePair key="ramb36e1" value="4" description="" />
   <keyValuePair key="srl16e" value="135" description="" />
   <keyValuePair key="srlc32e" value="141" description="" />
   <keyValuePair key="vcc" value="455" description="" />
  </section>
 </section>
 <section name="vivado_usage" level="1" order="12" description="">
  <section name="java_command_handlers" level="2" order="1" description="">
   <keyValuePair key="createtophdl" value="4" description="" />
   <keyValuePair key="customizersbblock" value="10" description="" />
   <keyValuePair key="editdelete" value="39" description="" />
   <keyValuePair key="managecompositetargets" value="2" description="" />
   <keyValuePair key="newexporthardware" value="1" description="" />
   <keyValuePair key="newlaunchhardware" value="2" description="" />
   <keyValuePair key="openblockdesign" value="1" description="" />
   <keyValuePair key="projectsettingscmdhandler" value="1" description="" />
   <keyValuePair key="reportipstatus" value="2" description="" />
   <keyValuePair key="runbitgen" value="10" description="" />
   <keyValuePair key="runsynthesis" value="1" description="" />
   <keyValuePair key="savedesign" value="3" description="" />
   <keyValuePair key="saversbdesign" value="6" description="" />
   <keyValuePair key="showview" value="6" description="" />
   <keyValuePair key="timingconstraintswizard" value="1" description="" />
   <keyValuePair key="upgradeip" value="1" description="" />
   <keyValuePair key="validatersbdesign" value="7" description="" />
  </section>
  <section name="other_data" level="2" order="2" description="">
   <keyValuePair key="guimode" value="3" description="" />
   <keyValuePair key="tclmode" value="1" description="" />
  </section>
  <section name="project_data" level="2" order="3" description="">
   <keyValuePair key="constraintsetcount" value="1" description="" />
   <keyValuePair key="core_container" value="false" description="" />
   <keyValuePair key="currentimplrun" value="impl_2" description="" />
   <keyValuePair key="currentsynthesisrun" value="synth_2" description="" />
   <keyValuePair key="default_library" value="xil_defaultlib" description="" />
   <keyValuePair key="designmode" value="RTL" description="" />
   <keyValuePair key="export_simulation_activehdl" value="1" description="" />
   <keyValuePair key="export_simulation_ies" value="1" description="" />
   <keyValuePair key="export_simulation_modelsim" value="1" description="" />
   <keyValuePair key="export_simulation_questa" value="1" description="" />
   <keyValuePair key="export_simulation_riviera" value="1" description="" />
   <keyValuePair key="export_simulation_vcs" value="1" description="" />
   <keyValuePair key="export_simulation_xsim" value="1" description="" />
   <keyValuePair key="implstrategy" value="Vivado Implementation Defaults" description="" />
   <keyValuePair key="launch_simulation_activehdl" value="0" description="" />
   <keyValuePair key="launch_simulation_ies" value="0" description="" />
   <keyValuePair key="launch_simulation_modelsim" value="0" description="" />
   <keyValuePair key="launch_simulation_questa" value="0" description="" />
   <keyValuePair key="launch_simulation_riviera" value="0" description="" />
   <keyValuePair key="launch_simulation_vcs" value="0" description="" />
   <keyValuePair key="launch_simulation_xsim" value="0" description="" />
   <keyValuePair key="simulator_language" value="Mixed" description="" />
   <keyValuePair key="srcsetcount" value="6" description="" />
   <keyValuePair key="synthesisstrategy" value="Flow_PerfOptimized_high" description="" />
   <keyValuePair key="target_language" value="Verilog" description="" />
   <keyValuePair key="target_simulator" value="XSim" description="" />
   <keyValuePair key="totalimplruns" value="2" description="" />
   <keyValuePair key="totalsynthesisruns" value="2" description="" />
  </section>
 </section>
</section>
</webTalkData>
