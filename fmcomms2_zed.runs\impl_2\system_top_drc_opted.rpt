Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 13:21:48 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : report_drc -file system_top_drc_opted.rpt
| Design       : system_top
| Device       : xc7z045ffg900-2
| Speed File   : -2
| Design State : Synthesized
------------------------------------------------------------------------------------

Report DRC

Table of Contents
-----------------
1. REPORT SUMMARY
2. REPORT DETAILS

1. REPORT SUMMARY
-----------------
            Netlist: netlist
          Floorplan: design_1
      Design limits: <entire design considered>
           Ruledeck: default
             Max violations: <unlimited>
             Violations found: 81
+-----------+----------+-------------------------------------------------------------------+------------+
| Rule      | Severity | Description                                                       | Violations |
+-----------+----------+-------------------------------------------------------------------+------------+
| CHECK-3   | Warning  | Report rule limit reached                                         | 1          |
| DPOP-1    | Warning  | PREG Output pipelining                                            | 4          |
| REQP-1839 | Warning  | RAMB36 async control check                                        | 20         |
| AVAL-4    | Advisory | enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND | 56         |
+-----------+----------+-------------------------------------------------------------------+------------+

2. REPORT DETAILS
-----------------
CHECK-3#1 Warning
Report rule limit reached  
REQP-1839 rule limit reached: 20 violations have been found.
Related violations: <none>

DPOP-1#1 Warning
PREG Output pipelining  
DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
Related violations: <none>

DPOP-1#2 Warning
PREG Output pipelining  
DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
Related violations: <none>

DPOP-1#3 Warning
PREG Output pipelining  
DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
Related violations: <none>

DPOP-1#4 Warning
PREG Output pipelining  
DSP i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_dcfilter/i_dsp48e1 output i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_dcfilter/i_dsp48e1/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
Related violations: <none>

REQP-1839#1 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#2 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#3 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/data0) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_wready_int_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#4 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ADDRBWRADDR[10] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/din_waddr_reg[4][4]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_waddr_reg[4]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#5 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ADDRBWRADDR[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/din_waddr_reg[4][0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_waddr_reg[0]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#6 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ADDRBWRADDR[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/din_waddr_reg[4][1]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_waddr_reg[1]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#7 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ADDRBWRADDR[8] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/din_waddr_reg[4][2]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_waddr_reg[2]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#8 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ADDRBWRADDR[9] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/din_waddr_reg[4][3]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_waddr_reg[3]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#9 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg_ENARDEN_cooolgate_en_sig_2) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_control/d_data_cntrl_int_reg[14]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#10 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg_ENARDEN_cooolgate_en_sig_2) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_tdd/i_up_tdd_cntrl/i_xfer_tdd_control/d_data_cntrl_int_reg[9]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#11 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ENARDEN (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg_ENARDEN_cooolgate_en_sig_2) which is driven by a register (i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_xfer_cntrl/d_data_cntrl_int_reg[17]) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#12 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/ENBWREN (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#13 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[0] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#14 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[1] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#15 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[2] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#16 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[3] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#17 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[4] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#18 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[5] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#19 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[6] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

REQP-1839#20 Warning
RAMB36 async control check  
The RAMB36E1 i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg has an input control pin i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/m_ram_reg/WEBWE[7] (net: i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/i_mem/WEBWE[0]) which is driven by a register (i_system_wrapper/system_i/axi_ad9361_dac_fifo/inst/din_wr_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
Related violations: <none>

AVAL-4#1 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#2 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_0/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#3 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#4 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_1/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#5 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#6 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_2/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#7 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#8 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#9 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#10 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#11 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#12 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#13 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#14 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#15 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#16 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#17 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#18 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#19 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#20 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#21 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#22 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#23 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#24 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#25 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#26 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#27 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#28 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#29 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#30 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#31 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#32 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#33 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#34 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#35 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#36 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#37 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#38 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#39 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#40 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#41 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#42 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#43 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#44 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#45 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_ad_iqcor/i_mul_i/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#46 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_ad_iqcor/i_mul_q/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#47 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#48 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#49 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#50 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#51 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_0/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#52 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_scale/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#53 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#54 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#55 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_1/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>

AVAL-4#56 Advisory
enum_USE_DPORT_FALSE_enum_DREG_ADREG_0_connects_CED_CEAD_RSTD_GND  
i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_dds/i_dds_1_1/i_dds_sine/i_mul_s3_2/i_mult_macro/dsp_v5_1.DSP48_V5_1: DSP48E1 is not using the D port (USE_DPORT = FALSE). For improved power characteristics, set DREG and ADREG to '1', tie CED, CEAD, and RSTD to logic '0'.
Related violations: <none>


