<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>analog.com</spirit:vendor>
  <spirit:library>customized_ip</spirit:library>
  <spirit:name>system_util_ad9361_tdd_sync_0</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:model>
    <spirit:views>
      <spirit:view>
        <spirit:name>xilinx_anylanguagesynthesis</spirit:name>
        <spirit:displayName>Synthesis</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:synthesis</spirit:envIdentifier>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_anylanguagesynthesis_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Tue Mar 17 05:31:32 UTC 2020</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>boundaryCRC</spirit:name>
            <spirit:value>85a778c8</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>boundaryCRCversion</spirit:name>
            <spirit:value>1</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>customizationCRC</spirit:name>
            <spirit:value>b37bc800</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>customizationCRCversion</spirit:name>
            <spirit:value>6</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsynthesiswrapper</spirit:name>
        <spirit:displayName>Verilog Synthesis Wrapper</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:synthesis.wrapper</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesiswrapper_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Tue Mar 17 05:31:32 UTC 2020</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>boundaryCRC</spirit:name>
            <spirit:value>85a778c8</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>boundaryCRCversion</spirit:name>
            <spirit:value>1</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>customizationCRC</spirit:name>
            <spirit:value>b37bc800</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>customizationCRCversion</spirit:name>
            <spirit:value>6</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_anylanguagebehavioralsimulation</spirit:name>
        <spirit:displayName>Simulation</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:simulation</spirit:envIdentifier>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_anylanguagebehavioralsimulation_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Tue Mar 17 05:31:32 UTC 2020</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>boundaryCRC</spirit:name>
            <spirit:value>85a778c8</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>boundaryCRCversion</spirit:name>
            <spirit:value>1</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>customizationCRC</spirit:name>
            <spirit:value>9ec9f656</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>customizationCRCversion</spirit:name>
            <spirit:value>6</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsimulationwrapper</spirit:name>
        <spirit:displayName>Verilog Simulation Wrapper</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:simulation.wrapper</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsimulationwrapper_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Tue Mar 17 05:31:32 UTC 2020</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>boundaryCRC</spirit:name>
            <spirit:value>85a778c8</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>boundaryCRCversion</spirit:name>
            <spirit:value>1</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>customizationCRC</spirit:name>
            <spirit:value>9ec9f656</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>customizationCRCversion</spirit:name>
            <spirit:value>6</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
    </spirit:views>
    <spirit:ports>
      <spirit:port>
        <spirit:name>clk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_anylanguagesynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>rstn</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_anylanguagesynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>sync_mode</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_anylanguagesynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>sync_in</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_anylanguagesynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>sync_out</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>reg</spirit:typeName>
              <spirit:viewNameRef>xilinx_anylanguagesynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
    </spirit:ports>
    <spirit:modelParameters>
      <spirit:modelParameter xsi:type="spirit:nameValueTypeType" spirit:dataType="integer">
        <spirit:name>TDD_SYNC_PERIOD</spirit:name>
        <spirit:displayName>Tdd Sync Period</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.TDD_SYNC_PERIOD">10000000</spirit:value>
      </spirit:modelParameter>
    </spirit:modelParameters>
  </spirit:model>
  <spirit:fileSets>
    <spirit:fileSet>
      <spirit:name>xilinx_anylanguagesynthesis_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>../../ipshared/common/util_pulse_gen.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../ipshared/2902/util_tdd_sync.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>util_tdd_sync_constr.xdc</spirit:name>
        <spirit:userFileType>xdc</spirit:userFileType>
        <spirit:userFileType>SCOPED_TO_REF_util_tdd_sync</spirit:userFileType>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesiswrapper_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>synth/system_util_ad9361_tdd_sync_0.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_anylanguagebehavioralsimulation_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>../../ipshared/common/util_pulse_gen.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../ipshared/2902/util_tdd_sync.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsimulationwrapper_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>sim/system_util_ad9361_tdd_sync_0.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
  </spirit:fileSets>
  <spirit:description>util_tdd_sync_v1_0</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>TDD_SYNC_PERIOD</spirit:name>
      <spirit:displayName>Tdd Sync Period</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.TDD_SYNC_PERIOD">10000000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="1">system_util_ad9361_tdd_sync_0</spirit:value>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:displayName>util_tdd_sync_v1_0</xilinx:displayName>
      <xilinx:coreRevision>1</xilinx:coreRevision>
      <xilinx:configElementInfos>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.TDD_SYNC_PERIOD" xilinx:valueSource="user"/>
      </xilinx:configElementInfos>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2016.4</xilinx:xilinxVersion>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
