/*****************************************************************************
 * MODIFICATION HISTORY:
 *
 * Ver   Who  Date     Changes
 * ----- ---- -------- ---------------------------------------------------
  * 3.02a sdm  05/30/11 Added Xuint64 typedef and XUINT64_MSW/XUINT64_LSW macros
 * 3.02a sdm  06/27/11 Added INST_SYNC and DATA_SYNC macros for all the CPUs
 * 3.02a sdm  07/07/11 Updated ppc440 boot.S to set guarded bit for all but
 *                     cacheable regions
 *                     Update ppc440/xil_cache.c to use CACHEABLE_REGION_MASK
 *                     generated by the cpu driver, for enabling caches
 * 3.02a sdm  07/08/11 Updated microblaze cache flush APIs based on write-back/
 *                     write-thru caches
 * 3.03a sdm  08/20/11 Updated the tag/data RAM latency values for L2CC
 *		       Updated the MMU table to mark OCM in high address space
 *		       as inner cacheable and reserved space as Invalid
 * 3.03a sdm  08/20/11 Changes to support FreeRTOS
 *		       Updated the MMU table to mark upper half of the DDR as
 *		       non-cacheable
 *		       Setup supervisor and abort mode stacks
 *		       Do not initialize/enable L2CC in case of AMP
 *		       Initialize UART1 for 9600bps in case of AMP
 * 3.03a sdm  08/27/11 Setup abort and supervisor mode stacks and don't init SMC
 *		       in case of AMP
 * 3.03a sdm  09/14/11 Added code for performance monitor and L2CC event
 *		       counters
 * 3.03a sdm  11/08/11 Updated microblaze xil_cache.h file to include
 *		       xparameters.h file for CR630532 -  Xil_DCacheFlush()/
 *		       Xil_DCacheFlushRange() functions in standalone BSP v3_02a
 *		       for MicroBlaze will invalidate data in the cache instead
 *		       of flushing it for writeback caches
 * 3.04a sdm  11/21/11 Updated to initialize stdio device for 115200bps, for PS7
 * 3.04a sdm  01/02/12 Updated to clear cp15 regs with unknown reset values
 *		       Remove redundant dsb/dmb instructions in cache maintenance
 *		       APIs
 *		       Remove redundant dsb in mcr instruction
 * 3.04a sdm  01/13/12 Updated MMU table to mark DDR memory as Shareable
 * 3.05a sdm  02/02/12 Removed some of the defines as they are being generated through
 *                     driver tcl in xparameters.h. Update the gcc/translationtable.s
 *                     for the QSPI complete address range - DT644567
 *                     Removed profile directory for armcc compiler and changed
 *                     profiling setting to false in standalone_v2_1_0.tcl file
 *                     Deleting boot.S file after preprocessing for armcc compiler
 * 3.05a asa  03/11/12 Updated the function Xil_EnableMMU in file xil_mmu.c to
 *		       invalidate the caches before enabling back the MMU and
 *		       D cache.
 * 3.05a asa  04/15/12 Updated the function Xil_SetTlbAttributes in file
 *		       xil_mmu.c. Now we invalidate UTLB, Branch predictor
 *		       array, flush the D-cache before changing the attributes
 *		       in translation table. The user need not call Xil_DisableMMU
 *		       before calling Xil_SetTlbAttributes.
 * 3.06a asa/ 06/17/12 Removed the UART initialization for Zynq. For PEEP, the UART
 *	 sgd	       initialization is present. Changes for this were done in
 *		       uart.c and xil-crt0.s.
 *		       Made changes in xil_io.c to use volatile pointers.
 *		       Made changes in xil_mmu.c to correct the function
 *		       Xil_SetTlbAttributes.
 *		       Changes are made xil-crt0.s to initialize the static
 *		       C++ constructors.
 *		       Changes are made in boot.s, to fix the TTBR settings,
 *		       correct the L2 Cache Auxiliary register settings, L2 cache
 *		       latency settings.
 * 3.07a asa/ 07/16/12 Made changes in cortexa9/xtime_l.c, xtime_l.h, sleep.c
 *	 sgd	       usleep.c to use global timer intstead of CP15.
 *		       Made changes in cortexa9/gcc/translation_table.s to map
 *		       the peripheral devices as shareable device memory.
 *		       Made changes in cortexa9/gcc/xil-crt0.s to initialize
 *		       the global timer.
 *		       Made changes in cortexa9/armcc/boot.S to initialize
 *		       the global timer.
 *		       Made changes in cortexa9/armcc/translation_table.s to
 *		       map the peripheral devices as shareable device memory.
 *		       Made changes in cortexa9/gcc/boot.S to optimize the
 *		       L2 cache settings. Changes the section properties for
 *		       ".mmu_tbl" and ".boot" sections in cortexa9/gcc/boot.S
 *			and cortexa9/gcc/translation_table.S.
 *		       Made changes in cortexa9/xil_cache.c to change the
 *		       cache invalidation order.
 * 3.07a asa  08/17/12 Made changes across files for Cortexa9 to remove
 *		       compilation/linking issues for C++ compiler.
 *		       Made changes in mb_interface.h to remove compilation/
 *		       linking issues for C++ compiler.
 *		       Added macros for swapb and swaph microblaze instructions
 *		       mb_interface.h
 *		       Remove barrier usage (SYNCHRONIZE_IO) from xil_io.c
 *		       for CortexA9.
 * 3.07a asa  08/30/12 Updated for CR 675636 to provide the L2 Base Address
 * 3.07a asa  08/31/12 Added xil_printf.h include
 * 3.07a sgd  09/18/12 Corrected the L2 cache enable settings
 *				Corrected L2 cache sequence disable sequence
 * 3.07a sgd  10/19/12 SMC NOR and SRAM initialization with compiler option
 * 3.09a asa  01/25/13 Updated to push and pop neon registers into stack for
 *		       irq/fiq handling.
 *		       Relocated COUNTS_PER_SECOND from sleep.c to xtime_l.h. This
 *		       fixes the CR #692094.
 * 3.09a sgd  02/14/13 Fix for CRs 697094 (SI#687034) and 675552.
 * 3.10a srt  04/18/13 Implemented ARM Erratas.
 *		       Cortex A9 Errata - 742230, 743622, 775420, 794073
 *		       L2Cache PL310 Errata - 588369, 727915, 759370
 *		       Please refer to file 'xil_errata.h' for errata
 *		       description.
 * 3.10a asa  05/04/13 Added support for L2 cache in MicroBlaze BSP. The older
 *		       cache APIs were corresponding to only Layer 1 cache
 *		       memories. New APIs were now added and the existing cache
 *		       related APIs were changed to provide a uniform interface
 *		       to flush/invalidate/enable/disable the complete cache
 *		       system which includes both L1 and L2 caches. The changes
 *		       for these were done in:
 *		       src/microblaze/xil_cache.c and src/microblaze/xil_cache.h
 *		       files.
 *		       Four new files were added for supporting L2 cache. They are:
 *		       microblaze_flush_cache_ext.S-> Flushes L2 cache
 *		       microblaze_flush_cache_ext_range.S -> Flushes a range of
 *		       memory in L2 cache.
 *		       microblaze_invalidate_cache_ext.S-> Invalidates L2 cache
 *		       microblaze_invalidate_cache_ext_range -> Invalidates a
 *		       range of memory in L2 cache.
 *		       These changes are done to implement PR #697214.
 * 3.10a  asa 05/13/13 Modified cache disable APIs at src/cortexa9/xil_cache.c to
 *		       fix the CR #706464. L2 cache disabling happens independent
 *		       of L1 data cache disable operation. Changes are done in the
 *		       same file in cache handling APIs to do a L2 cache sync
 *		       (poll reg7_?cache_?sync). This fixes CR #700542.
 * 3.10a asa  05/20/13 Added API/Macros for enabling and disabling nested
 *		       interrupts for ARM. These are done to fix the CR#699680.
 * 3.10a srt  05/20/13 Made changes in cache maintenance APIs to do a proper cach
 *		       sync operation. This fixes the CR# 716781.
 * 3.11a asa  09/07/13 Updated armcc specific BSP files to have proper support
 *		       for armcc toolchain.
 *		       Modified asm_vectors.S (gcc) and asm_vectors.s (armcc) to
 *		       fix issues related to NEON context saving. The assembly
 *		       routines for IRQ and FIQ handling are modified.
 *		       Deprecated the older BSP (3.10a).
 * 3.11a asa  09/22/13 Fix for CR#732704. Cache APIs are modified to avoid
 *		       various potential issues. Made changes in the function
 *		       Xil_SetAttributes in file xil_mmu.c.
 * 3.11a asa  09/23/13 Added files xil_misc_psreset_api.c and xil_misc_psreset_api.h
 *		       in src\cortexa9 and src\microblaze folders.
 * 3.11a asa  09/28/13 Modified the cache APIs (src\cortexa9) to fix handling of
 *		       L2 cache sync operation and to fix issues around complete
 *		       L2 cache flush/invalidation by ways.
 * 3.12a asa  10/22/13 Modified the files xpseudo_asm_rvct.c and xpseudo_asm_rvct.h
 *		       to fix linking issues with armcc/DS-5. Modified the armcc
 *		       makefile to fix issues.
 * 3.12a asa  11/15/13 Fix for CR#754800. It fixes issues around profiling for MB.
 * 4.0   hk   12/13/13 Added check for STDOUT_BASEADDRESS where outbyte is used.
 * 4.0 	 pkp  22/01/14 Modified return addresses for interrupt handlers (DataAbortHandler
 *		       and SWIHandler) in asm_vector.S (src\cortexa9\gcc\ and
 *		       src\cortexa9\armcc\) to fix CR#767251
 * 4.0	 pkp  24/01/14 Modified cache APIs (Xil_DCacheInvalidateRange and
 *		       Xil_L1DCacheInvalidate) in xil_cache.c (src\cortexa9) to fix the bugs.
 *		       Few cache lines were missed to invalidate when unaligned address
 *		       invalidation was accommodated in Xil_DCacheInvalidateRange.
 *		       In Xil_L1DCacheInvalidate, while invalidating all L1D cache
 *		       stack memory (which contains return address) was invalidated. So
 *		       stack memory is flushed first and then L1D cache is invalidated.
 *		       This is done to fix CR #763829
 * 4.0 adk   22/02/2014 Fixed the CR:775379 removed unnecessay _t(unit32_t etc) from
 *			mblaze_nt_types.h file and replace uint32_t with u32 in the
 *			profile_hist.c to fix the above CR.
 * 4.1 bss   04/14/14  Updated driver tcl to remove _interrupt_handler.o from libgloss.a
 * 		       instead of libxil.a and added prototypes for
 *		       microblaze_invalidate_cache_ext and microblaze_flush_cache_ext in
 *		       mb_interface.h
 * 4.1 hk    04/18/14  Add sleep function.
 * 4.1 asa   04/21/14  Fix for CR#764881. Added support for msrset and msrclr. Renamed
 *		       some of the *.s files inMB BSP source to *.S.
 * 4.1 asa   04/28/14  Fix for CR#772280. Made changes in file cortexa9/gcc/read.c.
 * 4.1 bss   04/29/14  Modified driver tcl to use libxil.a if libgloss.a does not exist
 *			CR#794205
 * 4.1 asa   05/09/14  Fix for CR#798230. Made changes in cortexa9/xil_cache.c and
 *		       common/xil_testcache.c
 *	               Fix for CR#764881.
 * 4.1 srt   06/27/14  Remove '#undef DEBUG' from src/common/xdebug.h, which allows to
 *                     output the DEBUG logs when -DDEBUG flag is enabled in BSP.
 * 4.2 pkp   06/27/14  Added support for IAR compiler in src/cortexa9/iccarm.
 *		       Also added explanatory notes in cortexa9/xil_cache.c for CR#785243.
 * 4.2 pkp   06/19/14  Asynchronous abort has been enabled into cortexa9/gcc/boot.s and
 *		       cortexa9/armcc/boot.s. Added default exception handlers for data
 *		       abort and prefetch abort using handlers called
 *		       DataAbortHandler and PrefetchAbortHandler respectively in
 *		       cortexa9/xil_exception.c to fix CR#802862.
 * 4.2 pkp   06/30/14  MakeFile for cortexa9/armcc has been changed to fixes the
 *		       issue of improper linking of translation_table.s
 * 4.2 pkp   07/04/14  added weak attribute for the function in BSP which are also present
 *		       in tool chain to avoid conflicts into some special cases
 * 4.2 pkp   07/21/14  Corrected reset value of event counter in function
 *		       Xpm_ResetEventCounters in src/cortexa9/xpm_counter.c to fix CR#796275
 * 4.2 pkp   07/21/14  Included xil_types.h file in xil_mmu.h which had contained a function
 * 		       containing type def u32 defined in xil_types.g to resolve issue of
 *		       CR#805869
 * 4.2 pkp   08/04/14  Removed unimplemented nanosleep routine from cortexa9/usleep.c as
 *		       it is not possible to generate timer in nanosecond due to limited
 *		       cpu frequency
 * 4.2 pkp   08/04/14  Removed PEEP board related code which contained initialization of
 *		       uart, smc nor and sram from cortexa9/gcc/xil-crt0.s and armcc/boot.s
 *		       and iccarm/boot.s. Also uart.c and smc.c have been removed. Also
 *		       removed function definition of XSmc_NorInit and XSmc_NorInit from
 *		       cortexa9/smc.h
 * 4.2 bss   08/11/14  Added microblaze_flush_cache_ext_range and microblaze_invalidate_
 *		       cache_ext_range declarations in mb_interface.h CR#783821.
 *		       Modified profile_mcount_mb.S to fix CR#808412.
 * 4.2 pkp   08/21/14  modified makefile of iccarm for proper linking of objectfiles in
 *		       cortexa9/iccarm to fix CR#816701
 * 4.2 pkp   09/02/14  modified translation table entries in cortexa9/gcc/translation_table.s,
 *		       armcc/translation_table.s and iccarm/translation_table.s
 *		       to properly defined reserved entries according to address map for
 *		       fixing CR#820146
 * 4.2 pkp   09/11/14  modified translation table entries in cortexa9/iccarm/translation_table.s
 *		       and  cortexa9/armcc/translation_table.s to resolve compilation
 *		       error for solving CR#822897
 * 5.0 kvn   12/9/14   Support for Zync Ultrascale Mp.Also modified code for
 *                     MISRA-C:2012 compliance.
 * 5.0 pkp   12/15/14  Added APIs to get information about the platforms running the code by
 *		       adding src/common/xplatform_info.*s
 * 5.0 pkp   16/12/14  Modified boot code to enable scu after MMU is enabled and
 *		       removed incorrect initialization of TLB lockdown register to fix
 *		       CR#830580 in cortexa9/gcc/boot.S & cpu_init.S, armcc/boot.S
 *		       and iccarm/boot.s
 * 5.0 pkp   25/02/15  Modified floating point flag to vfpv3 from vfpv3_d16 in BSP MakeFile
 *		       for iccarm and armcc compiler of cortexA9
 * 5.1 pkp   05/13/15  Changed the initialization order in cortexa9/gcc/boot.S, iccarm/boot.s
 *		       and armcc/boot.s so to first invalidate caches and TLB, enable MMU and
 *		       caches, then enable SMP bit in ACTLR. L2Cache invalidation and enabling
 *		       of L2Cache is done later.
 * 5.1 pkp   12/05/15  Modified cortexa9/xil_cache.c to modify Xil_DCacheInvalidateRange and
 *		       Xil_DCacheFlushRange to remove unnecessary dsb which is unnecessarily
 *		       taking long time to fix CR#853097. L2CacheSync is added into
 *		       Xil_L2CacheInvalidateRange API. Xil_L1DCacheInvalidate and
 *		       Xil_L2CacheInvalidate APIs are modified to flush the complete stack
 *		       instead of just System Stack
 * 5.1 pkp   14/05/15  Modified cortexa9/gcc/Makefile to keep a correct check of a compiler
 *		       to update ECC_FLAGS and also take the compiler and archiver as specified
 *		       in settings instead of hardcoding it.
 * 5.2 pkp   06/08/15  Modified cortexa9/gcc/translation_table.S to put a check for
 *		       XPAR_PS7_DDR_0_S_AXI_BASEADDR to confirm if DDR is present or not and
 *		       accordingly generate the	translation table
 * 5.2 pkp   23/07/15  Modified cortexa9/gcc/Makefile to keep a correct check of a compiler
 *		       to update ECC_FLAGS to fix a bug introduced during new version creation
 *		       of BSP.
 * 5.3 pkp   10/07/15  Modified cortexa9/xil_cache.c file to change cache API so that L2 Cache
 *		       functionalities are avoided for the OpenAMP slave application(when
 *		       USE_AMP flag is defined for BSP) as master CPU would be utilizing L2
 *		       cache for its operation. Also file operations such as read, write,
 *		       close, open are also avoided for OpenAMP support(when USE_AMP flag is
 *		       defined for BSP) because XilOpenAMP library contains own file operation.
 *		       The xil-crt0.S file is modified for not initializing global timer for
 *		       OpenAMP application as it might be already in use by master CPU
 * 5.3 pkp   10/09/15  Modified cortexa9/iccarm/xpseudo_asm_iccarm.h file to change function
 *		       definition for dsb, isb and dmb to fix the compilation error when used
 *     kvn   16/10/15  Encapsulated assembly code into macros for R5 xil_cache file.
 * 5.4 pkp   09/11/15  Modified cortexr5/gcc/boot.S to disable ACTLR.DBWR bit to avoid potential
 *		       R5 deadlock for errata 780125
 * 5.4 pkp   09/11/15  Modified cortexa53/32bit/gcc/boot.S to enable I-Cache and D-Cache for a53
 *		       32 bit BSP in the initialization
 * 5.4 pkp   09/11/15  Modified cortexa9/xil_misc_psreset_api.c file to change the description
 *		       for XOcm_Remap function
 * 5.4 pkp   16/11/15  Modified microblaze/xil_misc_psreset_api.c file to change the description
 *		       for XOcm_Remap function
 *     kvn   21/11/15  Added volatile keyword for ADDR varibles in Xil_Out API
 *     kvn   21/11/15  Changed ADDR variable type from u32 to UINTPTR. This is
 *                     required for MISRA-C:2012 Compliance.
 * 5.4 pkp   23/11/15  Added attribute definitions for Xil_SetTlbAttributes API of Cortex-A9
 *		       in cortexa9/xil_mmu.h
 * 5.4 pkp   23/11/15  Added default undefined exception handler for Cortex-A9
 * 5.4 pkp   11/12/15  Modified common/xplatform_info.h to add #defines for silicon for
 *		       checking the current executing platform
 * 5.4 pkp   18/12/15  Modified cortexa53/32bit/gcc/xil-crt0.S and 64bit/gcc/xil-crt0.S
 *		       to initialize global constructor for C++ applications
 * 5.4 pkp   18/12/15  Modified cortexr5/gcc/xil-crt0.S to initialize global constructor for
 *		       C++ applications
 * 5.4 pkp   18/12/15  Modified cortexa53/32bit/gcc/translation_table.S and 64bit/gcc/
 *		       translation_table.S to update the translation table according to proper
 *		       address map
 * 5.4 pkp   18/12/15  Modified cortexar5/mpu.c to initialize the MPU according to proper
 *		       address map
 * 5.4	pkp  05/01/16  Modified cortexa53/64bit/boot.S to set the reset vector register RVBAR
 *		       equivalent to vector table base address
 * 5.4 pkp   08/01/16  Modified cortexa9/gcc/Makefile to update the extra compiler flag
 *		       as per the toolchain update
 * 5.4 pkp   12/01/16  Changed common/xplatform_info.* to add platform information support
 *		       for Cortex-A53 32bit mode
 * 5.4 pkp   28/01/16  Modified cortexa53/32bit/sleep.c and usleep.c & cortexa53/64bit/sleep.c
 *		       and usleep.c to correct routines to avoid hardcoding the timer frequency,
 *		       instead take it from xparameters.h to properly configure the timestamp
 *		       clock frequency
 * 5.4 asa   29/01/16  Modified microblaze/mb_interface.h to add macros that support the
 *		       new instructions for MB address extension feature
 * 5.4 kvn   30/01/16  Modified xparameters_ps.h file to add interrupt ID number for
 *		       system monitor.
 * 5.4 pkp   04/02/16  Modified cortexr5/gcc/boot.S to enable fault log for lock-step mode
 * 5.4 pkp   19/02/16  Modified cortexr5/xtime_l.c to add an API XTime_StartTimer and updated
 *		       cortexr5/xil-crt0.S to configure the TTC3 timer when present. Modified
 *		       cortexr5/sleep.c, cortexr5/usleep.c to use TTC3 when present otherwise
 *		       use set of assembly instructions to provide required delay to fix
 *		       CR#913249.
 * 5.4 asa   25/02/16  Made changes in xil-crt0.S for R5, A53 64 and 32 bit BSPs, to replace
 *		       _exit with exit. We should not be directly calling _exit and should
 *		       always use the library exit. This fixes the CR#937036.
 * 5.4 pkp   25/02/16  Made change to cortexr5/gcc/boot.S to initialize the floating point
 *		       registers, banked registers for various modes and enabled
 *		       the cache ECC check before enabling the fault log for lock step mode
 *		       Also modified the cortexr5/gcc/Makefile to support floating point
 *		       registers initialization in boot code.
 * 5.4 pkp   03/01/16  Updated the exit function in cortexr5/gcc/_exit.c to enable the debug
 *		       logic in case of lock-step mode when fault log is enabled to fix
 *		       CR#938281
 * 5.4 pkp   03/02/16  Modified cortexa9/iccarm/xpseudo_asm_iccarm.h file to include
 *		       header file instrinsics.h which contains assembly instructions
 *		       definitions which can be used by C
 * 5.4 asa   03/02/16  Added print.c in MB BSP. Made other cosmetic changes to have uniform
 *                     proto for all print.c across the BSPs. This patch fixes CR#938738.
 * 5.4 pkp   03/09/16  Modified cortexr5/sleep.c and usleep.c to avoid disabling the
 *		       interrupts when sleep/usleep is being executed using assembly
 *		       instructions to fix CR#913249.
 * 5.4 pkp   03/11/16  Modified cortexr5/xtime_l.c to avoid enabling overflow interrupt,
 *		       instead modified cortexr5/sleep.c and usleep.c to poll the counter
 *		       value and compare it with previous value to detect the overflow
 *		       to fix CR#940209.
 * 5.4 pkp   03/24/16  Modified cortexr5/boot.S to reset the dbg_lpd_reset before enabling
 *		       the fault log to avoid intervention for lock-step mode and cortexr5/
 *		       _exit.c to enable the dbg_lpd_reset once the fault log is disabled
 *		       to fix CR#947335
 * 5.5 pkp   04/11/16  Modified cortexr5/boot.S to enable comparators for non-JTAG bootmode
 *		       in lock-step to avoid resetting the debug logic which restricts the
 *		       access for debugger and removed enabling back of debug modules in
 *		       cortexr5/_exit.c
 * 5.5 pkp   04/13/16  Modified cortexa9/gcc/read.c to return correct number of bytes when
 *		       read buffer is filled and removed the redundant NULL checking for
 *		       buffer to simplify the code
 * 5.5 pkp   04/13/16  Modified cortexa53/64bit/gcc/read.c and cortexa53/32bit/gcc/read.c
 *		       to return correct number of bytes when read buffer is filled and
 *		       removed the redundant NULL checking for buffer to simplify the code
 * 5.5 pkp   04/13/16  Modified cortexr5/gcc/read.c to return correct number of bytes when
 *		       read buffer is filled and removed the redundant NULL checking for
 *		       buffer to simplify the code
 * 5.5 pkp   04/13/16  Modified cortexa53/64bit/xpseudo_asm_gcc.h to add volatile to asm
 *		       instruction macros to disable certain optimizations which may move
 *		       code out of loops if optimizers believe that the code will always
 *		       return the same result or discard asm statements if optimizers
 *		       determine there is no need for the output variables
 * 5.5 pkp   04/13/16  Modified cortexa53/64bit/xtime_l.c to add XTime_StartTimer which
 *		       starts the timer if it is disabled and modified XTime_GetTime to
 *		       enable the timer if it is not enabled. Also modified cortexa53/64bit/
 *		       sleep.c and cortexa53/64bit/usleep.c to enable the timer if it is
 *		       disabled and read the counter value directly from register instead
 *		       of using XTime_GetTime for optimization
 * 5.5 pkp   04/13/16  Modified cortexa53/32bit/xtime_l.c to add XTime_StartTimer which
 *		       starts the timer if it is disabled and modified XTime_GetTime to
 *		       enable the timer if it is not enabled. Also modified cortexa53/32bit/
 *		       sleep.c and cortexa53/32bit/usleep.c to enable the timer if it is
 *		       disabled and read the counter value directly from register instead
 *		       of using XTime_GetTime for optimization
 * 5.5 pkp   04/13/16  Modified cortexa53/32bit/xil_cache.c and cortexa53/64bit/xil_cache.c
 *		       to update the Xil_DCacheInvalidate, Xil_DCacheInvalidateLine and
 * 		       Xil_DCacheInvalidateRange functions description for proper
 *		       explaination to fix CR#949801
 * 5.5 asa   04/20/16  Added missing macros for hibernate and suspend in Microblaze BSP
 *                     file mb_interface.h. This fixes the CR#949503.
 * 5.5 asa   04/29/16  Fix for CR#951080. Updated cache APIs for HW designs where cache
 *                     memory is not included for MicroBlaze.
 * 5.5 pkp   05/06/16  Modified the cortexa9/xil_exception.h to update the macros
 *		       Xil_EnableNestedInterrupts and Xil_DisableNestedInterrupts for fixing
 *		       the issue of lr being corrupted to resolve CR#950468
 * 5.5 pkp   05/06/16  Modified the cortexr5/xil_exception.h to update the macros
 *		       Xil_EnableNestedInterrupts and Xil_DisableNestedInterrupts for fixing
 *		       the issue of lr being corrupted to resolve CR#950468
 * 6.0 kvn   05/31/16  Make Xil_AsserWait a global variable
 * 6.0 pkp   06/27/16  Updated cortexr5/mpu.c to move the code related to Init_MPU to .boot
 *		       section since it is part of boot process to fix CR#949555
 *     hk    07/12/16  Correct masks for IOU SLCR GEM registers
 * 6.0 pkp   07/25/16  Program the counter frequency in boot code for CortexA53
 * 6.0 asa   08/03/16  Updated sleep_common function in microblaze_sleep.c to improve the
 *                     the accuracy of MB sleep functionality. This fixes the CR#954191.
 * 6.0 mus   08/03/16  Restructured the BSP to avoid code duplication across all BSPs.
 *                     Source code directories specific to ARM processor's are moved to src/arm
 *                     directory(i.e. src/cortexa53,src/cortexa9 and src/cortexr5 moved to src/arm/cortexa53,
 *                     src/arm/cortexa9 and src/arm/cortexr5 respectively).Files xil_printf.c,xil_printf.h,
 *                     print.c,xil_io.c and xil_io.h are consolidated across all BSPs into common file each and
 *                     consolidated files are kept at src/common directory.Files putnum.c,vectors.c,vectors.h,
 *                     xil_exception.c and xil_exception.h are consolidated across all ARM BSPs
 *                     into common file each and consolidated files are kept at src/arm/common directory.
 *                     GCC source files related to file  operations are consolidated and kept
 *                     at src/arm/common/gcc directory.
 *                     All io interfacing functions (i.e. All variants of xil_out, xil_in )
 *                     are made as static inline and implementation is kept in consolidated common/xil_io.h,
 *                     xil_io.h must be included as a header file to access io interfacing functions.
 *                     Added undefined exception handler for A53 32 bit and R5 processor
 * 6.0 mus   08/11/16  Updated xtime_l.c in R5 BSP to remove implementation of XTime_SetTime API, since
 *                     TTC counter value register is read only.
 * 6.0 asa   08/15/16  Modified the signatures for functions sleep and usleep. This fixes
 *                     the CR#956899.
 * 6.0 mus   08/18/16  Defined ARMA53_32 flag in cortexa53/32bit/xparameters_ps.h and ARMR5 flag
 *                     in cortexr5/xparameters_ps.h
 * 6.0 mus   08/18/16  Added support for the the Zynq 7000s devices
 * 6.0 mus   08/18/16  Removed unused variables from xil_printf.c and xplatform_info.c
 * 6.0 mus   08/19/16  Modified xil_io.h to remove __LITTLE_ENDIAN__ flag check for all ARM processors
 * 6.1 mus   11/03/16  Added APIs handle_stdin_parameter and handle_stdout_parameter in standalone tcl.
 *                     ::hsi::utils::handle_stdin and ::hsi::utils::handle_stdout are taken as a base for
 *                     these APIs and modifications are done on top of it to handle stdout/stdin
 *                     parameters for design which doesnt have UART.It fixes CR#953681
 *****************************************************************************************/
