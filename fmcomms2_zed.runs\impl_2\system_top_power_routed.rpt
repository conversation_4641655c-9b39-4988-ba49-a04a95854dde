Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
----------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version     : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date             : Tue Oct 29 13:26:06 2019
| Host             : cduser1 running 64-bit major release  (build 9200)
| Command          : report_power -file system_top_power_routed.rpt -pb system_top_power_summary_routed.pb -rpx system_top_power_routed.rpx
| Design           : system_top
| Device           : xc7z045ffg900-2
| Design State     : routed
| Grade            : commercial
| Process          : typical
| Characterization : Production
----------------------------------------------------------------------------------------------------------------------------------------------------

Power Report

Table of Contents
-----------------
1. Summary
1.1 On-Chip Components
1.2 Power Supply Summary
1.3 Confidence Level
2. Settings
2.1 Environment
2.2 Clock Constraints
3. Detailed Reports
3.1 By Hierarchy

1. Summary
----------

+--------------------------+-------+
| Total On-Chip Power (W)  | 2.517 |
| Dynamic (W)              | 2.261 |
| Device Static (W)        | 0.257 |
| Total Off-Chip Power (W) | 0.010 |
| Effective TJA (C/W)      | 1.8   |
| Max Ambient (C)          | 80.5  |
| Junction Temperature (C) | 29.5  |
| Confidence Level         | Low   |
| Setting File             | ---   |
| Simulation Activity File | ---   |
| Design Nets Matched      | NA    |
+--------------------------+-------+


1.1 On-Chip Components
----------------------

+-------------------------+-----------+----------+-----------+-----------------+
| On-Chip                 | Power (W) | Used     | Available | Utilization (%) |
+-------------------------+-----------+----------+-----------+-----------------+
| Clocks                  |     0.164 |        7 |       --- |             --- |
| Slice Logic             |     0.071 |    35885 |       --- |             --- |
|   LUT as Logic          |     0.053 |    10981 |    218600 |            5.02 |
|   Register              |     0.012 |    22149 |    437200 |            5.07 |
|   CARRY4                |     0.005 |      777 |     54650 |            1.42 |
|   LUT as Shift Register |    <0.001 |      220 |     70400 |            0.31 |
|   F7/F8 Muxes           |    <0.001 |      382 |    218600 |            0.17 |
|   Others                |     0.000 |     1019 |       --- |             --- |
| Signals                 |     0.128 |    28828 |       --- |             --- |
| Block RAM               |     0.009 |        4 |       545 |            0.73 |
| DSPs                    |     0.131 |       60 |       900 |            6.67 |
| I/O                     |     0.214 |       55 |       362 |           15.19 |
| PS7                     |     1.543 |        1 |       --- |             --- |
| Static Power            |     0.257 |          |           |                 |
| Total                   |     2.517 |          |           |                 |
+-------------------------+-----------+----------+-----------+-----------------+


1.2 Power Supply Summary
------------------------

+-----------+-------------+-----------+-------------+------------+
| Source    | Voltage (V) | Total (A) | Dynamic (A) | Static (A) |
+-----------+-------------+-----------+-------------+------------+
| Vccint    |       1.000 |     0.574 |       0.513 |      0.061 |
| Vccaux    |       1.800 |     0.078 |       0.024 |      0.054 |
| Vcco33    |       3.300 |     0.001 |       0.000 |      0.001 |
| Vcco25    |       2.500 |     0.001 |       0.000 |      0.001 |
| Vcco18    |       1.800 |     0.096 |       0.095 |      0.001 |
| Vcco15    |       1.500 |     0.001 |       0.000 |      0.001 |
| Vcco135   |       1.350 |     0.000 |       0.000 |      0.000 |
| Vcco12    |       1.200 |     0.000 |       0.000 |      0.000 |
| Vccaux_io |       1.800 |     0.000 |       0.000 |      0.000 |
| Vccbram   |       1.000 |     0.002 |       0.000 |      0.002 |
| MGTAVcc   |       1.000 |     0.000 |       0.000 |      0.000 |
| MGTAVtt   |       1.200 |     0.000 |       0.000 |      0.000 |
| MGTVccaux |       1.800 |     0.000 |       0.000 |      0.000 |
| Vccpint   |       1.000 |     0.749 |       0.730 |      0.019 |
| Vccpaux   |       1.800 |     0.061 |       0.051 |      0.010 |
| Vccpll    |       1.800 |     0.017 |       0.014 |      0.003 |
| Vcco_ddr  |       1.500 |     0.459 |       0.457 |      0.002 |
| Vcco_mio0 |       3.300 |     0.003 |       0.002 |      0.001 |
| Vcco_mio1 |       2.500 |     0.003 |       0.002 |      0.001 |
| Vccadc    |       1.800 |     0.020 |       0.000 |      0.020 |
+-----------+-------------+-----------+-------------+------------+


1.3 Confidence Level
--------------------

+-----------------------------+------------+--------------------------------------------------------+------------------------------------------------------------------------------------------------------------+
| User Input Data             | Confidence | Details                                                | Action                                                                                                     |
+-----------------------------+------------+--------------------------------------------------------+------------------------------------------------------------------------------------------------------------+
| Design implementation state | High       | Design is routed                                       |                                                                                                            |
| Clock nodes activity        | High       | User specified more than 95% of clocks                 |                                                                                                            |
| I/O nodes activity          | Low        | More than 75% of inputs are missing user specification | Provide missing input activity with simulation results or by editing the "By Resource Type -> I/Os" view   |
| Internal nodes activity     | Medium     | User specified less than 25% of internal nodes         | Provide missing internal nodes activity with simulation results or by editing the "By Resource Type" views |
| Device models               | High       | Device models are Production                           |                                                                                                            |
|                             |            |                                                        |                                                                                                            |
| Overall confidence level    | Low        |                                                        |                                                                                                            |
+-----------------------------+------------+--------------------------------------------------------+------------------------------------------------------------------------------------------------------------+


2. Settings
-----------

2.1 Environment
---------------

+-----------------------+--------------------------+
| Ambient Temp (C)      | 25.0                     |
| ThetaJA (C/W)         | 1.8                      |
| Airflow (LFM)         | 250                      |
| Heat Sink             | medium (Medium Profile)  |
| ThetaSA (C/W)         | 3.3                      |
| Board Selection       | medium (10"x10")         |
| # of Board Layers     | 12to15 (12 to 15 Layers) |
| Board Temperature (C) | 25.0                     |
+-----------------------+--------------------------+


2.2 Clock Constraints
---------------------

+-----------------+-------------------------------------------------------------------+-----------------+
| Clock           | Domain                                                            | Constraint (ns) |
+-----------------+-------------------------------------------------------------------+-----------------+
| clk_div_sel_0_s | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_div_sel_0_s |            16.0 |
| clk_div_sel_1_s | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_div_sel_1_s |             8.0 |
| clk_fpga_0      | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]     |            10.0 |
| clk_fpga_1      | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1]     |             5.0 |
| rx_clk          | rx_clk_in_p                                                       |             4.0 |
+-----------------+-------------------------------------------------------------------+-----------------+


3. Detailed Reports
-------------------

3.1 By Hierarchy
----------------

+----------------------------------------------------------------+-----------+
| Name                                                           | Power (W) |
+----------------------------------------------------------------+-----------+
| system_top                                                     |     2.261 |
|   gpio_ctl_IOBUF[0]_inst                                       |    <0.001 |
|   gpio_ctl_IOBUF[1]_inst                                       |    <0.001 |
|   gpio_ctl_IOBUF[2]_inst                                       |    <0.001 |
|   gpio_ctl_IOBUF[3]_inst                                       |    <0.001 |
|   gpio_en_agc_IOBUF_inst                                       |    <0.001 |
|   gpio_resetb_IOBUF_inst                                       |    <0.001 |
|   gpio_status_IOBUF[0]_inst                                    |    <0.001 |
|   gpio_status_IOBUF[1]_inst                                    |    <0.001 |
|   gpio_status_IOBUF[2]_inst                                    |    <0.001 |
|   gpio_status_IOBUF[3]_inst                                    |    <0.001 |
|   gpio_status_IOBUF[4]_inst                                    |    <0.001 |
|   gpio_status_IOBUF[5]_inst                                    |    <0.001 |
|   gpio_status_IOBUF[6]_inst                                    |    <0.001 |
|   gpio_status_IOBUF[7]_inst                                    |    <0.001 |
|   gpio_sync_IOBUF_inst                                         |    <0.001 |
|   i_system_wrapper                                             |     2.249 |
|     system_i                                                   |     2.249 |
|       axi_ad9361                                               |     0.627 |
|         inst                                                   |     0.627 |
|           i_dev_if                                             |     0.213 |
|             g_rx_data[0].i_rx_data                             |     0.008 |
|             g_rx_data[1].i_rx_data                             |     0.008 |
|             g_rx_data[2].i_rx_data                             |     0.008 |
|             g_rx_data[3].i_rx_data                             |     0.008 |
|             g_rx_data[4].i_rx_data                             |     0.008 |
|             g_rx_data[5].i_rx_data                             |     0.008 |
|             g_tx_data[0].i_tx_data                             |     0.018 |
|             g_tx_data[1].i_tx_data                             |     0.018 |
|             g_tx_data[2].i_tx_data                             |     0.018 |
|             g_tx_data[3].i_tx_data                             |     0.018 |
|             g_tx_data[4].i_tx_data                             |     0.018 |
|             g_tx_data[5].i_tx_data                             |     0.018 |
|             i_clk                                              |     0.007 |
|             i_enable                                           |    <0.001 |
|             i_rx_frame                                         |     0.009 |
|             i_tx_clk                                           |     0.016 |
|             i_tx_frame                                         |     0.018 |
|             i_txnrx                                            |    <0.001 |
|           i_rx                                                 |     0.089 |
|             i_delay_cntrl                                      |     0.002 |
|               i_delay_rst_reg                                  |     0.001 |
|             i_rx_channel_0                                     |     0.021 |
|               i_ad_datafmt                                     |    <0.001 |
|               i_ad_dcfilter                                    |     0.008 |
|               i_ad_iqcor                                       |     0.008 |
|                 i_mul_i                                        |     0.003 |
|                   i_mult_macro                                 |     0.002 |
|                 i_mul_q                                        |     0.002 |
|                   i_mult_macro                                 |     0.002 |
|               i_rx_pnmon                                       |     0.002 |
|                 i_pnmon                                        |    <0.001 |
|               i_up_adc_channel                                 |     0.003 |
|                 i_xfer_cntrl                                   |     0.002 |
|                 i_xfer_status                                  |    <0.001 |
|             i_rx_channel_1                                     |     0.021 |
|               i_ad_datafmt                                     |    <0.001 |
|               i_ad_dcfilter                                    |     0.008 |
|               i_ad_iqcor                                       |     0.008 |
|                 i_mul_i                                        |     0.002 |
|                   i_mult_macro                                 |     0.002 |
|                 i_mul_q                                        |     0.003 |
|                   i_mult_macro                                 |     0.002 |
|               i_rx_pnmon                                       |     0.002 |
|                 i_pnmon                                        |    <0.001 |
|               i_up_adc_channel                                 |     0.003 |
|                 i_xfer_cntrl                                   |     0.002 |
|                 i_xfer_status                                  |    <0.001 |
|             i_rx_channel_2                                     |     0.021 |
|               i_ad_datafmt                                     |    <0.001 |
|               i_ad_dcfilter                                    |     0.008 |
|               i_ad_iqcor                                       |     0.008 |
|                 i_mul_i                                        |     0.003 |
|                   i_mult_macro                                 |     0.002 |
|                 i_mul_q                                        |     0.002 |
|                   i_mult_macro                                 |     0.002 |
|               i_rx_pnmon                                       |     0.002 |
|                 i_pnmon                                        |    <0.001 |
|               i_up_adc_channel                                 |     0.002 |
|                 i_xfer_cntrl                                   |     0.002 |
|                 i_xfer_status                                  |    <0.001 |
|             i_rx_channel_3                                     |     0.021 |
|               i_ad_datafmt                                     |    <0.001 |
|               i_ad_dcfilter                                    |     0.008 |
|               i_ad_iqcor                                       |     0.008 |
|                 i_mul_i                                        |     0.002 |
|                   i_mult_macro                                 |     0.002 |
|                 i_mul_q                                        |     0.003 |
|                   i_mult_macro                                 |     0.002 |
|               i_rx_pnmon                                       |     0.002 |
|                 i_pnmon                                        |    <0.001 |
|               i_up_adc_channel                                 |     0.003 |
|                 i_xfer_cntrl                                   |     0.002 |
|                 i_xfer_status                                  |    <0.001 |
|             i_up_adc_common                                    |     0.002 |
|               i_clock_mon                                      |    <0.001 |
|               i_core_rst_reg                                   |    <0.001 |
|               i_xfer_cntrl                                     |    <0.001 |
|               i_xfer_status                                    |    <0.001 |
|           i_tdd                                                |     0.036 |
|             i_tdd_control                                      |     0.022 |
|               i_rx_off_1_comp                                  |     0.001 |
|               i_rx_off_2_comp                                  |     0.001 |
|               i_rx_on_1_comp                                   |     0.001 |
|               i_rx_on_2_comp                                   |     0.001 |
|               i_tx_dp_off_1_comp                               |     0.001 |
|               i_tx_dp_off_2_comp                               |     0.001 |
|               i_tx_dp_on_1_comp                                |     0.001 |
|               i_tx_dp_on_2_comp                                |     0.001 |
|               i_tx_off_1_comp                                  |     0.001 |
|               i_tx_off_2_comp                                  |     0.001 |
|               i_tx_on_1_comp                                   |     0.001 |
|               i_tx_on_2_comp                                   |     0.001 |
|               i_vco_rx_off_1_comp                              |     0.001 |
|               i_vco_rx_off_2_comp                              |     0.001 |
|               i_vco_rx_on_1_comp                               |     0.001 |
|               i_vco_rx_on_2_comp                               |     0.001 |
|               i_vco_tx_off_1_comp                              |     0.001 |
|               i_vco_tx_off_2_comp                              |     0.001 |
|               i_vco_tx_on_1_comp                               |     0.001 |
|               i_vco_tx_on_2_comp                               |     0.001 |
|             i_up_tdd_cntrl                                     |     0.014 |
|               i_xfer_tdd_control                               |    <0.001 |
|               i_xfer_tdd_counter_values                        |     0.010 |
|               i_xfer_tdd_status                                |    <0.001 |
|           i_tdd_if                                             |    <0.001 |
|           i_tx                                                 |     0.283 |
|             i_tx_channel_0                                     |     0.069 |
|               i_ad_iqcor                                       |     0.005 |
|                 i_mul_i                                        |     0.002 |
|                   i_mult_macro                                 |     0.001 |
|                 i_mul_q                                        |     0.001 |
|                   i_mult_macro                                 |     0.001 |
|               i_dds                                            |     0.058 |
|                 i_dds_1_0                                      |     0.028 |
|                   i_dds_scale                                  |     0.003 |
|                     i_mult_macro                               |     0.003 |
|                   i_dds_sine                                   |     0.025 |
|                     i_mul_s1                                   |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s2                                   |     0.005 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_1                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_2                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                 i_dds_1_1                                      |     0.028 |
|                   i_dds_scale                                  |     0.003 |
|                     i_mult_macro                               |     0.003 |
|                   i_dds_sine                                   |     0.025 |
|                     i_mul_s1                                   |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s2                                   |     0.005 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_1                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_2                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|               i_up_dac_channel                                 |     0.004 |
|                 i_xfer_cntrl                                   |     0.003 |
|             i_tx_channel_1                                     |     0.071 |
|               i_ad_iqcor                                       |     0.004 |
|                 i_mul_i                                        |     0.001 |
|                   i_mult_macro                                 |     0.001 |
|                 i_mul_q                                        |     0.001 |
|                   i_mult_macro                                 |     0.001 |
|               i_dds                                            |     0.060 |
|                 i_dds_1_0                                      |     0.030 |
|                   i_dds_scale                                  |     0.003 |
|                     i_mult_macro                               |     0.003 |
|                   i_dds_sine                                   |     0.026 |
|                     i_mul_s1                                   |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s2                                   |     0.005 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_1                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_2                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                 i_dds_1_1                                      |     0.028 |
|                   i_dds_scale                                  |     0.003 |
|                     i_mult_macro                               |     0.003 |
|                   i_dds_sine                                   |     0.025 |
|                     i_mul_s1                                   |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s2                                   |     0.005 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_1                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_2                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|               i_up_dac_channel                                 |     0.004 |
|                 i_xfer_cntrl                                   |     0.003 |
|             i_tx_channel_2                                     |     0.068 |
|               i_ad_iqcor                                       |     0.004 |
|                 i_mul_i                                        |     0.001 |
|                   i_mult_macro                                 |    <0.001 |
|                 i_mul_q                                        |    <0.001 |
|                   i_mult_macro                                 |    <0.001 |
|               i_dds                                            |     0.058 |
|                 i_dds_1_0                                      |     0.029 |
|                   i_dds_scale                                  |     0.003 |
|                     i_mult_macro                               |     0.003 |
|                   i_dds_sine                                   |     0.025 |
|                     i_mul_s1                                   |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s2                                   |     0.005 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_1                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_2                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                 i_dds_1_1                                      |     0.028 |
|                   i_dds_scale                                  |     0.003 |
|                     i_mult_macro                               |     0.003 |
|                   i_dds_sine                                   |     0.025 |
|                     i_mul_s1                                   |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s2                                   |     0.005 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_1                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_2                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|               i_up_dac_channel                                 |     0.004 |
|                 i_xfer_cntrl                                   |     0.003 |
|             i_tx_channel_3                                     |     0.070 |
|               i_ad_iqcor                                       |     0.004 |
|                 i_mul_i                                        |    <0.001 |
|                   i_mult_macro                                 |    <0.001 |
|                 i_mul_q                                        |     0.001 |
|                   i_mult_macro                                 |    <0.001 |
|               i_dds                                            |     0.059 |
|                 i_dds_1_0                                      |     0.028 |
|                   i_dds_scale                                  |     0.003 |
|                     i_mult_macro                               |     0.003 |
|                   i_dds_sine                                   |     0.025 |
|                     i_mul_s1                                   |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s2                                   |     0.005 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_1                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_2                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                 i_dds_1_1                                      |     0.029 |
|                   i_dds_scale                                  |     0.003 |
|                     i_mult_macro                               |     0.003 |
|                   i_dds_sine                                   |     0.025 |
|                     i_mul_s1                                   |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s2                                   |     0.005 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_1                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|                     i_mul_s3_2                                 |     0.003 |
|                       i_mult_macro                             |     0.003 |
|               i_up_dac_channel                                 |     0.004 |
|                 i_xfer_cntrl                                   |     0.003 |
|             i_up_dac_common                                    |     0.003 |
|               i_clock_mon                                      |    <0.001 |
|               i_core_rst_reg                                   |    <0.001 |
|               i_xfer_cntrl                                     |    <0.001 |
|               i_xfer_status                                    |    <0.001 |
|           i_up_axi                                             |     0.005 |
|       axi_ad9361_adc_dma                                       |     0.005 |
|         inst                                                   |     0.005 |
|           i_request_arb                                        |     0.003 |
|             i_dest_dma_mm                                      |    <0.001 |
|               i_addr_gen                                       |    <0.001 |
|               i_data_mover                                     |    <0.001 |
|               i_req_splitter                                   |    <0.001 |
|               i_response_handler                               |    <0.001 |
|             i_dest_req_fifo                                    |    <0.001 |
|             i_dest_response_fifo                               |    <0.001 |
|             i_fifo                                             |     0.002 |
|               i_address_gray                                   |    <0.001 |
|                 i_raddr_sync                                   |    <0.001 |
|                 i_waddr_sync                                   |    <0.001 |
|             i_req_gen                                          |    <0.001 |
|             i_req_splitter                                     |    <0.001 |
|             i_src_dma_fifo                                     |    <0.001 |
|               i_data_mover                                     |    <0.001 |
|             i_src_req_fifo                                     |    <0.001 |
|               i_raddr_sync                                     |    <0.001 |
|               i_waddr_sync                                     |    <0.001 |
|             i_sync_control_src                                 |    <0.001 |
|             i_sync_dest_request_id                             |    <0.001 |
|             i_sync_src_request_id                              |    <0.001 |
|             i_sync_status_src                                  |    <0.001 |
|           i_up_axi                                             |     0.001 |
|       axi_ad9361_dac_dma                                       |     0.005 |
|         inst                                                   |     0.005 |
|           i_request_arb                                        |     0.004 |
|             i_dest_dma_fifo                                    |    <0.001 |
|               i_data_mover                                     |    <0.001 |
|               i_response_generator                             |    <0.001 |
|             i_dest_req_fifo                                    |    <0.001 |
|               i_raddr_sync                                     |    <0.001 |
|               i_waddr_sync                                     |    <0.001 |
|             i_dest_response_fifo                               |    <0.001 |
|               i_raddr_sync                                     |    <0.001 |
|               i_waddr_sync                                     |    <0.001 |
|             i_dest_slice                                       |    <0.001 |
|             i_dest_slice2                                      |    <0.001 |
|             i_fifo                                             |     0.001 |
|               i_address_gray                                   |    <0.001 |
|                 i_raddr_sync                                   |    <0.001 |
|                 i_waddr_sync                                   |    <0.001 |
|             i_req_gen                                          |    <0.001 |
|             i_req_splitter                                     |    <0.001 |
|             i_src_dma_mm                                       |    <0.001 |
|               i_addr_gen                                       |    <0.001 |
|               i_data_mover                                     |    <0.001 |
|               i_req_splitter                                   |    <0.001 |
|             i_src_req_fifo                                     |    <0.001 |
|             i_sync_control_dest                                |    <0.001 |
|             i_sync_dest_request_id                             |    <0.001 |
|             i_sync_req_response_id                             |    <0.001 |
|             i_sync_status_dest                                 |    <0.001 |
|           i_up_axi                                             |     0.001 |
|       axi_ad9361_dac_fifo                                      |     0.005 |
|         inst                                                   |     0.005 |
|           i_mem                                                |     0.003 |
|       axi_cpu_interconnect                                     |     0.022 |
|         m07_couplers                                           |     0.005 |
|           auto_pc                                              |     0.005 |
|             inst                                               |     0.005 |
|               gen_axilite.gen_b2s_conv.axilite_b2s             |     0.005 |
|                 RD.ar_channel_0                                |    <0.001 |
|                   ar_cmd_fsm_0                                 |    <0.001 |
|                   cmd_translator_0                             |    <0.001 |
|                     incr_cmd_0                                 |    <0.001 |
|                     wrap_cmd_0                                 |    <0.001 |
|                 RD.r_channel_0                                 |    <0.001 |
|                   rd_data_fifo_0                               |    <0.001 |
|                   transaction_fifo_0                           |    <0.001 |
|                 SI_REG                                         |     0.002 |
|                   ar_pipe                                      |    <0.001 |
|                   aw_pipe                                      |    <0.001 |
|                   b_pipe                                       |    <0.001 |
|                   r_pipe                                       |    <0.001 |
|                 WR.aw_channel_0                                |    <0.001 |
|                   aw_cmd_fsm_0                                 |    <0.001 |
|                   cmd_translator_0                             |    <0.001 |
|                     incr_cmd_0                                 |    <0.001 |
|                     wrap_cmd_0                                 |    <0.001 |
|                 WR.b_channel_0                                 |    <0.001 |
|                   bid_fifo_0                                   |    <0.001 |
|                   bresp_fifo_0                                 |    <0.001 |
|         m08_couplers                                           |     0.005 |
|           auto_pc                                              |     0.005 |
|             inst                                               |     0.005 |
|               gen_axilite.gen_b2s_conv.axilite_b2s             |     0.005 |
|                 RD.ar_channel_0                                |    <0.001 |
|                   ar_cmd_fsm_0                                 |    <0.001 |
|                   cmd_translator_0                             |    <0.001 |
|                     incr_cmd_0                                 |    <0.001 |
|                     wrap_cmd_0                                 |    <0.001 |
|                 RD.r_channel_0                                 |    <0.001 |
|                   rd_data_fifo_0                               |    <0.001 |
|                   transaction_fifo_0                           |    <0.001 |
|                 SI_REG                                         |     0.002 |
|                   ar_pipe                                      |    <0.001 |
|                   aw_pipe                                      |    <0.001 |
|                   b_pipe                                       |    <0.001 |
|                   r_pipe                                       |    <0.001 |
|                 WR.aw_channel_0                                |    <0.001 |
|                   aw_cmd_fsm_0                                 |    <0.001 |
|                   cmd_translator_0                             |    <0.001 |
|                     incr_cmd_0                                 |    <0.001 |
|                     wrap_cmd_0                                 |    <0.001 |
|                 WR.b_channel_0                                 |    <0.001 |
|                   bid_fifo_0                                   |    <0.001 |
|                   bresp_fifo_0                                 |    <0.001 |
|         m09_couplers                                           |     0.005 |
|           auto_pc                                              |     0.005 |
|             inst                                               |     0.005 |
|               gen_axilite.gen_b2s_conv.axilite_b2s             |     0.005 |
|                 RD.ar_channel_0                                |    <0.001 |
|                   ar_cmd_fsm_0                                 |    <0.001 |
|                   cmd_translator_0                             |    <0.001 |
|                     incr_cmd_0                                 |    <0.001 |
|                     wrap_cmd_0                                 |    <0.001 |
|                 RD.r_channel_0                                 |    <0.001 |
|                   rd_data_fifo_0                               |    <0.001 |
|                   transaction_fifo_0                           |    <0.001 |
|                 SI_REG                                         |     0.002 |
|                   ar_pipe                                      |    <0.001 |
|                   aw_pipe                                      |    <0.001 |
|                   b_pipe                                       |    <0.001 |
|                   r_pipe                                       |    <0.001 |
|                 WR.aw_channel_0                                |    <0.001 |
|                   aw_cmd_fsm_0                                 |    <0.001 |
|                   cmd_translator_0                             |    <0.001 |
|                     incr_cmd_0                                 |    <0.001 |
|                     wrap_cmd_0                                 |    <0.001 |
|                 WR.b_channel_0                                 |    <0.001 |
|                   bid_fifo_0                                   |    <0.001 |
|                   bresp_fifo_0                                 |    <0.001 |
|         s00_couplers                                           |     0.000 |
|           auto_pc                                              |     0.000 |
|         xbar                                                   |     0.008 |
|           inst                                                 |     0.008 |
|             gen_samd.crossbar_samd                             |     0.008 |
|               addr_arbiter_ar                                  |    <0.001 |
|               addr_arbiter_aw                                  |    <0.001 |
|               gen_decerr_slave.decerr_slave_inst               |    <0.001 |
|               gen_master_slots[0].reg_slice_mi                 |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_master_slots[10].reg_slice_mi                |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_master_slots[1].reg_slice_mi                 |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_master_slots[2].reg_slice_mi                 |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_master_slots[3].reg_slice_mi                 |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_master_slots[4].reg_slice_mi                 |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_master_slots[5].reg_slice_mi                 |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_master_slots[6].reg_slice_mi                 |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_master_slots[7].reg_slice_mi                 |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_master_slots[8].reg_slice_mi                 |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_master_slots[9].reg_slice_mi                 |    <0.001 |
|                 b_pipe                                         |    <0.001 |
|                 r_pipe                                         |    <0.001 |
|               gen_slave_slots[0].gen_si_read.si_transactor_ar  |     0.002 |
|                 gen_multi_thread.arbiter_resp_inst             |    <0.001 |
|                 gen_multi_thread.mux_resp_multi_thread         |    <0.001 |
|               gen_slave_slots[0].gen_si_write.si_transactor_aw |     0.002 |
|                 gen_multi_thread.arbiter_resp_inst             |    <0.001 |
|                 gen_multi_thread.mux_resp_multi_thread         |    <0.001 |
|               gen_slave_slots[0].gen_si_write.splitter_aw_si   |    <0.001 |
|               gen_slave_slots[0].gen_si_write.wdata_router_w   |    <0.001 |
|                 wrouter_aw_fifo                                |    <0.001 |
|                   gen_srls[0].gen_rep[0].srl_nx1               |    <0.001 |
|                   gen_srls[0].gen_rep[1].srl_nx1               |    <0.001 |
|                   gen_srls[0].gen_rep[2].srl_nx1               |    <0.001 |
|                   gen_srls[0].gen_rep[3].srl_nx1               |    <0.001 |
|                   gen_srls[0].gen_rep[4].srl_nx1               |    <0.001 |
|               splitter_aw_mi                                   |    <0.001 |
|       axi_hp0_interconnect                                     |     0.000 |
|       axi_hp1_interconnect                                     |     0.000 |
|       axi_hp2_interconnect                                     |     0.000 |
|       sys_concat_intc                                          |     0.000 |
|       sys_ps7                                                  |     1.547 |
|         inst                                                   |     1.547 |
|       sys_rstgen                                               |    <0.001 |
|         U0                                                     |    <0.001 |
|           EXT_LPF                                              |    <0.001 |
|             ACTIVE_LOW_EXT.ACT_LO_EXT                          |    <0.001 |
|           SEQ                                                  |    <0.001 |
|             SEQ_COUNTER                                        |    <0.001 |
|       util_ad9361_adc_fifo                                     |     0.006 |
|         inst                                                   |     0.006 |
|           i_mem                                                |     0.004 |
|       util_ad9361_adc_pack                                     |     0.022 |
|         inst                                                   |     0.022 |
|           g_dsf[0].i_dsf                                       |     0.007 |
|           g_dsf[1].i_dsf                                       |     0.003 |
|           g_dsf[2].i_dsf                                       |     0.005 |
|           g_dsf[3].i_dsf                                       |    <0.001 |
|           g_mux[0].i_mux                                       |     0.004 |
|       util_ad9361_dac_upack                                    |     0.008 |
|         inst                                                   |     0.008 |
|           g_dmx[0].i_dmx                                       |     0.004 |
|           g_dsf[0].i_dsf                                       |     0.001 |
|           g_dsf[1].i_dsf                                       |     0.001 |
|           g_dsf[2].i_dsf                                       |     0.001 |
|           g_dsf[3].i_dsf                                       |    <0.001 |
|       util_ad9361_divclk                                       |    <0.001 |
|         inst                                                   |    <0.001 |
|       util_ad9361_divclk_reset                                 |    <0.001 |
|         U0                                                     |    <0.001 |
|           EXT_LPF                                              |    <0.001 |
|             ACTIVE_LOW_EXT.ACT_LO_EXT                          |    <0.001 |
|           SEQ                                                  |    <0.001 |
|             SEQ_COUNTER                                        |    <0.001 |
|       util_ad9361_divclk_sel                                   |    <0.001 |
|       util_ad9361_divclk_sel_concat                            |     0.000 |
|       util_ad9361_tdd_sync                                     |    <0.001 |
|         inst                                                   |    <0.001 |
|           i_tdd_sync                                           |    <0.001 |
+----------------------------------------------------------------+-----------+


