#-----------------------------------------------------------
# Vivado v2016.4 (64-bit)
# SW Build 1756540 on Mon Jan 23 19:11:23 MST 2017
# IP Build 1755317 on Mon Jan 23 20:30:07 MST 2017
# Start of session at: Thu Apr 30 10:28:06 2020
# Process ID: 1072
# Current directory: E:/wgfile/R75_Z7035_0320
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent5724 E:\wgfile\R75_Z7035_0320\fmcomms2_zed.xpr
# Log file: E:/wgfile/R75_Z7035_0320/vivado.log
# Journal file: E:/wgfile/R75_Z7035_0320\vivado.jou
#-----------------------------------------------------------
start_gui
open_project E:/wgfile/R75_Z7035_0320/fmcomms2_zed.xpr
open_hw
connect_hw_server
open_hw_target
set_property PROGRAM.FILE {E:/wgfile/R75_Z7035_0320/fmcomms2_zed.runs/impl_3/system_top.bit} [lindex [get_hw_devices xc7z035_1] 0]
set_property PROBES.FILE {E:\FPGA\xiaoE\fmcomms2_zed.runs\impl_3\debug_nets.ltx} [lindex [get_hw_devices xc7z035_1] 0]
current_hw_device [lindex [get_hw_devices xc7z035_1] 0]
refresh_hw_device -update_hw_probes false [lindex [get_hw_devices xc7z035_1] 0]
set_property PROBES.FILE {E:/wgfile/R75_Z7035_0320/fmcomms2_zed.runs/impl_3/debug_nets.ltx} [lindex [get_hw_devices xc7z035_1] 0]
refresh_hw_device [lindex [get_hw_devices xc7z035_1] 0]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
refresh_hw_device [lindex [get_hw_devices xc7z035_1] 0]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
refresh_hw_device [lindex [get_hw_devices xc7z035_1] 0]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
