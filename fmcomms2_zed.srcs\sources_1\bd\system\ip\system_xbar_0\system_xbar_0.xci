<?xml version="1.0" encoding="UTF-8"?>
<spirit:design xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>xci</spirit:library>
  <spirit:name>unknown</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:componentInstances>
    <spirit:componentInstance>
      <spirit:instanceName>system_xbar_0</spirit:instanceName>
      <spirit:componentRef spirit:vendor="xilinx.com" spirit:library="ip" spirit:name="axi_crossbar" spirit:version="2.1"/>
      <spirit:configurableElementValues>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLKIF.ASSOCIATED_BUSIF">M00_AXI:M01_AXI:M02_AXI:M03_AXI:M04_AXI:M05_AXI:M06_AXI:M07_AXI:M08_AXI:M09_AXI:M10_AXI:M11_AXI:M12_AXI:M13_AXI:M14_AXI:M15_AXI:S00_AXI:S01_AXI:S02_AXI:S03_AXI:S04_AXI:S05_AXI:S06_AXI:S07_AXI:S08_AXI:S09_AXI:S10_AXI:S11_AXI:S12_AXI:S13_AXI:S14_AXI:S15_AXI</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLKIF.ASSOCIATED_RESET">ARESETN</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLKIF.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLKIF.FREQ_HZ">10000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLKIF.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLKIF.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M00_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M01_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M02_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M03_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M04_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M05_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M06_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M07_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M08_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M09_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M10_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M11_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M12_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M13_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M14_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M15_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RSTIF.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RSTIF.POLARITY">ACTIVE_LOW</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RSTIF.TYPE">INTERCONNECT</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.NUM_READ_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.NUM_WRITE_OUTSTANDING">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S00_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S01_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S02_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S03_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S04_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S05_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S06_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S07_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S08_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S09_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S10_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S11_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S12_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S13_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S14_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.HAS_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.HAS_CACHE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.HAS_LOCK">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.HAS_PROT">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.HAS_QOS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.HAS_REGION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.MAX_BURST_LENGTH">256</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.PHASE">0.000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.SUPPORTS_NARROW_BURST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S15_AXI.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_AXI_ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_AXI_ARUSER_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_AXI_AWUSER_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_AXI_BUSER_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_AXI_DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_AXI_ID_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_AXI_PROTOCOL">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_AXI_RUSER_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_AXI_SUPPORTS_USER_SIGNALS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_AXI_WUSER_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_CONNECTIVITY_MODE">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_FAMILY">zynq</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_M_AXI_ADDR_WIDTH">0x00000000000000000000000000000000000000000000000000000000000000000000000000000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_M_AXI_BASE_ADDR">0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_M_AXI_READ_CONNECTIVITY">0x00000001000000010000000100000001000000010000000100000001000000010000000100000001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_M_AXI_READ_ISSUING">0x00000004000000040000000400000004000000040000000400000004000000040000000400000004</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_M_AXI_SECURE">0x00000000000000000000000000000000000000000000000000000000000000000000000000000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_M_AXI_WRITE_CONNECTIVITY">0x00000001000000010000000100000001000000010000000100000001000000010000000100000001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_M_AXI_WRITE_ISSUING">0x00000004000000040000000400000004000000040000000400000004000000040000000400000004</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_NUM_ADDR_RANGES">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_NUM_MASTER_SLOTS">10</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_NUM_SLAVE_SLOTS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_R_REGISTER">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_S_AXI_ARB_PRIORITY">0x00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_S_AXI_BASE_ID">0x00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_S_AXI_READ_ACCEPTANCE">0x00000002</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_S_AXI_SINGLE_THREAD">0x00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_S_AXI_THREAD_ID_WIDTH">0x00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_S_AXI_WRITE_ACCEPTANCE">0x00000002</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.ADDR_RANGES">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.CONNECTIVITY_MODE">SAMD</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Component_Name">system_xbar_0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M00_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M01_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M02_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M03_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M04_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M05_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M06_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M07_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M08_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M09_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M10_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M11_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M12_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M13_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M14_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A00_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A00_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A01_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A01_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A02_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A02_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A03_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A03_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A04_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A04_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A05_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A05_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A06_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A06_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A07_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A07_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A08_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A08_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A09_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A09_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A10_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A10_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A11_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A11_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A12_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A12_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A13_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A13_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A14_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A14_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A15_ADDR_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_A15_BASE_ADDR">0xffffffffffffffff</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_ERR_MODE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_READ_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S00_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S00_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S01_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S01_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S02_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S02_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S03_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S03_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S04_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S04_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S05_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S05_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S06_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S06_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S07_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S07_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S08_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S08_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S09_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S09_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S10_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S10_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S11_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S11_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S12_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S12_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S13_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S13_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S14_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S14_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S15_READ_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_S15_WRITE_CONNECTIVITY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_SECURE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.M15_WRITE_ISSUING">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.NUM_MI">10</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.NUM_SI">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.PROTOCOL">AXI4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.R_REGISTER">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S00_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S00_BASE_ID">0x00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S00_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S00_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S00_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S00_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S01_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S01_BASE_ID">0x00000001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S01_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S01_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S01_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S01_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S02_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S02_BASE_ID">0x00000002</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S02_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S02_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S02_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S02_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S03_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S03_BASE_ID">0x00000003</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S03_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S03_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S03_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S03_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S04_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S04_BASE_ID">0x00000004</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S04_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S04_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S04_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S04_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S05_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S05_BASE_ID">0x00000005</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S05_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S05_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S05_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S05_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S06_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S06_BASE_ID">0x00000006</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S06_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S06_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S06_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S06_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S07_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S07_BASE_ID">0x00000007</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S07_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S07_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S07_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S07_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S08_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S08_BASE_ID">0x00000008</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S08_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S08_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S08_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S08_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S09_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S09_BASE_ID">0x00000009</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S09_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S09_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S09_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S09_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S10_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S10_BASE_ID">0x0000000a</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S10_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S10_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S10_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S10_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S11_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S11_BASE_ID">0x0000000b</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S11_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S11_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S11_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S11_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S12_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S12_BASE_ID">0x0000000c</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S12_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S12_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S12_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S12_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S13_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S13_BASE_ID">0x0000000d</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S13_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S13_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S13_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S13_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S14_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S14_BASE_ID">0x0000000e</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S14_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S14_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S14_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S14_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S15_ARB_PRIORITY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S15_BASE_ID">0x0000000f</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S15_READ_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S15_SINGLE_THREAD">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S15_THREAD_ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.S15_WRITE_ACCEPTANCE">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.STRATEGY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.ARCHITECTURE">zynq</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.BASE_BOARD_PART"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.BOARD_CONNECTIONS"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.DEVICE">xc7z035</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.PACKAGE">ffg676</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.PREFHDL">VERILOG</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SILICON_REVISION"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SIMULATOR_LANGUAGE">MIXED</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SPEEDGRADE">-2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.TEMPERATURE_GRADE"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.USE_RDI_CUSTOMIZATION">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.USE_RDI_GENERATION">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.IPCONTEXT">IP_Integrator</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.IPREVISION">19</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.MANAGED">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.OUTPUTDIR">.</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SELECTEDSIMMODEL"/>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SHAREDDIR">../../ipshared</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SWVERSION">2018.3</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SYNTHESISFLOW">OUT_OF_CONTEXT</spirit:configurableElementValue>
      </spirit:configurableElementValues>
      <spirit:vendorExtensions>
        <xilinx:componentInstanceExtensions>
          <xilinx:configElementInfos>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.CLKIF.ASSOCIATED_BUSIF" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.CLKIF.ASSOCIATED_RESET" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.CLKIF.CLK_DOMAIN" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.CLKIF.FREQ_HZ" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.CLKIF.PHASE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_REGION" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M00_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_REGION" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M01_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_REGION" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M02_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_REGION" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M03_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_REGION" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M04_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_REGION" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M05_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_REGION" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M06_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_REGION" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M07_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_REGION" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M08_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_REGION" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M09_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.RSTIF.POLARITY" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.ADDR_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.ARUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.AWUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.BUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.DATA_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_BRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_CACHE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_LOCK" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_PROT" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_QOS" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_REGION" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_RRESP" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.HAS_WSTRB" xilinx:valueSource="constant" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.ID_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.MAX_BURST_LENGTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.NUM_READ_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.NUM_WRITE_OUTSTANDING" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.PROTOCOL" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.READ_WRITE_MODE" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.RUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.SUPPORTS_NARROW_BURST" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_AXI.WUSER_WIDTH" xilinx:valuePermission="bd"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M00_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M00_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M01_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M01_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M02_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M02_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M03_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M03_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M04_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M04_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M05_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M05_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M06_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M06_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M07_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M07_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M08_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M08_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M09_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M09_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M10_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M10_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M11_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M11_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M12_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M12_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M13_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M13_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M14_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M14_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M15_A00_ADDR_WIDTH" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M15_A00_BASE_ADDR" xilinx:valuePermission="bd_and_user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_MI" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_SI" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.STRATEGY" xilinx:valueSource="user"/>
          </xilinx:configElementInfos>
        </xilinx:componentInstanceExtensions>
      </spirit:vendorExtensions>
    </spirit:componentInstance>
  </spirit:componentInstances>
</spirit:design>
