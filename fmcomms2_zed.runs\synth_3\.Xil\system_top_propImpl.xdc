set_property SRC_FILE_INFO {cfile:d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xdc rfile:../../../fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xdc id:1 order:EARLY scoped_inst:i_system_wrapper/system_i/sys_ps7/inst} [current_design]
set_property SRC_FILE_INFO {cfile:D:/FPGA/xiaoE/fmcomms2_zed.srcs/constrs_1/new/cdc.xdc rfile:../../../fmcomms2_zed.srcs/constrs_1/new/cdc.xdc id:2} [current_design]
set_property SRC_FILE_INFO {cfile:D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc rfile:../../../fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc id:3} [current_design]
set_property SRC_FILE_INFO {cfile:d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc rfile:../../../fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc id:4 order:LATE scoped_inst:i_system_wrapper/system_i/util_ad9361_divclk/inst} [current_design]
set_property SRC_FILE_INFO {cfile:d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc rfile:../../../fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc id:5 order:LATE scoped_inst:i_system_wrapper/system_i/axi_ad9361_adc_dma/inst} [current_design]
set_property SRC_FILE_INFO {cfile:d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc rfile:../../../fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc id:6 order:LATE scoped_inst:i_system_wrapper/system_i/axi_ad9361_dac_dma/inst} [current_design]
set_property src_info {type:SCOPED_XDC file:1 line:21 export:INPUT save:INPUT read:READ} [current_design]
set_input_jitter clk_fpga_1 0.15
set_property src_info {type:SCOPED_XDC file:1 line:24 export:INPUT save:INPUT read:READ} [current_design]
set_input_jitter clk_fpga_0 0.3
set_property src_info {type:SCOPED_XDC file:1 line:34 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A19" [get_ports "MIO[53]"]
set_property src_info {type:SCOPED_XDC file:1 line:40 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A20" [get_ports "MIO[52]"]
set_property src_info {type:SCOPED_XDC file:1 line:46 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B20" [get_ports "MIO[51]"]
set_property src_info {type:SCOPED_XDC file:1 line:52 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B22" [get_ports "MIO[50]"]
set_property src_info {type:SCOPED_XDC file:1 line:58 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A18" [get_ports "MIO[49]"]
set_property src_info {type:SCOPED_XDC file:1 line:64 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B21" [get_ports "MIO[48]"]
set_property src_info {type:SCOPED_XDC file:1 line:70 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B19" [get_ports "MIO[47]"]
set_property src_info {type:SCOPED_XDC file:1 line:76 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E17" [get_ports "MIO[46]"]
set_property src_info {type:SCOPED_XDC file:1 line:82 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C18" [get_ports "MIO[45]"]
set_property src_info {type:SCOPED_XDC file:1 line:88 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E18" [get_ports "MIO[44]"]
set_property src_info {type:SCOPED_XDC file:1 line:94 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D18" [get_ports "MIO[43]"]
set_property src_info {type:SCOPED_XDC file:1 line:100 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F17" [get_ports "MIO[42]"]
set_property src_info {type:SCOPED_XDC file:1 line:106 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C19" [get_ports "MIO[41]"]
set_property src_info {type:SCOPED_XDC file:1 line:112 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C22" [get_ports "MIO[40]"]
set_property src_info {type:SCOPED_XDC file:1 line:118 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C21" [get_ports "MIO[39]"]
set_property src_info {type:SCOPED_XDC file:1 line:124 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D21" [get_ports "MIO[38]"]
set_property src_info {type:SCOPED_XDC file:1 line:130 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D20" [get_ports "MIO[37]"]
set_property src_info {type:SCOPED_XDC file:1 line:136 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K16" [get_ports "MIO[36]"]
set_property src_info {type:SCOPED_XDC file:1 line:142 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D19" [get_ports "MIO[35]"]
set_property src_info {type:SCOPED_XDC file:1 line:148 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J16" [get_ports "MIO[34]"]
set_property src_info {type:SCOPED_XDC file:1 line:154 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E22" [get_ports "MIO[33]"]
set_property src_info {type:SCOPED_XDC file:1 line:160 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K17" [get_ports "MIO[32]"]
set_property src_info {type:SCOPED_XDC file:1 line:166 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E21" [get_ports "MIO[31]"]
set_property src_info {type:SCOPED_XDC file:1 line:172 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K19" [get_ports "MIO[30]"]
set_property src_info {type:SCOPED_XDC file:1 line:178 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E20" [get_ports "MIO[29]"]
set_property src_info {type:SCOPED_XDC file:1 line:184 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J18" [get_ports "MIO[28]"]
set_property src_info {type:SCOPED_XDC file:1 line:190 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F18" [get_ports "MIO[27]"]
set_property src_info {type:SCOPED_XDC file:1 line:196 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H17" [get_ports "MIO[26]"]
set_property src_info {type:SCOPED_XDC file:1 line:202 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F19" [get_ports "MIO[25]"]
set_property src_info {type:SCOPED_XDC file:1 line:208 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J19" [get_ports "MIO[24]"]
set_property src_info {type:SCOPED_XDC file:1 line:214 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F20" [get_ports "MIO[23]"]
set_property src_info {type:SCOPED_XDC file:1 line:220 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G22" [get_ports "MIO[22]"]
set_property src_info {type:SCOPED_XDC file:1 line:226 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F22" [get_ports "MIO[21]"]
set_property src_info {type:SCOPED_XDC file:1 line:232 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H19" [get_ports "MIO[20]"]
set_property src_info {type:SCOPED_XDC file:1 line:238 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G19" [get_ports "MIO[19]"]
set_property src_info {type:SCOPED_XDC file:1 line:244 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G20" [get_ports "MIO[18]"]
set_property src_info {type:SCOPED_XDC file:1 line:250 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G17" [get_ports "MIO[17]"]
set_property src_info {type:SCOPED_XDC file:1 line:256 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G21" [get_ports "MIO[16]"]
set_property src_info {type:SCOPED_XDC file:1 line:262 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C24" [get_ports "MIO[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:268 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D23" [get_ports "MIO[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:274 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B25" [get_ports "MIO[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:280 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A23" [get_ports "MIO[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:286 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B26" [get_ports "MIO[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:292 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A25" [get_ports "MIO[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:298 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D24" [get_ports "MIO[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:304 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A24" [get_ports "MIO[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:310 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E23" [get_ports "MIO[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:316 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F23" [get_ports "MIO[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:322 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C26" [get_ports "MIO[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:328 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F24" [get_ports "MIO[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:334 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D25" [get_ports "MIO[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:340 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E25" [get_ports "MIO[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:346 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D26" [get_ports "MIO[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:352 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E26" [get_ports "MIO[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:357 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "W21" [get_ports "DDR_VRP"]
set_property src_info {type:SCOPED_XDC file:1 line:361 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "V21" [get_ports "DDR_VRN"]
set_property src_info {type:SCOPED_XDC file:1 line:365 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "V22" [get_ports "DDR_WEB"]
set_property src_info {type:SCOPED_XDC file:1 line:369 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "V23" [get_ports "DDR_RAS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:373 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "Y22" [get_ports "DDR_ODT"]
set_property src_info {type:SCOPED_XDC file:1 line:377 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H22" [get_ports "DDR_DRSTB"]
set_property src_info {type:SCOPED_XDC file:1 line:381 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "W24" [get_ports "DDR_DQS[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:385 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "P25" [get_ports "DDR_DQS[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:389 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "L24" [get_ports "DDR_DQS[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:393 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H24" [get_ports "DDR_DQS[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:397 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "W25" [get_ports "DDR_DQS_n[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:401 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "R25" [get_ports "DDR_DQS_n[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:405 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "L25" [get_ports "DDR_DQS_n[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:409 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G25" [get_ports "DDR_DQS_n[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:413 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "L23" [get_ports "DDR_DQ[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:417 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K26" [get_ports "DDR_DQ[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:421 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J23" [get_ports "DDR_DQ[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:425 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J24" [get_ports "DDR_DQ[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:429 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H23" [get_ports "DDR_DQ[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:433 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H26" [get_ports "DDR_DQ[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:437 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G26" [get_ports "DDR_DQ[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:441 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "W23" [get_ports "DDR_DQ[31]"]
set_property src_info {type:SCOPED_XDC file:1 line:445 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "Y26" [get_ports "DDR_DQ[30]"]
set_property src_info {type:SCOPED_XDC file:1 line:449 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J25" [get_ports "DDR_DQ[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:453 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "Y25" [get_ports "DDR_DQ[29]"]
set_property src_info {type:SCOPED_XDC file:1 line:457 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "W26" [get_ports "DDR_DQ[28]"]
set_property src_info {type:SCOPED_XDC file:1 line:461 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "U25" [get_ports "DDR_DQ[27]"]
set_property src_info {type:SCOPED_XDC file:1 line:465 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "U24" [get_ports "DDR_DQ[26]"]
set_property src_info {type:SCOPED_XDC file:1 line:469 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "U26" [get_ports "DDR_DQ[25]"]
set_property src_info {type:SCOPED_XDC file:1 line:473 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "V24" [get_ports "DDR_DQ[24]"]
set_property src_info {type:SCOPED_XDC file:1 line:477 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "R23" [get_ports "DDR_DQ[23]"]
set_property src_info {type:SCOPED_XDC file:1 line:481 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "T23" [get_ports "DDR_DQ[22]"]
set_property src_info {type:SCOPED_XDC file:1 line:485 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "T25" [get_ports "DDR_DQ[21]"]
set_property src_info {type:SCOPED_XDC file:1 line:489 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "T24" [get_ports "DDR_DQ[20]"]
set_property src_info {type:SCOPED_XDC file:1 line:493 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F25" [get_ports "DDR_DQ[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:497 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "P23" [get_ports "DDR_DQ[19]"]
set_property src_info {type:SCOPED_XDC file:1 line:501 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "N26" [get_ports "DDR_DQ[18]"]
set_property src_info {type:SCOPED_XDC file:1 line:505 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "P24" [get_ports "DDR_DQ[17]"]
set_property src_info {type:SCOPED_XDC file:1 line:509 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "R26" [get_ports "DDR_DQ[16]"]
set_property src_info {type:SCOPED_XDC file:1 line:513 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "N23" [get_ports "DDR_DQ[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:517 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "M24" [get_ports "DDR_DQ[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:521 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "N24" [get_ports "DDR_DQ[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:525 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "M25" [get_ports "DDR_DQ[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:529 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K23" [get_ports "DDR_DQ[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:533 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "M26" [get_ports "DDR_DQ[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:537 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J26" [get_ports "DDR_DQ[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:541 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "V26" [get_ports "DDR_DM[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:545 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "P26" [get_ports "DDR_DM[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:549 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K25" [get_ports "DDR_DM[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:553 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G24" [get_ports "DDR_DM[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:557 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "Y21" [get_ports "DDR_CS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:561 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "U21" [get_ports "DDR_CKE"]
set_property src_info {type:SCOPED_XDC file:1 line:565 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "R21" [get_ports "DDR_Clk"]
set_property src_info {type:SCOPED_XDC file:1 line:569 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "P21" [get_ports "DDR_Clk_n"]
set_property src_info {type:SCOPED_XDC file:1 line:573 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "Y23" [get_ports "DDR_CAS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:577 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "R22" [get_ports "DDR_BankAddr[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:581 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "T22" [get_ports "DDR_BankAddr[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:585 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "U22" [get_ports "DDR_BankAddr[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:589 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "U20" [get_ports "DDR_Addr[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:593 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "T20" [get_ports "DDR_Addr[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:597 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J21" [get_ports "DDR_Addr[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:601 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "L20" [get_ports "DDR_Addr[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:605 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "N22" [get_ports "DDR_Addr[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:609 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "M20" [get_ports "DDR_Addr[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:613 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "L22" [get_ports "DDR_Addr[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:617 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "N21" [get_ports "DDR_Addr[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:621 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K20" [get_ports "DDR_Addr[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:625 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "R20" [get_ports "DDR_Addr[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:629 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J20" [get_ports "DDR_Addr[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:633 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "P20" [get_ports "DDR_Addr[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:637 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H21" [get_ports "DDR_Addr[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:641 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "M22" [get_ports "DDR_Addr[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:645 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K22" [get_ports "DDR_Addr[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:649 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C23" [get_ports "PS_PORB"]
set_property src_info {type:SCOPED_XDC file:1 line:652 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A22" [get_ports "PS_SRSTB"]
set_property src_info {type:SCOPED_XDC file:1 line:655 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B24" [get_ports "PS_CLK"]
set_property src_info {type:XDC file:2 line:39 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AE25 [get_ports BK_CS1]
set_property src_info {type:XDC file:2 line:40 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AE23 [get_ports BK_IRQ]
set_property src_info {type:XDC file:2 line:41 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AF24 [get_ports BK_LOAD]
set_property src_info {type:XDC file:2 line:42 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AE26 [get_ports BK_MISO1]
set_property src_info {type:XDC file:2 line:43 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AD25 [get_ports BK_MOSI1]
set_property src_info {type:XDC file:2 line:44 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AF23 [get_ports BK_RST]
set_property src_info {type:XDC file:2 line:45 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AD26 [get_ports BK_SCLK1]
set_property src_info {type:XDC file:2 line:46 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AC24 [get_ports BK_TR_SW]
set_property src_info {type:XDC file:2 line:47 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AD24 [get_ports IO1]
set_property src_info {type:XDC file:2 line:48 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AF20 [get_ports IO2]
set_property src_info {type:XDC file:2 line:49 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AA22 [get_ports PL_CTRL_A]
set_property src_info {type:XDC file:2 line:50 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AA23 [get_ports PL_CTRL_B]
set_property src_info {type:XDC file:2 line:51 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AE22 [get_ports PL_QSPI_CCLK]
set_property src_info {type:XDC file:2 line:52 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AD18 [get_ports PL_QSPI_CS_N]
set_property src_info {type:XDC file:2 line:53 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AF22 [get_ports PL_QSPI_IO0]
set_property src_info {type:XDC file:2 line:54 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AD19 [get_ports PL_QSPI_IO1]
set_property src_info {type:XDC file:2 line:55 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AE18 [get_ports PL_QSPI_IO2]
set_property src_info {type:XDC file:2 line:56 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AF18 [get_ports PL_QSPI_IO3]
set_property src_info {type:XDC file:2 line:57 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AB26 [get_ports PL_UART1_RX]
set_property src_info {type:XDC file:2 line:58 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AC26 [get_ports PL_UART1_TX]
set_property src_info {type:XDC file:2 line:59 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AF25 [get_ports PL_UART2_RX]
set_property src_info {type:XDC file:2 line:60 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AB24 [get_ports PL_UART2_TX]
set_property src_info {type:XDC file:2 line:61 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AE21 [get_ports PL_UART4_RX]
set_property src_info {type:XDC file:2 line:62 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AE20 [get_ports PL_UART4_TX]
set_property src_info {type:XDC file:2 line:63 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN Y20 [get_ports PL_UART_RX]
set_property src_info {type:XDC file:2 line:64 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AB20 [get_ports PL_UART_TX]
set_property src_info {type:XDC file:2 line:65 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AF19 [get_ports TSELF]
set_property src_info {type:XDC file:2 line:66 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AB21 [get_ports XD_R_B]
set_property src_info {type:XDC file:2 line:67 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AC21 [get_ports XD_T_P]
set_property src_info {type:XDC file:2 line:68 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN W20 [get_ports XD_XS]
set_property src_info {type:XDC file:2 line:69 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AA20 [get_ports XD_HX_R]
set_property src_info {type:XDC file:2 line:70 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AB19 [get_ports XD_T_R]
set_property src_info {type:XDC file:2 line:71 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN Y18 [get_ports XD_T_P1]
set_property src_info {type:XDC file:2 line:72 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AA25 [get_ports {test[2]}]
set_property src_info {type:XDC file:2 line:73 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN W18 [get_ports {test[3]}]
set_property src_info {type:XDC file:2 line:74 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN W19 [get_ports {test[5]}]
set_property src_info {type:XDC file:2 line:75 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AA19 [get_ports {test[6]}]
set_property src_info {type:XDC file:2 line:76 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN AC23 [get_ports {test[4]}]
set_property src_info {type:XDC file:2 line:77 export:INPUT save:INPUT read:READ} [current_design]
set_property C_CLK_INPUT_FREQ_HZ 300000000 [get_debug_cores dbg_hub]
set_property src_info {type:XDC file:2 line:78 export:INPUT save:INPUT read:READ} [current_design]
set_property C_ENABLE_CLK_DIVIDER false [get_debug_cores dbg_hub]
set_property src_info {type:XDC file:2 line:79 export:INPUT save:INPUT read:READ} [current_design]
set_property C_USER_SCAN_CHAIN 1 [get_debug_cores dbg_hub]
set_property src_info {type:XDC file:2 line:80 export:INPUT save:INPUT read:READ} [current_design]
connect_debug_port dbg_hub/clk [get_nets FCLK_CLK0]
set_property src_info {type:XDC file:3 line:4 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN H13 IOSTANDARD LVDS DIFF_TERM 1} [get_ports rx_frame_in_p]
set_property src_info {type:XDC file:3 line:5 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN H12 IOSTANDARD LVDS DIFF_TERM 1} [get_ports rx_frame_in_n]
set_property src_info {type:XDC file:3 line:6 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN G14 IOSTANDARD LVDS DIFF_TERM 1} [get_ports rx_clk_in_p]
set_property src_info {type:XDC file:3 line:7 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN F14 IOSTANDARD LVDS DIFF_TERM 1} [get_ports rx_clk_in_n]
set_property src_info {type:XDC file:3 line:8 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN F12 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[0]}]
set_property src_info {type:XDC file:3 line:9 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN E12 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[0]}]
set_property src_info {type:XDC file:3 line:10 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN G10 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[1]}]
set_property src_info {type:XDC file:3 line:11 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN F10 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[1]}]
set_property src_info {type:XDC file:3 line:12 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN G12 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[2]}]
set_property src_info {type:XDC file:3 line:13 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN G11 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[2]}]
set_property src_info {type:XDC file:3 line:14 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN D15 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[3]}]
set_property src_info {type:XDC file:3 line:15 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN D14 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[3]}]
set_property src_info {type:XDC file:3 line:16 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN E10 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[4]}]
set_property src_info {type:XDC file:3 line:17 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN D10 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[4]}]
set_property src_info {type:XDC file:3 line:18 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN G16 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[5]}]
set_property src_info {type:XDC file:3 line:19 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN G15 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[5]}]
set_property src_info {type:XDC file:3 line:21 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN K15 IOSTANDARD LVDS} [get_ports tx_frame_out_p]
set_property src_info {type:XDC file:3 line:22 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN J15 IOSTANDARD LVDS} [get_ports tx_frame_out_n]
set_property src_info {type:XDC file:3 line:23 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN J14 IOSTANDARD LVDS} [get_ports tx_clk_out_p]
set_property src_info {type:XDC file:3 line:24 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN H14 IOSTANDARD LVDS} [get_ports tx_clk_out_n]
set_property src_info {type:XDC file:3 line:25 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN F15 IOSTANDARD LVDS} [get_ports {tx_data_out_p[0]}]
set_property src_info {type:XDC file:3 line:26 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN E15 IOSTANDARD LVDS} [get_ports {tx_data_out_n[0]}]
set_property src_info {type:XDC file:3 line:27 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN C17 IOSTANDARD LVDS} [get_ports {tx_data_out_p[1]}]
set_property src_info {type:XDC file:3 line:28 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN C16 IOSTANDARD LVDS} [get_ports {tx_data_out_n[1]}]
set_property src_info {type:XDC file:3 line:29 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B16 IOSTANDARD LVDS} [get_ports {tx_data_out_p[2]}]
set_property src_info {type:XDC file:3 line:30 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B15 IOSTANDARD LVDS} [get_ports {tx_data_out_n[2]}]
set_property src_info {type:XDC file:3 line:31 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN K13 IOSTANDARD LVDS} [get_ports {tx_data_out_p[3]}]
set_property src_info {type:XDC file:3 line:32 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN J13 IOSTANDARD LVDS} [get_ports {tx_data_out_n[3]}]
set_property src_info {type:XDC file:3 line:33 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B17 IOSTANDARD LVDS} [get_ports {tx_data_out_p[4]}]
set_property src_info {type:XDC file:3 line:34 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN A17 IOSTANDARD LVDS} [get_ports {tx_data_out_n[4]}]
set_property src_info {type:XDC file:3 line:35 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN C11 IOSTANDARD LVDS} [get_ports {tx_data_out_p[5]}]
set_property src_info {type:XDC file:3 line:36 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B11 IOSTANDARD LVDS} [get_ports {tx_data_out_n[5]}]
set_property src_info {type:XDC file:3 line:38 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN G7 IOSTANDARD LVCMOS18} [get_ports {gpio_status[0]}]
set_property src_info {type:XDC file:3 line:39 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B7 IOSTANDARD LVCMOS18} [get_ports {gpio_status[1]}]
set_property src_info {type:XDC file:3 line:40 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN D6 IOSTANDARD LVCMOS18} [get_ports {gpio_status[2]}]
set_property src_info {type:XDC file:3 line:41 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN A8 IOSTANDARD LVCMOS18} [get_ports {gpio_status[3]}]
set_property src_info {type:XDC file:3 line:42 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN A7 IOSTANDARD LVCMOS18} [get_ports {gpio_status[4]}]
set_property src_info {type:XDC file:3 line:43 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B6 IOSTANDARD LVCMOS18} [get_ports {gpio_status[5]}]
set_property src_info {type:XDC file:3 line:44 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN C6 IOSTANDARD LVCMOS18} [get_ports {gpio_status[6]}]
set_property src_info {type:XDC file:3 line:45 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN A5 IOSTANDARD LVCMOS18} [get_ports {gpio_status[7]}]
set_property src_info {type:XDC file:3 line:47 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN C9 IOSTANDARD LVCMOS18} [get_ports {gpio_ctl[0]}]
set_property src_info {type:XDC file:3 line:48 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN A10 IOSTANDARD LVCMOS18} [get_ports {gpio_ctl[1]}]
set_property src_info {type:XDC file:3 line:49 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN A9 IOSTANDARD LVCMOS18} [get_ports {gpio_ctl[2]}]
set_property src_info {type:XDC file:3 line:50 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B9 IOSTANDARD LVCMOS18} [get_ports {gpio_ctl[3]}]
set_property src_info {type:XDC file:3 line:52 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B4 IOSTANDARD LVCMOS18} [get_ports gpio_en_agc]
set_property src_info {type:XDC file:3 line:53 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B5 IOSTANDARD LVCMOS18} [get_ports txnrx]
set_property src_info {type:XDC file:3 line:54 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN A4 IOSTANDARD LVCMOS18} [get_ports enable]
set_property src_info {type:XDC file:3 line:55 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B2 IOSTANDARD LVCMOS18} [get_ports gpio_resetb]
set_property src_info {type:XDC file:3 line:57 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN A2 [get_ports spi_csn]
set_property src_info {type:XDC file:3 line:60 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN C3 IOSTANDARD LVCMOS18} [get_ports spi_clk]
set_property src_info {type:XDC file:3 line:61 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN B1 IOSTANDARD LVCMOS18} [get_ports spi_mosi]
set_property src_info {type:XDC file:3 line:62 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN C2 IOSTANDARD LVCMOS18} [get_ports spi_miso]
set_property src_info {type:XDC file:3 line:63 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN C4 IOSTANDARD LVCMOS18} [get_ports gpio_sync]
set_property src_info {type:XDC file:3 line:65 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN AB25 IOSTANDARD LVCMOS33} [get_ports {test[1]}]
set_property src_info {type:SCOPED_XDC file:4 line:1 export:INPUT save:INPUT read:READ} [current_design]
set_clock_groups -group [get_clocks clk_div_sel_0_s] -group [get_clocks clk_div_sel_1_s] -logically_exclusive
set_property src_info {type:SCOPED_XDC file:5 line:10 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axi_aclk]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_sync_src_request_id* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axi_aclk]]]
set_property src_info {type:SCOPED_XDC file:5 line:26 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axi_aclk]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_src_req_fifo/i_waddr_sync* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axi_aclk]]]
set_property src_info {type:SCOPED_XDC file:5 line:32 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axis_aclk]]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_src_req_fifo/i_raddr_sync* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axis_aclk]]]]
set_property src_info {type:SCOPED_XDC file:5 line:38 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_cells -quiet -hier *cdc_sync_fifo_ram_reg* -filter {NAME =~ *i_src_req_fifo* && IS_SEQUENTIAL}] -to [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axis_aclk]]] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axis_aclk]]]]
set_property src_info {type:SCOPED_XDC file:5 line:44 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_cells -quiet -hier *eot_mem_reg* -filter {NAME =~ *i_request_arb* && IS_SEQUENTIAL}] -to [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axis_aclk]]] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axis_aclk]]]]
set_property src_info {type:SCOPED_XDC file:5 line:50 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axis_aclk]]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_sync_dest_request_id* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axis_aclk]]]]
set_property src_info {type:SCOPED_XDC file:5 line:56 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axis_aclk]]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_fifo/i_address_gray/i_waddr_sync* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/s_axis_aclk]]]]
set_property src_info {type:SCOPED_XDC file:5 line:62 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_dest_axi_aclk]]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_fifo/i_address_gray/i_raddr_sync* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/m_dest_axi_aclk]]]]
set_property src_info {type:SCOPED_XDC file:6 line:10 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]] -to [get_cells -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_sync_req_response_id* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]]]
set_property src_info {type:SCOPED_XDC file:6 line:26 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/s_axi_aclk]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_dest_req_fifo/i_waddr_sync* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/s_axi_aclk]]]
set_property src_info {type:SCOPED_XDC file:6 line:32 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_dest_req_fifo/i_raddr_sync* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]]]
set_property src_info {type:SCOPED_XDC file:6 line:38 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_cells -quiet -hier *cdc_sync_fifo_ram_reg* -filter {NAME =~ *i_dest_req_fifo* && IS_SEQUENTIAL}] -to [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]]]
set_property src_info {type:SCOPED_XDC file:6 line:44 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_dest_response_fifo/i_waddr_sync* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]]]
set_property src_info {type:SCOPED_XDC file:6 line:50 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/s_axi_aclk]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_dest_response_fifo/i_raddr_sync* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/s_axi_aclk]]]
set_property src_info {type:SCOPED_XDC file:6 line:55 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_cells -quiet -hier *cdc_sync_fifo_ram_reg* -filter {NAME =~ *i_dest_response_fifo* && IS_SEQUENTIAL}] -to [get_clocks -of_objects [get_pins i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/s_axi_aclk]] [get_property -min PERIOD [get_clocks -of_objects [get_pins i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/s_axi_aclk]]]
set_property src_info {type:SCOPED_XDC file:6 line:61 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_cells -quiet -hier *eot_mem_reg* -filter {NAME =~ *i_request_arb* && IS_SEQUENTIAL}] -to [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]]]
set_property src_info {type:SCOPED_XDC file:6 line:67 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/s_axis_aclk]]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_sync_dest_request_id* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/s_axis_aclk]]]]
set_property src_info {type:SCOPED_XDC file:6 line:73 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/s_axis_aclk]]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_fifo/i_address_gray/i_waddr_sync* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_wr_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_src_axi_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/s_axis_aclk]]]]
set_property src_info {type:SCOPED_XDC file:6 line:79 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -quiet -datapath_only -from [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]] -to [get_cells -quiet -hier *cdc_sync_stage1_reg* -filter {NAME =~ *i_fifo/i_address_gray/i_raddr_sync* && IS_SEQUENTIAL}] [get_property -min PERIOD [get_clocks -of_objects [get_pins [list i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/fifo_rd_clk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_axis_aclk i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/m_dest_axi_aclk]]]]
