Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
-------------------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Mar 17 17:08:23 2020
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : report_timing_summary -warn_on_violation -max_paths 10 -file system_top_timing_summary_routed.rpt -rpx system_top_timing_summary_routed.rpx
| Design       : system_top
| Device       : 7z035-ffg676
| Speed File   : -2  PRODUCTION 1.11 2014-09-11
-------------------------------------------------------------------------------------------------------------------------------------------------------------

Timing Summary Report

------------------------------------------------------------------------------------------------
| Timer Settings
| --------------
------------------------------------------------------------------------------------------------

  Enable Multi Corner Analysis               :  Yes
  Enable Pessimism Removal                   :  Yes
  Pessimism Removal Resolution               :  Nearest Common Node
  Enable Input Delay Default Clock           :  No
  Enable Preset / Clear Arcs                 :  No
  Disable Flight Delays                      :  No
  Ignore I/O Paths                           :  No
  Timing Early Launch at Borrowing Latches   :  false

  Corner  Analyze    Analyze    
  Name    Max Paths  Min Paths  
  ------  ---------  ---------  
  Slow    Yes        Yes        
  Fast    Yes        Yes        



check_timing report

Table of Contents
-----------------
1. checking no_clock
2. checking constant_clock
3. checking pulse_width_clock
4. checking unconstrained_internal_endpoints
5. checking no_input_delay
6. checking no_output_delay
7. checking multiple_clock
8. checking generated_clocks
9. checking loops
10. checking partial_input_delay
11. checking partial_output_delay
12. checking latch_loops

1. checking no_clock
--------------------
 There are 13616 register/latch pins with no clock driven by root clock pin: rx_clk_in_p (HIGH)


2. checking constant_clock
--------------------------
 There are 0 register/latch pins with constant_clock.


3. checking pulse_width_clock
-----------------------------
 There are 0 register/latch pins which need pulse_width check


4. checking unconstrained_internal_endpoints
--------------------------------------------
 There are 18350 pins that are not constrained for maximum delay. (HIGH)

 There are 0 pins that are not constrained for maximum delay due to constant clock.


5. checking no_input_delay
--------------------------
 There are 11 input ports with no input delay specified. (HIGH)

 There are 0 input ports with no input delay but user has a false path constraint.


6. checking no_output_delay
---------------------------
 There are 39 ports with no output delay specified. (HIGH)

 There are 0 ports with no output delay but user has a false path constraint

 There are 0 ports with no output delay but with a timing clock defined on it or propagating through it


7. checking multiple_clock
--------------------------
 There are 0 register/latch pins with multiple clocks.


8. checking generated_clocks
----------------------------
 There are 0 generated clocks that are not connected to a clock source.


9. checking loops
-----------------
 There are 0 combinational loops in the design.


10. checking partial_input_delay
--------------------------------
 There are 0 input ports with partial input delay specified.


11. checking partial_output_delay
---------------------------------
 There are 0 ports with partial output delay specified.


12. checking latch_loops
------------------------
 There are 0 combinational latch loops in the design through latch input



------------------------------------------------------------------------------------------------
| Design Timing Summary
| ---------------------
------------------------------------------------------------------------------------------------

    WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
    -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
      2.616        0.000                      0                24186        0.066        0.000                      0                24163        0.264        0.000                       0                  9805  


All user specified timing constraints are met.


------------------------------------------------------------------------------------------------
| Clock Summary
| -------------
------------------------------------------------------------------------------------------------

Clock                                                                    Waveform(ns)         Period(ns)      Frequency(MHz)
-----                                                                    ------------         ----------      --------------
clk_fpga_0                                                               {0.000 5.000}        10.000          100.000         
clk_fpga_1                                                               {0.000 2.500}        5.000           200.000         
dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {0.000 16.500}       33.000          30.303          


------------------------------------------------------------------------------------------------
| Intra Clock Table
| -----------------
------------------------------------------------------------------------------------------------

Clock                                                                        WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
-----                                                                        -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
clk_fpga_0                                                                     2.703        0.000                      0                18060        0.066        0.000                      0                18060        4.232        0.000                       0                  9417  
clk_fpga_1                                                                     4.379        0.000                      0                    2        0.113        0.000                      0                    2        0.264        0.000                       0                     5  
dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK       29.507        0.000                      0                  692        0.072        0.000                      0                  692       15.732        0.000                       0                   383  


------------------------------------------------------------------------------------------------
| Inter Clock Table
| -----------------
------------------------------------------------------------------------------------------------

From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  
              clk_fpga_0          9.373        0.000                      0                    1                                                                        


------------------------------------------------------------------------------------------------
| Other Path Groups Table
| -----------------------
------------------------------------------------------------------------------------------------

Path Group                                                               From Clock                                                               To Clock                                                                     WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------                                                               ----------                                                               --------                                                                     -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  
**async_default**                                                        clk_fpga_0                                                               clk_fpga_0                                                                     2.616        0.000                      0                 5309        0.270        0.000                      0                 5309  
**async_default**                                                        dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK       30.410        0.000                      0                  100        0.268        0.000                      0                  100  
**default**                                                              clk_fpga_0                                                                                                                                              9.196        0.000                      0                   23                                                                        


------------------------------------------------------------------------------------------------
| Timing Details
| --------------
------------------------------------------------------------------------------------------------


---------------------------------------------------------------------------------------------------
From Clock:  clk_fpga_0
  To Clock:  clk_fpga_0

Setup :            0  Failing Endpoints,  Worst Slack        2.703ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.066ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack        4.232ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             2.703ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_type_reg/CE
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.636ns  (logic 0.266ns (4.008%)  route 6.370ns (95.992%))
  Logic Levels:           1  (LUT6=1)
  Clock Path Skew:        -0.306ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.540ns = ( 12.540 - 10.000 ) 
    Source Clock Delay      (SCD):    2.997ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.565     2.997    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X53Y295        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X53Y295        FDCE (Prop_fdce_C_Q)         0.223     3.220 f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/Q
                         net (fo=76, routed)          5.774     8.994    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_s[3]
    SLICE_X95Y254        LUT6 (Prop_lut6_I2_O)        0.043     9.037 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_dac_datafmt_i_1/O
                         net (fo=4, routed)           0.596     9.633    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_type0
    SLICE_X91Y246        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_type_reg/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.216    12.540    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/s_axi_aclk
    SLICE_X91Y246        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_type_reg/C
                         clock pessimism              0.151    12.691    
                         clock uncertainty           -0.154    12.537    
    SLICE_X91Y246        FDCE (Setup_fdce_C_CE)      -0.201    12.336    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_type_reg
  -------------------------------------------------------------------
                         required time                         12.336    
                         arrival time                          -9.633    
  -------------------------------------------------------------------
                         slack                                  2.703    

Slack (MET) :             2.726ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_enb_reg/CE
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.636ns  (logic 0.266ns (4.008%)  route 6.370ns (95.992%))
  Logic Levels:           1  (LUT6=1)
  Clock Path Skew:        -0.306ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.540ns = ( 12.540 - 10.000 ) 
    Source Clock Delay      (SCD):    2.997ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.565     2.997    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X53Y295        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X53Y295        FDCE (Prop_fdce_C_Q)         0.223     3.220 f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/Q
                         net (fo=76, routed)          5.774     8.994    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_s[3]
    SLICE_X95Y254        LUT6 (Prop_lut6_I2_O)        0.043     9.037 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_dac_datafmt_i_1/O
                         net (fo=4, routed)           0.596     9.633    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_type0
    SLICE_X90Y246        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_enb_reg/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.216    12.540    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/s_axi_aclk
    SLICE_X90Y246        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_enb_reg/C
                         clock pessimism              0.151    12.691    
                         clock uncertainty           -0.154    12.537    
    SLICE_X90Y246        FDCE (Setup_fdce_C_CE)      -0.178    12.359    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_enb_reg
  -------------------------------------------------------------------
                         required time                         12.359    
                         arrival time                          -9.633    
  -------------------------------------------------------------------
                         slack                                  2.726    

Slack (MET) :             2.853ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_datafmt_reg/CE
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.500ns  (logic 0.266ns (4.092%)  route 6.234ns (95.908%))
  Logic Levels:           1  (LUT6=1)
  Clock Path Skew:        -0.291ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.555ns = ( 12.555 - 10.000 ) 
    Source Clock Delay      (SCD):    2.997ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.565     2.997    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X53Y295        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X53Y295        FDCE (Prop_fdce_C_Q)         0.223     3.220 f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/Q
                         net (fo=76, routed)          5.774     8.994    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_s[3]
    SLICE_X95Y254        LUT6 (Prop_lut6_I2_O)        0.043     9.037 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_dac_datafmt_i_1/O
                         net (fo=4, routed)           0.460     9.497    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_type0
    SLICE_X99Y246        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_datafmt_reg/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.231    12.555    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/s_axi_aclk
    SLICE_X99Y246        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_datafmt_reg/C
                         clock pessimism              0.151    12.706    
                         clock uncertainty           -0.154    12.552    
    SLICE_X99Y246        FDCE (Setup_fdce_C_CE)      -0.201    12.351    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_datafmt_reg
  -------------------------------------------------------------------
                         required time                         12.351    
                         arrival time                          -9.497    
  -------------------------------------------------------------------
                         slack                                  2.853    

Slack (MET) :             2.853ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_r1_mode_reg/CE
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.500ns  (logic 0.266ns (4.092%)  route 6.234ns (95.908%))
  Logic Levels:           1  (LUT6=1)
  Clock Path Skew:        -0.291ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.555ns = ( 12.555 - 10.000 ) 
    Source Clock Delay      (SCD):    2.997ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.565     2.997    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X53Y295        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X53Y295        FDCE (Prop_fdce_C_Q)         0.223     3.220 f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[3]/Q
                         net (fo=76, routed)          5.774     8.994    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_s[3]
    SLICE_X95Y254        LUT6 (Prop_lut6_I2_O)        0.043     9.037 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_dac_datafmt_i_1/O
                         net (fo=4, routed)           0.460     9.497    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_par_type0
    SLICE_X99Y246        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_r1_mode_reg/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.231    12.555    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/s_axi_aclk
    SLICE_X99Y246        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_r1_mode_reg/C
                         clock pessimism              0.151    12.706    
                         clock uncertainty           -0.154    12.552    
    SLICE_X99Y246        FDCE (Setup_fdce_C_CE)      -0.201    12.351    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/up_dac_r1_mode_reg
  -------------------------------------------------------------------
                         required time                         12.351    
                         arrival time                          -9.497    
  -------------------------------------------------------------------
                         slack                                  2.853    

Slack (MET) :             2.891ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[0]/CE
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.455ns  (logic 0.302ns (4.679%)  route 6.153ns (95.321%))
  Logic Levels:           1  (LUT5=1)
  Clock Path Skew:        -0.299ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.547ns = ( 12.547 - 10.000 ) 
    Source Clock Delay      (SCD):    2.997ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.565     2.997    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X52Y295        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X52Y295        FDCE (Prop_fdce_C_Q)         0.259     3.256 f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/Q
                         net (fo=113, routed)         5.663     8.919    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_s[0]
    SLICE_X109Y225       LUT5 (Prop_lut5_I1_O)        0.043     8.962 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_dac_dds_scale_2[15]_i_1__1/O
                         net (fo=16, routed)          0.490     9.452    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_waddr_int_reg[0][0]
    SLICE_X112Y223       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[0]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.223    12.547    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aclk
    SLICE_X112Y223       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[0]/C
                         clock pessimism              0.151    12.698    
                         clock uncertainty           -0.154    12.544    
    SLICE_X112Y223       FDCE (Setup_fdce_C_CE)      -0.201    12.343    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[0]
  -------------------------------------------------------------------
                         required time                         12.343    
                         arrival time                          -9.452    
  -------------------------------------------------------------------
                         slack                                  2.891    

Slack (MET) :             2.891ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[1]/CE
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.455ns  (logic 0.302ns (4.679%)  route 6.153ns (95.321%))
  Logic Levels:           1  (LUT5=1)
  Clock Path Skew:        -0.299ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.547ns = ( 12.547 - 10.000 ) 
    Source Clock Delay      (SCD):    2.997ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.565     2.997    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X52Y295        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X52Y295        FDCE (Prop_fdce_C_Q)         0.259     3.256 f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/Q
                         net (fo=113, routed)         5.663     8.919    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_s[0]
    SLICE_X109Y225       LUT5 (Prop_lut5_I1_O)        0.043     8.962 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_dac_dds_scale_2[15]_i_1__1/O
                         net (fo=16, routed)          0.490     9.452    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_waddr_int_reg[0][0]
    SLICE_X112Y223       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[1]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.223    12.547    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aclk
    SLICE_X112Y223       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[1]/C
                         clock pessimism              0.151    12.698    
                         clock uncertainty           -0.154    12.544    
    SLICE_X112Y223       FDCE (Setup_fdce_C_CE)      -0.201    12.343    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[1]
  -------------------------------------------------------------------
                         required time                         12.343    
                         arrival time                          -9.452    
  -------------------------------------------------------------------
                         slack                                  2.891    

Slack (MET) :             2.891ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[2]/CE
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.455ns  (logic 0.302ns (4.679%)  route 6.153ns (95.321%))
  Logic Levels:           1  (LUT5=1)
  Clock Path Skew:        -0.299ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.547ns = ( 12.547 - 10.000 ) 
    Source Clock Delay      (SCD):    2.997ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.565     2.997    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X52Y295        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X52Y295        FDCE (Prop_fdce_C_Q)         0.259     3.256 f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/Q
                         net (fo=113, routed)         5.663     8.919    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_s[0]
    SLICE_X109Y225       LUT5 (Prop_lut5_I1_O)        0.043     8.962 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_dac_dds_scale_2[15]_i_1__1/O
                         net (fo=16, routed)          0.490     9.452    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_waddr_int_reg[0][0]
    SLICE_X112Y223       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[2]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.223    12.547    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aclk
    SLICE_X112Y223       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[2]/C
                         clock pessimism              0.151    12.698    
                         clock uncertainty           -0.154    12.544    
    SLICE_X112Y223       FDCE (Setup_fdce_C_CE)      -0.201    12.343    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[2]
  -------------------------------------------------------------------
                         required time                         12.343    
                         arrival time                          -9.452    
  -------------------------------------------------------------------
                         slack                                  2.891    

Slack (MET) :             2.891ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[3]/CE
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.455ns  (logic 0.302ns (4.679%)  route 6.153ns (95.321%))
  Logic Levels:           1  (LUT5=1)
  Clock Path Skew:        -0.299ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.547ns = ( 12.547 - 10.000 ) 
    Source Clock Delay      (SCD):    2.997ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.565     2.997    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X52Y295        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X52Y295        FDCE (Prop_fdce_C_Q)         0.259     3.256 f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/Q
                         net (fo=113, routed)         5.663     8.919    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_s[0]
    SLICE_X109Y225       LUT5 (Prop_lut5_I1_O)        0.043     8.962 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_dac_dds_scale_2[15]_i_1__1/O
                         net (fo=16, routed)          0.490     9.452    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_waddr_int_reg[0][0]
    SLICE_X112Y223       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[3]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.223    12.547    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aclk
    SLICE_X112Y223       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[3]/C
                         clock pessimism              0.151    12.698    
                         clock uncertainty           -0.154    12.544    
    SLICE_X112Y223       FDCE (Setup_fdce_C_CE)      -0.201    12.343    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[3]
  -------------------------------------------------------------------
                         required time                         12.343    
                         arrival time                          -9.452    
  -------------------------------------------------------------------
                         slack                                  2.891    

Slack (MET) :             3.001ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[13]/CE
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.346ns  (logic 0.302ns (4.759%)  route 6.044ns (95.241%))
  Logic Levels:           1  (LUT5=1)
  Clock Path Skew:        -0.298ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.548ns = ( 12.548 - 10.000 ) 
    Source Clock Delay      (SCD):    2.997ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.565     2.997    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X52Y295        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X52Y295        FDCE (Prop_fdce_C_Q)         0.259     3.256 f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/Q
                         net (fo=113, routed)         5.663     8.919    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_s[0]
    SLICE_X109Y225       LUT5 (Prop_lut5_I1_O)        0.043     8.962 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_dac_dds_scale_2[15]_i_1__1/O
                         net (fo=16, routed)          0.381     9.343    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_waddr_int_reg[0][0]
    SLICE_X113Y227       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[13]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.224    12.548    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aclk
    SLICE_X113Y227       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[13]/C
                         clock pessimism              0.151    12.699    
                         clock uncertainty           -0.154    12.545    
    SLICE_X113Y227       FDCE (Setup_fdce_C_CE)      -0.201    12.344    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[13]
  -------------------------------------------------------------------
                         required time                         12.344    
                         arrival time                          -9.343    
  -------------------------------------------------------------------
                         slack                                  3.001    

Slack (MET) :             3.001ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[14]/CE
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.346ns  (logic 0.302ns (4.759%)  route 6.044ns (95.241%))
  Logic Levels:           1  (LUT5=1)
  Clock Path Skew:        -0.298ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.548ns = ( 12.548 - 10.000 ) 
    Source Clock Delay      (SCD):    2.997ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.565     2.997    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X52Y295        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X52Y295        FDCE (Prop_fdce_C_Q)         0.259     3.256 f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_int_reg[0]/Q
                         net (fo=113, routed)         5.663     8.919    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_waddr_s[0]
    SLICE_X109Y225       LUT5 (Prop_lut5_I1_O)        0.043     8.962 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_dac_dds_scale_2[15]_i_1__1/O
                         net (fo=16, routed)          0.381     9.343    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_waddr_int_reg[0][0]
    SLICE_X113Y227       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[14]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.224    12.548    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aclk
    SLICE_X113Y227       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[14]/C
                         clock pessimism              0.151    12.699    
                         clock uncertainty           -0.154    12.545    
    SLICE_X113Y227       FDCE (Setup_fdce_C_CE)      -0.201    12.344    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_dds_scale_2_reg[14]
  -------------------------------------------------------------------
                         required time                         12.344    
                         arrival time                          -9.343    
  -------------------------------------------------------------------
                         slack                                  3.001    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.066ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_rdata_int_reg[22]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][22]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.234ns  (logic 0.100ns (42.749%)  route 0.134ns (57.251%))
  Logic Levels:           0  
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.742ns
    Source Clock Delay      (SCD):    1.448ns
    Clock Pessimism Removal (CPR):    0.280ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.697     1.448    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/s_axi_aclk
    SLICE_X37Y274        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_rdata_int_reg[22]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X37Y274        FDCE (Prop_fdce_C_Q)         0.100     1.548 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_up_axi/up_axi_rdata_int_reg[22]/Q
                         net (fo=1, routed)           0.134     1.682    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/in[22]
    SLICE_X38Y272        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][22]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.943     1.742    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/aclk
    SLICE_X38Y272        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][22]_srl32/CLK
                         clock pessimism             -0.280     1.462    
    SLICE_X38Y272        SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.154     1.616    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][22]_srl32
  -------------------------------------------------------------------
                         required time                         -1.616    
                         arrival time                           1.682    
  -------------------------------------------------------------------
                         slack                                  0.066    

Slack (MET) :             0.067ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_rdata_int_reg[13]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][13]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.203ns  (logic 0.100ns (49.171%)  route 0.103ns (50.829%))
  Logic Levels:           0  
  Clock Path Skew:        0.034ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.752ns
    Source Clock Delay      (SCD):    1.457ns
    Clock Pessimism Removal (CPR):    0.261ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.706     1.457    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/s_axi_aclk
    SLICE_X44Y261        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_rdata_int_reg[13]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X44Y261        FDCE (Prop_fdce_C_Q)         0.100     1.557 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_rdata_int_reg[13]/Q
                         net (fo=1, routed)           0.103     1.660    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/in[13]
    SLICE_X42Y261        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][13]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.953     1.752    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/aclk
    SLICE_X42Y261        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][13]_srl32/CLK
                         clock pessimism             -0.261     1.491    
    SLICE_X42Y261        SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.102     1.593    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][13]_srl32
  -------------------------------------------------------------------
                         required time                         -1.593    
                         arrival time                           1.660    
  -------------------------------------------------------------------
                         slack                                  0.067    

Slack (MET) :             0.068ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_rdata_int_reg[15]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][15]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.201ns  (logic 0.100ns (49.651%)  route 0.101ns (50.349%))
  Logic Levels:           0  
  Clock Path Skew:        0.034ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.752ns
    Source Clock Delay      (SCD):    1.457ns
    Clock Pessimism Removal (CPR):    0.261ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.706     1.457    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/s_axi_aclk
    SLICE_X44Y261        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_rdata_int_reg[15]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X44Y261        FDCE (Prop_fdce_C_Q)         0.100     1.557 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_rdata_int_reg[15]/Q
                         net (fo=1, routed)           0.101     1.658    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/in[15]
    SLICE_X42Y261        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][15]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.953     1.752    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/aclk
    SLICE_X42Y261        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][15]_srl32/CLK
                         clock pessimism             -0.261     1.491    
    SLICE_X42Y261        SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.099     1.590    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][15]_srl32
  -------------------------------------------------------------------
                         required time                         -1.590    
                         arrival time                           1.658    
  -------------------------------------------------------------------
                         slack                                  0.068    

Slack (MET) :             0.070ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_rdata_int_reg[30]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][30]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.236ns  (logic 0.100ns (42.386%)  route 0.136ns (57.614%))
  Logic Levels:           0  
  Clock Path Skew:        0.012ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.748ns
    Source Clock Delay      (SCD):    1.456ns
    Clock Pessimism Removal (CPR):    0.280ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.705     1.456    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X51Y262        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_rdata_int_reg[30]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y262        FDCE (Prop_fdce_C_Q)         0.100     1.556 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_rdata_int_reg[30]/Q
                         net (fo=1, routed)           0.136     1.692    i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/in[30]
    SLICE_X48Y263        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][30]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.949     1.748    i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/aclk
    SLICE_X48Y263        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][30]_srl32/CLK
                         clock pessimism             -0.280     1.468    
    SLICE_X48Y263        SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.154     1.622    i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][30]_srl32
  -------------------------------------------------------------------
                         required time                         -1.622    
                         arrival time                           1.692    
  -------------------------------------------------------------------
                         slack                                  0.070    

Slack (MET) :             0.071ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_rdata_int_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][6]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.239ns  (logic 0.100ns (41.911%)  route 0.139ns (58.089%))
  Logic Levels:           0  
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.751ns
    Source Clock Delay      (SCD):    1.457ns
    Clock Pessimism Removal (CPR):    0.280ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.706     1.457    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X49Y260        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_rdata_int_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y260        FDCE (Prop_fdce_C_Q)         0.100     1.557 r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_rdata_int_reg[6]/Q
                         net (fo=1, routed)           0.139     1.696    i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/in[6]
    SLICE_X50Y260        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][6]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.952     1.751    i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/aclk
    SLICE_X50Y260        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][6]_srl32/CLK
                         clock pessimism             -0.280     1.471    
    SLICE_X50Y260        SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.154     1.625    i_system_wrapper/system_i/axi_cpu_interconnect/m07_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][6]_srl32
  -------------------------------------------------------------------
                         required time                         -1.625    
                         arrival time                           1.696    
  -------------------------------------------------------------------
                         slack                                  0.071    

Slack (MET) :             0.071ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/r_arid_r_reg[8]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][9]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.238ns  (logic 0.100ns (42.030%)  route 0.138ns (57.970%))
  Logic Levels:           0  
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.750ns
    Source Clock Delay      (SCD):    1.457ns
    Clock Pessimism Removal (CPR):    0.280ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.706     1.457    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/aclk
    SLICE_X37Y284        FDRE                                         r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/r_arid_r_reg[8]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X37Y284        FDRE (Prop_fdre_C_Q)         0.100     1.557 r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/r_arid_r_reg[8]/Q
                         net (fo=1, routed)           0.138     1.695    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/in[9]
    SLICE_X38Y285        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][9]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.951     1.750    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/aclk
    SLICE_X38Y285        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][9]_srl32/CLK
                         clock pessimism             -0.280     1.470    
    SLICE_X38Y285        SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.154     1.624    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][9]_srl32
  -------------------------------------------------------------------
                         required time                         -1.624    
                         arrival time                           1.695    
  -------------------------------------------------------------------
                         slack                                  0.071    

Slack (MET) :             0.072ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/r_arid_r_reg[4]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][5]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.239ns  (logic 0.100ns (41.911%)  route 0.139ns (58.089%))
  Logic Levels:           0  
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.772ns
    Source Clock Delay      (SCD):    1.478ns
    Clock Pessimism Removal (CPR):    0.281ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.727     1.478    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/aclk
    SLICE_X29Y285        FDRE                                         r  i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/r_arid_r_reg[4]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X29Y285        FDRE (Prop_fdre_C_Q)         0.100     1.578 r  i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/r_arid_r_reg[4]/Q
                         net (fo=1, routed)           0.139     1.717    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/in[5]
    SLICE_X30Y285        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][5]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.973     1.772    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/aclk
    SLICE_X30Y285        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][5]_srl32/CLK
                         clock pessimism             -0.281     1.491    
    SLICE_X30Y285        SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.154     1.645    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][5]_srl32
  -------------------------------------------------------------------
                         required time                         -1.645    
                         arrival time                           1.717    
  -------------------------------------------------------------------
                         slack                                  0.072    

Slack (MET) :             0.072ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_rdata_int_reg[11]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][11]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.205ns  (logic 0.100ns (48.719%)  route 0.105ns (51.281%))
  Logic Levels:           0  
  Clock Path Skew:        0.034ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.753ns
    Source Clock Delay      (SCD):    1.458ns
    Clock Pessimism Removal (CPR):    0.261ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.707     1.458    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/s_axi_aclk
    SLICE_X40Y261        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_rdata_int_reg[11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X40Y261        FDCE (Prop_fdce_C_Q)         0.100     1.558 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_up_axi/up_axi_rdata_int_reg[11]/Q
                         net (fo=1, routed)           0.105     1.663    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/in[11]
    SLICE_X38Y261        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][11]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.954     1.753    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/aclk
    SLICE_X38Y261        SRLC32E                                      r  i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][11]_srl32/CLK
                         clock pessimism             -0.261     1.492    
    SLICE_X38Y261        SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.099     1.591    i_system_wrapper/system_i/axi_cpu_interconnect/m08_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/memory_reg[31][11]_srl32
  -------------------------------------------------------------------
                         required time                         -1.591    
                         arrival time                           1.663    
  -------------------------------------------------------------------
                         slack                                  0.072    

Slack (MET) :             0.072ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/up_adc_iqcor_coeff_1_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/up_adc_iqcor_coeff_tc_1_reg[12]/D
                            (rising edge-triggered cell FDCE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.442ns  (logic 0.303ns (68.501%)  route 0.139ns (31.499%))
  Logic Levels:           5  (CARRY4=4 LUT2=1)
  Clock Path Skew:        0.299ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.746ns
    Source Clock Delay      (SCD):    1.371ns
    Clock Pessimism Removal (CPR):    0.076ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.620     1.371    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/s_axi_aclk
    SLICE_X65Y247        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/up_adc_iqcor_coeff_1_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X65Y247        FDCE (Prop_fdce_C_Q)         0.100     1.471 r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/up_adc_iqcor_coeff_1_reg[3]/Q
                         net (fo=2, routed)           0.139     1.610    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/up_adc_iqcor_coeff_tc_1_reg[3]_0[3]
    SLICE_X60Y247        LUT2 (Prop_lut2_I1_O)        0.028     1.638 r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/sm2tc_return_carry_i_1__6/O
                         net (fo=1, routed)           0.000     1.638    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/sm2tc_return_carry_i_1__6_n_0
    SLICE_X60Y247        CARRY4 (Prop_carry4_S[3]_CO[3])
                                                      0.084     1.722 r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/sm2tc_return_carry/CO[3]
                         net (fo=1, routed)           0.000     1.722    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/sm2tc_return_carry_n_0
    SLICE_X60Y248        CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.025     1.747 r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/sm2tc_return_carry__0/CO[3]
                         net (fo=1, routed)           0.000     1.747    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/sm2tc_return_carry__0_n_0
    SLICE_X60Y249        CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.025     1.772 r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/sm2tc_return_carry__1/CO[3]
                         net (fo=1, routed)           0.001     1.772    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/sm2tc_return_carry__1_n_0
    SLICE_X60Y250        CARRY4 (Prop_carry4_CI_O[0])
                                                      0.041     1.813 r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/sm2tc_return_carry__2/O[0]
                         net (fo=1, routed)           0.000     1.813    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/sm2tc_return_carry__2_n_7
    SLICE_X60Y250        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/up_adc_iqcor_coeff_tc_1_reg[12]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.947     1.746    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/s_axi_aclk
    SLICE_X60Y250        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/up_adc_iqcor_coeff_tc_1_reg[12]/C
                         clock pessimism             -0.076     1.670    
    SLICE_X60Y250        FDCE (Hold_fdce_C_D)         0.071     1.741    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_rx_channel_3/i_up_adc_channel/up_adc_iqcor_coeff_tc_1_reg[12]
  -------------------------------------------------------------------
                         required time                         -1.741    
                         arrival time                           1.813    
  -------------------------------------------------------------------
                         slack                                  0.072    

Slack (MET) :             0.078ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/axaddr_incr_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/axaddr_incr_reg[4]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.468ns  (logic 0.293ns (62.656%)  route 0.175ns (37.344%))
  Logic Levels:           3  (CARRY4=2 LUT5=1)
  Clock Path Skew:        0.298ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.855ns
    Source Clock Delay      (SCD):    1.461ns
    Clock Pessimism Removal (CPR):    0.096ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.710     1.461    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/aclk
    SLICE_X42Y299        FDRE                                         r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/axaddr_incr_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X42Y299        FDRE (Prop_fdre_C_Q)         0.118     1.579 r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/axaddr_incr_reg[0]/Q
                         net (fo=1, routed)           0.174     1.753    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/aw_pipe/axaddr_incr_reg[0]
    SLICE_X42Y299        LUT5 (Prop_lut5_I2_O)        0.028     1.781 r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/aw_pipe/axaddr_incr[0]_i_10/O
                         net (fo=1, routed)           0.000     1.781    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/aw_pipe/axaddr_incr[0]_i_10_n_0
    SLICE_X42Y299        CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.106     1.887 r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/aw_pipe/axaddr_incr_reg[0]_i_2/CO[3]
                         net (fo=1, routed)           0.001     1.888    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/CO[0]
    SLICE_X42Y300        CARRY4 (Prop_carry4_CI_O[0])
                                                      0.041     1.929 r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/axaddr_incr_reg[4]_i_1/O[0]
                         net (fo=1, routed)           0.000     1.929    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/axaddr_incr_reg[4]_i_1_n_7
    SLICE_X42Y300        FDRE                                         r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/axaddr_incr_reg[4]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.056     1.855    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/aclk
    SLICE_X42Y300        FDRE                                         r  i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/axaddr_incr_reg[4]/C
                         clock pessimism             -0.096     1.759    
    SLICE_X42Y300        FDRE (Hold_fdre_C_D)         0.092     1.851    i_system_wrapper/system_i/axi_cpu_interconnect/m09_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/axaddr_incr_reg[4]
  -------------------------------------------------------------------
                         required time                         -1.851    
                         arrival time                           1.929    
  -------------------------------------------------------------------
                         slack                                  0.078    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clk_fpga_0
Waveform(ns):       { 0.000 5.000 }
Period(ns):         10.000
Sources:            { i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0] }

Check Type        Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     IDELAYE2/C          n/a            2.000         10.000      8.000      IDELAY_X1Y348   i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/g_rx_data[0].i_rx_data/i_rx_data_idelay/C
Min Period        n/a     IDELAYE2/C          n/a            2.000         10.000      8.000      IDELAY_X1Y344   i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/g_rx_data[1].i_rx_data/i_rx_data_idelay/C
Min Period        n/a     IDELAYE2/C          n/a            2.000         10.000      8.000      IDELAY_X1Y340   i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/g_rx_data[2].i_rx_data/i_rx_data_idelay/C
Min Period        n/a     IDELAYE2/C          n/a            2.000         10.000      8.000      IDELAY_X1Y324   i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/g_rx_data[3].i_rx_data/i_rx_data_idelay/C
Min Period        n/a     IDELAYE2/C          n/a            2.000         10.000      8.000      IDELAY_X1Y346   i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/g_rx_data[4].i_rx_data/i_rx_data_idelay/C
Min Period        n/a     IDELAYE2/C          n/a            2.000         10.000      8.000      IDELAY_X1Y330   i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/g_rx_data[5].i_rx_data/i_rx_data_idelay/C
Min Period        n/a     IDELAYE2/C          n/a            2.000         10.000      8.000      IDELAY_X1Y336   i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_rx_frame/i_rx_data_idelay/C
Min Period        n/a     RAMB36E1/CLKARDCLK  n/a            1.839         10.000      8.161      RAMB36_X1Y49    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/ram_reg/CLKARDCLK
Min Period        n/a     RAMB36E1/CLKBWRCLK  n/a            1.839         10.000      8.161      RAMB36_X2Y50    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/ram_reg/CLKBWRCLK
Min Period        n/a     BUFG/I              n/a            1.409         10.000      8.592      BUFGCTRL_X0Y17  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/I
Low Pulse Width   Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X6Y208    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMA/CLK
Low Pulse Width   Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X6Y208    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMA_D1/CLK
Low Pulse Width   Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X6Y208    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMB/CLK
Low Pulse Width   Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X6Y208    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMB_D1/CLK
Low Pulse Width   Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X6Y208    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMC/CLK
Low Pulse Width   Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X6Y208    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMC_D1/CLK
Low Pulse Width   Fast    RAMS32/CLK          n/a            0.768         5.000       4.232      SLICE_X6Y208    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMD/CLK
Low Pulse Width   Fast    RAMS32/CLK          n/a            0.768         5.000       4.232      SLICE_X6Y208    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMD_D1/CLK
Low Pulse Width   Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA/CLK
Low Pulse Width   Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA_D1/CLK
High Pulse Width  Slow    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA/CLK
High Pulse Width  Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA/CLK
High Pulse Width  Slow    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA_D1/CLK
High Pulse Width  Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA_D1/CLK
High Pulse Width  Slow    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB/CLK
High Pulse Width  Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB/CLK
High Pulse Width  Slow    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB_D1/CLK
High Pulse Width  Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB_D1/CLK
High Pulse Width  Slow    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMC/CLK
High Pulse Width  Fast    RAMD32/CLK          n/a            0.768         5.000       4.232      SLICE_X10Y208   dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMC/CLK



---------------------------------------------------------------------------------------------------
From Clock:  clk_fpga_1
  To Clock:  clk_fpga_1

Setup :            0  Failing Endpoints,  Worst Slack        4.379ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.113ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack        0.264ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             4.379ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_1  {rise@0.000ns fall@2.500ns period=5.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_1  {rise@0.000ns fall@2.500ns period=5.000ns})
  Path Group:             clk_fpga_1
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            5.000ns  (clk_fpga_1 rise@5.000ns - clk_fpga_1 rise@0.000ns)
  Data Path Delay:        0.424ns  (logic 0.204ns (48.074%)  route 0.220ns (51.926%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.924ns = ( 7.924 - 5.000 ) 
    Source Clock Delay      (SCD):    3.261ns
    Clock Pessimism Removal (CPR):    0.337ns
  Clock Uncertainty:      0.083ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.150ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_1 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[1]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1]
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG/O
                         net (fo=4, routed)           1.829     3.261    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/clk
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X168Y325       FDRE (Prop_fdre_C_Q)         0.204     3.465 r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/Q
                         net (fo=1, routed)           0.220     3.685    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_1 rise edge)
                                                      5.000     5.000 r  
    PS7_X0Y0             PS7                          0.000     5.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[1]
                         net (fo=1, routed)           1.241     6.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1]
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.083     6.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG/O
                         net (fo=4, routed)           1.600     7.924    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/clk
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/C
                         clock pessimism              0.337     8.261    
                         clock uncertainty           -0.083     8.178    
    SLICE_X168Y325       FDRE (Setup_fdre_C_D)       -0.114     8.064    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg
  -------------------------------------------------------------------
                         required time                          8.064    
                         arrival time                          -3.685    
  -------------------------------------------------------------------
                         slack                                  4.379    

Slack (MET) :             4.568ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_1  {rise@0.000ns fall@2.500ns period=5.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_1  {rise@0.000ns fall@2.500ns period=5.000ns})
  Path Group:             clk_fpga_1
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            5.000ns  (clk_fpga_1 rise@5.000ns - clk_fpga_1 rise@0.000ns)
  Data Path Delay:        0.339ns  (logic 0.223ns (65.753%)  route 0.116ns (34.247%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.924ns = ( 7.924 - 5.000 ) 
    Source Clock Delay      (SCD):    3.261ns
    Clock Pessimism Removal (CPR):    0.337ns
  Clock Uncertainty:      0.083ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.150ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_1 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[1]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1]
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG/O
                         net (fo=4, routed)           1.829     3.261    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/clk
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X168Y325       FDRE (Prop_fdre_C_Q)         0.223     3.484 r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/Q
                         net (fo=1, routed)           0.116     3.600    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_1 rise edge)
                                                      5.000     5.000 r  
    PS7_X0Y0             PS7                          0.000     5.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[1]
                         net (fo=1, routed)           1.241     6.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1]
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.083     6.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG/O
                         net (fo=4, routed)           1.600     7.924    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/clk
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
                         clock pessimism              0.337     8.261    
                         clock uncertainty           -0.083     8.178    
    SLICE_X168Y325       FDRE (Setup_fdre_C_D)       -0.010     8.168    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg
  -------------------------------------------------------------------
                         required time                          8.168    
                         arrival time                          -3.600    
  -------------------------------------------------------------------
                         slack                                  4.568    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.113ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_1  {rise@0.000ns fall@2.500ns period=5.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_1  {rise@0.000ns fall@2.500ns period=5.000ns})
  Path Group:             clk_fpga_1
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_1 rise@0.000ns - clk_fpga_1 rise@0.000ns)
  Data Path Delay:        0.160ns  (logic 0.100ns (62.442%)  route 0.060ns (37.558%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.912ns
    Source Clock Delay      (SCD):    1.599ns
    Clock Pessimism Removal (CPR):    0.313ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_1 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[1]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1]
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG/O
                         net (fo=4, routed)           0.848     1.599    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/clk
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X168Y325       FDRE (Prop_fdre_C_Q)         0.100     1.699 r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/Q
                         net (fo=1, routed)           0.060     1.759    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_1 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[1]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1]
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG/O
                         net (fo=4, routed)           1.113     1.912    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/clk
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
                         clock pessimism             -0.313     1.599    
    SLICE_X168Y325       FDRE (Hold_fdre_C_D)         0.047     1.646    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg
  -------------------------------------------------------------------
                         required time                         -1.646    
                         arrival time                           1.759    
  -------------------------------------------------------------------
                         slack                                  0.113    

Slack (MET) :             0.197ns  (arrival time - required time)
  Source:                 i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_1  {rise@0.000ns fall@2.500ns period=5.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_1  {rise@0.000ns fall@2.500ns period=5.000ns})
  Path Group:             clk_fpga_1
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_1 rise@0.000ns - clk_fpga_1 rise@0.000ns)
  Data Path Delay:        0.197ns  (logic 0.091ns (46.112%)  route 0.106ns (53.888%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.912ns
    Source Clock Delay      (SCD):    1.599ns
    Clock Pessimism Removal (CPR):    0.313ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_1 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[1]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1]
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG/O
                         net (fo=4, routed)           0.848     1.599    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/clk
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X168Y325       FDRE (Prop_fdre_C_Q)         0.091     1.690 r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/Q
                         net (fo=1, routed)           0.106     1.796    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_1 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[1]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1]
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG/O
                         net (fo=4, routed)           1.113     1.912    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/clk
    SLICE_X168Y325       FDRE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/C
                         clock pessimism             -0.313     1.599    
    SLICE_X168Y325       FDRE (Hold_fdre_C_D)         0.000     1.599    i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg
  -------------------------------------------------------------------
                         required time                         -1.599    
                         arrival time                           1.796    
  -------------------------------------------------------------------
                         slack                                  0.197    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clk_fpga_1
Waveform(ns):       { 0.000 2.500 }
Period(ns):         5.000
Sources:            { i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[1] }

Check Type        Corner  Lib Pin            Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location         Pin
Min Period        n/a     IDELAYCTRL/REFCLK  n/a            2.438         5.000       2.562      IDELAYCTRL_X1Y6  i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_rx_frame/i_delay_ctrl/REFCLK
Min Period        n/a     BUFG/I             n/a            1.409         5.000       3.592      BUFGCTRL_X0Y18   i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG/I
Min Period        n/a     FDRE/C             n/a            0.750         5.000       4.250      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
Min Period        n/a     FDRE/C             n/a            0.700         5.000       4.300      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/C
Min Period        n/a     FDRE/C             n/a            0.700         5.000       4.300      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/C
Max Period        n/a     IDELAYCTRL/REFCLK  n/a            5.264         5.000       0.264      IDELAYCTRL_X1Y6  i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_rx_frame/i_delay_ctrl/REFCLK
Low Pulse Width   Fast    FDRE/C             n/a            0.400         2.500       2.100      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
Low Pulse Width   Slow    FDRE/C             n/a            0.400         2.500       2.100      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
Low Pulse Width   Fast    FDRE/C             n/a            0.350         2.500       2.150      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/C
Low Pulse Width   Fast    FDRE/C             n/a            0.350         2.500       2.150      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/C
Low Pulse Width   Slow    FDRE/C             n/a            0.350         2.500       2.150      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/C
Low Pulse Width   Slow    FDRE/C             n/a            0.350         2.500       2.150      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/C
High Pulse Width  Slow    FDRE/C             n/a            0.350         2.500       2.150      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/C
High Pulse Width  Slow    FDRE/C             n/a            0.350         2.500       2.150      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
High Pulse Width  Slow    FDRE/C             n/a            0.350         2.500       2.150      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/C
High Pulse Width  Fast    FDRE/C             n/a            0.350         2.500       2.150      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_m1_reg/C
High Pulse Width  Fast    FDRE/C             n/a            0.350         2.500       2.150      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/ad_rst_sync_reg/C
High Pulse Width  Fast    FDRE/C             n/a            0.350         2.500       2.150      SLICE_X168Y325   i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_delay_cntrl/i_delay_rst_reg/rst_reg/C



---------------------------------------------------------------------------------------------------
From Clock:  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  To Clock:  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK

Setup :            0  Failing Endpoints,  Worst Slack       29.507ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.072ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack       15.732ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             29.507ns  (required time - arrival time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/shift_reg_in_reg[17]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_i_reg/D
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        3.426ns  (logic 0.395ns (11.529%)  route 3.031ns (88.471%))
  Logic Levels:           4  (LUT3=1 LUT4=1 LUT6=2)
  Clock Path Skew:        -0.044ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.750ns = ( 37.750 - 33.000 ) 
    Source Clock Delay      (SCD):    5.536ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.468     5.536    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/m_bscan_tck[0]
    SLICE_X9Y213         FDRE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/shift_reg_in_reg[17]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X9Y213         FDRE (Prop_fdre_C_Q)         0.223     5.759 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/shift_reg_in_reg[17]/Q
                         net (fo=9, routed)           0.811     6.570    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/shift_reg_in[17]
    SLICE_X13Y207        LUT3 (Prop_lut3_I0_O)        0.043     6.613 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/gc0.count_d1[3]_i_2/O
                         net (fo=22, routed)          0.421     7.034    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gr1.gr1_int.rfwft/shift_reg_in_reg[17]
    SLICE_X12Y205        LUT4 (Prop_lut4_I1_O)        0.043     7.077 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gr1.gr1_int.rfwft/gc0.count_d1[3]_i_1/O
                         net (fo=25, routed)          0.897     7.974    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/rpntr/E[0]
    SLICE_X4Y205         LUT6 (Prop_lut6_I0_O)        0.043     8.017 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/rpntr/ram_empty_i_i_2/O
                         net (fo=1, routed)           0.351     8.368    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/rpntr/ram_empty_i_i_2_n_0
    SLICE_X5Y205         LUT6 (Prop_lut6_I0_O)        0.043     8.411 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/rpntr/ram_empty_i_i_1/O
                         net (fo=2, routed)           0.551     8.962    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/gnxpm_cdc.wr_pntr_bin_reg[1]
    SLICE_X12Y205        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_i_reg/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.301    37.750    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/m_bscan_tck[0]
    SLICE_X12Y205        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_i_reg/C
                         clock pessimism              0.742    38.492    
                         clock uncertainty           -0.035    38.457    
    SLICE_X12Y205        FDPE (Setup_fdpe_C_D)        0.013    38.470    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_i_reg
  -------------------------------------------------------------------
                         required time                         38.470    
                         arrival time                          -8.962    
  -------------------------------------------------------------------
                         slack                                 29.507    

Slack (MET) :             29.609ns  (required time - arrival time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/shift_reg_in_reg[17]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_fb_i_reg/D
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        3.316ns  (logic 0.395ns (11.913%)  route 2.921ns (88.088%))
  Logic Levels:           4  (LUT3=1 LUT4=1 LUT6=2)
  Clock Path Skew:        -0.044ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.750ns = ( 37.750 - 33.000 ) 
    Source Clock Delay      (SCD):    5.536ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.468     5.536    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/m_bscan_tck[0]
    SLICE_X9Y213         FDRE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/shift_reg_in_reg[17]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X9Y213         FDRE (Prop_fdre_C_Q)         0.223     5.759 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/shift_reg_in_reg[17]/Q
                         net (fo=9, routed)           0.811     6.570    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/shift_reg_in[17]
    SLICE_X13Y207        LUT3 (Prop_lut3_I0_O)        0.043     6.613 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD5/gc0.count_d1[3]_i_2/O
                         net (fo=22, routed)          0.421     7.034    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gr1.gr1_int.rfwft/shift_reg_in_reg[17]
    SLICE_X12Y205        LUT4 (Prop_lut4_I1_O)        0.043     7.077 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gr1.gr1_int.rfwft/gc0.count_d1[3]_i_1/O
                         net (fo=25, routed)          0.897     7.974    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/rpntr/E[0]
    SLICE_X4Y205         LUT6 (Prop_lut6_I0_O)        0.043     8.017 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/rpntr/ram_empty_i_i_2/O
                         net (fo=1, routed)           0.351     8.368    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/rpntr/ram_empty_i_i_2_n_0
    SLICE_X5Y205         LUT6 (Prop_lut6_I0_O)        0.043     8.411 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/rpntr/ram_empty_i_i_1/O
                         net (fo=2, routed)           0.441     8.852    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/gnxpm_cdc.wr_pntr_bin_reg[1]
    SLICE_X12Y205        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_fb_i_reg/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.301    37.750    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/m_bscan_tck[0]
    SLICE_X12Y205        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_fb_i_reg/C
                         clock pessimism              0.742    38.492    
                         clock uncertainty           -0.035    38.457    
    SLICE_X12Y205        FDPE (Setup_fdpe_C_D)        0.004    38.461    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_fb_i_reg
  -------------------------------------------------------------------
                         required time                         38.461    
                         arrival time                          -8.852    
  -------------------------------------------------------------------
                         slack                                 29.609    

Slack (MET) :             29.737ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[0]/D
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        3.239ns  (logic 0.681ns (21.026%)  route 2.558ns (78.974%))
  Logic Levels:           4  (CARRY4=1 LUT5=2 LUT6=1)
  Clock Path Skew:        -0.023ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.736ns = ( 37.736 - 33.000 ) 
    Source Clock Delay      (SCD):    5.525ns
    Clock Pessimism Removal (CPR):    0.766ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.457     5.525    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y227        FDRE (Prop_fdre_C_Q)         0.204     5.729 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/Q
                         net (fo=57, routed)          1.016     6.745    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state[0]
    SLICE_X11Y228        LUT5 (Prop_lut5_I0_O)        0.124     6.869 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid_inferred_i_9/O
                         net (fo=2, routed)           0.539     7.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid[23]
    SLICE_X9Y228         LUT6 (Prop_lut6_I1_O)        0.043     7.452 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7/O
                         net (fo=1, routed)           0.000     7.452    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7_n_0
    SLICE_X9Y228         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.267     7.719 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3/CO[3]
                         net (fo=17, routed)          1.002     8.721    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3_n_0
    SLICE_X13Y226        LUT5 (Prop_lut5_I3_O)        0.043     8.764 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[0]_i_1/O
                         net (fo=1, routed)           0.000     8.764    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[0]_i_1_n_0
    SLICE_X13Y226        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[0]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.287    37.736    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y226        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[0]/C
                         clock pessimism              0.766    38.502    
                         clock uncertainty           -0.035    38.467    
    SLICE_X13Y226        FDRE (Setup_fdre_C_D)        0.034    38.501    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[0]
  -------------------------------------------------------------------
                         required time                         38.501    
                         arrival time                          -8.764    
  -------------------------------------------------------------------
                         slack                                 29.737    

Slack (MET) :             29.745ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_temp_reg[1]/D
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        3.229ns  (logic 0.681ns (21.089%)  route 2.548ns (78.911%))
  Logic Levels:           4  (CARRY4=1 LUT5=1 LUT6=2)
  Clock Path Skew:        -0.024ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.735ns = ( 37.735 - 33.000 ) 
    Source Clock Delay      (SCD):    5.525ns
    Clock Pessimism Removal (CPR):    0.766ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.457     5.525    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y227        FDRE (Prop_fdre_C_Q)         0.204     5.729 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/Q
                         net (fo=57, routed)          1.016     6.745    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state[0]
    SLICE_X11Y228        LUT5 (Prop_lut5_I0_O)        0.124     6.869 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid_inferred_i_9/O
                         net (fo=2, routed)           0.539     7.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid[23]
    SLICE_X9Y228         LUT6 (Prop_lut6_I1_O)        0.043     7.452 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7/O
                         net (fo=1, routed)           0.000     7.452    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7_n_0
    SLICE_X9Y228         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.267     7.719 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3/CO[3]
                         net (fo=17, routed)          0.992     8.711    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3_n_0
    SLICE_X13Y225        LUT6 (Prop_lut6_I4_O)        0.043     8.754 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_temp[1]_i_1/O
                         net (fo=1, routed)           0.000     8.754    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_temp[1]_i_1_n_0
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_temp_reg[1]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.286    37.735    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_temp_reg[1]/C
                         clock pessimism              0.766    38.501    
                         clock uncertainty           -0.035    38.466    
    SLICE_X13Y225        FDRE (Setup_fdre_C_D)        0.034    38.500    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_temp_reg[1]
  -------------------------------------------------------------------
                         required time                         38.500    
                         arrival time                          -8.754    
  -------------------------------------------------------------------
                         slack                                 29.745    

Slack (MET) :             29.854ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[7]/D
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        3.122ns  (logic 0.681ns (21.814%)  route 2.441ns (78.186%))
  Logic Levels:           4  (CARRY4=1 LUT5=2 LUT6=1)
  Clock Path Skew:        -0.023ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.736ns = ( 37.736 - 33.000 ) 
    Source Clock Delay      (SCD):    5.525ns
    Clock Pessimism Removal (CPR):    0.766ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.457     5.525    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y227        FDRE (Prop_fdre_C_Q)         0.204     5.729 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/Q
                         net (fo=57, routed)          1.016     6.745    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state[0]
    SLICE_X11Y228        LUT5 (Prop_lut5_I0_O)        0.124     6.869 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid_inferred_i_9/O
                         net (fo=2, routed)           0.539     7.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid[23]
    SLICE_X9Y228         LUT6 (Prop_lut6_I1_O)        0.043     7.452 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7/O
                         net (fo=1, routed)           0.000     7.452    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7_n_0
    SLICE_X9Y228         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.267     7.719 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3/CO[3]
                         net (fo=17, routed)          0.885     8.604    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3_n_0
    SLICE_X13Y226        LUT5 (Prop_lut5_I3_O)        0.043     8.647 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp[7]_i_1/O
                         net (fo=1, routed)           0.000     8.647    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/p_0_in__0[7]
    SLICE_X13Y226        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[7]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.287    37.736    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y226        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[7]/C
                         clock pessimism              0.766    38.502    
                         clock uncertainty           -0.035    38.467    
    SLICE_X13Y226        FDRE (Setup_fdre_C_D)        0.034    38.501    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[7]
  -------------------------------------------------------------------
                         required time                         38.501    
                         arrival time                          -8.647    
  -------------------------------------------------------------------
                         slack                                 29.854    

Slack (MET) :             29.957ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[2]/D
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        3.021ns  (logic 0.681ns (22.544%)  route 2.340ns (77.456%))
  Logic Levels:           4  (CARRY4=1 LUT5=2 LUT6=1)
  Clock Path Skew:        -0.021ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.738ns = ( 37.738 - 33.000 ) 
    Source Clock Delay      (SCD):    5.525ns
    Clock Pessimism Removal (CPR):    0.766ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.457     5.525    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y227        FDRE (Prop_fdre_C_Q)         0.204     5.729 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/Q
                         net (fo=57, routed)          1.016     6.745    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state[0]
    SLICE_X11Y228        LUT5 (Prop_lut5_I0_O)        0.124     6.869 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid_inferred_i_9/O
                         net (fo=2, routed)           0.539     7.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid[23]
    SLICE_X9Y228         LUT6 (Prop_lut6_I1_O)        0.043     7.452 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7/O
                         net (fo=1, routed)           0.000     7.452    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7_n_0
    SLICE_X9Y228         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.267     7.719 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3/CO[3]
                         net (fo=17, routed)          0.784     8.503    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3_n_0
    SLICE_X13Y228        LUT5 (Prop_lut5_I3_O)        0.043     8.546 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[2]_i_1/O
                         net (fo=1, routed)           0.000     8.546    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[2]_i_1_n_0
    SLICE_X13Y228        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[2]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.289    37.738    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y228        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[2]/C
                         clock pessimism              0.766    38.504    
                         clock uncertainty           -0.035    38.469    
    SLICE_X13Y228        FDRE (Setup_fdre_C_D)        0.034    38.503    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[2]
  -------------------------------------------------------------------
                         required time                         38.503    
                         arrival time                          -8.546    
  -------------------------------------------------------------------
                         slack                                 29.957    

Slack (MET) :             29.978ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[4]/D
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        3.031ns  (logic 0.681ns (22.466%)  route 2.350ns (77.534%))
  Logic Levels:           4  (CARRY4=1 LUT5=2 LUT6=1)
  Clock Path Skew:        -0.021ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.737ns = ( 37.737 - 33.000 ) 
    Source Clock Delay      (SCD):    5.525ns
    Clock Pessimism Removal (CPR):    0.767ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.457     5.525    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y227        FDRE (Prop_fdre_C_Q)         0.204     5.729 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/Q
                         net (fo=57, routed)          1.016     6.745    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state[0]
    SLICE_X11Y228        LUT5 (Prop_lut5_I0_O)        0.124     6.869 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid_inferred_i_9/O
                         net (fo=2, routed)           0.539     7.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid[23]
    SLICE_X9Y228         LUT6 (Prop_lut6_I1_O)        0.043     7.452 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7/O
                         net (fo=1, routed)           0.000     7.452    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7_n_0
    SLICE_X9Y228         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.267     7.719 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3/CO[3]
                         net (fo=17, routed)          0.794     8.513    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3_n_0
    SLICE_X12Y227        LUT5 (Prop_lut5_I3_O)        0.043     8.556 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[4]_i_1/O
                         net (fo=1, routed)           0.000     8.556    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[4]_i_1_n_0
    SLICE_X12Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[4]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.288    37.737    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X12Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[4]/C
                         clock pessimism              0.767    38.504    
                         clock uncertainty           -0.035    38.469    
    SLICE_X12Y227        FDRE (Setup_fdre_C_D)        0.066    38.535    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[4]
  -------------------------------------------------------------------
                         required time                         38.535    
                         arrival time                          -8.556    
  -------------------------------------------------------------------
                         slack                                 29.978    

Slack (MET) :             29.986ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[3]/D
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.990ns  (logic 0.681ns (22.777%)  route 2.309ns (77.223%))
  Logic Levels:           4  (CARRY4=1 LUT5=2 LUT6=1)
  Clock Path Skew:        -0.023ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.736ns = ( 37.736 - 33.000 ) 
    Source Clock Delay      (SCD):    5.525ns
    Clock Pessimism Removal (CPR):    0.766ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.457     5.525    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y227        FDRE (Prop_fdre_C_Q)         0.204     5.729 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/Q
                         net (fo=57, routed)          1.016     6.745    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state[0]
    SLICE_X11Y228        LUT5 (Prop_lut5_I0_O)        0.124     6.869 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid_inferred_i_9/O
                         net (fo=2, routed)           0.539     7.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid[23]
    SLICE_X9Y228         LUT6 (Prop_lut6_I1_O)        0.043     7.452 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7/O
                         net (fo=1, routed)           0.000     7.452    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7_n_0
    SLICE_X9Y228         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.267     7.719 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3/CO[3]
                         net (fo=17, routed)          0.753     8.472    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3_n_0
    SLICE_X13Y226        LUT5 (Prop_lut5_I3_O)        0.043     8.515 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp[3]_i_1/O
                         net (fo=1, routed)           0.000     8.515    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/p_0_in__0[3]
    SLICE_X13Y226        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[3]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.287    37.736    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y226        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[3]/C
                         clock pessimism              0.766    38.502    
                         clock uncertainty           -0.035    38.467    
    SLICE_X13Y226        FDRE (Setup_fdre_C_D)        0.034    38.501    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[3]
  -------------------------------------------------------------------
                         required time                         38.501    
                         arrival time                          -8.515    
  -------------------------------------------------------------------
                         slack                                 29.986    

Slack (MET) :             29.987ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[6]/D
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.988ns  (logic 0.681ns (22.792%)  route 2.307ns (77.208%))
  Logic Levels:           4  (CARRY4=1 LUT5=2 LUT6=1)
  Clock Path Skew:        -0.023ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.736ns = ( 37.736 - 33.000 ) 
    Source Clock Delay      (SCD):    5.525ns
    Clock Pessimism Removal (CPR):    0.766ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.457     5.525    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y227        FDRE (Prop_fdre_C_Q)         0.204     5.729 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/Q
                         net (fo=57, routed)          1.016     6.745    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state[0]
    SLICE_X11Y228        LUT5 (Prop_lut5_I0_O)        0.124     6.869 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid_inferred_i_9/O
                         net (fo=2, routed)           0.539     7.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid[23]
    SLICE_X9Y228         LUT6 (Prop_lut6_I1_O)        0.043     7.452 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7/O
                         net (fo=1, routed)           0.000     7.452    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7_n_0
    SLICE_X9Y228         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.267     7.719 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3/CO[3]
                         net (fo=17, routed)          0.751     8.470    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3_n_0
    SLICE_X13Y226        LUT5 (Prop_lut5_I3_O)        0.043     8.513 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp[6]_i_1/O
                         net (fo=1, routed)           0.000     8.513    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/p_0_in__0[6]
    SLICE_X13Y226        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[6]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.287    37.736    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y226        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[6]/C
                         clock pessimism              0.766    38.502    
                         clock uncertainty           -0.035    38.467    
    SLICE_X13Y226        FDRE (Setup_fdre_C_D)        0.033    38.500    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_flags_temp_reg[6]
  -------------------------------------------------------------------
                         required time                         38.500    
                         arrival time                          -8.513    
  -------------------------------------------------------------------
                         slack                                 29.987    

Slack (MET) :             29.988ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[3]/D
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        3.021ns  (logic 0.681ns (22.540%)  route 2.340ns (77.460%))
  Logic Levels:           4  (CARRY4=1 LUT5=2 LUT6=1)
  Clock Path Skew:        -0.021ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.737ns = ( 37.737 - 33.000 ) 
    Source Clock Delay      (SCD):    5.525ns
    Clock Pessimism Removal (CPR):    0.767ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.457     5.525    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y227        FDRE (Prop_fdre_C_Q)         0.204     5.729 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state_reg[0]/Q
                         net (fo=57, routed)          1.016     6.745    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/state[0]
    SLICE_X11Y228        LUT5 (Prop_lut5_I0_O)        0.124     6.869 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid_inferred_i_9/O
                         net (fo=2, routed)           0.539     7.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/curid[23]
    SLICE_X9Y228         LUT6 (Prop_lut6_I1_O)        0.043     7.452 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7/O
                         net (fo=1, routed)           0.000     7.452    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[5]_i_7_n_0
    SLICE_X9Y228         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.267     7.719 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3/CO[3]
                         net (fo=17, routed)          0.784     8.503    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[5]_i_3_n_0
    SLICE_X12Y227        LUT5 (Prop_lut5_I3_O)        0.043     8.546 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[3]_i_1/O
                         net (fo=1, routed)           0.000     8.546    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[3]_i_1_n_0
    SLICE_X12Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[3]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.288    37.737    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X12Y227        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[3]/C
                         clock pessimism              0.767    38.504    
                         clock uncertainty           -0.035    38.469    
    SLICE_X12Y227        FDRE (Setup_fdre_C_D)        0.066    38.535    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[3]
  -------------------------------------------------------------------
                         required time                         38.535    
                         arrival time                          -8.546    
  -------------------------------------------------------------------
                         slack                                 29.988    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.072ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[4]/C
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB_D1/I
                            (rising edge-triggered cell RAMD32 clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.201ns  (logic 0.100ns (49.635%)  route 0.101ns (50.365%))
  Logic Levels:           0  
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.471ns
    Source Clock Delay      (SCD):    2.868ns
    Clock Pessimism Removal (CPR):    0.589ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.668     2.868    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/m_bscan_tck[0]
    SLICE_X13Y210        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[4]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y210        FDCE (Prop_fdce_C_Q)         0.100     2.968 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[4]/Q
                         net (fo=2, routed)           0.101     3.069    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/DIB1
    SLICE_X14Y210        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.894     3.471    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/WCLK
    SLICE_X14Y210        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB_D1/CLK
                         clock pessimism             -0.589     2.882    
    SLICE_X14Y210        RAMD32 (Hold_ramd32_CLK_I)
                                                      0.115     2.997    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB_D1
  -------------------------------------------------------------------
                         required time                         -2.997    
                         arrival time                           3.069    
  -------------------------------------------------------------------
                         slack                                  0.072    

Slack (MET) :             0.073ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMC_D1/I
                            (rising edge-triggered cell RAMD32 clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.152ns  (logic 0.091ns (59.971%)  route 0.061ns (40.029%))
  Logic Levels:           0  
  Clock Path Skew:        0.011ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.471ns
    Source Clock Delay      (SCD):    2.868ns
    Clock Pessimism Removal (CPR):    0.592ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.668     2.868    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/m_bscan_tck[0]
    SLICE_X15Y210        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y210        FDCE (Prop_fdce_C_Q)         0.091     2.959 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[6]/Q
                         net (fo=2, routed)           0.061     3.020    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/DIC1
    SLICE_X14Y210        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMC_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.894     3.471    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/WCLK
    SLICE_X14Y210        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMC_D1/CLK
                         clock pessimism             -0.592     2.879    
    SLICE_X14Y210        RAMD32 (Hold_ramd32_CLK_I)
                                                      0.068     2.947    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMC_D1
  -------------------------------------------------------------------
                         required time                         -2.947    
                         arrival time                           3.020    
  -------------------------------------------------------------------
                         slack                                  0.073    

Slack (MET) :             0.074ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA/I
                            (rising edge-triggered cell RAMD32 clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.219ns  (logic 0.118ns (53.765%)  route 0.101ns (46.235%))
  Logic Levels:           0  
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.471ns
    Source Clock Delay      (SCD):    2.868ns
    Clock Pessimism Removal (CPR):    0.589ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.668     2.868    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/m_bscan_tck[0]
    SLICE_X12Y210        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X12Y210        FDCE (Prop_fdce_C_Q)         0.118     2.986 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[1]/Q
                         net (fo=2, routed)           0.101     3.087    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/DIA0
    SLICE_X14Y210        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA/I
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.894     3.471    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/WCLK
    SLICE_X14Y210        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA/CLK
                         clock pessimism             -0.589     2.882    
    SLICE_X14Y210        RAMD32 (Hold_ramd32_CLK_I)
                                                      0.131     3.013    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA
  -------------------------------------------------------------------
                         required time                         -3.013    
                         arrival time                           3.087    
  -------------------------------------------------------------------
                         slack                                  0.074    

Slack (MET) :             0.077ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[11]/C
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMC/I
                            (rising edge-triggered cell RAMD32 clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.220ns  (logic 0.118ns (53.566%)  route 0.102ns (46.434%))
  Logic Levels:           0  
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.869ns
    Clock Pessimism Removal (CPR):    0.589ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.669     2.869    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/m_bscan_tck[0]
    SLICE_X12Y208        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X12Y208        FDCE (Prop_fdce_C_Q)         0.118     2.987 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[11]/Q
                         net (fo=2, routed)           0.102     3.089    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/DIC0
    SLICE_X14Y208        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMC/I
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/WCLK
    SLICE_X14Y208        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMC/CLK
                         clock pessimism             -0.589     2.883    
    SLICE_X14Y208        RAMD32 (Hold_ramd32_CLK_I)
                                                      0.129     3.012    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMC
  -------------------------------------------------------------------
                         required time                         -3.012    
                         arrival time                           3.089    
  -------------------------------------------------------------------
                         slack                                  0.077    

Slack (MET) :             0.079ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[8]/C
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMA_D1/I
                            (rising edge-triggered cell RAMD32 clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.201ns  (logic 0.100ns (49.680%)  route 0.101ns (50.320%))
  Logic Levels:           0  
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.869ns
    Clock Pessimism Removal (CPR):    0.589ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.669     2.869    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/m_bscan_tck[0]
    SLICE_X13Y208        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[8]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y208        FDCE (Prop_fdce_C_Q)         0.100     2.969 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[8]/Q
                         net (fo=2, routed)           0.101     3.070    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/DIA1
    SLICE_X14Y208        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMA_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/WCLK
    SLICE_X14Y208        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMA_D1/CLK
                         clock pessimism             -0.589     2.883    
    SLICE_X14Y208        RAMD32 (Hold_ramd32_CLK_I)
                                                      0.108     2.991    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMA_D1
  -------------------------------------------------------------------
                         required time                         -2.991    
                         arrival time                           3.070    
  -------------------------------------------------------------------
                         slack                                  0.079    

Slack (MET) :             0.090ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[10]/C
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMB_D1/I
                            (rising edge-triggered cell RAMD32 clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.219ns  (logic 0.118ns (53.765%)  route 0.101ns (46.235%))
  Logic Levels:           0  
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.869ns
    Clock Pessimism Removal (CPR):    0.589ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.669     2.869    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/m_bscan_tck[0]
    SLICE_X12Y208        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[10]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X12Y208        FDCE (Prop_fdce_C_Q)         0.118     2.987 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[10]/Q
                         net (fo=2, routed)           0.101     3.088    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/DIB1
    SLICE_X14Y208        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMB_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/WCLK
    SLICE_X14Y208        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMB_D1/CLK
                         clock pessimism             -0.589     2.883    
    SLICE_X14Y208        RAMD32 (Hold_ramd32_CLK_I)
                                                      0.115     2.998    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMB_D1
  -------------------------------------------------------------------
                         required time                         -2.998    
                         arrival time                           3.088    
  -------------------------------------------------------------------
                         slack                                  0.090    

Slack (MET) :             0.094ns  (arrival time - required time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[1]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[1]/D
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.164ns  (logic 0.100ns (60.823%)  route 0.064ns (39.177%))
  Logic Levels:           0  
  Clock Path Skew:        0.011ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.461ns
    Source Clock Delay      (SCD):    2.860ns
    Clock Pessimism Removal (CPR):    0.590ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.660     2.860    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y228        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y228        FDRE (Prop_fdre_C_Q)         0.100     2.960 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp_reg[1]/Q
                         net (fo=2, routed)           0.064     3.024    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_temp[1]
    SLICE_X12Y228        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[1]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.884     3.461    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X12Y228        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[1]/C
                         clock pessimism             -0.590     2.871    
    SLICE_X12Y228        FDRE (Hold_fdre_C_D)         0.059     2.930    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[1]
  -------------------------------------------------------------------
                         required time                         -2.930    
                         arrival time                           3.024    
  -------------------------------------------------------------------
                         slack                                  0.094    

Slack (MET) :             0.096ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[9]/C
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMB/I
                            (rising edge-triggered cell RAMD32 clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.242ns  (logic 0.100ns (41.284%)  route 0.142ns (58.716%))
  Logic Levels:           0  
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.869ns
    Clock Pessimism Removal (CPR):    0.589ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.669     2.869    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/m_bscan_tck[0]
    SLICE_X13Y208        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[9]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y208        FDCE (Prop_fdce_C_Q)         0.100     2.969 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/shift_reg_in_reg[9]/Q
                         net (fo=2, routed)           0.142     3.111    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/DIB0
    SLICE_X14Y208        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMB/I
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/WCLK
    SLICE_X14Y208        RAMD32                                       r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMB/CLK
                         clock pessimism             -0.589     2.883    
    SLICE_X14Y208        RAMD32 (Hold_ramd32_CLK_I)
                                                      0.132     3.015    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMB
  -------------------------------------------------------------------
                         required time                         -3.015    
                         arrival time                           3.111    
  -------------------------------------------------------------------
                         slack                                  0.096    

Slack (MET) :             0.108ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/Q_reg_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/Q_reg_reg[0]/D
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.155ns  (logic 0.100ns (64.566%)  route 0.055ns (35.434%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.504ns
    Source Clock Delay      (SCD):    2.899ns
    Clock Pessimism Removal (CPR):    0.605ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.699     2.899    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/m_bscan_tck[0]
    SLICE_X5Y204         FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/Q_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X5Y204         FDCE (Prop_fdce_C_Q)         0.100     2.999 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/Q_reg_reg[0]/Q
                         net (fo=1, routed)           0.055     3.054    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/Q_reg_reg[3]_0[0]
    SLICE_X5Y204         FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/Q_reg_reg[0]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.927     3.504    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/m_bscan_tck[0]
    SLICE_X5Y204         FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/Q_reg_reg[0]/C
                         clock pessimism             -0.605     2.899    
    SLICE_X5Y204         FDCE (Hold_fdce_C_D)         0.047     2.946    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/Q_reg_reg[0]
  -------------------------------------------------------------------
                         required time                         -2.946    
                         arrival time                           3.054    
  -------------------------------------------------------------------
                         slack                                  0.108    

Slack (MET) :             0.108ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/Q_reg_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/Q_reg_reg[1]/D
                            (rising edge-triggered cell FDCE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.155ns  (logic 0.100ns (64.566%)  route 0.055ns (35.434%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.504ns
    Source Clock Delay      (SCD):    2.899ns
    Clock Pessimism Removal (CPR):    0.605ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.699     2.899    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/m_bscan_tck[0]
    SLICE_X5Y206         FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/Q_reg_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X5Y206         FDCE (Prop_fdce_C_Q)         0.100     2.999 r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/Q_reg_reg[1]/Q
                         net (fo=1, routed)           0.055     3.054    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/Q_reg_reg[3]_0[1]
    SLICE_X5Y206         FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/Q_reg_reg[1]/D
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.927     3.504    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/m_bscan_tck[0]
    SLICE_X5Y206         FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/Q_reg_reg[1]/C
                         clock pessimism             -0.605     2.899    
    SLICE_X5Y206         FDCE (Hold_fdce_C_D)         0.047     2.946    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[2].rd_stg_inst/Q_reg_reg[1]
  -------------------------------------------------------------------
                         required time                         -2.946    
                         arrival time                           3.054    
  -------------------------------------------------------------------
                         slack                                  0.108    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
Waveform(ns):       { 0.000 16.500 }
Period(ns):         33.000
Sources:            { dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK }

Check Type        Corner  Lib Pin     Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location       Pin
Min Period        n/a     BUFG/I      n/a            1.409         33.000      31.591     BUFGCTRL_X0Y0  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/I
Min Period        n/a     FDRE/C      n/a            0.750         33.000      32.250     SLICE_X12Y216  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/shift_reg_in_reg[1]/C
Min Period        n/a     FDRE/C      n/a            0.750         33.000      32.250     SLICE_X15Y215  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD2/shift_reg_in_reg[3]/C
Min Period        n/a     FDRE/C      n/a            0.750         33.000      32.250     SLICE_X13Y216  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD2/stat_reg_1_reg[0]/C
Min Period        n/a     FDRE/C      n/a            0.750         33.000      32.250     SLICE_X12Y215  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD2/stat_reg_1_reg[1]/C
Min Period        n/a     FDRE/C      n/a            0.750         33.000      32.250     SLICE_X13Y214  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD2/stat_reg_1_reg[2]/C
Min Period        n/a     FDRE/C      n/a            0.750         33.000      32.250     SLICE_X14Y215  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD2/stat_reg_1_reg[3]/C
Min Period        n/a     FDRE/C      n/a            0.750         33.000      32.250     SLICE_X16Y218  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD3/shift_reg_in_reg[2]/C
Min Period        n/a     FDRE/C      n/a            0.750         33.000      32.250     SLICE_X16Y215  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD3/stat_reg_1_reg[0]/C
Min Period        n/a     FDRE/C      n/a            0.750         33.000      32.250     SLICE_X17Y216  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD3/stat_reg_1_reg[1]/C
Low Pulse Width   Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y209  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_12_15/RAMA/CLK
Low Pulse Width   Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y209  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_12_15/RAMA_D1/CLK
Low Pulse Width   Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y209  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_12_15/RAMB/CLK
Low Pulse Width   Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y209  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_12_15/RAMB_D1/CLK
Low Pulse Width   Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y209  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_12_15/RAMC/CLK
Low Pulse Width   Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y209  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_12_15/RAMC_D1/CLK
Low Pulse Width   Slow    RAMS32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y209  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_12_15/RAMD/CLK
Low Pulse Width   Slow    RAMS32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y209  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_12_15/RAMD_D1/CLK
Low Pulse Width   Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y208  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMA/CLK
Low Pulse Width   Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y208  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_6_11/RAMA_D1/CLK
High Pulse Width  Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y210  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA/CLK
High Pulse Width  Fast    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y210  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA/CLK
High Pulse Width  Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y210  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA_D1/CLK
High Pulse Width  Fast    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y210  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMA_D1/CLK
High Pulse Width  Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y210  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB/CLK
High Pulse Width  Fast    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y210  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB/CLK
High Pulse Width  Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y210  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB_D1/CLK
High Pulse Width  Fast    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y210  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMB_D1/CLK
High Pulse Width  Slow    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y210  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMC/CLK
High Pulse Width  Fast    RAMD32/CLK  n/a            0.768         16.500      15.732     SLICE_X14Y210  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.mem/gdm.dm_gen.dm/RAM_reg_0_15_0_5/RAMC/CLK



---------------------------------------------------------------------------------------------------
From Clock:  
  To Clock:  clk_fpga_0

Setup :            0  Failing Endpoints,  Worst Slack        9.373ns,  Total Violation        0.000ns
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             9.373ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/cdc_sync_fifo_ram_reg[0]/C
                            (rising edge-triggered cell FDRE)
  Destination:            i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/eot_reg/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.692ns  (logic 0.266ns (38.433%)  route 0.426ns (61.567%))
  Logic Levels:           2  (FDRE=1 LUT3=1)
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X35Y266        FDRE                         0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/cdc_sync_fifo_ram_reg[0]/C
    SLICE_X35Y266        FDRE (Prop_fdre_C_Q)         0.223     0.223 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/cdc_sync_fifo_ram_reg[0]/Q
                         net (fo=2, routed)           0.426     0.649    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/i_waddr_sync/response_dest_resp_eot
    SLICE_X36Y265        LUT3 (Prop_lut3_I2_O)        0.043     0.692 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/i_waddr_sync/eot_i_1/O
                         net (fo=1, routed)           0.000     0.692    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/eot0
    SLICE_X36Y265        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/eot_reg/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X36Y265        FDRE (Setup_fdre_C_D)        0.065    10.065    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/eot_reg
  -------------------------------------------------------------------
                         required time                         10.065    
                         arrival time                          -0.692    
  -------------------------------------------------------------------
                         slack                                  9.373    





---------------------------------------------------------------------------------------------------
Path Group:  **async_default**
From Clock:  clk_fpga_0
  To Clock:  clk_fpga_0

Setup :            0  Failing Endpoints,  Worst Slack        2.616ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.270ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             2.616ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg/CLR
                            (recovery check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        7.018ns  (logic 0.266ns (3.790%)  route 6.752ns (96.210%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        0.000ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.818ns = ( 12.818 - 10.000 ) 
    Source Clock Delay      (SCD):    2.999ns
    Clock Pessimism Removal (CPR):    0.181ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.567     2.999    i_system_wrapper/system_i/sys_rstgen/U0/slowest_sync_clk
    SLICE_X43Y294        FDRE                                         r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y294        FDRE (Prop_fdre_C_Q)         0.223     3.222 r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/Q
                         net (fo=86, routed)          3.403     6.625    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/s_axi_aresetn
    SLICE_X100Y222       LUT1 (Prop_lut1_I0_O)        0.043     6.668 f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/up_axi_wready_int_i_2/O
                         net (fo=259, routed)         3.348    10.017    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]_2
    SLICE_X53Y300        FDCE                                         f  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.494    12.818    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/s_axi_aclk
    SLICE_X53Y300        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg/C
                         clock pessimism              0.181    12.999    
                         clock uncertainty           -0.154    12.845    
    SLICE_X53Y300        FDCE (Recov_fdce_C_CLR)     -0.212    12.633    i_system_wrapper/system_i/axi_ad9361/inst/i_up_axi/up_axi_wready_int_reg
  -------------------------------------------------------------------
                         required time                         12.633    
                         arrival time                         -10.017    
  -------------------------------------------------------------------
                         slack                                  2.616    

Slack (MET) :             2.920ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[12]/CLR
                            (recovery check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.394ns  (logic 0.266ns (4.160%)  route 6.128ns (95.840%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.320ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.528ns = ( 12.528 - 10.000 ) 
    Source Clock Delay      (SCD):    2.999ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.567     2.999    i_system_wrapper/system_i/sys_rstgen/U0/slowest_sync_clk
    SLICE_X43Y294        FDRE                                         r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y294        FDRE (Prop_fdre_C_Q)         0.223     3.222 r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/Q
                         net (fo=86, routed)          4.189     7.411    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/s_axi_aresetn
    SLICE_X94Y240        LUT1 (Prop_lut1_I0_O)        0.043     7.454 f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/up_dac_gpio_out_int[16]_i_1/O
                         net (fo=125, routed)         1.939     9.393    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]_4
    SLICE_X84Y220        FDCE                                         f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[12]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.204    12.528    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/s_axi_aclk
    SLICE_X84Y220        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[12]/C
                         clock pessimism              0.151    12.679    
                         clock uncertainty           -0.154    12.525    
    SLICE_X84Y220        FDCE (Recov_fdce_C_CLR)     -0.212    12.313    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[12]
  -------------------------------------------------------------------
                         required time                         12.313    
                         arrival time                          -9.393    
  -------------------------------------------------------------------
                         slack                                  2.920    

Slack (MET) :             2.920ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[13]/CLR
                            (recovery check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.394ns  (logic 0.266ns (4.160%)  route 6.128ns (95.840%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.320ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.528ns = ( 12.528 - 10.000 ) 
    Source Clock Delay      (SCD):    2.999ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.567     2.999    i_system_wrapper/system_i/sys_rstgen/U0/slowest_sync_clk
    SLICE_X43Y294        FDRE                                         r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y294        FDRE (Prop_fdre_C_Q)         0.223     3.222 r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/Q
                         net (fo=86, routed)          4.189     7.411    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/s_axi_aresetn
    SLICE_X94Y240        LUT1 (Prop_lut1_I0_O)        0.043     7.454 f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/up_dac_gpio_out_int[16]_i_1/O
                         net (fo=125, routed)         1.939     9.393    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]_4
    SLICE_X84Y220        FDCE                                         f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[13]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.204    12.528    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/s_axi_aclk
    SLICE_X84Y220        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[13]/C
                         clock pessimism              0.151    12.679    
                         clock uncertainty           -0.154    12.525    
    SLICE_X84Y220        FDCE (Recov_fdce_C_CLR)     -0.212    12.313    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[13]
  -------------------------------------------------------------------
                         required time                         12.313    
                         arrival time                          -9.393    
  -------------------------------------------------------------------
                         slack                                  2.920    

Slack (MET) :             2.920ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[14]/CLR
                            (recovery check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.394ns  (logic 0.266ns (4.160%)  route 6.128ns (95.840%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.320ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.528ns = ( 12.528 - 10.000 ) 
    Source Clock Delay      (SCD):    2.999ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.567     2.999    i_system_wrapper/system_i/sys_rstgen/U0/slowest_sync_clk
    SLICE_X43Y294        FDRE                                         r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y294        FDRE (Prop_fdre_C_Q)         0.223     3.222 r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/Q
                         net (fo=86, routed)          4.189     7.411    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/s_axi_aresetn
    SLICE_X94Y240        LUT1 (Prop_lut1_I0_O)        0.043     7.454 f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/up_dac_gpio_out_int[16]_i_1/O
                         net (fo=125, routed)         1.939     9.393    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]_4
    SLICE_X84Y220        FDCE                                         f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[14]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.204    12.528    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/s_axi_aclk
    SLICE_X84Y220        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[14]/C
                         clock pessimism              0.151    12.679    
                         clock uncertainty           -0.154    12.525    
    SLICE_X84Y220        FDCE (Recov_fdce_C_CLR)     -0.212    12.313    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[14]
  -------------------------------------------------------------------
                         required time                         12.313    
                         arrival time                          -9.393    
  -------------------------------------------------------------------
                         slack                                  2.920    

Slack (MET) :             2.920ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[15]/CLR
                            (recovery check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.394ns  (logic 0.266ns (4.160%)  route 6.128ns (95.840%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.320ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.528ns = ( 12.528 - 10.000 ) 
    Source Clock Delay      (SCD):    2.999ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.567     2.999    i_system_wrapper/system_i/sys_rstgen/U0/slowest_sync_clk
    SLICE_X43Y294        FDRE                                         r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y294        FDRE (Prop_fdre_C_Q)         0.223     3.222 r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/Q
                         net (fo=86, routed)          4.189     7.411    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/s_axi_aresetn
    SLICE_X94Y240        LUT1 (Prop_lut1_I0_O)        0.043     7.454 f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_2/i_up_dac_channel/up_dac_gpio_out_int[16]_i_1/O
                         net (fo=125, routed)         1.939     9.393    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]_4
    SLICE_X84Y220        FDCE                                         f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[15]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.204    12.528    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/s_axi_aclk
    SLICE_X84Y220        FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[15]/C
                         clock pessimism              0.151    12.679    
                         clock uncertainty           -0.154    12.525    
    SLICE_X84Y220        FDCE (Recov_fdce_C_CLR)     -0.212    12.313    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_0/i_up_dac_channel/up_dac_iqcor_coeff_2_reg[15]
  -------------------------------------------------------------------
                         required time                         12.313    
                         arrival time                          -9.393    
  -------------------------------------------------------------------
                         slack                                  2.920    

Slack (MET) :             2.928ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[10]/CLR
                            (recovery check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.410ns  (logic 0.266ns (4.150%)  route 6.144ns (95.850%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.296ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.552ns = ( 12.552 - 10.000 ) 
    Source Clock Delay      (SCD):    2.999ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.567     2.999    i_system_wrapper/system_i/sys_rstgen/U0/slowest_sync_clk
    SLICE_X43Y294        FDRE                                         r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y294        FDRE (Prop_fdre_C_Q)         0.223     3.222 r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/Q
                         net (fo=86, routed)          4.081     7.303    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aresetn
    SLICE_X96Y243        LUT1 (Prop_lut1_I0_O)        0.043     7.346 f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_gpio_out_int[12]_i_1/O
                         net (fo=125, routed)         2.064     9.409    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]_5
    SLICE_X108Y216       FDCE                                         f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[10]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.228    12.552    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/s_axi_aclk
    SLICE_X108Y216       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[10]/C
                         clock pessimism              0.151    12.703    
                         clock uncertainty           -0.154    12.549    
    SLICE_X108Y216       FDCE (Recov_fdce_C_CLR)     -0.212    12.337    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[10]
  -------------------------------------------------------------------
                         required time                         12.337    
                         arrival time                          -9.409    
  -------------------------------------------------------------------
                         slack                                  2.928    

Slack (MET) :             2.928ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[11]/CLR
                            (recovery check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.410ns  (logic 0.266ns (4.150%)  route 6.144ns (95.850%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.296ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.552ns = ( 12.552 - 10.000 ) 
    Source Clock Delay      (SCD):    2.999ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.567     2.999    i_system_wrapper/system_i/sys_rstgen/U0/slowest_sync_clk
    SLICE_X43Y294        FDRE                                         r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y294        FDRE (Prop_fdre_C_Q)         0.223     3.222 r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/Q
                         net (fo=86, routed)          4.081     7.303    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aresetn
    SLICE_X96Y243        LUT1 (Prop_lut1_I0_O)        0.043     7.346 f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_gpio_out_int[12]_i_1/O
                         net (fo=125, routed)         2.064     9.409    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]_5
    SLICE_X108Y216       FDCE                                         f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[11]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.228    12.552    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/s_axi_aclk
    SLICE_X108Y216       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[11]/C
                         clock pessimism              0.151    12.703    
                         clock uncertainty           -0.154    12.549    
    SLICE_X108Y216       FDCE (Recov_fdce_C_CLR)     -0.212    12.337    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[11]
  -------------------------------------------------------------------
                         required time                         12.337    
                         arrival time                          -9.409    
  -------------------------------------------------------------------
                         slack                                  2.928    

Slack (MET) :             2.928ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[8]/CLR
                            (recovery check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.410ns  (logic 0.266ns (4.150%)  route 6.144ns (95.850%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.296ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.552ns = ( 12.552 - 10.000 ) 
    Source Clock Delay      (SCD):    2.999ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.567     2.999    i_system_wrapper/system_i/sys_rstgen/U0/slowest_sync_clk
    SLICE_X43Y294        FDRE                                         r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y294        FDRE (Prop_fdre_C_Q)         0.223     3.222 r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/Q
                         net (fo=86, routed)          4.081     7.303    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aresetn
    SLICE_X96Y243        LUT1 (Prop_lut1_I0_O)        0.043     7.346 f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_gpio_out_int[12]_i_1/O
                         net (fo=125, routed)         2.064     9.409    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]_5
    SLICE_X108Y216       FDCE                                         f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[8]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.228    12.552    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/s_axi_aclk
    SLICE_X108Y216       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[8]/C
                         clock pessimism              0.151    12.703    
                         clock uncertainty           -0.154    12.549    
    SLICE_X108Y216       FDCE (Recov_fdce_C_CLR)     -0.212    12.337    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[8]
  -------------------------------------------------------------------
                         required time                         12.337    
                         arrival time                          -9.409    
  -------------------------------------------------------------------
                         slack                                  2.928    

Slack (MET) :             2.928ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[9]/CLR
                            (recovery check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.410ns  (logic 0.266ns (4.150%)  route 6.144ns (95.850%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.296ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.552ns = ( 12.552 - 10.000 ) 
    Source Clock Delay      (SCD):    2.999ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.567     2.999    i_system_wrapper/system_i/sys_rstgen/U0/slowest_sync_clk
    SLICE_X43Y294        FDRE                                         r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y294        FDRE (Prop_fdre_C_Q)         0.223     3.222 r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/Q
                         net (fo=86, routed)          4.081     7.303    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aresetn
    SLICE_X96Y243        LUT1 (Prop_lut1_I0_O)        0.043     7.346 f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_gpio_out_int[12]_i_1/O
                         net (fo=125, routed)         2.064     9.409    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]_5
    SLICE_X108Y216       FDCE                                         f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[9]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.228    12.552    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/s_axi_aclk
    SLICE_X108Y216       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[9]/C
                         clock pessimism              0.151    12.703    
                         clock uncertainty           -0.154    12.549    
    SLICE_X108Y216       FDCE (Recov_fdce_C_CLR)     -0.212    12.337    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_incr_2_reg[9]
  -------------------------------------------------------------------
                         required time                         12.337    
                         arrival time                          -9.409    
  -------------------------------------------------------------------
                         slack                                  2.928    

Slack (MET) :             2.933ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_scale_1_reg[11]/CLR
                            (recovery check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.406ns  (logic 0.266ns (4.152%)  route 6.140ns (95.848%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.295ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.553ns = ( 12.553 - 10.000 ) 
    Source Clock Delay      (SCD):    2.999ns
    Clock Pessimism Removal (CPR):    0.151ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.339     1.339    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.093     1.432 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.567     2.999    i_system_wrapper/system_i/sys_rstgen/U0/slowest_sync_clk
    SLICE_X43Y294        FDRE                                         r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y294        FDRE (Prop_fdre_C_Q)         0.223     3.222 r  i_system_wrapper/system_i/sys_rstgen/U0/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]/Q
                         net (fo=86, routed)          4.081     7.303    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/s_axi_aresetn
    SLICE_X96Y243        LUT1 (Prop_lut1_I0_O)        0.043     7.346 f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_3/i_up_dac_channel/up_dac_gpio_out_int[12]_i_1/O
                         net (fo=125, routed)         2.059     9.405    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/ACTIVE_LOW_PR_OUT_DFF[0].peripheral_aresetn_reg[0]_5
    SLICE_X107Y215       FDCE                                         f  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_scale_1_reg[11]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           1.241    11.241    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.083    11.324 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        1.229    12.553    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/s_axi_aclk
    SLICE_X107Y215       FDCE                                         r  i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_scale_1_reg[11]/C
                         clock pessimism              0.151    12.704    
                         clock uncertainty           -0.154    12.550    
    SLICE_X107Y215       FDCE (Recov_fdce_C_CLR)     -0.212    12.338    i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_tx_channel_1/i_up_dac_channel/up_dac_dds_scale_1_reg[11]
  -------------------------------------------------------------------
                         required time                         12.338    
                         arrival time                          -9.405    
  -------------------------------------------------------------------
                         slack                                  2.933    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.270ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_reg_reg[2]/C
                            (rising edge-triggered cell FDPE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_fb_i_reg/PRE
                            (removal check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.250ns  (logic 0.100ns (40.047%)  route 0.150ns (59.953%))
  Logic Levels:           0  
  Clock Path Skew:        0.032ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.691ns
    Source Clock Delay      (SCD):    1.418ns
    Clock Pessimism Removal (CPR):    0.241ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.667     1.418    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/clk
    SLICE_X15Y208        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_reg_reg[2]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y208        FDPE (Prop_fdpe_C_Q)         0.100     1.518 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_reg_reg[2]/Q
                         net (fo=10, routed)          0.150     1.668    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/AR[0]
    SLICE_X16Y210        FDPE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_fb_i_reg/PRE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.892     1.691    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/clk
    SLICE_X16Y210        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_fb_i_reg/C
                         clock pessimism             -0.241     1.450    
    SLICE_X16Y210        FDPE (Remov_fdpe_C_PRE)     -0.052     1.398    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_fb_i_reg
  -------------------------------------------------------------------
                         required time                         -1.398    
                         arrival time                           1.668    
  -------------------------------------------------------------------
                         slack                                  0.270    

Slack (MET) :             0.270ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_reg_reg[2]/C
                            (rising edge-triggered cell FDPE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_i_reg/PRE
                            (removal check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.250ns  (logic 0.100ns (40.047%)  route 0.150ns (59.953%))
  Logic Levels:           0  
  Clock Path Skew:        0.032ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.691ns
    Source Clock Delay      (SCD):    1.418ns
    Clock Pessimism Removal (CPR):    0.241ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.667     1.418    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/clk
    SLICE_X15Y208        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_reg_reg[2]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y208        FDPE (Prop_fdpe_C_Q)         0.100     1.518 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_reg_reg[2]/Q
                         net (fo=10, routed)          0.150     1.668    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/AR[0]
    SLICE_X16Y210        FDPE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_i_reg/PRE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.892     1.691    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/clk
    SLICE_X16Y210        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_i_reg/C
                         clock pessimism             -0.241     1.450    
    SLICE_X16Y210        FDPE (Remov_fdpe_C_PRE)     -0.052     1.398    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.rd/gras.rsts/ram_empty_i_reg
  -------------------------------------------------------------------
                         required time                         -1.398    
                         arrival time                           1.668    
  -------------------------------------------------------------------
                         slack                                  0.270    

Slack (MET) :             0.298ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/in_idle_mode_reg/CLR
                            (removal check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.261ns  (logic 0.100ns (38.357%)  route 0.161ns (61.643%))
  Logic Levels:           0  
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.688ns
    Source Clock Delay      (SCD):    1.415ns
    Clock Pessimism Removal (CPR):    0.260ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.664     1.415    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/clk
    SLICE_X15Y213        FDRE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y213        FDRE (Prop_fdre_C_Q)         0.100     1.515 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/Q
                         net (fo=162, routed)         0.161     1.676    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/out[0]
    SLICE_X12Y213        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/in_idle_mode_reg/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.889     1.688    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/clk
    SLICE_X12Y213        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/in_idle_mode_reg/C
                         clock pessimism             -0.260     1.428    
    SLICE_X12Y213        FDCE (Remov_fdce_C_CLR)     -0.050     1.378    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/in_idle_mode_reg
  -------------------------------------------------------------------
                         required time                         -1.378    
                         arrival time                           1.676    
  -------------------------------------------------------------------
                         slack                                  0.298    

Slack (MET) :             0.298ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/in_read_mode_reg/CLR
                            (removal check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.261ns  (logic 0.100ns (38.357%)  route 0.161ns (61.643%))
  Logic Levels:           0  
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.688ns
    Source Clock Delay      (SCD):    1.415ns
    Clock Pessimism Removal (CPR):    0.260ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.664     1.415    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/clk
    SLICE_X15Y213        FDRE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y213        FDRE (Prop_fdre_C_Q)         0.100     1.515 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/Q
                         net (fo=162, routed)         0.161     1.676    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/out[0]
    SLICE_X12Y213        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/in_read_mode_reg/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.889     1.688    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/clk
    SLICE_X12Y213        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/in_read_mode_reg/C
                         clock pessimism             -0.260     1.428    
    SLICE_X12Y213        FDCE (Remov_fdce_C_CLR)     -0.050     1.378    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/in_read_mode_reg
  -------------------------------------------------------------------
                         required time                         -1.378    
                         arrival time                           1.676    
  -------------------------------------------------------------------
                         slack                                  0.298    

Slack (MET) :             0.298ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/inc_addr_r_reg/CLR
                            (removal check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.261ns  (logic 0.100ns (38.357%)  route 0.161ns (61.643%))
  Logic Levels:           0  
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.688ns
    Source Clock Delay      (SCD):    1.415ns
    Clock Pessimism Removal (CPR):    0.260ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.664     1.415    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/clk
    SLICE_X15Y213        FDRE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y213        FDRE (Prop_fdre_C_Q)         0.100     1.515 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/Q
                         net (fo=162, routed)         0.161     1.676    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/out[0]
    SLICE_X12Y213        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/inc_addr_r_reg/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.889     1.688    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/clk
    SLICE_X12Y213        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/inc_addr_r_reg/C
                         clock pessimism             -0.260     1.428    
    SLICE_X12Y213        FDCE (Remov_fdce_C_CLR)     -0.050     1.378    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/inc_addr_r_reg
  -------------------------------------------------------------------
                         required time                         -1.378    
                         arrival time                           1.676    
  -------------------------------------------------------------------
                         slack                                  0.298    

Slack (MET) :             0.298ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/sl_dwe_r_reg[0]/CLR
                            (removal check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.261ns  (logic 0.100ns (38.357%)  route 0.161ns (61.643%))
  Logic Levels:           0  
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.688ns
    Source Clock Delay      (SCD):    1.415ns
    Clock Pessimism Removal (CPR):    0.260ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.664     1.415    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/clk
    SLICE_X15Y213        FDRE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y213        FDRE (Prop_fdre_C_Q)         0.100     1.515 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/Q
                         net (fo=162, routed)         0.161     1.676    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/out[0]
    SLICE_X12Y213        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/sl_dwe_r_reg[0]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.889     1.688    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/clk
    SLICE_X12Y213        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/sl_dwe_r_reg[0]/C
                         clock pessimism             -0.260     1.428    
    SLICE_X12Y213        FDCE (Remov_fdce_C_CLR)     -0.050     1.378    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/sl_dwe_r_reg[0]
  -------------------------------------------------------------------
                         required time                         -1.378    
                         arrival time                           1.676    
  -------------------------------------------------------------------
                         slack                                  0.298    

Slack (MET) :             0.312ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/dec_wdc_r_reg/CLR
                            (removal check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.275ns  (logic 0.100ns (36.313%)  route 0.175ns (63.687%))
  Logic Levels:           0  
  Clock Path Skew:        0.032ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.688ns
    Source Clock Delay      (SCD):    1.415ns
    Clock Pessimism Removal (CPR):    0.241ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.664     1.415    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/clk
    SLICE_X15Y213        FDRE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y213        FDRE (Prop_fdre_C_Q)         0.100     1.515 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/Q
                         net (fo=162, routed)         0.175     1.690    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/out[0]
    SLICE_X17Y213        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/dec_wdc_r_reg/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.889     1.688    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/clk
    SLICE_X17Y213        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/dec_wdc_r_reg/C
                         clock pessimism             -0.241     1.447    
    SLICE_X17Y213        FDCE (Remov_fdce_C_CLR)     -0.069     1.378    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/dec_wdc_r_reg
  -------------------------------------------------------------------
                         required time                         -1.378    
                         arrival time                           1.690    
  -------------------------------------------------------------------
                         slack                                  0.312    

Slack (MET) :             0.312ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/ma_err_r_reg[0]/CLR
                            (removal check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.275ns  (logic 0.100ns (36.313%)  route 0.175ns (63.687%))
  Logic Levels:           0  
  Clock Path Skew:        0.032ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.688ns
    Source Clock Delay      (SCD):    1.415ns
    Clock Pessimism Removal (CPR):    0.241ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.664     1.415    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/clk
    SLICE_X15Y213        FDRE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y213        FDRE (Prop_fdre_C_Q)         0.100     1.515 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/Q
                         net (fo=162, routed)         0.175     1.690    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/out[0]
    SLICE_X17Y213        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/ma_err_r_reg[0]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.889     1.688    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/clk
    SLICE_X17Y213        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/ma_err_r_reg[0]/C
                         clock pessimism             -0.241     1.447    
    SLICE_X17Y213        FDCE (Remov_fdce_C_CLR)     -0.069     1.378    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/ma_err_r_reg[0]
  -------------------------------------------------------------------
                         required time                         -1.378    
                         arrival time                           1.690    
  -------------------------------------------------------------------
                         slack                                  0.312    

Slack (MET) :             0.312ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/ma_err_r_reg[1]/CLR
                            (removal check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.275ns  (logic 0.100ns (36.313%)  route 0.175ns (63.687%))
  Logic Levels:           0  
  Clock Path Skew:        0.032ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.688ns
    Source Clock Delay      (SCD):    1.415ns
    Clock Pessimism Removal (CPR):    0.241ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.664     1.415    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/clk
    SLICE_X15Y213        FDRE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y213        FDRE (Prop_fdre_C_Q)         0.100     1.515 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD1/ctl_reg_reg[0]/Q
                         net (fo=162, routed)         0.175     1.690    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/out[0]
    SLICE_X17Y213        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/ma_err_r_reg[1]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.889     1.688    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/clk
    SLICE_X17Y213        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/ma_err_r_reg[1]/C
                         clock pessimism             -0.241     1.447    
    SLICE_X17Y213        FDCE (Remov_fdce_C_CLR)     -0.069     1.378    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_XSDB_BUS_CONTROLLER/ma_err_r_reg[1]
  -------------------------------------------------------------------
                         required time                         -1.378    
                         arrival time                           1.690    
  -------------------------------------------------------------------
                         slack                                  0.312    

Slack (MET) :             0.315ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_reg_reg[1]/C
                            (rising edge-triggered cell FDPE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/Q_reg_reg[2]/CLR
                            (removal check against rising-edge clock clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.260ns  (logic 0.100ns (38.533%)  route 0.160ns (61.467%))
  Logic Levels:           0  
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.692ns
    Source Clock Delay      (SCD):    1.418ns
    Clock Pessimism Removal (CPR):    0.260ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.725     0.725    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.751 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.667     1.418    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/clk
    SLICE_X15Y208        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_reg_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X15Y208        FDPE (Prop_fdpe_C_Q)         0.100     1.518 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_reg_reg[1]/Q
                         net (fo=16, routed)          0.160     1.678    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/ngwrdrst.grst.g7serrst.rd_rst_reg_reg[1][0]
    SLICE_X15Y209        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/Q_reg_reg[2]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.769     0.769    i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.030     0.799 r  i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=9423, routed)        0.893     1.692    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/clk
    SLICE_X15Y209        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/Q_reg_reg[2]/C
                         clock pessimism             -0.260     1.432    
    SLICE_X15Y209        FDCE (Remov_fdce_C_CLR)     -0.069     1.363    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.gsync_stage[1].rd_stg_inst/Q_reg_reg[2]
  -------------------------------------------------------------------
                         required time                         -1.363    
                         arrival time                           1.678    
  -------------------------------------------------------------------
                         slack                                  0.315    





---------------------------------------------------------------------------------------------------
Path Group:  **async_default**
From Clock:  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
  To Clock:  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK

Setup :            0  Failing Endpoints,  Worst Slack       30.410ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.268ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             30.410ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[11]/CLR
                            (recovery check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.303ns  (logic 0.352ns (15.281%)  route 1.951ns (84.719%))
  Logic Levels:           3  (LUT1=1 LUT4=1 LUT6=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.741ns = ( 37.741 - 33.000 ) 
    Source Clock Delay      (SCD):    5.522ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.454     5.522    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y225        FDRE (Prop_fdre_C_Q)         0.223     5.745 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/Q
                         net (fo=34, routed)          0.664     6.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno[0]
    SLICE_X12Y228        LUT6 (Prop_lut6_I2_O)        0.043     6.452 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1/O
                         net (fo=5, routed)           0.438     6.891    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1_n_0
    SLICE_X11Y226        LUT4 (Prop_lut4_I3_O)        0.043     6.934 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_sel[0]_INST_0/O
                         net (fo=2, routed)           0.514     7.448    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/state_reg[2]_0
    SLICE_X11Y221        LUT1 (Prop_lut1_I0_O)        0.043     7.491 f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET[15]_i_2/O
                         net (fo=10, routed)          0.334     7.825    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iSEL_n
    SLICE_X11Y219        FDCE                                         f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[11]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.292    37.741    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/m_bscan_tck[0]
    SLICE_X11Y219        FDCE                                         r  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[11]/C
                         clock pessimism              0.742    38.483    
                         clock uncertainty           -0.035    38.448    
    SLICE_X11Y219        FDCE (Recov_fdce_C_CLR)     -0.212    38.236    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[11]
  -------------------------------------------------------------------
                         required time                         38.236    
                         arrival time                          -7.825    
  -------------------------------------------------------------------
                         slack                                 30.410    

Slack (MET) :             30.410ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[13]/CLR
                            (recovery check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.303ns  (logic 0.352ns (15.281%)  route 1.951ns (84.719%))
  Logic Levels:           3  (LUT1=1 LUT4=1 LUT6=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.741ns = ( 37.741 - 33.000 ) 
    Source Clock Delay      (SCD):    5.522ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.454     5.522    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y225        FDRE (Prop_fdre_C_Q)         0.223     5.745 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/Q
                         net (fo=34, routed)          0.664     6.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno[0]
    SLICE_X12Y228        LUT6 (Prop_lut6_I2_O)        0.043     6.452 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1/O
                         net (fo=5, routed)           0.438     6.891    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1_n_0
    SLICE_X11Y226        LUT4 (Prop_lut4_I3_O)        0.043     6.934 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_sel[0]_INST_0/O
                         net (fo=2, routed)           0.514     7.448    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/state_reg[2]_0
    SLICE_X11Y221        LUT1 (Prop_lut1_I0_O)        0.043     7.491 f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET[15]_i_2/O
                         net (fo=10, routed)          0.334     7.825    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iSEL_n
    SLICE_X11Y219        FDCE                                         f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[13]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.292    37.741    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/m_bscan_tck[0]
    SLICE_X11Y219        FDCE                                         r  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[13]/C
                         clock pessimism              0.742    38.483    
                         clock uncertainty           -0.035    38.448    
    SLICE_X11Y219        FDCE (Recov_fdce_C_CLR)     -0.212    38.236    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[13]
  -------------------------------------------------------------------
                         required time                         38.236    
                         arrival time                          -7.825    
  -------------------------------------------------------------------
                         slack                                 30.410    

Slack (MET) :             30.410ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[14]/CLR
                            (recovery check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.303ns  (logic 0.352ns (15.281%)  route 1.951ns (84.719%))
  Logic Levels:           3  (LUT1=1 LUT4=1 LUT6=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.741ns = ( 37.741 - 33.000 ) 
    Source Clock Delay      (SCD):    5.522ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.454     5.522    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y225        FDRE (Prop_fdre_C_Q)         0.223     5.745 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/Q
                         net (fo=34, routed)          0.664     6.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno[0]
    SLICE_X12Y228        LUT6 (Prop_lut6_I2_O)        0.043     6.452 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1/O
                         net (fo=5, routed)           0.438     6.891    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1_n_0
    SLICE_X11Y226        LUT4 (Prop_lut4_I3_O)        0.043     6.934 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_sel[0]_INST_0/O
                         net (fo=2, routed)           0.514     7.448    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/state_reg[2]_0
    SLICE_X11Y221        LUT1 (Prop_lut1_I0_O)        0.043     7.491 f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET[15]_i_2/O
                         net (fo=10, routed)          0.334     7.825    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iSEL_n
    SLICE_X11Y219        FDCE                                         f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[14]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.292    37.741    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/m_bscan_tck[0]
    SLICE_X11Y219        FDCE                                         r  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[14]/C
                         clock pessimism              0.742    38.483    
                         clock uncertainty           -0.035    38.448    
    SLICE_X11Y219        FDCE (Recov_fdce_C_CLR)     -0.212    38.236    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[14]
  -------------------------------------------------------------------
                         required time                         38.236    
                         arrival time                          -7.825    
  -------------------------------------------------------------------
                         slack                                 30.410    

Slack (MET) :             30.410ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[15]/CLR
                            (recovery check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.303ns  (logic 0.352ns (15.281%)  route 1.951ns (84.719%))
  Logic Levels:           3  (LUT1=1 LUT4=1 LUT6=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.741ns = ( 37.741 - 33.000 ) 
    Source Clock Delay      (SCD):    5.522ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.454     5.522    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y225        FDRE (Prop_fdre_C_Q)         0.223     5.745 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/Q
                         net (fo=34, routed)          0.664     6.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno[0]
    SLICE_X12Y228        LUT6 (Prop_lut6_I2_O)        0.043     6.452 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1/O
                         net (fo=5, routed)           0.438     6.891    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1_n_0
    SLICE_X11Y226        LUT4 (Prop_lut4_I3_O)        0.043     6.934 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_sel[0]_INST_0/O
                         net (fo=2, routed)           0.514     7.448    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/state_reg[2]_0
    SLICE_X11Y221        LUT1 (Prop_lut1_I0_O)        0.043     7.491 f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET[15]_i_2/O
                         net (fo=10, routed)          0.334     7.825    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iSEL_n
    SLICE_X11Y219        FDCE                                         f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[15]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.292    37.741    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/m_bscan_tck[0]
    SLICE_X11Y219        FDCE                                         r  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[15]/C
                         clock pessimism              0.742    38.483    
                         clock uncertainty           -0.035    38.448    
    SLICE_X11Y219        FDCE (Recov_fdce_C_CLR)     -0.212    38.236    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[15]
  -------------------------------------------------------------------
                         required time                         38.236    
                         arrival time                          -7.825    
  -------------------------------------------------------------------
                         slack                                 30.410    

Slack (MET) :             30.410ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[8]/CLR
                            (recovery check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.303ns  (logic 0.352ns (15.281%)  route 1.951ns (84.719%))
  Logic Levels:           3  (LUT1=1 LUT4=1 LUT6=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.741ns = ( 37.741 - 33.000 ) 
    Source Clock Delay      (SCD):    5.522ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.454     5.522    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y225        FDRE (Prop_fdre_C_Q)         0.223     5.745 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/Q
                         net (fo=34, routed)          0.664     6.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno[0]
    SLICE_X12Y228        LUT6 (Prop_lut6_I2_O)        0.043     6.452 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1/O
                         net (fo=5, routed)           0.438     6.891    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1_n_0
    SLICE_X11Y226        LUT4 (Prop_lut4_I3_O)        0.043     6.934 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_sel[0]_INST_0/O
                         net (fo=2, routed)           0.514     7.448    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/state_reg[2]_0
    SLICE_X11Y221        LUT1 (Prop_lut1_I0_O)        0.043     7.491 f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET[15]_i_2/O
                         net (fo=10, routed)          0.334     7.825    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iSEL_n
    SLICE_X11Y219        FDCE                                         f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[8]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.292    37.741    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/m_bscan_tck[0]
    SLICE_X11Y219        FDCE                                         r  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[8]/C
                         clock pessimism              0.742    38.483    
                         clock uncertainty           -0.035    38.448    
    SLICE_X11Y219        FDCE (Recov_fdce_C_CLR)     -0.212    38.236    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[8]
  -------------------------------------------------------------------
                         required time                         38.236    
                         arrival time                          -7.825    
  -------------------------------------------------------------------
                         slack                                 30.410    

Slack (MET) :             30.410ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[9]/CLR
                            (recovery check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.303ns  (logic 0.352ns (15.281%)  route 1.951ns (84.719%))
  Logic Levels:           3  (LUT1=1 LUT4=1 LUT6=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.741ns = ( 37.741 - 33.000 ) 
    Source Clock Delay      (SCD):    5.522ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.454     5.522    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y225        FDRE (Prop_fdre_C_Q)         0.223     5.745 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/Q
                         net (fo=34, routed)          0.664     6.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno[0]
    SLICE_X12Y228        LUT6 (Prop_lut6_I2_O)        0.043     6.452 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1/O
                         net (fo=5, routed)           0.438     6.891    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1_n_0
    SLICE_X11Y226        LUT4 (Prop_lut4_I3_O)        0.043     6.934 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_sel[0]_INST_0/O
                         net (fo=2, routed)           0.514     7.448    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/state_reg[2]_0
    SLICE_X11Y221        LUT1 (Prop_lut1_I0_O)        0.043     7.491 f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET[15]_i_2/O
                         net (fo=10, routed)          0.334     7.825    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iSEL_n
    SLICE_X11Y219        FDCE                                         f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[9]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.292    37.741    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/m_bscan_tck[0]
    SLICE_X11Y219        FDCE                                         r  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[9]/C
                         clock pessimism              0.742    38.483    
                         clock uncertainty           -0.035    38.448    
    SLICE_X11Y219        FDCE (Recov_fdce_C_CLR)     -0.212    38.236    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[9]
  -------------------------------------------------------------------
                         required time                         38.236    
                         arrival time                          -7.825    
  -------------------------------------------------------------------
                         slack                                 30.410    

Slack (MET) :             30.468ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[10]/CLR
                            (recovery check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.303ns  (logic 0.352ns (15.281%)  route 1.951ns (84.719%))
  Logic Levels:           3  (LUT1=1 LUT4=1 LUT6=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.741ns = ( 37.741 - 33.000 ) 
    Source Clock Delay      (SCD):    5.522ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.454     5.522    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y225        FDRE (Prop_fdre_C_Q)         0.223     5.745 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/Q
                         net (fo=34, routed)          0.664     6.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno[0]
    SLICE_X12Y228        LUT6 (Prop_lut6_I2_O)        0.043     6.452 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1/O
                         net (fo=5, routed)           0.438     6.891    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1_n_0
    SLICE_X11Y226        LUT4 (Prop_lut4_I3_O)        0.043     6.934 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_sel[0]_INST_0/O
                         net (fo=2, routed)           0.514     7.448    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/state_reg[2]_0
    SLICE_X11Y221        LUT1 (Prop_lut1_I0_O)        0.043     7.491 f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET[15]_i_2/O
                         net (fo=10, routed)          0.334     7.825    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iSEL_n
    SLICE_X10Y219        FDCE                                         f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[10]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.292    37.741    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/m_bscan_tck[0]
    SLICE_X10Y219        FDCE                                         r  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[10]/C
                         clock pessimism              0.742    38.483    
                         clock uncertainty           -0.035    38.448    
    SLICE_X10Y219        FDCE (Recov_fdce_C_CLR)     -0.154    38.294    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[10]
  -------------------------------------------------------------------
                         required time                         38.294    
                         arrival time                          -7.825    
  -------------------------------------------------------------------
                         slack                                 30.468    

Slack (MET) :             30.468ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[12]/CLR
                            (recovery check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.303ns  (logic 0.352ns (15.281%)  route 1.951ns (84.719%))
  Logic Levels:           3  (LUT1=1 LUT4=1 LUT6=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.741ns = ( 37.741 - 33.000 ) 
    Source Clock Delay      (SCD):    5.522ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.454     5.522    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y225        FDRE (Prop_fdre_C_Q)         0.223     5.745 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/Q
                         net (fo=34, routed)          0.664     6.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno[0]
    SLICE_X12Y228        LUT6 (Prop_lut6_I2_O)        0.043     6.452 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1/O
                         net (fo=5, routed)           0.438     6.891    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1_n_0
    SLICE_X11Y226        LUT4 (Prop_lut4_I3_O)        0.043     6.934 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_sel[0]_INST_0/O
                         net (fo=2, routed)           0.514     7.448    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/state_reg[2]_0
    SLICE_X11Y221        LUT1 (Prop_lut1_I0_O)        0.043     7.491 f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET[15]_i_2/O
                         net (fo=10, routed)          0.334     7.825    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iSEL_n
    SLICE_X10Y219        FDCE                                         f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[12]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.292    37.741    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/m_bscan_tck[0]
    SLICE_X10Y219        FDCE                                         r  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[12]/C
                         clock pessimism              0.742    38.483    
                         clock uncertainty           -0.035    38.448    
    SLICE_X10Y219        FDCE (Recov_fdce_C_CLR)     -0.154    38.294    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[12]
  -------------------------------------------------------------------
                         required time                         38.294    
                         arrival time                          -7.825    
  -------------------------------------------------------------------
                         slack                                 30.468    

Slack (MET) :             30.468ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[6]/CLR
                            (recovery check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.303ns  (logic 0.352ns (15.281%)  route 1.951ns (84.719%))
  Logic Levels:           3  (LUT1=1 LUT4=1 LUT6=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.741ns = ( 37.741 - 33.000 ) 
    Source Clock Delay      (SCD):    5.522ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.454     5.522    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y225        FDRE (Prop_fdre_C_Q)         0.223     5.745 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/Q
                         net (fo=34, routed)          0.664     6.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno[0]
    SLICE_X12Y228        LUT6 (Prop_lut6_I2_O)        0.043     6.452 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1/O
                         net (fo=5, routed)           0.438     6.891    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1_n_0
    SLICE_X11Y226        LUT4 (Prop_lut4_I3_O)        0.043     6.934 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_sel[0]_INST_0/O
                         net (fo=2, routed)           0.514     7.448    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/state_reg[2]_0
    SLICE_X11Y221        LUT1 (Prop_lut1_I0_O)        0.043     7.491 f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET[15]_i_2/O
                         net (fo=10, routed)          0.334     7.825    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iSEL_n
    SLICE_X10Y219        FDCE                                         f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[6]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.292    37.741    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/m_bscan_tck[0]
    SLICE_X10Y219        FDCE                                         r  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[6]/C
                         clock pessimism              0.742    38.483    
                         clock uncertainty           -0.035    38.448    
    SLICE_X10Y219        FDCE (Recov_fdce_C_CLR)     -0.154    38.294    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[6]
  -------------------------------------------------------------------
                         required time                         38.294    
                         arrival time                          -7.825    
  -------------------------------------------------------------------
                         slack                                 30.468    

Slack (MET) :             30.468ns  (required time - arrival time)
  Source:                 dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[7]/CLR
                            (recovery check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Recovery (Max at Slow Process Corner)
  Requirement:            33.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@33.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        2.303ns  (logic 0.352ns (15.281%)  route 1.951ns (84.719%))
  Logic Levels:           3  (LUT1=1 LUT4=1 LUT6=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.741ns = ( 37.741 - 33.000 ) 
    Source Clock Delay      (SCD):    5.522ns
    Clock Pessimism Removal (CPR):    0.742ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.975     3.975    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.093     4.068 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.454     5.522    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/s_bscan_tck
    SLICE_X13Y225        FDRE                                         r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y225        FDRE (Prop_fdre_C_Q)         0.223     5.745 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno_reg[0]/Q
                         net (fo=34, routed)          0.664     6.409    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/portno[0]
    SLICE_X12Y228        LUT6 (Prop_lut6_I2_O)        0.043     6.452 f  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1/O
                         net (fo=5, routed)           0.438     6.891    dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_drck[0]_INST_0_i_1_n_0
    SLICE_X11Y226        LUT4 (Prop_lut4_I3_O)        0.043     6.934 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_switch/m_bscan_sel[0]_INST_0/O
                         net (fo=2, routed)           0.514     7.448    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/state_reg[2]_0
    SLICE_X11Y221        LUT1 (Prop_lut1_I0_O)        0.043     7.491 f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET[15]_i_2/O
                         net (fo=10, routed)          0.334     7.825    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iSEL_n
    SLICE_X10Y219        FDCE                                         f  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[7]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                     33.000    33.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000    33.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           3.366    36.366    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.083    36.449 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         1.292    37.741    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/m_bscan_tck[0]
    SLICE_X10Y219        FDCE                                         r  dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[7]/C
                         clock pessimism              0.742    38.483    
                         clock uncertainty           -0.035    38.448    
    SLICE_X10Y219        FDCE (Recov_fdce_C_CLR)     -0.154    38.294    dbg_hub/inst/CORE_XSDB.U_ICON/U_CMD/iTARGET_reg[7]
  -------------------------------------------------------------------
                         required time                         38.294    
                         arrival time                          -7.825    
  -------------------------------------------------------------------
                         slack                                 30.468    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.268ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[0]/C
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.rd_pntr_bin_reg[3]/CLR
                            (removal check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.253ns  (logic 0.100ns (39.587%)  route 0.153ns (60.413%))
  Logic Levels:           0  
  Clock Path Skew:        0.035ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.867ns
    Clock Pessimism Removal (CPR):    0.570ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.667     2.867    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X21Y207        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X21Y207        FDPE (Prop_fdpe_C_Q)         0.100     2.967 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[0]/Q
                         net (fo=18, routed)          0.153     3.120    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/AR[0]
    SLICE_X18Y207        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.rd_pntr_bin_reg[3]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/m_bscan_tck[0]
    SLICE_X18Y207        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.rd_pntr_bin_reg[3]/C
                         clock pessimism             -0.570     2.902    
    SLICE_X18Y207        FDCE (Remov_fdce_C_CLR)     -0.050     2.852    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gcx.clkx/gnxpm_cdc.rd_pntr_bin_reg[3]
  -------------------------------------------------------------------
                         required time                         -2.852    
                         arrival time                           3.120    
  -------------------------------------------------------------------
                         slack                                  0.268    

Slack (MET) :             0.268ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[0]/C
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/ram_full_fb_i_reg/CLR
                            (removal check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.253ns  (logic 0.100ns (39.587%)  route 0.153ns (60.413%))
  Logic Levels:           0  
  Clock Path Skew:        0.035ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.867ns
    Clock Pessimism Removal (CPR):    0.570ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.667     2.867    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X21Y207        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X21Y207        FDPE (Prop_fdpe_C_Q)         0.100     2.967 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[0]/Q
                         net (fo=18, routed)          0.153     3.120    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/out[0]
    SLICE_X18Y207        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/ram_full_fb_i_reg/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/m_bscan_tck[0]
    SLICE_X18Y207        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/ram_full_fb_i_reg/C
                         clock pessimism             -0.570     2.902    
    SLICE_X18Y207        FDCE (Remov_fdce_C_CLR)     -0.050     2.852    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/ram_full_fb_i_reg
  -------------------------------------------------------------------
                         required time                         -2.852    
                         arrival time                           3.120    
  -------------------------------------------------------------------
                         slack                                  0.268    

Slack (MET) :             0.268ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[0]/C
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/ram_full_i_reg/CLR
                            (removal check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.253ns  (logic 0.100ns (39.587%)  route 0.153ns (60.413%))
  Logic Levels:           0  
  Clock Path Skew:        0.035ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.867ns
    Clock Pessimism Removal (CPR):    0.570ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.667     2.867    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X21Y207        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X21Y207        FDPE (Prop_fdpe_C_Q)         0.100     2.967 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[0]/Q
                         net (fo=18, routed)          0.153     3.120    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/out[0]
    SLICE_X18Y207        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/ram_full_i_reg/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/m_bscan_tck[0]
    SLICE_X18Y207        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/ram_full_i_reg/C
                         clock pessimism             -0.570     2.902    
    SLICE_X18Y207        FDCE (Remov_fdce_C_CLR)     -0.050     2.852    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/gwas.wsts/ram_full_i_reg
  -------------------------------------------------------------------
                         required time                         -2.852    
                         arrival time                           3.120    
  -------------------------------------------------------------------
                         slack                                  0.268    

Slack (MET) :             0.290ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rst_wr_reg2_reg/C
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_asreg_reg/PRE
                            (removal check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.233ns  (logic 0.091ns (38.996%)  route 0.142ns (61.004%))
  Logic Levels:           0  
  Clock Path Skew:        0.033ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.471ns
    Source Clock Delay      (SCD):    2.868ns
    Clock Pessimism Removal (CPR):    0.570ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.668     2.868    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X19Y208        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rst_wr_reg2_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X19Y208        FDPE (Prop_fdpe_C_Q)         0.091     2.959 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rst_wr_reg2_reg/Q
                         net (fo=1, routed)           0.142     3.101    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/rst_wr_reg2
    SLICE_X20Y208        FDPE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_asreg_reg/PRE
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.894     3.471    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X20Y208        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_asreg_reg/C
                         clock pessimism             -0.570     2.901    
    SLICE_X20Y208        FDPE (Remov_fdpe_C_PRE)     -0.090     2.811    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_asreg_reg
  -------------------------------------------------------------------
                         required time                         -2.811    
                         arrival time                           3.101    
  -------------------------------------------------------------------
                         slack                                  0.290    

Slack (MET) :             0.326ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/C
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_d1_reg[2]/CLR
                            (removal check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.292ns  (logic 0.100ns (34.226%)  route 0.192ns (65.774%))
  Logic Levels:           0  
  Clock Path Skew:        0.035ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.867ns
    Clock Pessimism Removal (CPR):    0.570ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.667     2.867    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X21Y207        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X21Y207        FDPE (Prop_fdpe_C_Q)         0.100     2.967 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/Q
                         net (fo=12, routed)          0.192     3.159    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/out[0]
    SLICE_X19Y207        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_d1_reg[2]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/m_bscan_tck[0]
    SLICE_X19Y207        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_d1_reg[2]/C
                         clock pessimism             -0.570     2.902    
    SLICE_X19Y207        FDCE (Remov_fdce_C_CLR)     -0.069     2.833    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_d1_reg[2]
  -------------------------------------------------------------------
                         required time                         -2.833    
                         arrival time                           3.159    
  -------------------------------------------------------------------
                         slack                                  0.326    

Slack (MET) :             0.326ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/C
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_d1_reg[3]/CLR
                            (removal check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.292ns  (logic 0.100ns (34.226%)  route 0.192ns (65.774%))
  Logic Levels:           0  
  Clock Path Skew:        0.035ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.867ns
    Clock Pessimism Removal (CPR):    0.570ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.667     2.867    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X21Y207        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X21Y207        FDPE (Prop_fdpe_C_Q)         0.100     2.967 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/Q
                         net (fo=12, routed)          0.192     3.159    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/out[0]
    SLICE_X19Y207        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_d1_reg[3]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/m_bscan_tck[0]
    SLICE_X19Y207        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_d1_reg[3]/C
                         clock pessimism             -0.570     2.902    
    SLICE_X19Y207        FDCE (Remov_fdce_C_CLR)     -0.069     2.833    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_d1_reg[3]
  -------------------------------------------------------------------
                         required time                         -2.833    
                         arrival time                           3.159    
  -------------------------------------------------------------------
                         slack                                  0.326    

Slack (MET) :             0.326ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/C
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[2]/CLR
                            (removal check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.292ns  (logic 0.100ns (34.226%)  route 0.192ns (65.774%))
  Logic Levels:           0  
  Clock Path Skew:        0.035ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.867ns
    Clock Pessimism Removal (CPR):    0.570ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.667     2.867    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X21Y207        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X21Y207        FDPE (Prop_fdpe_C_Q)         0.100     2.967 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/Q
                         net (fo=12, routed)          0.192     3.159    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/out[0]
    SLICE_X19Y207        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[2]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/m_bscan_tck[0]
    SLICE_X19Y207        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[2]/C
                         clock pessimism             -0.570     2.902    
    SLICE_X19Y207        FDCE (Remov_fdce_C_CLR)     -0.069     2.833    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[2]
  -------------------------------------------------------------------
                         required time                         -2.833    
                         arrival time                           3.159    
  -------------------------------------------------------------------
                         slack                                  0.326    

Slack (MET) :             0.326ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/C
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[3]/CLR
                            (removal check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.292ns  (logic 0.100ns (34.226%)  route 0.192ns (65.774%))
  Logic Levels:           0  
  Clock Path Skew:        0.035ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.867ns
    Clock Pessimism Removal (CPR):    0.570ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.667     2.867    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X21Y207        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X21Y207        FDPE (Prop_fdpe_C_Q)         0.100     2.967 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/Q
                         net (fo=12, routed)          0.192     3.159    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/out[0]
    SLICE_X19Y207        FDCE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[3]/CLR
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/m_bscan_tck[0]
    SLICE_X19Y207        FDCE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[3]/C
                         clock pessimism             -0.570     2.902    
    SLICE_X19Y207        FDCE (Remov_fdce_C_CLR)     -0.069     2.833    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[3]
  -------------------------------------------------------------------
                         required time                         -2.833    
                         arrival time                           3.159    
  -------------------------------------------------------------------
                         slack                                  0.326    

Slack (MET) :             0.327ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rst_rd_reg2_reg/C
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_asreg_reg/PRE
                            (removal check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.231ns  (logic 0.091ns (39.388%)  route 0.140ns (60.611%))
  Logic Levels:           0  
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.473ns
    Source Clock Delay      (SCD):    2.870ns
    Clock Pessimism Removal (CPR):    0.589ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.670     2.870    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X13Y204        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rst_rd_reg2_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X13Y204        FDPE (Prop_fdpe_C_Q)         0.091     2.961 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rst_rd_reg2_reg/Q
                         net (fo=1, routed)           0.140     3.101    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/rst_rd_reg2
    SLICE_X13Y203        FDPE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_asreg_reg/PRE
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.896     3.473    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X13Y203        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_asreg_reg/C
                         clock pessimism             -0.589     2.884    
    SLICE_X13Y203        FDPE (Remov_fdpe_C_PRE)     -0.110     2.774    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_RD/U_RD_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.rd_rst_asreg_reg
  -------------------------------------------------------------------
                         required time                         -2.774    
                         arrival time                           3.101    
  -------------------------------------------------------------------
                         slack                                  0.327    

Slack (MET) :             0.329ns  (arrival time - required time)
  Source:                 dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/C
                            (rising edge-triggered cell FDPE clocked by dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Destination:            dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[1]/PRE
                            (removal check against rising-edge clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK  {rise@0.000ns fall@16.500ns period=33.000ns})
  Path Group:             **async_default**
  Path Type:              Removal (Min at Fast Process Corner)
  Requirement:            0.000ns  (dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns - dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise@0.000ns)
  Data Path Delay:        0.292ns  (logic 0.100ns (34.226%)  route 0.192ns (65.774%))
  Logic Levels:           0  
  Clock Path Skew:        0.035ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    3.472ns
    Source Clock Delay      (SCD):    2.867ns
    Clock Pessimism Removal (CPR):    0.570ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.174     2.174    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     2.200 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.667     2.867    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/m_bscan_tck[0]
    SLICE_X21Y207        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X21Y207        FDPE (Prop_fdpe_C_Q)         0.100     2.967 f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/rstblk/ngwrdrst.grst.g7serrst.wr_rst_reg_reg[1]/Q
                         net (fo=12, routed)          0.192     3.159    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/out[0]
    SLICE_X19Y207        FDPE                                         f  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[1]/PRE
  -------------------------------------------------------------------    -------------------

                         (clock dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK rise edge)
                                                      0.000     0.000 r  
    BSCAN_X0Y0           BSCANE2                      0.000     0.000 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK
                         net (fo=1, routed)           2.547     2.547    dbg_hub/inst/tck_bs
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.030     2.577 r  dbg_hub/inst/SWITCH_N_EXT_BSCAN.u_bufg_icon_tck/O
                         net (fo=382, routed)         0.895     3.472    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/m_bscan_tck[0]
    SLICE_X19Y207        FDPE                                         r  dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[1]/C
                         clock pessimism             -0.570     2.902    
    SLICE_X19Y207        FDPE (Remov_fdpe_C_PRE)     -0.072     2.830    dbg_hub/inst/CORE_XSDB.UUT_MASTER/U_ICON_INTERFACE/U_CMD6_WR/U_WR_FIFO/SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst/inst_fifo_gen/gconvfifo.rf/grf.rf/gntv_or_sync_fifo.gl0.wr/wpntr/gic0.gc0.count_reg[1]
  -------------------------------------------------------------------
                         required time                         -2.830    
                         arrival time                           3.159    
  -------------------------------------------------------------------
                         slack                                  0.329    





---------------------------------------------------------------------------------------------------
Path Group:  **default**
From Clock:  clk_fpga_0
  To Clock:  

Setup :            0  Failing Endpoints,  Worst Slack        9.196ns,  Total Violation        0.000ns
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             9.196ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_src_dma_mm/i_data_mover/id_reg[2]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/cdc_sync_stage1_reg[2]/D
  Path Group:             **default**
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.785ns  (logic 0.223ns (28.425%)  route 0.562ns (71.575%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X31Y264                                     0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_src_dma_mm/i_data_mover/id_reg[2]/C
    SLICE_X31Y264        FDRE (Prop_fdre_C_Q)         0.223     0.223 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_src_dma_mm/i_data_mover/id_reg[2]/Q
                         net (fo=10, routed)          0.562     0.785    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/dbg_ids1[2]
    SLICE_X35Y265        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/cdc_sync_stage1_reg[2]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X35Y265        FDRE (Setup_fdre_C_D)       -0.019     9.981    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/cdc_sync_stage1_reg[2]
  -------------------------------------------------------------------
                         required time                          9.981    
                         arrival time                          -0.785    
  -------------------------------------------------------------------
                         slack                                  9.196    

Slack (MET) :             9.248ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_src_dma_mm/i_data_mover/id_reg[1]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/cdc_sync_stage1_reg[1]/D
  Path Group:             **default**
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.743ns  (logic 0.223ns (29.995%)  route 0.520ns (70.005%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X29Y264                                     0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_src_dma_mm/i_data_mover/id_reg[1]/C
    SLICE_X29Y264        FDRE (Prop_fdre_C_Q)         0.223     0.223 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_src_dma_mm/i_data_mover/id_reg[1]/Q
                         net (fo=10, routed)          0.520     0.743    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/dbg_ids1[1]
    SLICE_X35Y265        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/cdc_sync_stage1_reg[1]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X35Y265        FDRE (Setup_fdre_C_D)       -0.009     9.991    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/cdc_sync_stage1_reg[1]
  -------------------------------------------------------------------
                         required time                          9.991    
                         arrival time                          -0.743    
  -------------------------------------------------------------------
                         slack                                  9.248    

Slack (MET) :             9.278ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_req_fifo/s_axis_waddr_reg/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_req_fifo/i_waddr_sync/cdc_sync_stage1_reg[0]/D
  Path Group:             **default**
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.748ns  (logic 0.223ns (29.811%)  route 0.525ns (70.189%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X31Y264                                     0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_req_fifo/s_axis_waddr_reg/C
    SLICE_X31Y264        FDRE (Prop_fdre_C_Q)         0.223     0.223 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_req_fifo/s_axis_waddr_reg/Q
                         net (fo=5, routed)           0.525     0.748    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_req_fifo/i_waddr_sync/s_axis_waddr
    SLICE_X32Y264        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_req_fifo/i_waddr_sync/cdc_sync_stage1_reg[0]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X32Y264        FDRE (Setup_fdre_C_D)        0.026    10.026    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_req_fifo/i_waddr_sync/cdc_sync_stage1_reg[0]
  -------------------------------------------------------------------
                         required time                         10.026    
                         arrival time                          -0.748    
  -------------------------------------------------------------------
                         slack                                  9.278    

Slack (MET) :             9.301ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/i_address_gray/i_waddr_sync/cdc_sync_stage0_reg[1]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/i_address_gray/i_waddr_sync/cdc_sync_stage1_reg[1]/D
  Path Group:             **default**
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.690ns  (logic 0.223ns (32.303%)  route 0.467ns (67.697%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X28Y250                                     0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/i_address_gray/i_waddr_sync/cdc_sync_stage0_reg[1]/C
    SLICE_X28Y250        FDRE (Prop_fdre_C_Q)         0.223     0.223 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/i_address_gray/i_waddr_sync/cdc_sync_stage0_reg[1]/Q
                         net (fo=1, routed)           0.467     0.690    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/i_address_gray/i_waddr_sync/cdc_sync_stage0[1]
    SLICE_X29Y249        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/i_address_gray/i_waddr_sync/cdc_sync_stage1_reg[1]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X29Y249        FDRE (Setup_fdre_C_D)       -0.009     9.991    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_fifo/i_address_gray/i_waddr_sync/cdc_sync_stage1_reg[1]
  -------------------------------------------------------------------
                         required time                          9.991    
                         arrival time                          -0.690    
  -------------------------------------------------------------------
                         slack                                  9.301    

Slack (MET) :             9.318ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage0_reg[5]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage1_reg[5]/D
  Path Group:             **default**
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.677ns  (logic 0.223ns (32.916%)  route 0.454ns (67.084%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X25Y246                                     0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage0_reg[5]/C
    SLICE_X25Y246        FDRE (Prop_fdre_C_Q)         0.223     0.223 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage0_reg[5]/Q
                         net (fo=1, routed)           0.454     0.677    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage0_reg_n_0_[5]
    SLICE_X28Y246        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage1_reg[5]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X28Y246        FDRE (Setup_fdre_C_D)       -0.005     9.995    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage1_reg[5]
  -------------------------------------------------------------------
                         required time                          9.995    
                         arrival time                          -0.677    
  -------------------------------------------------------------------
                         slack                                  9.318    

Slack (MET) :             9.330ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_req_gen/id_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_sync_src_request_id/cdc_sync_stage1_reg[0]/D
  Path Group:             **default**
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.691ns  (logic 0.259ns (37.501%)  route 0.432ns (62.499%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X34Y256                                     0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_req_gen/id_reg[0]/C
    SLICE_X34Y256        FDRE (Prop_fdre_C_Q)         0.259     0.259 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_req_gen/id_reg[0]/Q
                         net (fo=14, routed)          0.432     0.691    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_sync_src_request_id/request_id[0]
    SLICE_X34Y254        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_sync_src_request_id/cdc_sync_stage1_reg[0]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X34Y254        FDRE (Setup_fdre_C_D)        0.021    10.021    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_sync_src_request_id/cdc_sync_stage1_reg[0]
  -------------------------------------------------------------------
                         required time                         10.021    
                         arrival time                          -0.691    
  -------------------------------------------------------------------
                         slack                                  9.330    

Slack (MET) :             9.332ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_src_dma_mm/i_data_mover/id_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/cdc_sync_stage1_reg[0]/D
  Path Group:             **default**
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.649ns  (logic 0.223ns (34.365%)  route 0.426ns (65.635%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X29Y264                                     0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_src_dma_mm/i_data_mover/id_reg[0]/C
    SLICE_X29Y264        FDRE (Prop_fdre_C_Q)         0.223     0.223 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_src_dma_mm/i_data_mover/id_reg[0]/Q
                         net (fo=10, routed)          0.426     0.649    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/dbg_ids1[0]
    SLICE_X31Y267        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/cdc_sync_stage1_reg[0]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X31Y267        FDRE (Setup_fdre_C_D)       -0.019     9.981    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_dest_request_id/cdc_sync_stage1_reg[0]
  -------------------------------------------------------------------
                         required time                          9.981    
                         arrival time                          -0.649    
  -------------------------------------------------------------------
                         slack                                  9.332    

Slack (MET) :             9.354ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage0_reg[4]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage1_reg[4]/D
  Path Group:             **default**
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.615ns  (logic 0.223ns (36.263%)  route 0.392ns (63.737%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X25Y246                                     0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage0_reg[4]/C
    SLICE_X25Y246        FDRE (Prop_fdre_C_Q)         0.223     0.223 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage0_reg[4]/Q
                         net (fo=1, routed)           0.392     0.615    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage0_reg_n_0_[4]
    SLICE_X25Y247        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage1_reg[4]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X25Y247        FDRE (Setup_fdre_C_D)       -0.031     9.969    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_fifo/i_address_gray/i_raddr_sync/cdc_sync_stage1_reg[4]
  -------------------------------------------------------------------
                         required time                          9.969    
                         arrival time                          -0.615    
  -------------------------------------------------------------------
                         slack                                  9.354    

Slack (MET) :             9.383ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/m_axis_raddr_reg/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/i_raddr_sync/cdc_sync_stage1_reg[0]/D
  Path Group:             **default**
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.525ns  (logic 0.236ns (44.966%)  route 0.289ns (55.034%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X36Y265                                     0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/m_axis_raddr_reg/C
    SLICE_X36Y265        FDRE (Prop_fdre_C_Q)         0.236     0.236 r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/m_axis_raddr_reg/Q
                         net (fo=2, routed)           0.289     0.525    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/i_raddr_sync/m_axis_raddr_reg
    SLICE_X35Y265        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/i_raddr_sync/cdc_sync_stage1_reg[0]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X35Y265        FDRE (Setup_fdre_C_D)       -0.092     9.908    i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_dest_response_fifo/i_raddr_sync/cdc_sync_stage1_reg[0]
  -------------------------------------------------------------------
                         required time                          9.908    
                         arrival time                          -0.525    
  -------------------------------------------------------------------
                         slack                                  9.383    

Slack (MET) :             9.419ns  (required time - arrival time)
  Source:                 i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_req_gen/id_reg[2]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_sync_src_request_id/cdc_sync_stage1_reg[2]/D
  Path Group:             **default**
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.604ns  (logic 0.259ns (42.845%)  route 0.345ns (57.155%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X32Y256                                     0.000     0.000 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_req_gen/id_reg[2]/C
    SLICE_X32Y256        FDRE (Prop_fdre_C_Q)         0.259     0.259 r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_req_gen/id_reg[2]/Q
                         net (fo=14, routed)          0.345     0.604    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_sync_src_request_id/request_id[2]
    SLICE_X34Y254        FDRE                                         r  i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_sync_src_request_id/cdc_sync_stage1_reg[2]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X34Y254        FDRE (Setup_fdre_C_D)        0.023    10.023    i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_sync_src_request_id/cdc_sync_stage1_reg[2]
  -------------------------------------------------------------------
                         required time                         10.023    
                         arrival time                          -0.604    
  -------------------------------------------------------------------
                         slack                                  9.419    





