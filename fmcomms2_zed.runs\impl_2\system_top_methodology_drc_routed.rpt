Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 13:25:55 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : report_methodology -file system_top_methodology_drc_routed.rpt -rpx system_top_methodology_drc_routed.rpx
| Design       : system_top
| Device       : xc7z045ffg900-2
| Speed File   : -2
| Design State : Routed
---------------------------------------------------------------------------------------------------------------------------

Report Methodology

Table of Contents
-----------------
1. REPORT SUMMARY
2. REPORT DETAILS

1. REPORT SUMMARY
-----------------
            Netlist: netlist
          Floorplan: design_1
      Design limits: <entire design considered>
             Max violations: <unlimited>
             Violations found: 40
+-----------+----------+------------------------------------------------------+------------+
| Rule      | Severity | Description                                          | Violations |
+-----------+----------+------------------------------------------------------+------------+
| PDRC-190  | Warning  | Suboptimally placed synchronized register chain      | 4          |
| TIMING-9  | Warning  | Unknown CDC Logic                                    | 1          |
| TIMING-10 | Warning  | Missing property on synchronizer                     | 1          |
| TIMING-18 | Warning  | Missing input or output delay                        | 32         |
| TIMING-28 | Warning  | Auto-derived clock referenced by a timing constraint | 2          |
+-----------+----------+------------------------------------------------------+------------+

2. REPORT DETAILS
-----------------
PDRC-190#1 Warning
Suboptimally placed synchronized register chain  
The FDCE cell i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_clock_mon/d_count_run_m3_reg in site SLICE_X53Y228 is part of a synchronized register chain that is suboptimally placed as the load FDCE cell i_system_wrapper/system_i/axi_ad9361/inst/i_rx/i_up_adc_common/i_clock_mon/up_count_running_m1_reg is not placed in the same (SLICE) site.
Related violations: <none>

PDRC-190#2 Warning
Suboptimally placed synchronized register chain  
The FDCE cell i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_clock_mon/d_count_run_m3_reg in site SLICE_X75Y223 is part of a synchronized register chain that is suboptimally placed as the load FDCE cell i_system_wrapper/system_i/axi_ad9361/inst/i_tx/i_up_dac_common/i_clock_mon/up_count_running_m1_reg is not placed in the same (SLICE) site.
Related violations: <none>

PDRC-190#3 Warning
Suboptimally placed synchronized register chain  
The FDRE cell i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_sync_control_src/cdc_sync_stage2_reg[0] in site SLICE_X32Y255 is part of a synchronized register chain that is suboptimally placed as the load FDRE cell i_system_wrapper/system_i/axi_ad9361_adc_dma/inst/i_request_arb/i_sync_status_src/cdc_sync_stage1_reg[1] is not placed in the same (SLICE) site.
Related violations: <none>

PDRC-190#4 Warning
Suboptimally placed synchronized register chain  
The FDRE cell i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_control_dest/cdc_sync_stage2_reg[0] in site SLICE_X34Y267 is part of a synchronized register chain that is suboptimally placed as the load FDRE cell i_system_wrapper/system_i/axi_ad9361_dac_dma/inst/i_request_arb/i_sync_status_dest/cdc_sync_stage1_reg[0] is not placed in the same (SLICE) site.
Related violations: <none>

TIMING-9#1 Warning
Unknown CDC Logic  
One or more asynchronous Clock Domain Crossing has been detected between 2 clock domains through a set_false_path or a set_clock_groups or set_max_delay -datapath_only constraint but no double-registers logic synchronizer has been found on the side of the capture clock. It is recommended to run report_cdc for a complete and detailed CDC coverage
Related violations: <none>

TIMING-10#1 Warning
Missing property on synchronizer  
One or more logic synchronizer has been detected between 2 clock domains but the synchronizer does not have the property ASYNC_REG defined on one or both registers. It is recommended to run report_cdc for a complete and detailed CDC coverage
Related violations: <none>

TIMING-18#1 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_n[0] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#2 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_n[1] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#3 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_n[2] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#4 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_n[3] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#5 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_n[4] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#6 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_n[5] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#7 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_p[0] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#8 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_p[1] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#9 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_p[2] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#10 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_p[3] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#11 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_p[4] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#12 Warning
Missing input or output delay  
An input delay is missing on rx_data_in_p[5] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#13 Warning
Missing input or output delay  
An input delay is missing on rx_frame_in_n relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#14 Warning
Missing input or output delay  
An input delay is missing on rx_frame_in_p relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#15 Warning
Missing input or output delay  
An output delay is missing on enable relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#16 Warning
Missing input or output delay  
An output delay is missing on tx_clk_out_n relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#17 Warning
Missing input or output delay  
An output delay is missing on tx_clk_out_p relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#18 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_n[0] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#19 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_n[1] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#20 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_n[2] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#21 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_n[3] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#22 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_n[4] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#23 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_n[5] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#24 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_p[0] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#25 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_p[1] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#26 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_p[2] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#27 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_p[3] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#28 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_p[4] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#29 Warning
Missing input or output delay  
An output delay is missing on tx_data_out_p[5] relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#30 Warning
Missing input or output delay  
An output delay is missing on tx_frame_out_n relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#31 Warning
Missing input or output delay  
An output delay is missing on tx_frame_out_p relative to clock(s) rx_clk 
Related violations: <none>

TIMING-18#32 Warning
Missing input or output delay  
An output delay is missing on txnrx relative to clock(s) rx_clk 
Related violations: <none>

TIMING-28#1 Warning
Auto-derived clock referenced by a timing constraint  
The auto-derived clock clk_div_sel_0_s is referenced by name inside timing constraint (see constraint position 89 in the Timing Constraint window in Vivado IDE). It is recommended to reference an auto-derived clock by the pin name attached to the clock: get_clocks -of_objects [get_pins i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_divide_sel_0/O]
Related violations: <none>

TIMING-28#2 Warning
Auto-derived clock referenced by a timing constraint  
The auto-derived clock clk_div_sel_1_s is referenced by name inside timing constraint (see constraint position 89 in the Timing Constraint window in Vivado IDE). It is recommended to reference an auto-derived clock by the pin name attached to the clock: get_clocks -of_objects [get_pins i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_divide_sel_1/O]
Related violations: <none>


