<?xml version="1.0" encoding="UTF-8"?>
<spirit:design xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>xci</spirit:library>
  <spirit:name>unknown</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:componentInstances>
    <spirit:componentInstance>
      <spirit:instanceName>system_util_ad9361_divclk_sel_concat_0</spirit:instanceName>
      <spirit:componentRef spirit:vendor="xilinx.com" spirit:library="ip" spirit:name="xlconcat" spirit:version="2.1"/>
      <spirit:configurableElementValues>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN0_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN10_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN11_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN12_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN13_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN14_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN15_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN16_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN17_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN18_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN19_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN1_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN20_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN21_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN22_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN23_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN24_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN25_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN26_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN27_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN28_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN29_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN2_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN30_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN31_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN3_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN4_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN5_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN6_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN7_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN8_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.IN9_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.NUM_PORTS">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.dout_width">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Component_Name">system_util_ad9361_divclk_sel_concat_0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN0_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN10_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN11_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN12_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN13_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN14_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN15_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN16_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN17_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN18_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN19_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN1_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN20_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN21_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN22_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN23_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN24_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN25_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN26_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN27_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN28_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN29_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN2_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN30_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN31_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN3_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN4_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN5_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN6_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN7_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN8_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IN9_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.NUM_PORTS">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.dout_width">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.ARCHITECTURE">zynq</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.BASE_BOARD_PART"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.BOARD_CONNECTIONS"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.DEVICE">xc7z035</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.PACKAGE">ffg676</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.PREFHDL">VERILOG</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SILICON_REVISION"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SIMULATOR_LANGUAGE">MIXED</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SPEEDGRADE">-2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.TEMPERATURE_GRADE"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.USE_RDI_CUSTOMIZATION">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.USE_RDI_GENERATION">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.IPCONTEXT">IP_Integrator</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.IPREVISION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.MANAGED">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.OUTPUTDIR">.</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SELECTEDSIMMODEL"/>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SHAREDDIR">../../ipshared</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SWVERSION">2018.3</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SYNTHESISFLOW">OUT_OF_CONTEXT</spirit:configurableElementValue>
      </spirit:configurableElementValues>
      <spirit:vendorExtensions>
        <xilinx:componentInstanceExtensions>
          <xilinx:configElementInfos>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.IN0_WIDTH" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.IN1_WIDTH" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_PORTS" xilinx:valueSource="user"/>
          </xilinx:configElementInfos>
        </xilinx:componentInstanceExtensions>
      </spirit:vendorExtensions>
    </spirit:componentInstance>
  </spirit:componentInstances>
</spirit:design>
