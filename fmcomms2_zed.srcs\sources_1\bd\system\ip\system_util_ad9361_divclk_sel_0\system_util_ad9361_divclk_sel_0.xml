<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>customized_ip</spirit:library>
  <spirit:name>system_util_ad9361_divclk_sel_0</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:model>
    <spirit:ports>
      <spirit:port>
        <spirit:name>Op1</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_SIZE&apos;)) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>Res</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
    </spirit:ports>
    <spirit:modelParameters>
      <spirit:modelParameter xsi:type="spirit:nameValueTypeType" spirit:dataType="string">
        <spirit:name>C_OPERATION</spirit:name>
        <spirit:displayName>C Operation</spirit:displayName>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OPERATION">and</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_SIZE</spirit:name>
        <spirit:displayName>C Size</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SIZE">2</spirit:value>
      </spirit:modelParameter>
    </spirit:modelParameters>
  </spirit:model>
  <spirit:choices>
    <spirit:choice>
      <spirit:name>choice_list_eda61b3d</spirit:name>
      <spirit:enumeration>and</spirit:enumeration>
      <spirit:enumeration>or</spirit:enumeration>
      <spirit:enumeration>xor</spirit:enumeration>
    </spirit:choice>
  </spirit:choices>
  <spirit:description>Performs logic operations on a n-bit input to produce a one bit output</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="2">system_util_ad9361_divclk_sel_0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_SIZE</spirit:name>
      <spirit:displayName>C Size</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_SIZE" spirit:order="3">2</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_OPERATION</spirit:name>
      <spirit:displayName>C Operation</spirit:displayName>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.C_OPERATION" spirit:choiceRef="choice_list_eda61b3d" spirit:order="4">and</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>LOGO_FILE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.LOGO_FILE" spirit:order="5">data/sym_andgate.png</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:id="PARAM_ENABLEMENT.LOGO_FILE">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:displayName>Utility Reduced Logic</xilinx:displayName>
      <xilinx:coreRevision>4</xilinx:coreRevision>
      <xilinx:configElementInfos>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.C_SIZE" xilinx:valueSource="user"/>
      </xilinx:configElementInfos>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2018.3</xilinx:xilinxVersion>
      <xilinx:checksum xilinx:scope="fileGroups" xilinx:value="00d631c9"/>
      <xilinx:checksum xilinx:scope="ports" xilinx:value="227a81b6"/>
      <xilinx:checksum xilinx:scope="hdlParameters" xilinx:value="426d7563"/>
      <xilinx:checksum xilinx:scope="parameters" xilinx:value="04a326c2"/>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
