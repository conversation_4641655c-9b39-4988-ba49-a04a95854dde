
set currentWorkingDirectory [pwd]
set bitFileName "/system_top_flash_50M.bit"
set elfFileName "/ad9361/Debug/ad9361.elf"

set bitFilePath $currentWorkingDirectory$bitFileName
set elfFilePath $currentWorkingDirectory$elfFileName

#fpga -f $bitFilePath
connect arm hw
dow $elfFilePath

puts "Capturing data..."

run
after 5000
#stop

puts "Moving data into ad1_test.csv file..."

set startAddress 8388608
set readData [mrd $startAddress 512]

set fp [ open ad1_test.csv w ]
for {set index 1} {$index < 1024} {incr index 4} {
	set data [lindex $readData $index]
	set intData [expr 0x$data]
	
	set sampleQ1 [expr {$intData & 0xFFFF}]
	set sampleI1 [expr {($intData >> 16) & 0xFFFF}]
	
	set data [lindex $readData [expr {$index + 2}]]
	set intData [expr 0x$data]
	
	set sampleQ2 [expr {$intData & 0xFFFF}]
	set sampleI2 [expr {($intData >> 16) & 0xFFFF}]
	
	set line $sampleI1,$sampleQ1,$sampleI2,$sampleQ2
	
	puts $fp $line
}
close $fp

run
puts "Done."

disconnect 64
exit
