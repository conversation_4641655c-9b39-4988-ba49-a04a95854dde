connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
bpadd -addr &main
