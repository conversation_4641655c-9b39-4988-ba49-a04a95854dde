<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_a1zU4YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_a1zU4oqJEeq6Bc7V6nnrmg" bindingContexts="_a1zU6IqJEeq6Bc7V6nnrmg">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <tags>ModelMigrationProcessor.001</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_a1zU4oqJEeq6Bc7V6nnrmg" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_bMSiQ4qJEeq6Bc7V6nnrmg" x="50" y="50" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1588213345940"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_bMSiQ4qJEeq6Bc7V6nnrmg" selectedElement="_bMSiRIqJEeq6Bc7V6nnrmg" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_bMSiRIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_bWjRgoqJEeq6Bc7V6nnrmg">
        <children xsi:type="advanced:Perspective" xmi:id="_bWjRgoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_bWjRg4qJEeq6Bc7V6nnrmg" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif" tooltip="C/C++">
          <persistedState key="persp.hiddenItems" value="persp.hideMenuSC:org.eclipse.jdt.ui.refactoring.menu,persp.hideMenuSC:org.eclipse.jdt.ui.source.menu,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:com.xilinx.sdk.appwiz.AppWizard</tags>
          <tags>persp.newWizSC:com.xilinx.sdk.profile.ui.wizards.ZpeProjectWizard</tags>
          <tags>persp.newWizSC:com.xilinx.sdk.sw.ui.NewBspWizard</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_bWjRg4qJEeq6Bc7V6nnrmg" selectedElement="_bWjRhIqJEeq6Bc7V6nnrmg" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_bWjRhIqJEeq6Bc7V6nnrmg" containerData="2500" selectedElement="_bWjRhYqJEeq6Bc7V6nnrmg">
              <children xsi:type="basic:PartStack" xmi:id="_bWjRhYqJEeq6Bc7V6nnrmg" elementId="topLeft" containerData="7500" selectedElement="_bWjRhoqJEeq6Bc7V6nnrmg">
                <tags>active</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_bWjRhoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_bV9b5IqJEeq6Bc7V6nnrmg"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_bWjRh4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_bV9b5YqJEeq6Bc7V6nnrmg"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_bWjRiIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_bV9b5oqJEeq6Bc7V6nnrmg"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_bWjRiYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_bWHMoIqJEeq6Bc7V6nnrmg"/>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_bWjRioqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementViewMStack" containerData="2500" selectedElement="_bWjRi4qJEeq6Bc7V6nnrmg">
                <children xsi:type="advanced:Placeholder" xmi:id="_bWjRi4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView" ref="_bWaHkYqJEeq6Bc7V6nnrmg"/>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_bWjRjIqJEeq6Bc7V6nnrmg" containerData="7500">
              <children xsi:type="basic:PartSashContainer" xmi:id="_bWjRjYqJEeq6Bc7V6nnrmg" containerData="7500" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_bWjRjoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_bVzqoIqJEeq6Bc7V6nnrmg"/>
                <children xsi:type="basic:PartStack" xmi:id="_bWjRj4qJEeq6Bc7V6nnrmg" elementId="topRight" containerData="2500" selectedElement="_bWjRkIqJEeq6Bc7V6nnrmg">
                  <children xsi:type="advanced:Placeholder" xmi:id="_bWjRkIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ContentOutline" ref="_bWHMpYqJEeq6Bc7V6nnrmg"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_bWjRkYqJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView" ref="_bWjRgIqJEeq6Bc7V6nnrmg"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_bWjRkoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_bWjRgYqJEeq6Bc7V6nnrmg"/>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_bWjRk4qJEeq6Bc7V6nnrmg" containerData="2500" horizontal="true">
                <children xsi:type="basic:PartStack" xmi:id="_bWjRlIqJEeq6Bc7V6nnrmg" elementId="bottom" containerData="5000" selectedElement="_bWjRlYqJEeq6Bc7V6nnrmg">
                  <children xsi:type="advanced:Placeholder" xmi:id="_bWjRlYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ProblemView" ref="_bWHMoYqJEeq6Bc7V6nnrmg"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_bWjRloqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.TaskList" ref="_bWHMooqJEeq6Bc7V6nnrmg"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_bWjRl4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.console.ConsoleView" ref="_bWHMo4qJEeq6Bc7V6nnrmg"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_bWjRmIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.PropertySheet" ref="_bWHMpIqJEeq6Bc7V6nnrmg"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_bWjRmYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.terminal.sdkterminal" ref="_bWaHkoqJEeq6Bc7V6nnrmg"/>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_bWjRmoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.logger.SdkLogViewMStack" containerData="5000" selectedElement="_bWjRm4qJEeq6Bc7V6nnrmg">
                  <children xsi:type="advanced:Placeholder" xmi:id="_bWjRm4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.logger.SdkLogView" ref="_bWaHkIqJEeq6Bc7V6nnrmg"/>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_bMSiRYqJEeq6Bc7V6nnrmg" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_bMSiRoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_bMSiQIqJEeq6Bc7V6nnrmg"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_bMSiR4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_bMSiQYqJEeq6Bc7V6nnrmg"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_bMSiSIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_bMSiQoqJEeq6Bc7V6nnrmg"/>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_bMSiQIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bMSiQYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;root&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_cfwVQIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_cfwVQYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bMSiQoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_bVzqoIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_bVzqoYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bV9b5IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_bXmaYIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bXmaYYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bV9b5YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bV9b5oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWHMoIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWHMoYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot; partName=&quot;Problems&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_bnhFsIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bnhFsYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ProblemView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWHMooqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWHMo4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_dSCGwIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_dSCGwYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWHMpIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWHMpYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_bmnt0IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bmnt0YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ContentOutline"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWaHkIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.logger.SdkLogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="SDK Log" iconURI="platform:/plugin/com.xilinx.sdk.loggers/icons/icon.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_boQskIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.logger.SdkLogView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_boQskYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.logger.SdkLogView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWaHkYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Target Connections" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/target-mgmt-view.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_bkr0IIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_bkr0IYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWaHkoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.terminal.sdkterminal" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="SDK Terminal" iconURI="platform:/plugin/com.xilinx.sdk.terminal/icons/console.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWjRgIqJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Documents" iconURI="platform:/plugin/ilg.gnuarmeclipse.managedbuild.packs/icons/pdficon_small.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:C/C++ Packs</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_bWjRgYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Make Target" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
    </sharedElements>
    <trimBars xmi:id="_a1zU44qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_bRWBYIqJEeq6Bc7V6nnrmg" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bRWBYYqJEeq6Bc7V6nnrmg" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bRWBYoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_EBpuAIqREeqP-evVvQYjCQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_a2i8_YqJEeq6Bc7V6nnrmg"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bRfyYIqJEeq6Bc7V6nnrmg" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bRfyYYqJEeq6Bc7V6nnrmg" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bt6ooIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.CElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_buWtgIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_buDykIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_btd8sIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bRfyYoqJEeq6Bc7V6nnrmg" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bRfyY4qJEeq6Bc7V6nnrmg" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bRfyZIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_EBpuCoqREeqP-evVvQYjCQ" elementId="org.eclipse.ui.window.pinEditor" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_a2i8rYqJEeq6Bc7V6nnrmg"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bRfyZYqJEeq6Bc7V6nnrmg" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bRfyZoqJEeq6Bc7V6nnrmg" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bRfyZ4qJEeq6Bc7V6nnrmg" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_bRfyaIqJEeq6Bc7V6nnrmg" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_bRfyaYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.workbench.help">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bSsFMYqJEeq6Bc7V6nnrmg" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bS1PIIqJEeq6Bc7V6nnrmg" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_a1zU5IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_bS_AIIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bTIxIIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_bTunAIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_a1zU5YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_cuxosIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_a1zU5oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Right"/>
  </children>
  <bindingTables xmi:id="_a1zU54qJEeq6Bc7V6nnrmg" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_a1zU6IqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3SivoqJEeq6Bc7V6nnrmg" keySequence="CTRL+A" command="_a2ZzRIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si0oqJEeq6Bc7V6nnrmg" keySequence="CTRL+C" command="_a2i8AoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si1YqJEeq6Bc7V6nnrmg" keySequence="ALT+PAGE_UP" command="_a2i724qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si5IqJEeq6Bc7V6nnrmg" keySequence="SHIFT+INSERT" command="_a2ZyKYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si_IqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+D" command="_a2i9XoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si_YqJEeq6Bc7V6nnrmg" keySequence="ALT+PAGE_DOWN" command="_a2i8hYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTuYqJEeq6Bc7V6nnrmg" keySequence="CTRL+X" command="_a2Zy_oqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT0YqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+L" command="_a2i9NoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT24qJEeq6Bc7V6nnrmg" keySequence="CTRL+V" command="_a2ZyKYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT4YqJEeq6Bc7V6nnrmg" keySequence="CTRL+1" command="_a2ZyuYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT7IqJEeq6Bc7V6nnrmg" keySequence="CTRL+Z" command="_a2Zy9oqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUAIqJEeq6Bc7V6nnrmg" keySequence="CTRL+Y" command="_a2i7zoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUBIqJEeq6Bc7V6nnrmg" keySequence="CTRL+INSERT" command="_a2i8AoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUCoqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+I" command="_a2ZynoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEqYqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+F3" command="_a2i9BYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEq4qJEeq6Bc7V6nnrmg" keySequence="ALT+/" command="_a2i80IqJEeq6Bc7V6nnrmg">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_a3mEvoqJEeq6Bc7V6nnrmg" keySequence="CTRL+F10" command="_a2ZyQ4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE0YqJEeq6Bc7V6nnrmg" keySequence="SHIFT+DEL" command="_a2Zy_oqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3JYsIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_a2i9tIqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3SioIqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+C" command="_a2i8G4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Sip4qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_a2ZyUYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Sis4qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_DOWN" command="_a2ZyeYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SitoqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+ARROW_UP" command="_a2i72IqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Sit4qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+B" command="_a2i9kYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Siw4qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_UP" command="_a2ZzUIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si9YqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+F" command="_a2i9QYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SjBYqJEeq6Bc7V6nnrmg" keySequence="CTRL+F3" command="_a2i9cYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SjB4qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+/" command="_a2i7yoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cToIqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+U" command="_a2i8rIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTqoqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+O" command="_a2Zy8YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTsIqJEeq6Bc7V6nnrmg" keySequence="CTRL+T" command="_a2i8I4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTtoqJEeq6Bc7V6nnrmg" keySequence="CTRL+7" command="_a2i8G4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTuIqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+M" command="_a2ZyxYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTyYqJEeq6Bc7V6nnrmg" keySequence="CTRL+O" command="_a2ZzRYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUB4qJEeq6Bc7V6nnrmg" keySequence="CTRL+I" command="_a2ZzEYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUH4qJEeq6Bc7V6nnrmg" keySequence="CTRL+/" command="_a2i8G4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEuoqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+P" command="_a2i8j4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEyIqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+\" command="_a2ZyVoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE04qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_a2ZzR4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE1oqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_LEFT" command="_a2ZzGoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE5YqJEeq6Bc7V6nnrmg" keySequence="CTRL+2 R" command="_a2i8vIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE54qJEeq6Bc7V6nnrmg" keySequence="CTRL+2 L" command="_a2ZyS4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE8YqJEeq6Bc7V6nnrmg" keySequence="CTRL+2 M" command="_a2ZzHYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE9oqJEeq6Bc7V6nnrmg" keySequence="CTRL+2 F" command="_a2i9YYqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3SioYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_a2i9uoqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3SiooqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+C" command="_a2i8G4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTt4qJEeq6Bc7V6nnrmg" keySequence="CTRL+7" command="_a2i8G4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUIIqJEeq6Bc7V6nnrmg" keySequence="CTRL+/" command="_a2i8G4qJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3Sio4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_a2i9ooqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3SipIqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+C" command="_a2i8sYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEsIqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+V" command="_a2ZzKoqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3SipYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_a2i9uIqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3SipoqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_a2i9MYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SisoqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_DOWN" command="_a2i9moqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SitYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+ARROW_UP" command="_a2i84oqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SivYqJEeq6Bc7V6nnrmg" keySequence="ALT+C" command="_a2i8LIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SiwoqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_UP" command="_a2ZzKIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SizIqJEeq6Bc7V6nnrmg" keySequence="CTRL+#" command="_a2i8toqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si14qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+G" command="_a2ZyAIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si3IqJEeq6Bc7V6nnrmg" keySequence="SHIFT+TAB" command="_a2i8T4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si3YqJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+H" command="_a2ZyvIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si4IqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+H" command="_a2i8TYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si7YqJEeq6Bc7V6nnrmg" keySequence="CTRL+G" command="_a2i9eIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si9IqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+F" command="_a2i9mYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SjBoqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+/" command="_a2i9XYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTqYqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+O" command="_a2ZzBIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTroqJEeq6Bc7V6nnrmg" keySequence="CTRL+T" command="_a2i8kYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTs4qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+T" command="_a2Zy14qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTvYqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+S" command="_a2ZyRoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTyIqJEeq6Bc7V6nnrmg" keySequence="CTRL+O" command="_a2i8FIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTzYqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+R" command="_a2i83YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT1IqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+N" command="_a2ZypIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT5IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+M" command="_a2ZyH4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT9IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Z" command="_a2i89YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT94qJEeq6Bc7V6nnrmg" keySequence="F4" command="_a2i9WYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT-oqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+O" command="_a2ZzEIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT_YqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+L" command="_a2ZzSoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUA4qJEeq6Bc7V6nnrmg" keySequence="CTRL+TAB" command="_a2i9mIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUBoqJEeq6Bc7V6nnrmg" keySequence="CTRL+I" command="_a2ZyloqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUCIqJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+I" command="_a2i8LoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUE4qJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+S" command="_a2i9goqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUHoqJEeq6Bc7V6nnrmg" keySequence="CTRL+/" command="_a2ZyLIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEpYqJEeq6Bc7V6nnrmg" keySequence="F3" command="_a2i9oYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEs4qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+T" command="_a2i8vYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEuYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+P" command="_a2ZzHoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEx4qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+\" command="_a2i8KoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEz4qJEeq6Bc7V6nnrmg" keySequence="CTRL+=" command="_a2i8toqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE0oqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_a2i8h4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE1YqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_LEFT" command="_a2i80YqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3SiqIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" bindingContext="_a2i9p4qJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3SiqYqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_a2i8Q4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SitIqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_DOWN" command="_a2ZyHIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SixIqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_UP" command="_a2ZzFIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si6IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+END" command="_a2Zy64qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si9oqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+HOME" command="_a2i9bYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT2IqJEeq6Bc7V6nnrmg" keySequence="ALT+R" command="_a2i8zoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE14qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+ARROW_LEFT" command="_a2i78IqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3SiqoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.textEditorScope" bindingContext="_a2i9pIqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3Siq4qJEeq6Bc7V6nnrmg" keySequence="CTRL+ARROW_DOWN" command="_a2i9nIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SisYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_a2ZzNoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SivIqJEeq6Bc7V6nnrmg" keySequence="CTRL+ARROW_UP" command="_a2Zyd4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Siv4qJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+ARROW_UP" command="_a2i9b4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SiwIqJEeq6Bc7V6nnrmg" keySequence="ALT+ARROW_DOWN" command="_a2i8jIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SiwYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+DEL" command="_a2i81YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SixYqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+A" command="_a2i8JIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si1oqJEeq6Bc7V6nnrmg" keySequence="CTRL+ARROW_LEFT" command="_a2i7_YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si5YqJEeq6Bc7V6nnrmg" keySequence="CTRL+BS" command="_a2ZyBIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si5oqJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+J" command="_a2ZysIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si54qJEeq6Bc7V6nnrmg" keySequence="CTRL+END" command="_a2i8joqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si6oqJEeq6Bc7V6nnrmg" keySequence="SHIFT+END" command="_a2ZzNIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si_oqJEeq6Bc7V6nnrmg" keySequence="CTRL+D" command="_a2ZyVIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SjAIqJEeq6Bc7V6nnrmg" keySequence="CTRL+ARROW_RIGHT" command="_a2ZypYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SjAYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_a2ZyjoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SjAoqJEeq6Bc7V6nnrmg" keySequence="SHIFT+HOME" command="_a2ZzJYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SjBIqJEeq6Bc7V6nnrmg" keySequence="CTRL+HOME" command="_a2ZyKIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTrYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+Y" command="_a2ZzL4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTu4qJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+/" command="_a2i9SoqJEeq6Bc7V6nnrmg">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_a3cTwIqJEeq6Bc7V6nnrmg" keySequence="CTRL+NUMPAD_ADD" command="_a2i9YoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTwYqJEeq6Bc7V6nnrmg" keySequence="SHIFT+CR" command="_a2i9QoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT3YqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+Q" command="_a2ZypoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT3oqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+K" command="_a2Zyc4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT5oqJEeq6Bc7V6nnrmg" keySequence="CTRL+NUMPAD_SUBTRACT" command="_a2i9A4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT9oqJEeq6Bc7V6nnrmg" keySequence="END" command="_a2i9GoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT_IqJEeq6Bc7V6nnrmg" keySequence="CTRL+L" command="_a2i85oqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUBYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+J" command="_a2ZymYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUDIqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+INSERT" command="_a2ZyiIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUDoqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_a2i8pIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUD4qJEeq6Bc7V6nnrmg" keySequence="CTRL+NUMPAD_MULTIPLY" command="_a2i8nYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUF4qJEeq6Bc7V6nnrmg" keySequence="CTRL+K" command="_a2i8hIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUGYqJEeq6Bc7V6nnrmg" keySequence="HOME" command="_a2i9R4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUGoqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+X" command="_a2i8C4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEqoqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+CR" command="_a2i9BIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mErYqJEeq6Bc7V6nnrmg" keySequence="F2" command="_a2ZyxIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEroqJEeq6Bc7V6nnrmg" keySequence="CTRL+J" command="_a2ZySoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEr4qJEeq6Bc7V6nnrmg" keySequence="INSERT" command="_a2i8PYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEsoqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_a2i8P4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEvIqJEeq6Bc7V6nnrmg" keySequence="CTRL+NUMPAD_DIVIDE" command="_a2ZyeoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEv4qJEeq6Bc7V6nnrmg" keySequence="CTRL+F10" command="_a2i9AYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEzYqJEeq6Bc7V6nnrmg" keySequence="ALT+ARROW_UP" command="_a2i9kIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEzoqJEeq6Bc7V6nnrmg" keySequence="CTRL+DEL" command="_a2Zy7oqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE2oqJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+ARROW_DOWN" command="_a2ZzSYqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3SirIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_a2i9qoqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3SirYqJEeq6Bc7V6nnrmg" keySequence="ALT+ARROW_RIGHT" command="_a2i8ooqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SiuoqJEeq6Bc7V6nnrmg" keySequence="ALT+ARROW_LEFT" command="_a2i8koqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEp4qJEeq6Bc7V6nnrmg" keySequence="F3" command="_a2i9oYqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3SiroqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.contexts.window" bindingContext="_a1zU6YqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3Sir4qJEeq6Bc7V6nnrmg" keySequence="ALT+ARROW_RIGHT" command="_a2ZzE4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SisIqJEeq6Bc7V6nnrmg" keySequence="CTRL+B" command="_a2ZyMIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Siu4qJEeq6Bc7V6nnrmg" keySequence="ALT+ARROW_LEFT" command="_a2ZySIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SixoqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+D Q" command="_a2ZzUYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Six4qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+D J" command="_a2i8qIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SiyIqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+D A" command="_a2i86IqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SiyYqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+D T" command="_a2ZyDIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SiyoqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+V" command="_a2ZzNYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Siy4qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+U" command="_a2Zyh4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SizYqJEeq6Bc7V6nnrmg" keySequence="CTRL+#" command="_a2ZyRIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si2oqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+G" command="_a2i9f4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si24qJEeq6Bc7V6nnrmg" keySequence="CTRL+H" command="_a2i8z4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si34qJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+H" command="_a2ZyQIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si4oqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+H" command="_a2i7-oqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si44qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+I" command="_a2ZyRYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si6YqJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+G" command="_a2i8b4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si8YqJEeq6Bc7V6nnrmg" keySequence="CTRL+G" command="_a2ZyBYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si-IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+F" command="_a2i9DYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si-oqJEeq6Bc7V6nnrmg" keySequence="CTRL+E" command="_a2Zy7IqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si-4qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+E" command="_a2Zyg4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SjA4qJEeq6Bc7V6nnrmg" keySequence="CTRL+F" command="_a2ZyX4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SjCIqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+N" command="_a2Zy-YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTo4qJEeq6Bc7V6nnrmg" keySequence="CTRL+S" command="_a2ZzMYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTpIqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+W" command="_a2i9boqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTp4qJEeq6Bc7V6nnrmg" keySequence="CTRL+N" command="_a2i9hIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTq4qJEeq6Bc7V6nnrmg" keySequence="CTRL+M" command="_a2i8yYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTrIqJEeq6Bc7V6nnrmg" keySequence="CTRL+Q" command="_a2i9CoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTtYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+T" command="_a2Zy_YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTvIqJEeq6Bc7V6nnrmg" keySequence="F11" command="_a2i9VIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTvoqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+S" command="_a2i77YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTxIqJEeq6Bc7V6nnrmg" keySequence="CTRL+W" command="_a2i71YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTxYqJEeq6Bc7V6nnrmg" keySequence="SHIFT+F9" command="_a2ZzDoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTx4qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+R" command="_a2i9noqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTy4qJEeq6Bc7V6nnrmg" keySequence="ALT+-" command="_a2i8F4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTz4qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+R" command="_a2i70oqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT0IqJEeq6Bc7V6nnrmg" keySequence="CTRL+F8" command="_a2Zyu4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT1YqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+N" command="_a2i8GIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT2YqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+F6" command="_a2i8ZoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT3IqJEeq6Bc7V6nnrmg" keySequence="F12" command="_a2i804qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT4oqJEeq6Bc7V6nnrmg" keySequence="CTRL+." command="_a2i9coqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT44qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+S" command="_a2i8X4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT5YqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+M" command="_a2i9Y4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT54qJEeq6Bc7V6nnrmg" keySequence="CTRL+F11" command="_a2i9HIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT6YqJEeq6Bc7V6nnrmg" keySequence="CTRL+F6" command="_a2ZyaIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT7YqJEeq6Bc7V6nnrmg" keySequence="CTRL+," command="_a2ZyLYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT8YqJEeq6Bc7V6nnrmg" keySequence="CTRL+U" command="_a2i7yYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT84qJEeq6Bc7V6nnrmg" keySequence="F5" command="_a2ZzG4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT9YqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Z" command="_a2i79YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT-YqJEeq6Bc7V6nnrmg" keySequence="F4" command="_a2ZyN4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT-4qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+O" command="_a2i9WIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT_oqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+L" command="_a2ZytYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT_4qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+W" command="_a2Zy_IqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUAoqJEeq6Bc7V6nnrmg" keySequence="F9" command="_a2ZzIIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUC4qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+J" command="_a2Zy6IqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUDYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_a2i8XoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUEIqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+F4" command="_a2Zy_IqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUEoqJEeq6Bc7V6nnrmg" keySequence="CTRL+3" command="_a2Zyw4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUFYqJEeq6Bc7V6nnrmg" keySequence="CTRL+{" command="_a2Zy24qJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3cUFoqJEeq6Bc7V6nnrmg" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_a3mEoIqJEeq6Bc7V6nnrmg" keySequence="SHIFT+F2" command="_a2i8ToqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEoYqJEeq6Bc7V6nnrmg" keySequence="CTRL+F4" command="_a2i71YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEooqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+F7" command="_a2i8kIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEqIqJEeq6Bc7V6nnrmg" keySequence="F3" command="_a2ZysoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mErIqJEeq6Bc7V6nnrmg" keySequence="F2" command="_a2ZyL4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEsYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_a2Zy3YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEtIqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+T" command="_a2i8JYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEtYqJEeq6Bc7V6nnrmg" keySequence="CTRL+P" command="_a2i8_YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEtoqJEeq6Bc7V6nnrmg" keySequence="DEL" command="_a2ZybYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEt4qJEeq6Bc7V6nnrmg" keySequence="ALT+F7" command="_a2i8KIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEuIqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+F7" command="_a2i9VYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEu4qJEeq6Bc7V6nnrmg" keySequence="CTRL+F7" command="_a2i8A4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEvYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+F8" command="_a2Zy2oqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEw4qJEeq6Bc7V6nnrmg" keySequence="ALT+CR" command="_a2i8uYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mExYqJEeq6Bc7V6nnrmg" keySequence="SHIFT+F5" command="_a2ZzTIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEyYqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+?" command="_a2Zy2IqJEeq6Bc7V6nnrmg">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_a3mEy4qJEeq6Bc7V6nnrmg" keySequence="CTRL+_" command="_a2Zy24qJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mEzIqJEeq6Bc7V6nnrmg" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_a3mE0IqJEeq6Bc7V6nnrmg" keySequence="ALT+?" command="_a2Zy2IqJEeq6Bc7V6nnrmg">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_a3mE1IqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+B" command="_a2ZydIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE2IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+C" command="_a2i81IqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE2YqJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+B" command="_a2i8gYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE24qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q Y" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE3IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_a3mE3YqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q J" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE3oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_a3mE34qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q B" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE4IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_a3mE4YqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+X J" command="_a2i8iIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE4oqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q T" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE44qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_a3mE5IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+X T" command="_a2ZzQoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE5oqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+X A" command="_a2ZyK4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE6IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q C" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE6YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_a3mE6oqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q X" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE64qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_a3mE7IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q H" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE7YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_a3mE7oqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q V" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE74qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_a3mE8IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+X Q" command="_a2ZymoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE8oqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q D" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE84qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_a3mE9IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q Z" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE9YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_a3mE94qJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q O" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE-IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_a3mE-YqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q Q" command="_a2i8eIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mE-oqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q S" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE-4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_a3mE_IqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+Q P" command="_a2i8eIqJEeq6Bc7V6nnrmg">
      <parameters xmi:id="_a3mE_YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_a3SiuIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_a2i9s4qJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3SiuYqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+B" command="_a2i9kYqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3SizoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_a2i9vIqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3Siz4qJEeq6Bc7V6nnrmg" keySequence="CTRL+C" command="_a2ZyzIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT2oqJEeq6Bc7V6nnrmg" keySequence="CTRL+V" command="_a2i8CYqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3Si0IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_a2i9soqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3Si0YqJEeq6Bc7V6nnrmg" keySequence="CTRL+C" command="_a2ZyaYqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3Si04qJEeq6Bc7V6nnrmg" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_a2i9tYqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3Si1IqJEeq6Bc7V6nnrmg" keySequence="ALT+E" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si94qJEeq6Bc7V6nnrmg" keySequence="ALT+H" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si-YqJEeq6Bc7V6nnrmg" keySequence="ALT+G" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si_4qJEeq6Bc7V6nnrmg" keySequence="ALT+F" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTuoqJEeq6Bc7V6nnrmg" keySequence="ALT+V" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTv4qJEeq6Bc7V6nnrmg" keySequence="ALT+N" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTxoqJEeq6Bc7V6nnrmg" keySequence="ALT+T" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTyoqJEeq6Bc7V6nnrmg" keySequence="ALT+P" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT14qJEeq6Bc7V6nnrmg" keySequence="ALT+R" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT6IqJEeq6Bc7V6nnrmg" keySequence="ALT+S" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEwIqJEeq6Bc7V6nnrmg" keySequence="ALT+W" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEyoqJEeq6Bc7V6nnrmg" keySequence="ALT+A" command="_a2i8E4qJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3Si2IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_a2i9u4qJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3Si2YqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+G" command="_a2ZyAIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si3oqJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+H" command="_a2ZyvIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si4YqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+H" command="_a2i8TYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3Si7oqJEeq6Bc7V6nnrmg" keySequence="CTRL+G" command="_a2i9eIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTtIqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+T" command="_a2Zy14qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTzoqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+R" command="_a2i83YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT-IqJEeq6Bc7V6nnrmg" keySequence="F4" command="_a2i9WYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUCYqJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+I" command="_a2i8LoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEpoqJEeq6Bc7V6nnrmg" keySequence="F3" command="_a2i9oYqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3Si64qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_a2i9sYqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3Si7IqJEeq6Bc7V6nnrmg" keySequence="CTRL+G" command="_a2i9gIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUGIqJEeq6Bc7V6nnrmg" keySequence="HOME" command="_a2ZyxoqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3Si74qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_a2i9r4qJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3Si8IqJEeq6Bc7V6nnrmg" keySequence="CTRL+G" command="_a2i8x4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3SjCYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+." command="_a2i8xYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUEYqJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+," command="_a2i9DIqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3Si8oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_a2i9t4qJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3Si84qJEeq6Bc7V6nnrmg" keySequence="CTRL+SHIFT+F" command="_a2i9QYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTqIqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+O" command="_a2ZyCoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTzIqJEeq6Bc7V6nnrmg" keySequence="ALT+SHIFT+R" command="_a2ZyNoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUIYqJEeq6Bc7V6nnrmg" keySequence="SHIFT+F2" command="_a2i8MoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEo4qJEeq6Bc7V6nnrmg" keySequence="F3" command="_a2ZyD4qJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3cToYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.debugging" bindingContext="_a2i9roqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3cTooqJEeq6Bc7V6nnrmg" keySequence="CTRL+R" command="_a2i8CIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTwoqJEeq6Bc7V6nnrmg" keySequence="CTRL+F2" command="_a2i82YqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT0oqJEeq6Bc7V6nnrmg" keySequence="F7" command="_a2i9dIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT1oqJEeq6Bc7V6nnrmg" keySequence="F6" command="_a2ZzN4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT8IqJEeq6Bc7V6nnrmg" keySequence="CTRL+F5" command="_a2i9UYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT8oqJEeq6Bc7V6nnrmg" keySequence="F5" command="_a2ZyPoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUFIqJEeq6Bc7V6nnrmg" keySequence="F8" command="_a2i8O4qJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3cTpYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_a2i9rIqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3cTpoqJEeq6Bc7V6nnrmg" keySequence="CTRL+N" command="_a2i8bIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTr4qJEeq6Bc7V6nnrmg" keySequence="CTRL+T" command="_a2Zyx4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cTw4qJEeq6Bc7V6nnrmg" keySequence="CTRL+W" command="_a2i8QYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT04qJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+M" command="_a2ZzQYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT7oqJEeq6Bc7V6nnrmg" keySequence="ALT+CTRL+N" command="_a2i9ZYqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3cTsYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_a2i9vYqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3cTsoqJEeq6Bc7V6nnrmg" keySequence="SHIFT+F8" command="_a2i8w4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cT74qJEeq6Bc7V6nnrmg" keySequence="CTRL+F5" command="_a2i9S4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUAYqJEeq6Bc7V6nnrmg" keySequence="SHIFT+F7" command="_a2i8VIqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3cUG4qJEeq6Bc7V6nnrmg" keySequence="SHIFT+F6" command="_a2ZyMoqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mExIqJEeq6Bc7V6nnrmg" keySequence="SHIFT+F5" command="_a2ZzOIqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3cT34qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_a2i9rYqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3cT4IqJEeq6Bc7V6nnrmg" keySequence="CTRL+1" command="_a2i9e4qJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3cT6oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.console" bindingContext="_a2i9poqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3cT64qJEeq6Bc7V6nnrmg" keySequence="CTRL+Z" command="_a2i9a4qJEeq6Bc7V6nnrmg">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_a3cUHIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_a2i9toqJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3cUHYqJEeq6Bc7V6nnrmg" keySequence="CTRL+/" command="_a2Zyf4qJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mEpIqJEeq6Bc7V6nnrmg" keySequence="F3" command="_a2i8VYqJEeq6Bc7V6nnrmg"/>
    <bindings xmi:id="_a3mExoqJEeq6Bc7V6nnrmg" keySequence="CTRL+\" command="_a2Zya4qJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_a3mEwYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_a2i9o4qJEeq6Bc7V6nnrmg">
    <bindings xmi:id="_a3mEwoqJEeq6Bc7V6nnrmg" keySequence="ALT+CR" command="_a2i7-YqJEeq6Bc7V6nnrmg"/>
  </bindingTables>
  <bindingTables xmi:id="_bVzqo4qJEeq6Bc7V6nnrmg" bindingContext="_bVzqooqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bVzqpYqJEeq6Bc7V6nnrmg" bindingContext="_bVzqpIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bVzqp4qJEeq6Bc7V6nnrmg" bindingContext="_bVzqpoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bVzqqYqJEeq6Bc7V6nnrmg" bindingContext="_bVzqqIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bVzqq4qJEeq6Bc7V6nnrmg" bindingContext="_bVzqqoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bVzqrYqJEeq6Bc7V6nnrmg" bindingContext="_bVzqrIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bVzqr4qJEeq6Bc7V6nnrmg" bindingContext="_bVzqroqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bVzqsYqJEeq6Bc7V6nnrmg" bindingContext="_bVzqsIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bVzqs4qJEeq6Bc7V6nnrmg" bindingContext="_bVzqsoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9boYqJEeq6Bc7V6nnrmg" bindingContext="_bV9boIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bo4qJEeq6Bc7V6nnrmg" bindingContext="_bV9booqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bpYqJEeq6Bc7V6nnrmg" bindingContext="_bV9bpIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bp4qJEeq6Bc7V6nnrmg" bindingContext="_bV9bpoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bqYqJEeq6Bc7V6nnrmg" bindingContext="_bV9bqIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bq4qJEeq6Bc7V6nnrmg" bindingContext="_bV9bqoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9brYqJEeq6Bc7V6nnrmg" bindingContext="_bV9brIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9br4qJEeq6Bc7V6nnrmg" bindingContext="_bV9broqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bsYqJEeq6Bc7V6nnrmg" bindingContext="_bV9bsIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bs4qJEeq6Bc7V6nnrmg" bindingContext="_bV9bsoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9btYqJEeq6Bc7V6nnrmg" bindingContext="_bV9btIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bt4qJEeq6Bc7V6nnrmg" bindingContext="_bV9btoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9buYqJEeq6Bc7V6nnrmg" bindingContext="_bV9buIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bu4qJEeq6Bc7V6nnrmg" bindingContext="_bV9buoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bvYqJEeq6Bc7V6nnrmg" bindingContext="_bV9bvIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bv4qJEeq6Bc7V6nnrmg" bindingContext="_bV9bvoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bwYqJEeq6Bc7V6nnrmg" bindingContext="_bV9bwIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bw4qJEeq6Bc7V6nnrmg" bindingContext="_bV9bwoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bxYqJEeq6Bc7V6nnrmg" bindingContext="_bV9bxIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bx4qJEeq6Bc7V6nnrmg" bindingContext="_bV9bxoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9byYqJEeq6Bc7V6nnrmg" bindingContext="_bV9byIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9by4qJEeq6Bc7V6nnrmg" bindingContext="_bV9byoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bzYqJEeq6Bc7V6nnrmg" bindingContext="_bV9bzIqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9bz4qJEeq6Bc7V6nnrmg" bindingContext="_bV9bzoqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9b0YqJEeq6Bc7V6nnrmg" bindingContext="_bV9b0IqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9b04qJEeq6Bc7V6nnrmg" bindingContext="_bV9b0oqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9b1YqJEeq6Bc7V6nnrmg" bindingContext="_bV9b1IqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9b14qJEeq6Bc7V6nnrmg" bindingContext="_bV9b1oqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9b2YqJEeq6Bc7V6nnrmg" bindingContext="_bV9b2IqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9b24qJEeq6Bc7V6nnrmg" bindingContext="_bV9b2oqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9b3YqJEeq6Bc7V6nnrmg" bindingContext="_bV9b3IqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9b34qJEeq6Bc7V6nnrmg" bindingContext="_bV9b3oqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9b4YqJEeq6Bc7V6nnrmg" bindingContext="_bV9b4IqJEeq6Bc7V6nnrmg"/>
  <bindingTables xmi:id="_bV9b44qJEeq6Bc7V6nnrmg" bindingContext="_bV9b4oqJEeq6Bc7V6nnrmg"/>
  <rootContext xmi:id="_a1zU6IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_a1zU6YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_a1zU6oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_a2i9ooqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal widget context" description="Override ALT+x menu access keys"/>
      <children xmi:id="_a2i9o4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_a2i9pIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_a2i9rYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_a2i9tIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_a2i9toqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_a2i9t4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_a2i9uIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
        <children xmi:id="_a2i9uoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_a2i9poqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_a2i9qIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_a2i9q4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_a2i9rIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_a2i9roqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_a2i9r4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_a2i9sIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tcf.debug.ui.debugging" name="Debugging using Target Communication Framework" description="Debugging using Target Communication Framework"/>
        <children xmi:id="_a2i9sYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_a2i9uYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
        <children xmi:id="_a2i9vYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_a2i9soqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_a2i9u4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_a2i9vIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
    </children>
    <children xmi:id="_a1zU64qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_a2i9qoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_a2i9pYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_a2i9p4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" name="UML2 Sequence Diagram Viewer"/>
  <rootContext xmi:id="_a2i9qYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_a2i9s4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_a2i9tYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal widget context" description="Override ALT+x menu access keys"/>
  <rootContext xmi:id="_bVzqooqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_bVzqpIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_bVzqpoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_bVzqqIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_bVzqqoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_bVzqrIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_bVzqroqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_bVzqsIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_bVzqsoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_bV9boIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_bV9booqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_bV9bpIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_bV9bpoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_bV9bqIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_bV9bqoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_bV9brIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_bV9broqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_bV9bsIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_bV9bsoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_bV9btIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_bV9btoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_bV9buIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_bV9buoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_bV9bvIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_bV9bvoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_bV9bwIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_bV9bwoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_bV9bxIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_bV9bxoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_bV9byIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_bV9byoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.core.search.searchActionSet" name="Auto::org.eclipse.rse.core.search.searchActionSet"/>
  <rootContext xmi:id="_bV9bzIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_bV9bzoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_bV9b0IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_bV9b0oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_bV9b1IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_bV9b1oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_bV9b2IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_bV9b2oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_bV9b3IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_bV9b3oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_bV9b4IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_bV9b4oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_a8pjwIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
  </descriptors>
  <descriptors xmi:id="_a8pjwYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.logger.SdkLogView" label="SDK Log" iconURI="platform:/plugin/com.xilinx.sdk.loggers/icons/icon.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a8zUwIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.oprofile.view" label="Oprofile View" iconURI="platform:/plugin/com.xilinx.sdk.oprofile/icons/gprof.gif" tooltip="" category="Oprofile" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Oprofile</tags>
  </descriptors>
  <descriptors xmi:id="_a8zUwYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.performance.ui.views.PsPerfGraphsView" label="PS Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a8zUwoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.performance.ui.views.PsPerfTableView" label="PS Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a8zUw4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfTableView" label="APM Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a8zUxIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfGraphsView" label="APM Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88esIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.performance.ui.views.PerfSessionManagerView" label="Performance Session Manager" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88esYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.performance.ui.views.MbPerfGraphsView" label="MicroBlaze Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88esoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.performance.ui.views.MbPerfTableView" label="MicroBlaze Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88es4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.performance.ui.views.TraceSessionManagerView" label="Trace Session Manager" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88etIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.performance.stm.freertos.trace.FreeRtosAnalysisView" label="FreeRTOS Analysis" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88etYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView" label="XSCT Console" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/xsdbconsole_icon.png" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88etoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView" label="Target Connections" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/target-mgmt-view.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88et4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView" label="QEMU Console" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/xsdbconsole_icon.png" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88euIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.terminal.sdkterminal" label="SDK Terminal" iconURI="platform:/plugin/com.xilinx.sdk.terminal/icons/console.gif" tooltip="" allowMultiple="true" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88euYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdsoc.trace.view.AxiEventsView" label="AXI State View" iconURI="platform:/plugin/com.xilinx.sdsoc.trace/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_a88euoqJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView" label="Documents" iconURI="platform:/plugin/ilg.gnuarmeclipse.managedbuild.packs/icons/pdficon_small.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_a88eu4qJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.packs.ui.views.DevicesView" label="Devices" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/hardware_chip.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_a88evIqJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.packs.ui.views.BoardsView" label="Boards" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/board.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPsIqJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.packs.ui.views.KeywordsView" label="Keywords" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/info_obj.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPsYqJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.packs.ui.views.PackagesView" label="Packs" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/packages.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPsoqJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.packs.ui.views.OutlineView" label="Outline" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/outline_co.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPs4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPtIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPtYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPtoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPt4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPuIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPuYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9GPuoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Make Target" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZoIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZoYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZooqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZo4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZpIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZpYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZpoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZp4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZqIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZqYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZqoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZq4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZrIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZrYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZroqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZr4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZsIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_a9PZsYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKoIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKoYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Display" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKooqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKo4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKpIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKpYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKpoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKp4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKqIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKqYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKqoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKq4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKrIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" tooltip="" allowMultiple="true" category="Charts" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKrYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.gprof.view" label="gprof" iconURI="platform:/plugin/org.eclipse.linuxtools.gprof/icons/toggle.gif" tooltip="Gprof view displays the profiling information contained in a gmon.out file" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKroqJEeq6Bc7V6nnrmg" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKr4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.shells.ui.view.commandsView" label="Remote Shell" iconURI="platform:/plugin/org.eclipse.rse.shells.ui/icons/full/cview16/commands_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_a9ZKsIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.terminals.ui.view.TerminalView" label="Terminals" iconURI="platform:/plugin/org.eclipse.rse.terminals.ui/icons/terminal_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7oIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.ui.view.systemView" label="Remote Systems" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7oYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.ui.view.teamView" label="Team" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/team_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7ooqJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.ui.view.systemTableView" label="Remote System Details" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7o4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.ui.view.SystemSearchView" label="Remote Search" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/obj16/system_search.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7pIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.ui.view.scratchpad.SystemScratchpadViewPart" label="Remote Scratchpad" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/scratchpad_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7pYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.ui.view.monitorView" label="Remote Monitor" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7poqJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7p4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7qIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tcf.TraceView" label="TCF Trace" iconURI="platform:/plugin/org.eclipse.tcf.debug.ui/icons/tcf.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7qYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tcf.ProfilerView" label="TCF Profiler" iconURI="platform:/plugin/org.eclipse.tcf.debug.ui/icons/profiler.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7qoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7q4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7rIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tm.terminal.view.TerminalView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view/icons/cview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_a9i7rYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tracecompass.analysis.os.linux.views.controlflow" label="Control Flow" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/control_flow_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFkIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tracecompass.analysis.os.linux.views.resources" label="Resources" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFkYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tracecompass.analysis.os.linux.views.cpuusage" label="CPU Usage" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFkoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.views.control" label="Control" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.control.ui/icons/eview16/control_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFk4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.lttng2.ust.memoryusage" label="UST Memory Usage" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFlIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.views.timegraph" label="XML Time Graph View" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFlYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.tmf.analysis.xml.ui.views.xyview" label="XML XY Chart View" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFloqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.pcap.ui.view.stream.list" label="Stream List" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.pcap.ui/icons/stream_list_view.gif" tooltip="" category="Network Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Network Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFl4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.timechart" label="Time Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/timechart_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFmIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.ssvisualizer" label="State System Explorer" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/events_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFmYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" label="Colors" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/colors_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFmoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" label="Filters" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/filters_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFm4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.tmfUml2SDSyncView" label="Sequence Diagram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/sequencediagram_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFnIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" label="Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFnYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" label="Histogram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_a9sFnoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.callstack" label="Call Stack" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/callstack_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_a912kIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.synchronization" label="Synchronization" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/synced.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_a912kYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912koqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912k4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_a912lIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912lYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912loqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912l4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912mIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912mYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912moqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912m4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912nIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_a912nYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <snippets xsi:type="advanced:Perspective" xmi:id="_eGRAEIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_eGRAEYqJEeq6Bc7V6nnrmg" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif" tooltip="C/C++">
    <persistedState key="persp.hiddenItems" value="persp.hideMenuSC:org.eclipse.jdt.ui.refactoring.menu,persp.hideMenuSC:org.eclipse.jdt.ui.source.menu,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
    <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
    <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
    <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
    <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
    <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
    <tags>persp.viewSC:ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView</tags>
    <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
    <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
    <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
    <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
    <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
    <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
    <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
    <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
    <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
    <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
    <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
    <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
    <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
    <tags>persp.newWizSC:com.xilinx.sdk.appwiz.AppWizard</tags>
    <tags>persp.newWizSC:com.xilinx.sdk.profile.ui.wizards.ZpeProjectWizard</tags>
    <tags>persp.newWizSC:com.xilinx.sdk.sw.ui.NewBspWizard</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_eGRAEYqJEeq6Bc7V6nnrmg" selectedElement="_eGRAEoqJEeq6Bc7V6nnrmg" horizontal="true">
      <children xsi:type="basic:PartSashContainer" xmi:id="_eGRAEoqJEeq6Bc7V6nnrmg" containerData="2500" selectedElement="_eGRAE4qJEeq6Bc7V6nnrmg">
        <children xsi:type="basic:PartStack" xmi:id="_eGRAE4qJEeq6Bc7V6nnrmg" elementId="topLeft" containerData="7500" selectedElement="_eGRAFIqJEeq6Bc7V6nnrmg">
          <children xsi:type="advanced:Placeholder" xmi:id="_eGRAFIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
          <children xsi:type="advanced:Placeholder" xmi:id="_eGRAFYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false"/>
          <children xsi:type="advanced:Placeholder" xmi:id="_eGRAFoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false"/>
          <children xsi:type="advanced:Placeholder" xmi:id="_eGRAF4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false"/>
        </children>
        <children xsi:type="basic:PartStack" xmi:id="_eGRAGIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementViewMStack" containerData="2500" selectedElement="_eGRAGYqJEeq6Bc7V6nnrmg">
          <children xsi:type="advanced:Placeholder" xmi:id="_eGRAGYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView"/>
        </children>
      </children>
      <children xsi:type="basic:PartSashContainer" xmi:id="_eGRAGoqJEeq6Bc7V6nnrmg" containerData="7500">
        <children xsi:type="basic:PartSashContainer" xmi:id="_eGRAG4qJEeq6Bc7V6nnrmg" containerData="7500" horizontal="true">
          <children xsi:type="advanced:Placeholder" xmi:id="_eGRAHIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.editorss" containerData="7500"/>
          <children xsi:type="basic:PartStack" xmi:id="_eGRAHYqJEeq6Bc7V6nnrmg" elementId="topRight" containerData="2500" selectedElement="_eGRAHoqJEeq6Bc7V6nnrmg">
            <children xsi:type="advanced:Placeholder" xmi:id="_eGRAHoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ContentOutline"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_eGRAH4qJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_eGRAIIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.views.MakeView"/>
          </children>
        </children>
        <children xsi:type="basic:PartSashContainer" xmi:id="_eGRAIYqJEeq6Bc7V6nnrmg" containerData="2500" horizontal="true">
          <children xsi:type="basic:PartStack" xmi:id="_eGRAIoqJEeq6Bc7V6nnrmg" elementId="bottom" containerData="5000" selectedElement="_eGRAI4qJEeq6Bc7V6nnrmg">
            <children xsi:type="advanced:Placeholder" xmi:id="_eGRAI4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.ProblemView"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_eGRAJIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.TaskList"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_eGRAJYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.console.ConsoleView"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_eGRAJoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.PropertySheet"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_eGRAJ4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.terminal.sdkterminal"/>
          </children>
          <children xsi:type="basic:PartStack" xmi:id="_eGRAKIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.logger.SdkLogViewMStack" containerData="5000" selectedElement="_eGRAKYqJEeq6Bc7V6nnrmg">
            <children xsi:type="advanced:Placeholder" xmi:id="_eGRAKYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.logger.SdkLogView"/>
          </children>
        </children>
      </children>
    </children>
  </snippets>
  <commands xmi:id="_a2Zx-4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zx_IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zx_YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_a2Zx9YqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2Zx_oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_a2Zx_4qJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.packs.commands.updateCommand" commandName="Refresh" category="_a2Zx2YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyAIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyAYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyAoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawarerefresh" commandName="Refresh" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyA4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyBIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyBYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyBoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyB4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyCIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyCYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.bootimage.commands.CreateBootImage" commandName="Create Boot Image" category="_a2Zx54qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyCoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyC4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyDIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyDYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_a2Zx8IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyDoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyD4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyEIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyEYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_a2Zx5YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyEoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyE4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.commands.configureQEMU" commandName="Configure QEMU Settings" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyFIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.commands.viewXSDBConsole" commandName="XSCT Console" description="View XSCT Console" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyFYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tracecompass.tmf.ui.copy_to_clipboard" commandName="Copy to Clipboard" description="Copy to Clipboard" category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyFoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyF4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_a2Zx84qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyGIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.internal.reflog.CheckoutCommand" commandName="Checkout" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyGYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyGoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyG4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.delete" commandName="Delete" description="Delete Target Node" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyHIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDDown" commandName="Scroll down" description="Scroll down the sequence diagram" category="_a2Zx7oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyHYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_a2Zx3IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyHoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyH4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_a2Zx4IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyIIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewRefresh" commandName="Refresh" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyIYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_a2Zx2oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2ZyIoqJEeq6Bc7V6nnrmg" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_a2ZyI4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to" description="Go to a particular resource in the active view" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyJIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyJYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyJoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyJ4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyKIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyKYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyKoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyK4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyLIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Comment/Uncomment" description="Comments/Uncomments the selected lines" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyLYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyLoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyL4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyMIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyMYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyMoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_a2Zx9IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyM4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyNIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyNYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyNoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyN4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyOIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyOYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyOoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences" commandName="Modify ATF Configuration"/>
  <commands xmi:id="_a2ZyO4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyPIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tcf.debug.ui.commands.toggleFilterVariants" commandName="Filter Variants by Discriminant Value" category="_a2Zx94qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyPYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_a2Zx8YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyPoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyP4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyQIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyQYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyQoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnChannel" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyQ4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyRIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyRYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyRoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyR4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZySIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZySYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZySoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyS4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyTIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyTYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyToqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyT4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.profile.ui.commands.configurefsbl" commandName="Configure FSBL Parameters" category="_a2Zx-IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyUIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyUYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyUoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyU4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyVIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyVYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.executeScript" commandName="Execute Command Script..." description="Execute Command Script" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyVoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyV4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method in its hierarchy" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyWIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnChannel" commandName="Enable Event..." description="Enable Event" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyWYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyWoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyW4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_a2Zx2oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2ZyXIqJEeq6Bc7V6nnrmg" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_a2ZyXYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.start" commandName="Start" description="Start Trace Session" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyXoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyX4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyYIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.openFile" commandName="Open File" description="Opens a file" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyYYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyYoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.utils.enablewebtalk" commandName="Enable Webtalk" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyY4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_a2Zx9YqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2ZyZIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_a2ZyZYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyZoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyZ4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_a2Zx0IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyaIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyaYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy SHA-1" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyaoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_a2Zx4YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zya4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.uncomment" commandName="Uncomment" description="Uncomment the selected # style comment lines" category="_a2Zx6IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZybIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZybYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyboqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyb4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZycIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZycYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZycoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.sw.commands.bspsettings" commandName="BSP Settings" category="_a2Zx1oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyc4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZydIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZydYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.Revert" commandName="Revert Commit" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZydoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.StashDrop" commandName="Delete Stashed Commit..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyd4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyeIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyeYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyeoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zye4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyfIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableChannel" commandName="Disable Channel" description="Disable a Trace Channel" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyfYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyfoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyf4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.comment" commandName="Comment" description="Turn the selected lines into # style comments" category="_a2Zx6IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZygIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_a2Zx24qJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2ZygYqJEeq6Bc7V6nnrmg" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_a2ZygoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyg4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyhIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Dynamic Help" description="Open the dynamic help" category="_a2Zx4YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyhYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyhoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.CreateTag" commandName="Create Tag..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyh4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyiIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyiYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyioqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyi4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyjIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyjYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyjoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyj4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_a2Zx8IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZykIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZykYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_a2Zx4IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZykoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyk4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Directory" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZylIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZylYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyloqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyl4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZymIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZymYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZymoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zym4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZynIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.commands.progflash" commandName="Program Flash Memory" description="Program Flash Memory" category="_a2Zx-IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZynYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZynoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyn4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_a2Zx14qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyoIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_a2Zx2IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyoYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyooqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyo4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZypIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZypYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZypoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyp4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyqIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyqYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyqoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyq4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyrIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_a2Zx4YqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2ZyrYqJEeq6Bc7V6nnrmg" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_a2ZyroqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyr4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZysIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZysYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZysoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zys4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZytIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_a2Zx14qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZytYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZytoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableEvent" commandName="Disable Event" description="Disable Event" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyt4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyuIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyuYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyuoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyu4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyvIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyvYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.StashApply" commandName="Apply Stashed Changes" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyvoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyv4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZywIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open" category="_a2Zx9YqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2ZywYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_a2ZywoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyw4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.quickAccess" commandName="Quick Access" description="Quickly access UI elements" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyxIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyxYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyxoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyx4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyyIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyyYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyyoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zyy4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyzIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyzYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZyzoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_a2Zx3oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2Zyz4qJEeq6Bc7V6nnrmg" elementId="url" name="URL"/>
    <parameters xmi:id="_a2Zy0IqJEeq6Bc7V6nnrmg" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_a2Zy0YqJEeq6Bc7V6nnrmg" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_a2Zy0oqJEeq6Bc7V6nnrmg" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_a2Zy04qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy1IqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.xlcm.commands.acquire" commandName="Acquire a License Key..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy1YqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.libdoc.sdklibhelpint" commandName="Xilinx OS and Libraries Help" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy1oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy14qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy2IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy2YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy2oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy24qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_a2Zx3oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2Zy3IqJEeq6Bc7V6nnrmg" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_a2Zy3YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy3oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_a2Zx3IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy34qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy4IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.lockToolBar" commandName="Lock the Toolbars" description="Lock the Toolbars" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy4YqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.hw.commands.changehwspec" commandName="Change Hardware Platform Specification" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy4oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.exportToText" commandName="Export to Text..." description="Export trace to text..." category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy44qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy5IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy5YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy5oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy54qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy6IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy6YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnSession" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy6oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="%RebaseInteractiveCurrentHandler.name" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy64qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeEnd" commandName="Show node end" description="Show the node end" category="_a2Zx7oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy7IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy7YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.importtracepkg" commandName="Import Trace Package..." category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy7oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy74qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.importxml" commandName="Import XML analysis" description="Import an XML file containing analysis information" category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy8IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy8YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy8oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy84qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy9IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy9YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy9oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy94qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disconnect" commandName="Disconnect" description="Disconnect to Target Node" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy-IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.managecustomparsers" commandName="Manage Custom Parsers..." description="Manage Custom Parsers" category="_a2Zx9oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy-YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy-oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy-4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy_IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy_YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy_oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2Zy_4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzAIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnEvent" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzAYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzAoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzA4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzBIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzBYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzBoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzB4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzCIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzCYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_a2Zx24qJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2ZzCoqJEeq6Bc7V6nnrmg" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_a2ZzC4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.tcf.cdt.ui.add_watchpoint" commandName="Add Watchpoint (C/C++)" description="Allows to add a new watchpoint on an arbitrary symbol" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzDIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzDYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzDoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Make Target Build" description="Invoke a make target build for the selected container." category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzD4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_a2Zx8IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzEIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzEYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzEoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzE4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzFIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDUp" commandName="Scroll up" description="Scroll up the sequence diagram" category="_a2Zx7oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzFYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzFoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzF4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzGIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzGYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzGoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzG4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzHIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzHYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzHoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzH4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzIIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzIYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzIoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnDomain" commandName="Enable Channel..." description="Enable a Trace Channel" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzI4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzJIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_a2Zx5YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzJYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzJoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzJ4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzKIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzKYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzKoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_a2Zx5oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzK4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzLIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzLYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzLoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzL4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzMIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzMYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzMoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzM4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_a2Zx0IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzNIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzNYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzNoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzN4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzOIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_a2Zx9IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzOYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_a2Zx3IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzOoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_a2Zx24qJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2ZzO4qJEeq6Bc7V6nnrmg" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_a2ZzPIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzPYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzPoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzP4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.event" commandName="Enable Event..." description="Assign Event to Session and Channel and Enable Event " category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzQIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzQYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzQoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzQ4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzRIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzRYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzRoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.delete_suppl_files" commandName="Delete Supplementary Files..." category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzR4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzSIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzSYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzSoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_a2Zx4IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzS4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_a2Zx4YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzTIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzTYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzToqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzT4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzUIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzUYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzUoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewRenameBranch" commandName="Rename Branch..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2ZzU4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7wIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_a2Zx3oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i7wYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_a2i7woqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.commands.programfpga2" commandName="Configure FPGA with download.bit" category="_a2Zx44qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7w4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_a2Zx4YqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i7xIqJEeq6Bc7V6nnrmg" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_a2i7xYqJEeq6Bc7V6nnrmg" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_a2i7xoqJEeq6Bc7V6nnrmg" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_a2i7x4qJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.packs.commands.showPerspectiveCommand" commandName="Switch to C/C++ Packs Perspective" category="_a2Zx2YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7yIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7yYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7yoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7y4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.shells.ui.actions.LaunchShellCommand" commandName="Launch Shell" category="_a2Zx3YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7zIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7zYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7zoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7z4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_a2Zx4IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i70IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i70YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_a2Zx5IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i70oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i704qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i71IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.CherryPick" commandName="Cherry Pick" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i71YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i71oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i714qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i72IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i72YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_a2Zx9YqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i72oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_a2i724qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i73IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.newConnection" commandName="New Connection..." description="New Connection to Target Node" category="_a2Zx6YqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i73YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.lttng2.control.ui.remoteServicesIdParameter" name="Remote Services ID"/>
    <parameters xmi:id="_a2i73oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.lttng2.control.ui.connectionNameParameter" name="Connection Name"/>
  </commands>
  <commands xmi:id="_a2i734qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i74IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_a2Zx2oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i74YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_a2i74oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.new_folder" commandName="New Folder..." description="Create a new trace folder" category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i744qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i75IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_a2Zx7IqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i75YqJEeq6Bc7V6nnrmg" elementId="title" name="Title"/>
    <parameters xmi:id="_a2i75oqJEeq6Bc7V6nnrmg" elementId="message" name="Message"/>
    <parameters xmi:id="_a2i754qJEeq6Bc7V6nnrmg" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_a2i76IqJEeq6Bc7V6nnrmg" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_a2i76YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i76oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_a2Zx-YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i764qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i77IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i77YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i77oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i774qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.Revert" commandName="Revert Commit" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i78IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDLeft" commandName="Scroll left" description="Scroll left the sequence diagram" category="_a2Zx7oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i78YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i78oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_a2Zx3IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i784qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i79IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Make Target" description="Create a new make build target for the selected container." category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i79YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i79oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i794qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7-IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7-YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7-oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7-4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id To Clipboard" description="Copies the build id to the clipboard." category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7_IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7_YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7_oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_a2Zx5YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i7_4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_a2Zx3oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i8AIqJEeq6Bc7V6nnrmg" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_a2i8AYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8AoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8A4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8BIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_a2Zx0oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i8BYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_a2i8BoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8B4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8CIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8CYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8CoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8C4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8DIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8DYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8DoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8D4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8EIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8EYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8EoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8E4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_a2Zx5oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8FIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8FYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8FoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.xlcm.commands.manage" commandName="Manage License..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8F4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8GIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8GYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8GoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8G4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8HIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8HYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_a2Zx-YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8HoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.clear_offset" commandName="Clear Time Offset" description="Clear time offset" category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8H4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8IIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8IYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8IoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor " description="Toggles linking of a view's selection with the active editor's selection" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8I4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8JIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8JYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8JoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8J4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8KIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8KYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8KoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8K4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.CreateBranch" commandName="Create Branch" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8LIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_a2Zx4IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8LYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8LoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8L4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8MIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.sw.commands.scanrepo" commandName="Scan Repositories" category="_a2Zx1oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8MYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8MoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8M4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8NIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8NYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_a2Zx14qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8NoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8N4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8OIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_a2Zx4YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8OYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8OoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_a2Zx-oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8O4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8PIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8PYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8PoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8P4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8QIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8QYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8QoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8Q4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDRight" commandName="Scroll right" description="Scroll right the sequence diagram" category="_a2Zx7oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8RIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8RYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8RoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8R4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8SIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8SYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8SoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawareModeExec" commandName="Auto refresh on exec" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8S4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.analysis_help" commandName="Help" description="Help" category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8TIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8TYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8ToqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8T4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8UIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8UYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8UoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.tools.command.linkgen" commandName="Generate Linker Script" description="Generates a Linker Script" category="_a2Zx-IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8U4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8VIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_a2Zx9IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8VYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_a2Zx6IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8VoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8V4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.refresh" commandName="Refresh" description="Refresh Node Configuration" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8WIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_a2Zx8IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8WYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.RenameBranch" commandName="Rename Branch..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8WoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnSession" commandName="Enable Channel..." description="Enable a Trace Channel" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8W4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_a2Zx0oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i8XIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_a2i8XYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8XoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8X4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8YIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8YYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8YoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8Y4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8ZIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8ZYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8ZoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8Z4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8aIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8aYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.import" commandName="Import..." description="Import traces into project" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8aoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8a4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8bIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8bYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8boqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Field" description="Create getting and setting methods for the field and use only those to access the field" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8b4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8cIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.commands.programfpga" commandName="Program FPGA" category="_a2Zx-IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8cYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.exporttracepkg" commandName="Export Trace Package..." category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8coqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8c4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8dIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_a2Zx9IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8dYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8doqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.ui.commands.buildsettings" commandName="Change C/C++ Build Settings" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8d4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8eIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_a2Zx0YqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i8eYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_a2i8eoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_a2i8e4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_a2i8fIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_a2Zx4IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8fYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8foqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8f4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_a2Zx2oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i8gIqJEeq6Bc7V6nnrmg" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_a2i8gYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8goqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8g4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.createSession" commandName="Create Session..." description="Create a Trace Session" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8hIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8hYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8hoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8h4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8iIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8iYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8ioqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_a2Zx4YqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i8i4qJEeq6Bc7V6nnrmg" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_a2i8jIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8jYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8joqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8j4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8kIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8kYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8koqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8k4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.synchronize_traces" commandName="Synchronize Traces" description="Synchronize 2 or more traces" category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8lIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8lYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8loqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_a2Zx5YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8l4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.CherryPick" commandName="Cherry Pick" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8mIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8mYqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.profile.ui.commands.configureApm" commandName="Configure Performance Analysis" category="_a2Zx-IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8moqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8m4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8nIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8nYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8noqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.select_traces" commandName="Select Traces..." description="Select Traces" category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8n4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8oIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tcf.cdt.debug.ui.command.breakpointCategoryProperties" commandName="Breakpoint Scope Properties" description="Edits the breakpoint scope settings of the given group of breakpoints. " category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8oYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_a2Zx4YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8ooqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8o4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8pIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8pYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_a2Zx4YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8poqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.commands.viewQEMUConsole" commandName="QEMU Console" description="View QEMU Console" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8p4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8qIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8qYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.destroySession" commandName="Destroy Session..." description="Destroy a Trace Session" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8qoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8q4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8rIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8rYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8roqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8r4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8sIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8sYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_a2Zx5oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8soqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8s4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8tIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_a2Zx3IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8tYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_a2Zx8YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8toqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8t4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8uIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.ShowInHistory" commandName="Show in History" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8uYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8uoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnDomain" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8u4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8vIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8vYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_a2Zx4IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8voqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8v4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.tools.command.dumpmem" commandName="Dump/Restore Data File" description="Dump/Restore Data File" category="_a2Zx-IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8wIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8wYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8woqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8w4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_a2Zx9IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8xIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8xYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8xoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.calibrate" commandName="Calibrate" description="Quantify LTTng overhead" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8x4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8yIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8yYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8yoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawarestate" commandName="Enable Linux OS Awareness" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8y4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8zIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8zYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.newEditor" commandName="New Editor" description="Open another editor on the active editor's input" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8zoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.GoToMessage" commandName="Go to associated message" description="Go to the associated message" category="_a2Zx7oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8z4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i80IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i80YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i80oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i804qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i81IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i81YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i81oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.CreateBranch" commandName="Create Branch..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i814qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.profile.ui.commands.exportascsv" commandName="Export As CSV" category="_a2Zx-IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i82IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i82YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i82oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i824qJEeq6Bc7V6nnrmg" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_a2Zx4YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i83IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_a2Zx3IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i83YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_a2Zx4IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i83oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_a2Zx-oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i834qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i84IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i84YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i84oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i844qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_a2Zx8IqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i85IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_a2i85YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_a2i85oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i854qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i86IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i86YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i86oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i864qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i87IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i87YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_a2Zx3IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i87oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i874qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Annotations" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i88IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i88YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i88oqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.xbash.command" commandName="Launch Bash Shell" description="Launch Bash Shell" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i884qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i89IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i89YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i89oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i894qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8-IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnDomain" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8-YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tcf.debug.ui.commands.toggleQualifiedTypeNames" commandName="Show Qualified Type Names" category="_a2Zx94qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8-oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8-4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8_IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_a2Zx8YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8_YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_a2Zx24qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8_oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i8_4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9AIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9AYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9AoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9A4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9BIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9BYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9BoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_a2Zx4YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9B4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9CIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9CYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.Checkout" commandName="Checkout" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9CoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Last Edit Location" description="Last edit location" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9C4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9DIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9DYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression " category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9DoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.configureFilters" commandName="Configure Contents..." description="Configure the filters to apply to the markers view" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9D4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9EIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_a2Zx7IqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i9EYqJEeq6Bc7V6nnrmg" elementId="title" name="Title"/>
    <parameters xmi:id="_a2i9EoqJEeq6Bc7V6nnrmg" elementId="message" name="Message"/>
    <parameters xmi:id="_a2i9E4qJEeq6Bc7V6nnrmg" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_a2i9FIqJEeq6Bc7V6nnrmg" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_a2i9FYqJEeq6Bc7V6nnrmg" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_a2i9FoqJEeq6Bc7V6nnrmg" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_a2i9F4qJEeq6Bc7V6nnrmg" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_a2i9GIqJEeq6Bc7V6nnrmg" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_a2i9GYqJEeq6Bc7V6nnrmg" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_a2i9GoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9G4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commit.CreateTag" commandName="Create Tag..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9HIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9HYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9HoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.sw.ui.commands.RegenBspSources" commandName="Re-generate BSP Sources" category="_a2Zx1oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9H4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.sw.commands.repositories" commandName="Repositories" category="_a2Zx1oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9IIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9IYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9IoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_a2Zx-oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9I4qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawareModeSuspend" commandName="Auto refresh on suspend" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9JIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.connect" commandName="Connect" description="Connect to Target Node" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9JYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9JoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9J4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.new_experiment" commandName="New..." description="Create Tracing Experiment" category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9KIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9KYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tcf.debug.ui.commands.refresh" commandName="Refresh" category="_a2Zx94qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9KoqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.sw.ui.commands.ChangeReferencedBSP" commandName="Change Referenced BSP" category="_a2Zx1oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9K4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9LIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_a2Zx8IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9LYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9LoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Tag" commandName="Tag" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9L4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9MIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9MYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9MoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9M4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_a2Zx9YqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i9NIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_a2i9NYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_a2i9NoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9N4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9OIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_a2Zx-oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9OYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_a2Zx2oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i9OoqJEeq6Bc7V6nnrmg" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_a2i9O4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.terminals.ui.actions.LaunchTerminalCommand" commandName="Launch Terminal " category="_a2Zx3YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9PIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawarecontext" commandName="Select Linux OS Aware File" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9PYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9PoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_a2Zx-YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9P4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_a2Zx4YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9QIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.stop" commandName="Stop" description="Stop Trace Session" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9QYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9QoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9Q4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.select_trace_type" commandName="Select Trace Type..." description="Select a trace type" category="_a2Zx74qJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i9RIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.bundle" name="Bundle" optional="false"/>
    <parameters xmi:id="_a2i9RYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
    <parameters xmi:id="_a2i9RoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.icon" name="Icon" optional="false"/>
  </commands>
  <commands xmi:id="_a2i9R4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9SIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9SYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9SoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9S4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9TIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9TYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9ToqJEeq6Bc7V6nnrmg" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_a2Zx0IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9T4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_a2Zx4oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9UIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9UYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9UoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9U4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9VIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9VYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9VoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.import" commandName="Import..." description="Import Traces to LTTng Project" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9V4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9WIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9WYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9WoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_a2Zx14qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9W4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9XIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9XYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9XoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9X4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9YIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Annotations" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9YYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9YoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9Y4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9ZIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_a2Zx84qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9ZYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9ZoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9Z4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9aIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.command.offset_traces" commandName="Apply Time Offset..." description="Shift traces by a constant time offset" category="_a2Zx74qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9aYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9aoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9a4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9bIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_a2Zx-oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9bYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeStart" commandName="Show node start " description="Show the node start" category="_a2Zx7oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9boqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9b4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9cIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9cYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9coqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9c4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.snapshot" commandName="Record Snapshot" description="Record a snapshot" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9dIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9dYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_a2Zx3oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i9doqJEeq6Bc7V6nnrmg" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_a2i9d4qJEeq6Bc7V6nnrmg" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_a2i9eIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9eYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEvent" commandName="Enable Event" description="Enable Event" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9eoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_a2Zx4YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9e4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9fIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9fYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_a2Zx84qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9foqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.CheckoutCommand" commandName="Checkout" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9f4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9gIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9gYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9goqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9g4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9hIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_a2Zx24qJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i9hYqJEeq6Bc7V6nnrmg" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_a2i9hoqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9h4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9iIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9iYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9ioqJEeq6Bc7V6nnrmg" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9i4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9jIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9jYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Checkout" category="_a2Zx7YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9joqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_a2Zx6oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9j4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9kIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9kYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_a2Zx2oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9koqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_a2Zx34qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9k4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_a2Zx8oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9lIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_a2Zx3IqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9lYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9loqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9l4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9mIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9mYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9moqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_a2Zx0oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9m4qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_a2Zx64qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9nIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_a2Zx1YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9nYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_a2Zx3oqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9noqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_a2Zx2oqJEeq6Bc7V6nnrmg">
    <parameters xmi:id="_a2i9n4qJEeq6Bc7V6nnrmg" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_a2i9oIqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannel" commandName="Enable Channel" description="Enable a Trace Channel" category="_a2Zx6YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_a2i9oYqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_a2Zx04qJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAcIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAcYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAcoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAc4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAdIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAdYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAdoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAd4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAeIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAeYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAeoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.manageConfigsAction2" commandName="Manage..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAe4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigMenuAction" commandName="Set Active" description="Change active build configuration for project(s)" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLQAfIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.wsselection" commandName="Manage Working Sets..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKYIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKYYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKYoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKY4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKZIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKZYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKZoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKZ4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKaIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKaYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKaoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKa4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKbIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKbYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKboqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKb4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKcIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKcYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKcoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.rse.core.search.searchActionSet/org.eclipse.rse.core.search.searchAction" commandName="Remote..." description="Opens Remote Search dialog page for text and file searching on remote systems" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKc4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKdIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLZKdYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7YIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7YYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7YoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7Y4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7ZIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7ZYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7ZoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7Z4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7aIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7aYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7aoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7a4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7bIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7bYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7boqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7b4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7cIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7cYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::com.xilinx.sdk.targetmanager.ui.viewContribution.restartXsdb/com.xilinx.sdk.targetmanager.ui.xsdb.ClearXSDBConsoleActionDelegate" commandName="Clear XSCT Console" description="Clear XSCT Console" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7coqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::com.xilinx.sdk.targetmanager.ui.viewContribution.clearQEMUConsole/com.xilinx.sdk.targetmanager.ui.qemu.ClearQEMUConsoleActionDelegate" commandName="Clear QEMU Console" description="Clear QEMU Console" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7c4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::com.xilinx.sdk.ui.filter/com.xilinx.sdk.ui.filter.action" commandName="Enable Filtering" description="Enable filtering functionality for project explorer view." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7dIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7dYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7doqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7d4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7eIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7eYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7eoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLi7e4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.RemoveAllGlobalsActionDelegate" commandName="Remove All Global Variables" description="Remove All Global Variables" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssYIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.RemoveGlobalsActionDelegate" commandName="Remove Global Variables" description="Remove Selected Global Variables" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssYYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.AddGlobalsActionDelegate" commandName="Add Global Variables..." description="Add Global Variables" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssYoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssY4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssZIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssZYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssZoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssZ4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssaIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssaYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssaoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssa4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssbIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssbYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssboqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssb4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLsscIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLsscYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLsscoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssc4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssdIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssdYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssdoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssd4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLsseIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLsseYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLsseoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLsse4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssfIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssfYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssfoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssf4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssgIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssgYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssgoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLssg4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bLsshIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12UIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12UYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12UoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12U4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12VIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12VYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12VoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12V4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12WIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12WYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12WoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12W4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12XIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12XYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12XoqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12X4qJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12YIqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <commands xmi:id="_bL12YYqJEeq6Bc7V6nnrmg" elementId="AUTOGEN:::org.eclipse.rse.ui.view.systemView.toolbar/org.eclipse.rse.ui.view.systemView.toolbar.linkWithSystemView" commandName="Link with Editor" category="_a2Zx9YqJEeq6Bc7V6nnrmg"/>
  <addons xmi:id="_a1zU7IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_a1zU7YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_a1zU7oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_a1zU74qJEeq6Bc7V6nnrmg" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_a1zU8IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_a1zU8YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_a1zU8oqJEeq6Bc7V6nnrmg" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_a1zU84qJEeq6Bc7V6nnrmg" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_a1zU9IqJEeq6Bc7V6nnrmg" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon">
    <persistedState key="org.eclipse.cdt.ui.CPerspective" value=""/>
  </addons>
  <addons xmi:id="_a1zU9YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_a1zU9oqJEeq6Bc7V6nnrmg" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <addons xmi:id="_a3vOkIqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_Ds8pUYqREeqP-evVvQYjCQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <categories xmi:id="_a2Zx0IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.team.ui.category.team" name="Team" description="Actions that apply when working with a Team"/>
  <categories xmi:id="_a2Zx0YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_a2Zx0oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_a2Zx04qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_a2Zx1IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_a2Zx1YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_a2Zx1oqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.sw.commands.category" name="Sw Commands Category"/>
  <categories xmi:id="_a2Zx14qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_a2Zx2IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_a2Zx2YqJEeq6Bc7V6nnrmg" elementId="ilg.gnuarmeclipse.packs.commands.category" name="C/C++ Packages Category"/>
  <categories xmi:id="_a2Zx2oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_a2Zx24qJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_a2Zx3IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_a2Zx3YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.rse.ui.commands.category" name="Remote Systems"/>
  <categories xmi:id="_a2Zx3oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_a2Zx34qJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_a2Zx4IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_a2Zx4YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_a2Zx4oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_a2Zx44qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.hw.commands.hwcategory" name="Hardware Design"/>
  <categories xmi:id="_a2Zx5IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_a2Zx5YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_a2Zx5oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_a2Zx54qJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.bootimage.category" name="Bootgen Category"/>
  <categories xmi:id="_a2Zx6IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_a2Zx6YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.category" name="LTTng Trace Control Commands" description="LTTng Trace Control Commands"/>
  <categories xmi:id="_a2Zx6oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_a2Zx64qJEeq6Bc7V6nnrmg" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_a2Zx7IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_a2Zx7YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_a2Zx7oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.category" name="UML2 Sequence Diagram Viewer Commands" description="UML2 Sequence Diagram Viewer Commands"/>
  <categories xmi:id="_a2Zx74qJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.commands.category" name="Tracing" description="Tracing Commands"/>
  <categories xmi:id="_a2Zx8IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_a2Zx8YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_a2Zx8oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_a2Zx84qJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_a2Zx9IqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_a2Zx9YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_a2Zx9oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.linuxtools.tmf.ui.commands.parser.category" name="Parser Commands" description="Parser Commands"/>
  <categories xmi:id="_a2Zx94qJEeq6Bc7V6nnrmg" elementId="org.eclipse.tcf.debug.ui.commands" name="TCF Debugger" description="TCF Debugger Commands"/>
  <categories xmi:id="_a2Zx-IqJEeq6Bc7V6nnrmg" elementId="com.xilinx.sdk.tools.command.category" name="SDK Tools"/>
  <categories xmi:id="_a2Zx-YqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_a2Zx-oqJEeq6Bc7V6nnrmg" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
