#-----------------------------------------------------------
# Webtalk v2016.4 (64-bit)
# SW Build 1756540 on Mon Jan 23 19:11:23 MST 2017
# 
# Start of session at: Tue Oct 29 18:29:52 2019
# Process ID: 19760
# Current directory: D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.sdk/webtalk
# Command line: wbtcv.exe -mode batch -source sdk_webtalk.tcl
# Log file: D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.sdk/webtalk/webtalk.log
# Journal file: D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.sdk/webtalk\webtalk.jou
#-----------------------------------------------------------
source sdk_webtalk.tcl
# webtalk_init -webtalk_dir D:\\hdl-hdl_2017_r1\\hdl-hdl_2017_r1\\projects\\fmcomms2\\zed\\fmcomms2_zed.sdk\\webtalk
# webtalk_register_client -client project
# webtalk_add_data -client project -key date_generated -value "2019-10-29 18:29:51" -context "software_version_and_target_device"
# webtalk_add_data -client project -key product_version -value "SDK v2016.4" -context "software_version_and_target_device"
# webtalk_add_data -client project -key build_version -value "2016.4" -context "software_version_and_target_device"
# webtalk_add_data -client project -key os_platform -value "amd64" -context "software_version_and_target_device"
# webtalk_add_data -client project -key registration_id -value "205589051_15690819_210558907_866" -context "software_version_and_target_device"
# webtalk_add_data -client project -key tool_flow -value "SDK" -context "software_version_and_target_device"
# webtalk_add_data -client project -key beta -value "false" -context "software_version_and_target_device"
# webtalk_add_data -client project -key route_design -value "NA" -context "software_version_and_target_device"
# webtalk_add_data -client project -key target_family -value "NA" -context "software_version_and_target_device"
# webtalk_add_data -client project -key target_device -value "NA" -context "software_version_and_target_device"
# webtalk_add_data -client project -key target_package -value "NA" -context "software_version_and_target_device"
# webtalk_add_data -client project -key target_speed -value "NA" -context "software_version_and_target_device"
# webtalk_add_data -client project -key random_id -value "mok1vbp53jgskat3veavqntc6i" -context "software_version_and_target_device"
# webtalk_add_data -client project -key project_id -value "2016.4_6" -context "software_version_and_target_device"
# webtalk_add_data -client project -key project_iteration -value "6" -context "software_version_and_target_device"
# webtalk_add_data -client project -key os_name -value "Microsoft Windows 8 or later , 64-bit" -context "user_environment"
# webtalk_add_data -client project -key os_release -value "major release  (build 9200)" -context "user_environment"
# webtalk_add_data -client project -key cpu_name -value "AMD Ryzen 5 2400G with Radeon Vega Graphics    " -context "user_environment"
# webtalk_add_data -client project -key cpu_speed -value "3593 MHz" -context "user_environment"
# webtalk_add_data -client project -key total_processors -value "1" -context "user_environment"
# webtalk_add_data -client project -key system_ram -value "16.000 GB" -context "user_environment"
# webtalk_register_client -client sdk
# webtalk_add_data -client sdk -key uid -value "1572325599953" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key isZynq -value "true" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key isZynqMP -value "false" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key Processors -value "2" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key VivadoVersion -value "2016.4" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key Arch -value "zynq" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key Device -value "7z045" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key IsHandoff -value "true" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key os -value "NA" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key apptemplate -value "NA" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key RecordType -value "HWCreation" -context "sdk\\\\hardware/1572325599953"
# webtalk_add_data -client sdk -key uid -value "NA" -context "sdk\\\\bsp/1572344991307"
# webtalk_add_data -client sdk -key RecordType -value "ToolUsage" -context "sdk\\\\bsp/1572344991307"
# webtalk_add_data -client sdk -key BootgenCount -value "0" -context "sdk\\\\bsp/1572344991307"
# webtalk_add_data -client sdk -key DebugCount -value "0" -context "sdk\\\\bsp/1572344991307"
# webtalk_add_data -client sdk -key PerfCount -value "0" -context "sdk\\\\bsp/1572344991307"
# webtalk_add_data -client sdk -key FlashCount -value "2" -context "sdk\\\\bsp/1572344991307"
# webtalk_add_data -client sdk -key CrossTriggCount -value "0" -context "sdk\\\\bsp/1572344991307"
# webtalk_add_data -client sdk -key QemuDebugCount -value "0" -context "sdk\\\\bsp/1572344991307"
# webtalk_transmit -clientid 1939873093 -regid "205589051_15690819_210558907_866" -xml D:\\hdl-hdl_2017_r1\\hdl-hdl_2017_r1\\projects\\fmcomms2\\zed\\fmcomms2_zed.sdk\\webtalk\\usage_statistics_ext_sdk.xml -html D:\\hdl-hdl_2017_r1\\hdl-hdl_2017_r1\\projects\\fmcomms2\\zed\\fmcomms2_zed.sdk\\webtalk\\usage_statistics_ext_sdk.html -wdm D:\\hdl-hdl_2017_r1\\hdl-hdl_2017_r1\\projects\\fmcomms2\\zed\\fmcomms2_zed.sdk\\webtalk\\sdk_webtalk.wdm -intro "<H3>SDK Usage Report</H3><BR>"
INFO: [Common 17-186] 'D:\hdl-hdl_2017_r1\hdl-hdl_2017_r1\projects\fmcomms2\zed\fmcomms2_zed.sdk\webtalk\usage_statistics_ext_sdk.xml' has been successfully sent to Xilinx on Tue Oct 29 18:29:59 2019. For additional details about this file, please refer to the WebTalk help file at D:/Xilinx2016/SDK/2016.4/doc/webtalk_introduction.html.
# webtalk_terminate
INFO: [Common 17-206] Exiting Webtalk at Tue Oct 29 18:29:59 2019...
