<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>customized_ip</spirit:library>
  <spirit:name>system_axi_hp1_interconnect_0</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:choices>
    <spirit:choice>
      <spirit:name>choice_list_40181835</spirit:name>
      <spirit:enumeration>32</spirit:enumeration>
      <spirit:enumeration>64</spirit:enumeration>
      <spirit:enumeration>128</spirit:enumeration>
      <spirit:enumeration>256</spirit:enumeration>
      <spirit:enumeration>512</spirit:enumeration>
      <spirit:enumeration>1024</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_661c4a03</spirit:name>
      <spirit:enumeration>2</spirit:enumeration>
      <spirit:enumeration>4</spirit:enumeration>
      <spirit:enumeration>8</spirit:enumeration>
      <spirit:enumeration>16</spirit:enumeration>
      <spirit:enumeration>32</spirit:enumeration>
      <spirit:enumeration>64</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_4873554b</spirit:name>
      <spirit:enumeration spirit:text="false">0</spirit:enumeration>
      <spirit:enumeration spirit:text="true">1</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_76d086ea</spirit:name>
      <spirit:enumeration spirit:text="None">0</spirit:enumeration>
      <spirit:enumeration spirit:text="32 deep">1</spirit:enumeration>
      <spirit:enumeration spirit:text="512 deep (packet mode)">2</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_ab2668a2</spirit:name>
      <spirit:enumeration spirit:text="Custom">0</spirit:enumeration>
      <spirit:enumeration spirit:text="Minimize Area">1</spirit:enumeration>
      <spirit:enumeration spirit:text="Maximize Performance">2</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_b6c9535e</spirit:name>
      <spirit:enumeration spirit:text="None">0</spirit:enumeration>
      <spirit:enumeration spirit:text="Outer">1</spirit:enumeration>
      <spirit:enumeration spirit:text="Auto">3</spirit:enumeration>
      <spirit:enumeration spirit:text="Outer and Auto">4</spirit:enumeration>
    </spirit:choice>
  </spirit:choices>
  <spirit:description>The AXI Interconnect IP connects one or more AXI memory-mapped master devices to one or more AXI memory mapped slave devices</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>NUM_SI</spirit:name>
      <spirit:displayName>Number of Slave Interfaces</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.NUM_SI" spirit:order="2" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>NUM_MI</spirit:name>
      <spirit:displayName>Number of Master Interfaces</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.NUM_MI" spirit:order="3" spirit:minimum="1" spirit:maximum="64" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>STRATEGY</spirit:name>
      <spirit:displayName>Interconnect Optimization Strategy</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.STRATEGY" spirit:choiceRef="choice_pairs_ab2668a2" spirit:order="4">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ENABLE_ADVANCED_OPTIONS</spirit:name>
      <spirit:displayName>Enable Advanced Configuration Options</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.ENABLE_ADVANCED_OPTIONS" spirit:choiceRef="choice_pairs_4873554b" spirit:order="5">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ENABLE_PROTOCOL_CHECKERS</spirit:name>
      <spirit:displayName>Enable Protocol Checkers and mark interfaces for debug</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.ENABLE_PROTOCOL_CHECKERS" spirit:choiceRef="choice_pairs_4873554b" spirit:order="6">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>XBAR_DATA_WIDTH</spirit:name>
      <spirit:displayName>Data Width of the AXI Crossbar</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.XBAR_DATA_WIDTH" spirit:choiceRef="choice_list_40181835" spirit:order="7">32</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PCHK_WAITS</spirit:name>
      <spirit:displayName>Maximum number of idle cycles for READY monitoring</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PCHK_WAITS" spirit:order="8" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PCHK_MAX_RD_BURSTS</spirit:name>
      <spirit:displayName>Maximum outstanding READ Transactions per ID</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PCHK_MAX_RD_BURSTS" spirit:choiceRef="choice_list_661c4a03" spirit:order="9">2</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PCHK_MAX_WR_BURSTS</spirit:name>
      <spirit:displayName>Maximum outstanding WRITE Transactions per ID</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PCHK_MAX_WR_BURSTS" spirit:choiceRef="choice_list_661c4a03" spirit:order="10">2</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SYNCHRONIZATION_STAGES</spirit:name>
      <spirit:displayName>Synchronization Stages</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.SYNCHRONIZATION_STAGES" spirit:order="11" spirit:minimum="2" spirit:maximum="8" spirit:rangeType="long">2</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M00_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635284">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M01_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635285">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M02_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635286">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M03_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635287">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M04_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635288">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M05_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635289">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M06_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635290">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M07_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635291">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M08_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635292">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M09_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635293">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M10_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635294">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M11_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635295">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M12_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635296">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M13_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635297">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M14_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635298">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M15_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635299">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M16_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M16_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M16_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635300">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M17_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M17_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M17_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635301">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M18_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M18_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M18_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635302">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M19_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M19_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M19_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635303">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M20_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M20_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M20_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635304">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M21_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M21_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M21_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635305">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M22_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M22_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M22_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635306">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M23_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M23_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M23_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635307">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M24_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M24_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M24_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635308">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M25_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M25_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M25_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635309">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M26_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M26_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M26_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635310">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M27_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M27_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M27_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635311">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M28_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M28_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M28_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635312">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M29_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M29_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M29_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635313">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M30_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M30_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M30_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635314">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M31_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M31_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M31_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635315">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M32_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M32_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M32_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635316">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M33_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M33_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M33_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635317">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M34_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M34_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M34_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635318">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M35_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M35_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M35_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635319">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M36_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M36_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M36_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635320">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M37_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M37_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M37_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635321">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M38_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M38_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M38_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635322">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M39_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M39_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M39_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635323">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M40_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M40_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M40_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635324">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M41_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M41_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M41_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635325">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M42_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M42_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M42_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635326">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M43_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M43_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M43_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635327">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M44_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M44_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M44_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635328">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M45_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M45_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M45_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635329">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M46_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M46_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M46_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635330">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M47_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M47_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M47_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635331">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M48_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M48_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M48_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635332">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M49_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M49_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M49_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635333">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M50_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M50_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M50_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635334">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M51_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M51_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M51_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635335">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M52_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M52_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M52_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635336">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M53_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M53_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M53_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635337">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M54_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M54_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M54_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635338">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M55_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M55_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M55_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635339">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M56_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M56_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M56_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635340">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M57_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M57_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M57_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635341">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M58_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M58_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M58_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635342">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M59_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M59_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M59_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635343">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M60_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M60_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M60_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635344">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M61_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M61_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M61_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635345">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M62_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M62_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M62_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635346">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M63_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface M63_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M63_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635347">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M00_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635348">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M01_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635349">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M02_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635350">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M03_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635351">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M04_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635352">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M05_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635353">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M06_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635354">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M07_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635355">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M08_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635356">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M09_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635357">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M10_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635358">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M11_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635359">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M12_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635360">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M13_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635361">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M14_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635362">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M15_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635363">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M16_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M16_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M16_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635364">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M17_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M17_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M17_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635365">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M18_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M18_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M18_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635366">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M19_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M19_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M19_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635367">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M20_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M20_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M20_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635368">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M21_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M21_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M21_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635369">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M22_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M22_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M22_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635370">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M23_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M23_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M23_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635371">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M24_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M24_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M24_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635372">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M25_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M25_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M25_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635373">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M26_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M26_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M26_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635374">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M27_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M27_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M27_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635375">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M28_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M28_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M28_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635376">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M29_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M29_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M29_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635377">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M30_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M30_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M30_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635378">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M31_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M31_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M31_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635379">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M32_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M32_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M32_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635380">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M33_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M33_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M33_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635381">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M34_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M34_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M34_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635382">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M35_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M35_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M35_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635383">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M36_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M36_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M36_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635384">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M37_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M37_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M37_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635385">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M38_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M38_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M38_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635386">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M39_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M39_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M39_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635387">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M40_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M40_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M40_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635388">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M41_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M41_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M41_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635389">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M42_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M42_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M42_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635390">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M43_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M43_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M43_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635391">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M44_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M44_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M44_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635392">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M45_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M45_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M45_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635393">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M46_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M46_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M46_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635394">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M47_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M47_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M47_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635395">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M48_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M48_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M48_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635396">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M49_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M49_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M49_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635397">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M50_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M50_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M50_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635398">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M51_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M51_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M51_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635399">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M52_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M52_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M52_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635400">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M53_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M53_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M53_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635401">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M54_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M54_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M54_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635402">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M55_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M55_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M55_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635403">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M56_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M56_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M56_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635404">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M57_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M57_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M57_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635405">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M58_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M58_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M58_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635406">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M59_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M59_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M59_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635407">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M60_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M60_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M60_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635408">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M61_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M61_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M61_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635409">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M62_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M62_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M62_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635410">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M63_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface M63_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M63_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635411">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S00_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S00_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S00_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635412">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S01_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S01_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S01_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635413">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S02_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S02_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S02_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635414">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S03_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S03_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S03_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635415">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S04_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S04_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S04_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635416">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S05_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S05_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S05_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635417">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S06_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S06_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S06_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635418">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S07_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S07_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S07_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635419">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S08_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S08_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S08_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635420">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S09_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S09_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S09_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635421">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S10_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S10_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S10_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635422">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S11_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S11_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S11_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635423">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S12_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S12_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S12_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635424">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S13_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S13_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S13_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635425">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S14_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S14_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S14_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635426">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S15_HAS_REGSLICE</spirit:name>
      <spirit:displayName>Enable Register Slice on interface S15_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S15_HAS_REGSLICE" spirit:choiceRef="choice_pairs_b6c9535e" spirit:order="1635427">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S00_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S00_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S00_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635428">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S01_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S01_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S01_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635429">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S02_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S02_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S02_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635430">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S03_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S03_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S03_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635431">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S04_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S04_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S04_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635432">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S05_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S05_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S05_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635433">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S06_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S06_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S06_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635434">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S07_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S07_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S07_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635435">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S08_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S08_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S08_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635436">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S09_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S09_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S09_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635437">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S10_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S10_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S10_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635438">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S11_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S11_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S11_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635439">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S12_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S12_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S12_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635440">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S13_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S13_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S13_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635441">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S14_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S14_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S14_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635442">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S15_HAS_DATA_FIFO</spirit:name>
      <spirit:displayName>Enable Data FIFO on interface S15_AXI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S15_HAS_DATA_FIFO" spirit:choiceRef="choice_pairs_76d086ea" spirit:order="1635443">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M00_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635444">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M01_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635445">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M02_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635446">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M03_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635447">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M04_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635448">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M05_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635449">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M06_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635450">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M07_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635451">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M08_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635452">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M09_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635453">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M10_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635454">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M11_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635455">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M12_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635456">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M13_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635457">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M14_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635458">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M15_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635459">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M16_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M16_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M16_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635460">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M17_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M17_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M17_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635461">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M18_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M18_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M18_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635462">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M19_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M19_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M19_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635463">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M20_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M20_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M20_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635464">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M21_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M21_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M21_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635465">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M22_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M22_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M22_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635466">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M23_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M23_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M23_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635467">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M24_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M24_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M24_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635468">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M25_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M25_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M25_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635469">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M26_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M26_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M26_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635470">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M27_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M27_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M27_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635471">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M28_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M28_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M28_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635472">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M29_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M29_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M29_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635473">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M30_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M30_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M30_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635474">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M31_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M31_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M31_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635475">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M32_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M32_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M32_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635476">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M33_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M33_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M33_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635477">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M34_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M34_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M34_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635478">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M35_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M35_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M35_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635479">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M36_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M36_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M36_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635480">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M37_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M37_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M37_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635481">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M38_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M38_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M38_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635482">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M39_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M39_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M39_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635483">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M40_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M40_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M40_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635484">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M41_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M41_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M41_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635485">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M42_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M42_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M42_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635486">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M43_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M43_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M43_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635487">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M44_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M44_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M44_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635488">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M45_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M45_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M45_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635489">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M46_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M46_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M46_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635490">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M47_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M47_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M47_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635491">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M48_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M48_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M48_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635492">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M49_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M49_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M49_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635493">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M50_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M50_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M50_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635494">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M51_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M51_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M51_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635495">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M52_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M52_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M52_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635496">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M53_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M53_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M53_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635497">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M54_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M54_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M54_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635498">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M55_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M55_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M55_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635499">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M56_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M56_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M56_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635500">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M57_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M57_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M57_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635501">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M58_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M58_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M58_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635502">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M59_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M59_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M59_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635503">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M60_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M60_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M60_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635504">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M61_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M61_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M61_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635505">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M62_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M62_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M62_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635506">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M63_ISSUANCE</spirit:name>
      <spirit:displayName>Incicates whether M63_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M63_ISSUANCE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635507">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M00_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635508">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M01_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635509">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M02_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635510">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M03_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635511">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M04_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635512">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M05_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635513">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M06_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635514">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M07_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635515">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M08_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635516">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M09_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635517">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M10_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635518">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M11_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635519">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M12_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635520">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M13_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635521">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M14_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635522">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M15_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635523">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M16_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M16_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M16_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635524">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M17_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M17_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M17_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635525">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M18_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M18_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M18_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635526">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M19_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M19_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M19_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635527">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M20_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M20_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M20_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635528">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M21_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M21_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M21_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635529">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M22_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M22_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M22_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635530">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M23_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M23_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M23_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635531">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M24_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M24_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M24_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635532">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M25_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M25_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M25_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635533">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M26_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M26_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M26_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635534">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M27_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M27_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M27_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635535">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M28_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M28_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M28_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635536">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M29_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M29_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M29_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635537">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M30_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M30_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M30_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635538">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M31_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M31_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M31_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635539">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M32_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M32_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M32_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635540">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M33_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M33_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M33_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635541">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M34_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M34_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M34_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635542">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M35_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M35_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M35_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635543">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M36_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M36_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M36_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635544">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M37_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M37_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M37_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635545">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M38_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M38_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M38_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635546">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M39_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M39_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M39_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635547">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M40_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M40_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M40_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635548">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M41_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M41_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M41_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635549">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M42_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M42_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M42_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635550">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M43_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M43_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M43_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635551">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M44_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M44_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M44_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635552">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M45_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M45_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M45_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635553">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M46_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M46_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M46_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635554">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M47_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M47_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M47_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635555">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M48_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M48_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M48_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635556">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M49_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M49_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M49_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635557">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M50_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M50_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M50_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635558">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M51_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M51_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M51_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635559">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M52_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M52_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M52_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635560">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M53_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M53_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M53_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635561">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M54_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M54_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M54_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635562">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M55_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M55_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M55_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635563">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M56_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M56_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M56_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635564">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M57_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M57_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M57_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635565">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M58_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M58_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M58_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635566">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M59_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M59_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M59_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635567">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M60_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M60_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M60_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635568">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M61_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M61_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M61_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635569">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M62_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M62_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M62_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635570">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M63_SECURE</spirit:name>
      <spirit:displayName>Incicates whether M63_AXI connects to a secure slave</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M63_SECURE" spirit:choiceRef="choice_pairs_4873554b" spirit:order="1635571">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S00_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S00_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S00_ARB_PRIORITY" spirit:order="1635572" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S01_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S01_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S01_ARB_PRIORITY" spirit:order="1635573" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S02_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S02_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S02_ARB_PRIORITY" spirit:order="1635574" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S03_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S03_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S03_ARB_PRIORITY" spirit:order="1635575" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S04_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S04_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S04_ARB_PRIORITY" spirit:order="1635576" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S05_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S05_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S05_ARB_PRIORITY" spirit:order="1635577" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S06_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S06_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S06_ARB_PRIORITY" spirit:order="1635578" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S07_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S07_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S07_ARB_PRIORITY" spirit:order="1635579" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S08_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S08_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S08_ARB_PRIORITY" spirit:order="1635580" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S09_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S09_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S09_ARB_PRIORITY" spirit:order="1635581" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S10_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S10_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S10_ARB_PRIORITY" spirit:order="1635582" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S11_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S11_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S11_ARB_PRIORITY" spirit:order="1635583" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S12_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S12_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S12_ARB_PRIORITY" spirit:order="1635584" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S13_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S13_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S13_ARB_PRIORITY" spirit:order="1635585" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S14_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S14_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S14_ARB_PRIORITY" spirit:order="1635586" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S15_ARB_PRIORITY</spirit:name>
      <spirit:displayName>Controls S15_ARB_PRIORITY on SI axi_crossbar instance within interconnect</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S15_ARB_PRIORITY" spirit:order="1635587" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="1">system_axi_hp1_interconnect_0</spirit:value>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:displayName>AXI Interconnect</xilinx:displayName>
      <xilinx:coreRevision>19</xilinx:coreRevision>
      <xilinx:configElementInfos>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_MI" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_SI" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.SYNCHRONIZATION_STAGES" xilinx:valueSource="user"/>
      </xilinx:configElementInfos>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2018.3</xilinx:xilinxVersion>
      <xilinx:checksum xilinx:scope="fileGroups" xilinx:value="2192f355"/>
      <xilinx:checksum xilinx:scope="parameters" xilinx:value="e3c1d15b"/>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
