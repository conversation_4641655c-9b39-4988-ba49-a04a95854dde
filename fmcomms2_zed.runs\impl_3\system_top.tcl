proc start_step { step } {
  set stopFile ".stop.rst"
  if {[file isfile .stop.rst]} {
    puts ""
    puts "*** Halting run - EA reset detected ***"
    puts ""
    puts ""
    return -code error
  }
  set beginFile ".$step.begin.rst"
  set platform "$::tcl_platform(platform)"
  set user "$::tcl_platform(user)"
  set pid [pid]
  set host ""
  if { [string equal $platform unix] } {
    if { [info exist ::env(HOSTNAME)] } {
      set host $::env(HOSTNAME)
    }
  } else {
    if { [info exist ::env(COMPUTERNAME)] } {
      set host $::env(COMPUTERNAME)
    }
  }
  set ch [open $beginFile w]
  puts $ch "<?xml version=\"1.0\"?>"
  puts $ch "<ProcessHandle Version=\"1\" Minor=\"0\">"
  puts $ch "    <Process Command=\".planAhead.\" Owner=\"$user\" Host=\"$host\" Pid=\"$pid\">"
  puts $ch "    </Process>"
  puts $ch "</ProcessHandle>"
  close $ch
}

proc end_step { step } {
  set endFile ".$step.end.rst"
  set ch [open $endFile w]
  close $ch
}

proc step_failed { step } {
  set endFile ".$step.error.rst"
  set ch [open $endFile w]
  close $ch
}

set_msg_config -id {HDL 9-1061} -limit 100000
set_msg_config -id {HDL 9-1654} -limit 100000
set_msg_config  -ruleid {271}  -id {BD 41-1348}  -new_severity {INFO} 
set_msg_config  -ruleid {272}  -id {BD 41-1343}  -new_severity {INFO} 
set_msg_config  -ruleid {273}  -id {BD 41-1306}  -new_severity {INFO} 
set_msg_config  -ruleid {274}  -id {IP_Flow 19-1687}  -new_severity {INFO} 
set_msg_config  -ruleid {275}  -id {filemgmt 20-1763}  -new_severity {INFO} 
set_msg_config  -ruleid {276}  -id {BD 41-1276}  -severity {CRITICAL WARNING}  -new_severity {ERROR} 

start_step init_design
set ACTIVE_STEP init_design
set rc [catch {
  create_msg_db init_design.pb
  set_property design_mode GateLvl [current_fileset]
  set_param project.singleFileAddWarning.threshold 0
  set_property webtalk.parent_dir D:/FPGA/xiaoE/fmcomms2_zed.cache/wt [current_project]
  set_property parent.project_path D:/FPGA/xiaoE/fmcomms2_zed.xpr [current_project]
  set_property ip_repo_paths D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/library [current_project]
  set_property ip_output_repo D:/FPGA/xiaoE/fmcomms2_zed.cache/ip [current_project]
  set_property ip_cache_permissions {read write} [current_project]
  set_property XPM_LIBRARIES XPM_CDC [current_project]
  add_files -quiet D:/FPGA/xiaoE/fmcomms2_zed.runs/synth_3/system_top.dcp
  add_files -quiet d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0.dcp
  set_property netlist_only true [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0.dcp]
  read_xdc -ref system_sys_ps7_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xdc]
  read_xdc -prop_thru_buffers -ref system_sys_rstgen_0 -cells U0 d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0_board.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0_board.xdc]
  read_xdc -ref system_sys_rstgen_0 -cells U0 d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0.xdc]
  read_xdc -ref up_xfer_cntrl d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc]
  read_xdc -ref ad_rst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc]
  read_xdc -ref up_xfer_status d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc]
  read_xdc -ref up_clock_mon d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc]
  read_xdc -ref system_axi_ad9361_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/axi_ad9361_constr.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/axi_ad9361_constr.xdc]
  read_xdc -ref system_util_ad9361_tdd_sync_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_tdd_sync_0/util_tdd_sync_constr.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_tdd_sync_0/util_tdd_sync_constr.xdc]
  read_xdc -prop_thru_buffers -ref system_util_ad9361_divclk_reset_0 -cells U0 d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0_board.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0_board.xdc]
  read_xdc -ref system_util_ad9361_divclk_reset_0 -cells U0 d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0.xdc]
  read_xdc -ref system_util_ad9361_adc_fifo_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_fifo_0/util_wfifo_constr.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_fifo_0/util_wfifo_constr.xdc]
  read_xdc -ref system_util_ad9361_adc_pack_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_pack_0/util_cpack_constr.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_pack_0/util_cpack_constr.xdc]
  read_xdc -ref system_axi_ad9361_dac_fifo_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_fifo_0/util_rfifo_constr.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_fifo_0/util_rfifo_constr.xdc]
  read_xdc -ref system_util_ad9361_dac_upack_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_dac_upack_0/util_upack_constr.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_dac_upack_0/util_upack_constr.xdc]
  read_xdc -mode out_of_context -ref vio_0 d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0_ooc.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0_ooc.xdc]
  read_xdc -ref vio_0 d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0.xdc
  set_property processing_order EARLY [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0.xdc]
  read_xdc D:/FPGA/xiaoE/fmcomms2_zed.srcs/constrs_1/new/cdc.xdc
  read_xdc D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc
  read_xdc D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc
  read_xdc -unmanaged d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/bd/bd.tcl
  read_xdc -unmanaged d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/bd/bd.tcl
  read_xdc -ref system_axi_ad9361_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/system_axi_ad9361_0_pps_constr.xdc
  set_property processing_order LATE [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/system_axi_ad9361_0_pps_constr.xdc]
  read_xdc -ref system_util_ad9361_divclk_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc
  set_property processing_order LATE [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc]
  read_xdc -ref system_axi_ad9361_adc_dma_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc
  set_property processing_order LATE [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc]
  read_xdc -ref system_axi_ad9361_dac_dma_0 -cells inst d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc
  set_property processing_order LATE [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc]
  link_design -top system_top -part xc7z035ffg676-2
  write_hwdef -file system_top.hwdef
  close_msg_db -file init_design.pb
} RESULT]
if {$rc} {
  step_failed init_design
  return -code error $RESULT
} else {
  end_step init_design
  unset ACTIVE_STEP 
}

start_step opt_design
set ACTIVE_STEP opt_design
set rc [catch {
  create_msg_db opt_design.pb
  opt_design 
  write_checkpoint -force system_top_opt.dcp
  catch { report_drc -file system_top_drc_opted.rpt }
  close_msg_db -file opt_design.pb
} RESULT]
if {$rc} {
  step_failed opt_design
  return -code error $RESULT
} else {
  end_step opt_design
  unset ACTIVE_STEP 
}

start_step place_design
set ACTIVE_STEP place_design
set rc [catch {
  create_msg_db place_design.pb
  implement_debug_core 
  place_design 
  write_checkpoint -force system_top_placed.dcp
  catch { report_io -file system_top_io_placed.rpt }
  catch { report_utilization -file system_top_utilization_placed.rpt -pb system_top_utilization_placed.pb }
  catch { report_control_sets -verbose -file system_top_control_sets_placed.rpt }
  close_msg_db -file place_design.pb
} RESULT]
if {$rc} {
  step_failed place_design
  return -code error $RESULT
} else {
  end_step place_design
  unset ACTIVE_STEP 
}

start_step route_design
set ACTIVE_STEP route_design
set rc [catch {
  create_msg_db route_design.pb
  route_design 
  write_checkpoint -force system_top_routed.dcp
  catch { report_drc -file system_top_drc_routed.rpt -pb system_top_drc_routed.pb -rpx system_top_drc_routed.rpx }
  catch { report_methodology -file system_top_methodology_drc_routed.rpt -rpx system_top_methodology_drc_routed.rpx }
  catch { report_timing_summary -warn_on_violation -max_paths 10 -file system_top_timing_summary_routed.rpt -rpx system_top_timing_summary_routed.rpx }
  catch { report_power -file system_top_power_routed.rpt -pb system_top_power_summary_routed.pb -rpx system_top_power_routed.rpx }
  catch { report_route_status -file system_top_route_status.rpt -pb system_top_route_status.pb }
  catch { report_clock_utilization -file system_top_clock_utilization_routed.rpt }
  close_msg_db -file route_design.pb
} RESULT]
if {$rc} {
  write_checkpoint -force system_top_routed_error.dcp
  step_failed route_design
  return -code error $RESULT
} else {
  end_step route_design
  unset ACTIVE_STEP 
}

start_step write_bitstream
set ACTIVE_STEP write_bitstream
set rc [catch {
  create_msg_db write_bitstream.pb
  set_property XPM_LIBRARIES XPM_CDC [current_project]
  catch { write_mem_info -force system_top.mmi }
  write_bitstream -force -no_partial_bitfile system_top.bit 
  catch { write_sysdef -hwdef system_top.hwdef -bitfile system_top.bit -meminfo system_top.mmi -file system_top.sysdef }
  catch {write_debug_probes -quiet -force debug_nets}
  close_msg_db -file write_bitstream.pb
} RESULT]
if {$rc} {
  step_failed write_bitstream
  return -code error $RESULT
} else {
  end_step write_bitstream
  unset ACTIVE_STEP 
}

