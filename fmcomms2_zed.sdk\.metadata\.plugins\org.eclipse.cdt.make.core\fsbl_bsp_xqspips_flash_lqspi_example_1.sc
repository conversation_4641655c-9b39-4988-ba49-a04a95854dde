<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?scdStore version="2"?><scannerInfo id="org.eclipse.cdt.make.core.discoveredScannerInfo">
<instance id="xilinx.gnu.armv7.exe.debug.456566226;xilinx.gnu.armv7.exe.debug.456566226.">
<collector id="org.eclipse.cdt.make.core.PerProjectSICollector">
<includePath path="d:\xilinx\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/include"/>
<includePath path="d:\xilinx\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/include-fixed"/>
<includePath path="d:\xilinx\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/../../../../arm-none-eabi/include"/>
<includePath path="d:\xilinx\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../arm-none-eabi/libc/usr/include"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/include"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/include-fixed"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/../../../../arm-none-eabi/include"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../arm-none-eabi/libc/usr/include"/>
<definedSymbol symbol="__STDC__=1"/>
<definedSymbol symbol="__STDC_VERSION__=201112L"/>
<definedSymbol symbol="__STDC_UTF_16__=1"/>
<definedSymbol symbol="__STDC_UTF_32__=1"/>
<definedSymbol symbol="__STDC_HOSTED__=1"/>
<definedSymbol symbol="__GNUC__=5"/>
<definedSymbol symbol="__GNUC_MINOR__=2"/>
<definedSymbol symbol="__GNUC_PATCHLEVEL__=1"/>
<definedSymbol symbol="__VERSION__=&quot;5.2.1 20151005&quot;"/>
<definedSymbol symbol="__LINARO_RELEASE__=201511"/>
<definedSymbol symbol="__LINARO_SPIN__=2"/>
<definedSymbol symbol="__ATOMIC_RELAXED=0"/>
<definedSymbol symbol="__ATOMIC_SEQ_CST=5"/>
<definedSymbol symbol="__ATOMIC_ACQUIRE=2"/>
<definedSymbol symbol="__ATOMIC_RELEASE=3"/>
<definedSymbol symbol="__ATOMIC_ACQ_REL=4"/>
<definedSymbol symbol="__ATOMIC_CONSUME=1"/>
<definedSymbol symbol="__FINITE_MATH_ONLY__=0"/>
<definedSymbol symbol="__SIZEOF_INT__=4"/>
<definedSymbol symbol="__SIZEOF_LONG__=4"/>
<definedSymbol symbol="__SIZEOF_LONG_LONG__=8"/>
<definedSymbol symbol="__SIZEOF_SHORT__=2"/>
<definedSymbol symbol="__SIZEOF_FLOAT__=4"/>
<definedSymbol symbol="__SIZEOF_DOUBLE__=8"/>
<definedSymbol symbol="__SIZEOF_LONG_DOUBLE__=8"/>
<definedSymbol symbol="__SIZEOF_SIZE_T__=4"/>
<definedSymbol symbol="__CHAR_BIT__=8"/>
<definedSymbol symbol="__BIGGEST_ALIGNMENT__=8"/>
<definedSymbol symbol="__ORDER_LITTLE_ENDIAN__=1234"/>
<definedSymbol symbol="__ORDER_BIG_ENDIAN__=4321"/>
<definedSymbol symbol="__ORDER_PDP_ENDIAN__=3412"/>
<definedSymbol symbol="__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__"/>
<definedSymbol symbol="__FLOAT_WORD_ORDER__=__ORDER_LITTLE_ENDIAN__"/>
<definedSymbol symbol="__SIZEOF_POINTER__=4"/>
<definedSymbol symbol="__SIZE_TYPE__=unsigned int"/>
<definedSymbol symbol="__PTRDIFF_TYPE__=int"/>
<definedSymbol symbol="__WCHAR_TYPE__=unsigned int"/>
<definedSymbol symbol="__WINT_TYPE__=unsigned int"/>
<definedSymbol symbol="__INTMAX_TYPE__=long long int"/>
<definedSymbol symbol="__UINTMAX_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__CHAR16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__CHAR32_TYPE__=long unsigned int"/>
<definedSymbol symbol="__SIG_ATOMIC_TYPE__=int"/>
<definedSymbol symbol="__INT8_TYPE__=signed char"/>
<definedSymbol symbol="__INT16_TYPE__=short int"/>
<definedSymbol symbol="__INT32_TYPE__=long int"/>
<definedSymbol symbol="__INT64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT32_TYPE__=long unsigned int"/>
<definedSymbol symbol="__UINT64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INT_LEAST8_TYPE__=signed char"/>
<definedSymbol symbol="__INT_LEAST16_TYPE__=short int"/>
<definedSymbol symbol="__INT_LEAST32_TYPE__=long int"/>
<definedSymbol symbol="__INT_LEAST64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT_LEAST8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT_LEAST16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT_LEAST32_TYPE__=long unsigned int"/>
<definedSymbol symbol="__UINT_LEAST64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INT_FAST8_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST16_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST32_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT_FAST8_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST16_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST32_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INTPTR_TYPE__=int"/>
<definedSymbol symbol="__UINTPTR_TYPE__=unsigned int"/>
<definedSymbol symbol="__has_include(STR)=__has_include__(STR)"/>
<definedSymbol symbol="__has_include_next(STR)=__has_include_next__(STR)"/>
<definedSymbol symbol="__GXX_ABI_VERSION=1009"/>
<definedSymbol symbol="__SCHAR_MAX__=0x7f"/>
<definedSymbol symbol="__SHRT_MAX__=0x7fff"/>
<definedSymbol symbol="__INT_MAX__=0x7fffffff"/>
<definedSymbol symbol="__LONG_MAX__=0x7fffffffL"/>
<definedSymbol symbol="__LONG_LONG_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__WCHAR_MAX__=0xffffffffU"/>
<definedSymbol symbol="__WCHAR_MIN__=0U"/>
<definedSymbol symbol="__WINT_MAX__=0xffffffffU"/>
<definedSymbol symbol="__WINT_MIN__=0U"/>
<definedSymbol symbol="__PTRDIFF_MAX__=0x7fffffff"/>
<definedSymbol symbol="__SIZE_MAX__=0xffffffffU"/>
<definedSymbol symbol="__INTMAX_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__INTMAX_C(c)=c ## LL"/>
<definedSymbol symbol="__UINTMAX_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__UINTMAX_C(c)=c ## ULL"/>
<definedSymbol symbol="__SIG_ATOMIC_MAX__=0x7fffffff"/>
<definedSymbol symbol="__SIG_ATOMIC_MIN__=(-__SIG_ATOMIC_MAX__ - 1)"/>
<definedSymbol symbol="__INT8_MAX__=0x7f"/>
<definedSymbol symbol="__INT16_MAX__=0x7fff"/>
<definedSymbol symbol="__INT32_MAX__=0x7fffffffL"/>
<definedSymbol symbol="__INT64_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__UINT8_MAX__=0xff"/>
<definedSymbol symbol="__UINT16_MAX__=0xffff"/>
<definedSymbol symbol="__UINT32_MAX__=0xffffffffUL"/>
<definedSymbol symbol="__UINT64_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__INT_LEAST8_MAX__=0x7f"/>
<definedSymbol symbol="__INT8_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST16_MAX__=0x7fff"/>
<definedSymbol symbol="__INT16_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST32_MAX__=0x7fffffffL"/>
<definedSymbol symbol="__INT32_C(c)=c ## L"/>
<definedSymbol symbol="__INT_LEAST64_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__INT64_C(c)=c ## LL"/>
<definedSymbol symbol="__UINT_LEAST8_MAX__=0xff"/>
<definedSymbol symbol="__UINT8_C(c)=c"/>
<definedSymbol symbol="__UINT_LEAST16_MAX__=0xffff"/>
<definedSymbol symbol="__UINT16_C(c)=c"/>
<definedSymbol symbol="__UINT_LEAST32_MAX__=0xffffffffUL"/>
<definedSymbol symbol="__UINT32_C(c)=c ## UL"/>
<definedSymbol symbol="__UINT_LEAST64_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__UINT64_C(c)=c ## ULL"/>
<definedSymbol symbol="__INT_FAST8_MAX__=0x7fffffff"/>
<definedSymbol symbol="__INT_FAST16_MAX__=0x7fffffff"/>
<definedSymbol symbol="__INT_FAST32_MAX__=0x7fffffff"/>
<definedSymbol symbol="__INT_FAST64_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__UINT_FAST8_MAX__=0xffffffffU"/>
<definedSymbol symbol="__UINT_FAST16_MAX__=0xffffffffU"/>
<definedSymbol symbol="__UINT_FAST32_MAX__=0xffffffffU"/>
<definedSymbol symbol="__UINT_FAST64_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__INTPTR_MAX__=0x7fffffff"/>
<definedSymbol symbol="__UINTPTR_MAX__=0xffffffffU"/>
<definedSymbol symbol="__GCC_IEC_559=0"/>
<definedSymbol symbol="__GCC_IEC_559_COMPLEX=0"/>
<definedSymbol symbol="__FLT_EVAL_METHOD__=0"/>
<definedSymbol symbol="__DEC_EVAL_METHOD__=2"/>
<definedSymbol symbol="__FLT_RADIX__=2"/>
<definedSymbol symbol="__FLT_MANT_DIG__=24"/>
<definedSymbol symbol="__FLT_DIG__=6"/>
<definedSymbol symbol="__FLT_MIN_EXP__=(-125)"/>
<definedSymbol symbol="__FLT_MIN_10_EXP__=(-37)"/>
<definedSymbol symbol="__FLT_MAX_EXP__=128"/>
<definedSymbol symbol="__FLT_MAX_10_EXP__=38"/>
<definedSymbol symbol="__FLT_DECIMAL_DIG__=9"/>
<definedSymbol symbol="__FLT_MAX__=3.4028234663852886e+38F"/>
<definedSymbol symbol="__FLT_MIN__=1.1754943508222875e-38F"/>
<definedSymbol symbol="__FLT_EPSILON__=1.1920928955078125e-7F"/>
<definedSymbol symbol="__FLT_DENORM_MIN__=1.4012984643248171e-45F"/>
<definedSymbol symbol="__FLT_HAS_DENORM__=1"/>
<definedSymbol symbol="__FLT_HAS_INFINITY__=1"/>
<definedSymbol symbol="__FLT_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__DBL_MANT_DIG__=53"/>
<definedSymbol symbol="__DBL_DIG__=15"/>
<definedSymbol symbol="__DBL_MIN_EXP__=(-1021)"/>
<definedSymbol symbol="__DBL_MIN_10_EXP__=(-307)"/>
<definedSymbol symbol="__DBL_MAX_EXP__=1024"/>
<definedSymbol symbol="__DBL_MAX_10_EXP__=308"/>
<definedSymbol symbol="__DBL_DECIMAL_DIG__=17"/>
<definedSymbol symbol="__DBL_MAX__=((double)1.7976931348623157e+308L)"/>
<definedSymbol symbol="__DBL_MIN__=((double)2.2250738585072014e-308L)"/>
<definedSymbol symbol="__DBL_EPSILON__=((double)2.2204460492503131e-16L)"/>
<definedSymbol symbol="__DBL_DENORM_MIN__=((double)4.9406564584124654e-324L)"/>
<definedSymbol symbol="__DBL_HAS_DENORM__=1"/>
<definedSymbol symbol="__DBL_HAS_INFINITY__=1"/>
<definedSymbol symbol="__DBL_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__LDBL_MANT_DIG__=53"/>
<definedSymbol symbol="__LDBL_DIG__=15"/>
<definedSymbol symbol="__LDBL_MIN_EXP__=(-1021)"/>
<definedSymbol symbol="__LDBL_MIN_10_EXP__=(-307)"/>
<definedSymbol symbol="__LDBL_MAX_EXP__=1024"/>
<definedSymbol symbol="__LDBL_MAX_10_EXP__=308"/>
<definedSymbol symbol="__DECIMAL_DIG__=17"/>
<definedSymbol symbol="__LDBL_MAX__=1.7976931348623157e+308L"/>
<definedSymbol symbol="__LDBL_MIN__=2.2250738585072014e-308L"/>
<definedSymbol symbol="__LDBL_EPSILON__=2.2204460492503131e-16L"/>
<definedSymbol symbol="__LDBL_DENORM_MIN__=4.9406564584124654e-324L"/>
<definedSymbol symbol="__LDBL_HAS_DENORM__=1"/>
<definedSymbol symbol="__LDBL_HAS_INFINITY__=1"/>
<definedSymbol symbol="__LDBL_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__DEC32_MANT_DIG__=7"/>
<definedSymbol symbol="__DEC32_MIN_EXP__=(-94)"/>
<definedSymbol symbol="__DEC32_MAX_EXP__=97"/>
<definedSymbol symbol="__DEC32_MIN__=1E-95DF"/>
<definedSymbol symbol="__DEC32_MAX__=9.999999E96DF"/>
<definedSymbol symbol="__DEC32_EPSILON__=1E-6DF"/>
<definedSymbol symbol="__DEC32_SUBNORMAL_MIN__=0.000001E-95DF"/>
<definedSymbol symbol="__DEC64_MANT_DIG__=16"/>
<definedSymbol symbol="__DEC64_MIN_EXP__=(-382)"/>
<definedSymbol symbol="__DEC64_MAX_EXP__=385"/>
<definedSymbol symbol="__DEC64_MIN__=1E-383DD"/>
<definedSymbol symbol="__DEC64_MAX__=9.999999999999999E384DD"/>
<definedSymbol symbol="__DEC64_EPSILON__=1E-15DD"/>
<definedSymbol symbol="__DEC64_SUBNORMAL_MIN__=0.000000000000001E-383DD"/>
<definedSymbol symbol="__DEC128_MANT_DIG__=34"/>
<definedSymbol symbol="__DEC128_MIN_EXP__=(-6142)"/>
<definedSymbol symbol="__DEC128_MAX_EXP__=6145"/>
<definedSymbol symbol="__DEC128_MIN__=1E-6143DL"/>
<definedSymbol symbol="__DEC128_MAX__=9.999999999999999999999999999999999E6144DL"/>
<definedSymbol symbol="__DEC128_EPSILON__=1E-33DL"/>
<definedSymbol symbol="__DEC128_SUBNORMAL_MIN__=0.000000000000000000000000000000001E-6143DL"/>
<definedSymbol symbol="__SFRACT_FBIT__=7"/>
<definedSymbol symbol="__SFRACT_IBIT__=0"/>
<definedSymbol symbol="__SFRACT_MIN__=(-0.5HR-0.5HR)"/>
<definedSymbol symbol="__SFRACT_MAX__=0X7FP-7HR"/>
<definedSymbol symbol="__SFRACT_EPSILON__=0x1P-7HR"/>
<definedSymbol symbol="__USFRACT_FBIT__=8"/>
<definedSymbol symbol="__USFRACT_IBIT__=0"/>
<definedSymbol symbol="__USFRACT_MIN__=0.0UHR"/>
<definedSymbol symbol="__USFRACT_MAX__=0XFFP-8UHR"/>
<definedSymbol symbol="__USFRACT_EPSILON__=0x1P-8UHR"/>
<definedSymbol symbol="__FRACT_FBIT__=15"/>
<definedSymbol symbol="__FRACT_IBIT__=0"/>
<definedSymbol symbol="__FRACT_MIN__=(-0.5R-0.5R)"/>
<definedSymbol symbol="__FRACT_MAX__=0X7FFFP-15R"/>
<definedSymbol symbol="__FRACT_EPSILON__=0x1P-15R"/>
<definedSymbol symbol="__UFRACT_FBIT__=16"/>
<definedSymbol symbol="__UFRACT_IBIT__=0"/>
<definedSymbol symbol="__UFRACT_MIN__=0.0UR"/>
<definedSymbol symbol="__UFRACT_MAX__=0XFFFFP-16UR"/>
<definedSymbol symbol="__UFRACT_EPSILON__=0x1P-16UR"/>
<definedSymbol symbol="__LFRACT_FBIT__=31"/>
<definedSymbol symbol="__LFRACT_IBIT__=0"/>
<definedSymbol symbol="__LFRACT_MIN__=(-0.5LR-0.5LR)"/>
<definedSymbol symbol="__LFRACT_MAX__=0X7FFFFFFFP-31LR"/>
<definedSymbol symbol="__LFRACT_EPSILON__=0x1P-31LR"/>
<definedSymbol symbol="__ULFRACT_FBIT__=32"/>
<definedSymbol symbol="__ULFRACT_IBIT__=0"/>
<definedSymbol symbol="__ULFRACT_MIN__=0.0ULR"/>
<definedSymbol symbol="__ULFRACT_MAX__=0XFFFFFFFFP-32ULR"/>
<definedSymbol symbol="__ULFRACT_EPSILON__=0x1P-32ULR"/>
<definedSymbol symbol="__LLFRACT_FBIT__=63"/>
<definedSymbol symbol="__LLFRACT_IBIT__=0"/>
<definedSymbol symbol="__LLFRACT_MIN__=(-0.5LLR-0.5LLR)"/>
<definedSymbol symbol="__LLFRACT_MAX__=0X7FFFFFFFFFFFFFFFP-63LLR"/>
<definedSymbol symbol="__LLFRACT_EPSILON__=0x1P-63LLR"/>
<definedSymbol symbol="__ULLFRACT_FBIT__=64"/>
<definedSymbol symbol="__ULLFRACT_IBIT__=0"/>
<definedSymbol symbol="__ULLFRACT_MIN__=0.0ULLR"/>
<definedSymbol symbol="__ULLFRACT_MAX__=0XFFFFFFFFFFFFFFFFP-64ULLR"/>
<definedSymbol symbol="__ULLFRACT_EPSILON__=0x1P-64ULLR"/>
<definedSymbol symbol="__SACCUM_FBIT__=7"/>
<definedSymbol symbol="__SACCUM_IBIT__=8"/>
<definedSymbol symbol="__SACCUM_MIN__=(-0X1P7HK-0X1P7HK)"/>
<definedSymbol symbol="__SACCUM_MAX__=0X7FFFP-7HK"/>
<definedSymbol symbol="__SACCUM_EPSILON__=0x1P-7HK"/>
<definedSymbol symbol="__USACCUM_FBIT__=8"/>
<definedSymbol symbol="__USACCUM_IBIT__=8"/>
<definedSymbol symbol="__USACCUM_MIN__=0.0UHK"/>
<definedSymbol symbol="__USACCUM_MAX__=0XFFFFP-8UHK"/>
<definedSymbol symbol="__USACCUM_EPSILON__=0x1P-8UHK"/>
<definedSymbol symbol="__ACCUM_FBIT__=15"/>
<definedSymbol symbol="__ACCUM_IBIT__=16"/>
<definedSymbol symbol="__ACCUM_MIN__=(-0X1P15K-0X1P15K)"/>
<definedSymbol symbol="__ACCUM_MAX__=0X7FFFFFFFP-15K"/>
<definedSymbol symbol="__ACCUM_EPSILON__=0x1P-15K"/>
<definedSymbol symbol="__UACCUM_FBIT__=16"/>
<definedSymbol symbol="__UACCUM_IBIT__=16"/>
<definedSymbol symbol="__UACCUM_MIN__=0.0UK"/>
<definedSymbol symbol="__UACCUM_MAX__=0XFFFFFFFFP-16UK"/>
<definedSymbol symbol="__UACCUM_EPSILON__=0x1P-16UK"/>
<definedSymbol symbol="__LACCUM_FBIT__=31"/>
<definedSymbol symbol="__LACCUM_IBIT__=32"/>
<definedSymbol symbol="__LACCUM_MIN__=(-0X1P31LK-0X1P31LK)"/>
<definedSymbol symbol="__LACCUM_MAX__=0X7FFFFFFFFFFFFFFFP-31LK"/>
<definedSymbol symbol="__LACCUM_EPSILON__=0x1P-31LK"/>
<definedSymbol symbol="__ULACCUM_FBIT__=32"/>
<definedSymbol symbol="__ULACCUM_IBIT__=32"/>
<definedSymbol symbol="__ULACCUM_MIN__=0.0ULK"/>
<definedSymbol symbol="__ULACCUM_MAX__=0XFFFFFFFFFFFFFFFFP-32ULK"/>
<definedSymbol symbol="__ULACCUM_EPSILON__=0x1P-32ULK"/>
<definedSymbol symbol="__LLACCUM_FBIT__=31"/>
<definedSymbol symbol="__LLACCUM_IBIT__=32"/>
<definedSymbol symbol="__LLACCUM_MIN__=(-0X1P31LLK-0X1P31LLK)"/>
<definedSymbol symbol="__LLACCUM_MAX__=0X7FFFFFFFFFFFFFFFP-31LLK"/>
<definedSymbol symbol="__LLACCUM_EPSILON__=0x1P-31LLK"/>
<definedSymbol symbol="__ULLACCUM_FBIT__=32"/>
<definedSymbol symbol="__ULLACCUM_IBIT__=32"/>
<definedSymbol symbol="__ULLACCUM_MIN__=0.0ULLK"/>
<definedSymbol symbol="__ULLACCUM_MAX__=0XFFFFFFFFFFFFFFFFP-32ULLK"/>
<definedSymbol symbol="__ULLACCUM_EPSILON__=0x1P-32ULLK"/>
<definedSymbol symbol="__QQ_FBIT__=7"/>
<definedSymbol symbol="__QQ_IBIT__=0"/>
<definedSymbol symbol="__HQ_FBIT__=15"/>
<definedSymbol symbol="__HQ_IBIT__=0"/>
<definedSymbol symbol="__SQ_FBIT__=31"/>
<definedSymbol symbol="__SQ_IBIT__=0"/>
<definedSymbol symbol="__DQ_FBIT__=63"/>
<definedSymbol symbol="__DQ_IBIT__=0"/>
<definedSymbol symbol="__TQ_FBIT__=127"/>
<definedSymbol symbol="__TQ_IBIT__=0"/>
<definedSymbol symbol="__UQQ_FBIT__=8"/>
<definedSymbol symbol="__UQQ_IBIT__=0"/>
<definedSymbol symbol="__UHQ_FBIT__=16"/>
<definedSymbol symbol="__UHQ_IBIT__=0"/>
<definedSymbol symbol="__USQ_FBIT__=32"/>
<definedSymbol symbol="__USQ_IBIT__=0"/>
<definedSymbol symbol="__UDQ_FBIT__=64"/>
<definedSymbol symbol="__UDQ_IBIT__=0"/>
<definedSymbol symbol="__UTQ_FBIT__=128"/>
<definedSymbol symbol="__UTQ_IBIT__=0"/>
<definedSymbol symbol="__HA_FBIT__=7"/>
<definedSymbol symbol="__HA_IBIT__=8"/>
<definedSymbol symbol="__SA_FBIT__=15"/>
<definedSymbol symbol="__SA_IBIT__=16"/>
<definedSymbol symbol="__DA_FBIT__=31"/>
<definedSymbol symbol="__DA_IBIT__=32"/>
<definedSymbol symbol="__TA_FBIT__=63"/>
<definedSymbol symbol="__TA_IBIT__=64"/>
<definedSymbol symbol="__UHA_FBIT__=8"/>
<definedSymbol symbol="__UHA_IBIT__=8"/>
<definedSymbol symbol="__USA_FBIT__=16"/>
<definedSymbol symbol="__USA_IBIT__=16"/>
<definedSymbol symbol="__UDA_FBIT__=32"/>
<definedSymbol symbol="__UDA_IBIT__=32"/>
<definedSymbol symbol="__UTA_FBIT__=64"/>
<definedSymbol symbol="__UTA_IBIT__=64"/>
<definedSymbol symbol="__REGISTER_PREFIX__="/>
<definedSymbol symbol="__USER_LABEL_PREFIX__="/>
<definedSymbol symbol="__GNUC_STDC_INLINE__=1"/>
<definedSymbol symbol="__NO_INLINE__=1"/>
<definedSymbol symbol="__CHAR_UNSIGNED__=1"/>
<definedSymbol symbol="__GCC_ATOMIC_BOOL_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR16_T_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR32_T_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_WCHAR_T_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_SHORT_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_INT_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_LONG_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_LLONG_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1"/>
<definedSymbol symbol="__GCC_ATOMIC_POINTER_LOCK_FREE=1"/>
<definedSymbol symbol="__PRAGMA_REDEFINE_EXTNAME=1"/>
<definedSymbol symbol="__SIZEOF_WCHAR_T__=4"/>
<definedSymbol symbol="__SIZEOF_WINT_T__=4"/>
<definedSymbol symbol="__SIZEOF_PTRDIFF_T__=4"/>
<definedSymbol symbol="__ARM_32BIT_STATE=1"/>
<definedSymbol symbol="__ARM_SIZEOF_MINIMAL_ENUM=1"/>
<definedSymbol symbol="__ARM_SIZEOF_WCHAR_T=4"/>
<definedSymbol symbol="__arm__=1"/>
<definedSymbol symbol="__ARM_ARCH=4"/>
<definedSymbol symbol="__ARM_ARCH_ISA_ARM=1"/>
<definedSymbol symbol="__APCS_32__=1"/>
<definedSymbol symbol="__ARM_ARCH_ISA_THUMB=1"/>
<definedSymbol symbol="__ARMEL__=1"/>
<definedSymbol symbol="__SOFTFP__=1"/>
<definedSymbol symbol="__VFP_FP__=1"/>
<definedSymbol symbol="__THUMB_INTERWORK__=1"/>
<definedSymbol symbol="__ARM_ARCH_4T__=1"/>
<definedSymbol symbol="__ARM_PCS=1"/>
<definedSymbol symbol="__ARM_EABI__=1"/>
<definedSymbol symbol="__GXX_TYPEINFO_EQUALITY_INLINE=0"/>
<definedSymbol symbol="__ELF__=1"/>
<definedSymbol symbol="__USES_INITFINI__=1"/>
</collector>
</instance>
<instance id="xilinx.gnu.armv7.exe.release.647963491;xilinx.gnu.armv7.exe.release.647963491.">
<collector id="org.eclipse.cdt.make.core.PerProjectSICollector">
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/include"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/include-fixed"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/../../../../arm-none-eabi/include"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../arm-none-eabi/libc/usr/include"/>
<definedSymbol symbol="__STDC__=1"/>
<definedSymbol symbol="__STDC_VERSION__=201112L"/>
<definedSymbol symbol="__STDC_UTF_16__=1"/>
<definedSymbol symbol="__STDC_UTF_32__=1"/>
<definedSymbol symbol="__STDC_HOSTED__=1"/>
<definedSymbol symbol="__GNUC__=5"/>
<definedSymbol symbol="__GNUC_MINOR__=2"/>
<definedSymbol symbol="__GNUC_PATCHLEVEL__=1"/>
<definedSymbol symbol="__VERSION__=&quot;5.2.1 20151005&quot;"/>
<definedSymbol symbol="__LINARO_RELEASE__=201511"/>
<definedSymbol symbol="__LINARO_SPIN__=2"/>
<definedSymbol symbol="__ATOMIC_RELAXED=0"/>
<definedSymbol symbol="__ATOMIC_SEQ_CST=5"/>
<definedSymbol symbol="__ATOMIC_ACQUIRE=2"/>
<definedSymbol symbol="__ATOMIC_RELEASE=3"/>
<definedSymbol symbol="__ATOMIC_ACQ_REL=4"/>
<definedSymbol symbol="__ATOMIC_CONSUME=1"/>
<definedSymbol symbol="__FINITE_MATH_ONLY__=0"/>
<definedSymbol symbol="__SIZEOF_INT__=4"/>
<definedSymbol symbol="__SIZEOF_LONG__=4"/>
<definedSymbol symbol="__SIZEOF_LONG_LONG__=8"/>
<definedSymbol symbol="__SIZEOF_SHORT__=2"/>
<definedSymbol symbol="__SIZEOF_FLOAT__=4"/>
<definedSymbol symbol="__SIZEOF_DOUBLE__=8"/>
<definedSymbol symbol="__SIZEOF_LONG_DOUBLE__=8"/>
<definedSymbol symbol="__SIZEOF_SIZE_T__=4"/>
<definedSymbol symbol="__CHAR_BIT__=8"/>
<definedSymbol symbol="__BIGGEST_ALIGNMENT__=8"/>
<definedSymbol symbol="__ORDER_LITTLE_ENDIAN__=1234"/>
<definedSymbol symbol="__ORDER_BIG_ENDIAN__=4321"/>
<definedSymbol symbol="__ORDER_PDP_ENDIAN__=3412"/>
<definedSymbol symbol="__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__"/>
<definedSymbol symbol="__FLOAT_WORD_ORDER__=__ORDER_LITTLE_ENDIAN__"/>
<definedSymbol symbol="__SIZEOF_POINTER__=4"/>
<definedSymbol symbol="__SIZE_TYPE__=unsigned int"/>
<definedSymbol symbol="__PTRDIFF_TYPE__=int"/>
<definedSymbol symbol="__WCHAR_TYPE__=unsigned int"/>
<definedSymbol symbol="__WINT_TYPE__=unsigned int"/>
<definedSymbol symbol="__INTMAX_TYPE__=long long int"/>
<definedSymbol symbol="__UINTMAX_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__CHAR16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__CHAR32_TYPE__=long unsigned int"/>
<definedSymbol symbol="__SIG_ATOMIC_TYPE__=int"/>
<definedSymbol symbol="__INT8_TYPE__=signed char"/>
<definedSymbol symbol="__INT16_TYPE__=short int"/>
<definedSymbol symbol="__INT32_TYPE__=long int"/>
<definedSymbol symbol="__INT64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT32_TYPE__=long unsigned int"/>
<definedSymbol symbol="__UINT64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INT_LEAST8_TYPE__=signed char"/>
<definedSymbol symbol="__INT_LEAST16_TYPE__=short int"/>
<definedSymbol symbol="__INT_LEAST32_TYPE__=long int"/>
<definedSymbol symbol="__INT_LEAST64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT_LEAST8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT_LEAST16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT_LEAST32_TYPE__=long unsigned int"/>
<definedSymbol symbol="__UINT_LEAST64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INT_FAST8_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST16_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST32_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT_FAST8_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST16_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST32_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INTPTR_TYPE__=int"/>
<definedSymbol symbol="__UINTPTR_TYPE__=unsigned int"/>
<definedSymbol symbol="__has_include(STR)=__has_include__(STR)"/>
<definedSymbol symbol="__has_include_next(STR)=__has_include_next__(STR)"/>
<definedSymbol symbol="__GXX_ABI_VERSION=1009"/>
<definedSymbol symbol="__SCHAR_MAX__=0x7f"/>
<definedSymbol symbol="__SHRT_MAX__=0x7fff"/>
<definedSymbol symbol="__INT_MAX__=0x7fffffff"/>
<definedSymbol symbol="__LONG_MAX__=0x7fffffffL"/>
<definedSymbol symbol="__LONG_LONG_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__WCHAR_MAX__=0xffffffffU"/>
<definedSymbol symbol="__WCHAR_MIN__=0U"/>
<definedSymbol symbol="__WINT_MAX__=0xffffffffU"/>
<definedSymbol symbol="__WINT_MIN__=0U"/>
<definedSymbol symbol="__PTRDIFF_MAX__=0x7fffffff"/>
<definedSymbol symbol="__SIZE_MAX__=0xffffffffU"/>
<definedSymbol symbol="__INTMAX_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__INTMAX_C(c)=c ## LL"/>
<definedSymbol symbol="__UINTMAX_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__UINTMAX_C(c)=c ## ULL"/>
<definedSymbol symbol="__SIG_ATOMIC_MAX__=0x7fffffff"/>
<definedSymbol symbol="__SIG_ATOMIC_MIN__=(-__SIG_ATOMIC_MAX__ - 1)"/>
<definedSymbol symbol="__INT8_MAX__=0x7f"/>
<definedSymbol symbol="__INT16_MAX__=0x7fff"/>
<definedSymbol symbol="__INT32_MAX__=0x7fffffffL"/>
<definedSymbol symbol="__INT64_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__UINT8_MAX__=0xff"/>
<definedSymbol symbol="__UINT16_MAX__=0xffff"/>
<definedSymbol symbol="__UINT32_MAX__=0xffffffffUL"/>
<definedSymbol symbol="__UINT64_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__INT_LEAST8_MAX__=0x7f"/>
<definedSymbol symbol="__INT8_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST16_MAX__=0x7fff"/>
<definedSymbol symbol="__INT16_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST32_MAX__=0x7fffffffL"/>
<definedSymbol symbol="__INT32_C(c)=c ## L"/>
<definedSymbol symbol="__INT_LEAST64_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__INT64_C(c)=c ## LL"/>
<definedSymbol symbol="__UINT_LEAST8_MAX__=0xff"/>
<definedSymbol symbol="__UINT8_C(c)=c"/>
<definedSymbol symbol="__UINT_LEAST16_MAX__=0xffff"/>
<definedSymbol symbol="__UINT16_C(c)=c"/>
<definedSymbol symbol="__UINT_LEAST32_MAX__=0xffffffffUL"/>
<definedSymbol symbol="__UINT32_C(c)=c ## UL"/>
<definedSymbol symbol="__UINT_LEAST64_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__UINT64_C(c)=c ## ULL"/>
<definedSymbol symbol="__INT_FAST8_MAX__=0x7fffffff"/>
<definedSymbol symbol="__INT_FAST16_MAX__=0x7fffffff"/>
<definedSymbol symbol="__INT_FAST32_MAX__=0x7fffffff"/>
<definedSymbol symbol="__INT_FAST64_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__UINT_FAST8_MAX__=0xffffffffU"/>
<definedSymbol symbol="__UINT_FAST16_MAX__=0xffffffffU"/>
<definedSymbol symbol="__UINT_FAST32_MAX__=0xffffffffU"/>
<definedSymbol symbol="__UINT_FAST64_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__INTPTR_MAX__=0x7fffffff"/>
<definedSymbol symbol="__UINTPTR_MAX__=0xffffffffU"/>
<definedSymbol symbol="__GCC_IEC_559=0"/>
<definedSymbol symbol="__GCC_IEC_559_COMPLEX=0"/>
<definedSymbol symbol="__FLT_EVAL_METHOD__=0"/>
<definedSymbol symbol="__DEC_EVAL_METHOD__=2"/>
<definedSymbol symbol="__FLT_RADIX__=2"/>
<definedSymbol symbol="__FLT_MANT_DIG__=24"/>
<definedSymbol symbol="__FLT_DIG__=6"/>
<definedSymbol symbol="__FLT_MIN_EXP__=(-125)"/>
<definedSymbol symbol="__FLT_MIN_10_EXP__=(-37)"/>
<definedSymbol symbol="__FLT_MAX_EXP__=128"/>
<definedSymbol symbol="__FLT_MAX_10_EXP__=38"/>
<definedSymbol symbol="__FLT_DECIMAL_DIG__=9"/>
<definedSymbol symbol="__FLT_MAX__=3.4028234663852886e+38F"/>
<definedSymbol symbol="__FLT_MIN__=1.1754943508222875e-38F"/>
<definedSymbol symbol="__FLT_EPSILON__=1.1920928955078125e-7F"/>
<definedSymbol symbol="__FLT_DENORM_MIN__=1.4012984643248171e-45F"/>
<definedSymbol symbol="__FLT_HAS_DENORM__=1"/>
<definedSymbol symbol="__FLT_HAS_INFINITY__=1"/>
<definedSymbol symbol="__FLT_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__DBL_MANT_DIG__=53"/>
<definedSymbol symbol="__DBL_DIG__=15"/>
<definedSymbol symbol="__DBL_MIN_EXP__=(-1021)"/>
<definedSymbol symbol="__DBL_MIN_10_EXP__=(-307)"/>
<definedSymbol symbol="__DBL_MAX_EXP__=1024"/>
<definedSymbol symbol="__DBL_MAX_10_EXP__=308"/>
<definedSymbol symbol="__DBL_DECIMAL_DIG__=17"/>
<definedSymbol symbol="__DBL_MAX__=((double)1.7976931348623157e+308L)"/>
<definedSymbol symbol="__DBL_MIN__=((double)2.2250738585072014e-308L)"/>
<definedSymbol symbol="__DBL_EPSILON__=((double)2.2204460492503131e-16L)"/>
<definedSymbol symbol="__DBL_DENORM_MIN__=((double)4.9406564584124654e-324L)"/>
<definedSymbol symbol="__DBL_HAS_DENORM__=1"/>
<definedSymbol symbol="__DBL_HAS_INFINITY__=1"/>
<definedSymbol symbol="__DBL_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__LDBL_MANT_DIG__=53"/>
<definedSymbol symbol="__LDBL_DIG__=15"/>
<definedSymbol symbol="__LDBL_MIN_EXP__=(-1021)"/>
<definedSymbol symbol="__LDBL_MIN_10_EXP__=(-307)"/>
<definedSymbol symbol="__LDBL_MAX_EXP__=1024"/>
<definedSymbol symbol="__LDBL_MAX_10_EXP__=308"/>
<definedSymbol symbol="__DECIMAL_DIG__=17"/>
<definedSymbol symbol="__LDBL_MAX__=1.7976931348623157e+308L"/>
<definedSymbol symbol="__LDBL_MIN__=2.2250738585072014e-308L"/>
<definedSymbol symbol="__LDBL_EPSILON__=2.2204460492503131e-16L"/>
<definedSymbol symbol="__LDBL_DENORM_MIN__=4.9406564584124654e-324L"/>
<definedSymbol symbol="__LDBL_HAS_DENORM__=1"/>
<definedSymbol symbol="__LDBL_HAS_INFINITY__=1"/>
<definedSymbol symbol="__LDBL_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__DEC32_MANT_DIG__=7"/>
<definedSymbol symbol="__DEC32_MIN_EXP__=(-94)"/>
<definedSymbol symbol="__DEC32_MAX_EXP__=97"/>
<definedSymbol symbol="__DEC32_MIN__=1E-95DF"/>
<definedSymbol symbol="__DEC32_MAX__=9.999999E96DF"/>
<definedSymbol symbol="__DEC32_EPSILON__=1E-6DF"/>
<definedSymbol symbol="__DEC32_SUBNORMAL_MIN__=0.000001E-95DF"/>
<definedSymbol symbol="__DEC64_MANT_DIG__=16"/>
<definedSymbol symbol="__DEC64_MIN_EXP__=(-382)"/>
<definedSymbol symbol="__DEC64_MAX_EXP__=385"/>
<definedSymbol symbol="__DEC64_MIN__=1E-383DD"/>
<definedSymbol symbol="__DEC64_MAX__=9.999999999999999E384DD"/>
<definedSymbol symbol="__DEC64_EPSILON__=1E-15DD"/>
<definedSymbol symbol="__DEC64_SUBNORMAL_MIN__=0.000000000000001E-383DD"/>
<definedSymbol symbol="__DEC128_MANT_DIG__=34"/>
<definedSymbol symbol="__DEC128_MIN_EXP__=(-6142)"/>
<definedSymbol symbol="__DEC128_MAX_EXP__=6145"/>
<definedSymbol symbol="__DEC128_MIN__=1E-6143DL"/>
<definedSymbol symbol="__DEC128_MAX__=9.999999999999999999999999999999999E6144DL"/>
<definedSymbol symbol="__DEC128_EPSILON__=1E-33DL"/>
<definedSymbol symbol="__DEC128_SUBNORMAL_MIN__=0.000000000000000000000000000000001E-6143DL"/>
<definedSymbol symbol="__SFRACT_FBIT__=7"/>
<definedSymbol symbol="__SFRACT_IBIT__=0"/>
<definedSymbol symbol="__SFRACT_MIN__=(-0.5HR-0.5HR)"/>
<definedSymbol symbol="__SFRACT_MAX__=0X7FP-7HR"/>
<definedSymbol symbol="__SFRACT_EPSILON__=0x1P-7HR"/>
<definedSymbol symbol="__USFRACT_FBIT__=8"/>
<definedSymbol symbol="__USFRACT_IBIT__=0"/>
<definedSymbol symbol="__USFRACT_MIN__=0.0UHR"/>
<definedSymbol symbol="__USFRACT_MAX__=0XFFP-8UHR"/>
<definedSymbol symbol="__USFRACT_EPSILON__=0x1P-8UHR"/>
<definedSymbol symbol="__FRACT_FBIT__=15"/>
<definedSymbol symbol="__FRACT_IBIT__=0"/>
<definedSymbol symbol="__FRACT_MIN__=(-0.5R-0.5R)"/>
<definedSymbol symbol="__FRACT_MAX__=0X7FFFP-15R"/>
<definedSymbol symbol="__FRACT_EPSILON__=0x1P-15R"/>
<definedSymbol symbol="__UFRACT_FBIT__=16"/>
<definedSymbol symbol="__UFRACT_IBIT__=0"/>
<definedSymbol symbol="__UFRACT_MIN__=0.0UR"/>
<definedSymbol symbol="__UFRACT_MAX__=0XFFFFP-16UR"/>
<definedSymbol symbol="__UFRACT_EPSILON__=0x1P-16UR"/>
<definedSymbol symbol="__LFRACT_FBIT__=31"/>
<definedSymbol symbol="__LFRACT_IBIT__=0"/>
<definedSymbol symbol="__LFRACT_MIN__=(-0.5LR-0.5LR)"/>
<definedSymbol symbol="__LFRACT_MAX__=0X7FFFFFFFP-31LR"/>
<definedSymbol symbol="__LFRACT_EPSILON__=0x1P-31LR"/>
<definedSymbol symbol="__ULFRACT_FBIT__=32"/>
<definedSymbol symbol="__ULFRACT_IBIT__=0"/>
<definedSymbol symbol="__ULFRACT_MIN__=0.0ULR"/>
<definedSymbol symbol="__ULFRACT_MAX__=0XFFFFFFFFP-32ULR"/>
<definedSymbol symbol="__ULFRACT_EPSILON__=0x1P-32ULR"/>
<definedSymbol symbol="__LLFRACT_FBIT__=63"/>
<definedSymbol symbol="__LLFRACT_IBIT__=0"/>
<definedSymbol symbol="__LLFRACT_MIN__=(-0.5LLR-0.5LLR)"/>
<definedSymbol symbol="__LLFRACT_MAX__=0X7FFFFFFFFFFFFFFFP-63LLR"/>
<definedSymbol symbol="__LLFRACT_EPSILON__=0x1P-63LLR"/>
<definedSymbol symbol="__ULLFRACT_FBIT__=64"/>
<definedSymbol symbol="__ULLFRACT_IBIT__=0"/>
<definedSymbol symbol="__ULLFRACT_MIN__=0.0ULLR"/>
<definedSymbol symbol="__ULLFRACT_MAX__=0XFFFFFFFFFFFFFFFFP-64ULLR"/>
<definedSymbol symbol="__ULLFRACT_EPSILON__=0x1P-64ULLR"/>
<definedSymbol symbol="__SACCUM_FBIT__=7"/>
<definedSymbol symbol="__SACCUM_IBIT__=8"/>
<definedSymbol symbol="__SACCUM_MIN__=(-0X1P7HK-0X1P7HK)"/>
<definedSymbol symbol="__SACCUM_MAX__=0X7FFFP-7HK"/>
<definedSymbol symbol="__SACCUM_EPSILON__=0x1P-7HK"/>
<definedSymbol symbol="__USACCUM_FBIT__=8"/>
<definedSymbol symbol="__USACCUM_IBIT__=8"/>
<definedSymbol symbol="__USACCUM_MIN__=0.0UHK"/>
<definedSymbol symbol="__USACCUM_MAX__=0XFFFFP-8UHK"/>
<definedSymbol symbol="__USACCUM_EPSILON__=0x1P-8UHK"/>
<definedSymbol symbol="__ACCUM_FBIT__=15"/>
<definedSymbol symbol="__ACCUM_IBIT__=16"/>
<definedSymbol symbol="__ACCUM_MIN__=(-0X1P15K-0X1P15K)"/>
<definedSymbol symbol="__ACCUM_MAX__=0X7FFFFFFFP-15K"/>
<definedSymbol symbol="__ACCUM_EPSILON__=0x1P-15K"/>
<definedSymbol symbol="__UACCUM_FBIT__=16"/>
<definedSymbol symbol="__UACCUM_IBIT__=16"/>
<definedSymbol symbol="__UACCUM_MIN__=0.0UK"/>
<definedSymbol symbol="__UACCUM_MAX__=0XFFFFFFFFP-16UK"/>
<definedSymbol symbol="__UACCUM_EPSILON__=0x1P-16UK"/>
<definedSymbol symbol="__LACCUM_FBIT__=31"/>
<definedSymbol symbol="__LACCUM_IBIT__=32"/>
<definedSymbol symbol="__LACCUM_MIN__=(-0X1P31LK-0X1P31LK)"/>
<definedSymbol symbol="__LACCUM_MAX__=0X7FFFFFFFFFFFFFFFP-31LK"/>
<definedSymbol symbol="__LACCUM_EPSILON__=0x1P-31LK"/>
<definedSymbol symbol="__ULACCUM_FBIT__=32"/>
<definedSymbol symbol="__ULACCUM_IBIT__=32"/>
<definedSymbol symbol="__ULACCUM_MIN__=0.0ULK"/>
<definedSymbol symbol="__ULACCUM_MAX__=0XFFFFFFFFFFFFFFFFP-32ULK"/>
<definedSymbol symbol="__ULACCUM_EPSILON__=0x1P-32ULK"/>
<definedSymbol symbol="__LLACCUM_FBIT__=31"/>
<definedSymbol symbol="__LLACCUM_IBIT__=32"/>
<definedSymbol symbol="__LLACCUM_MIN__=(-0X1P31LLK-0X1P31LLK)"/>
<definedSymbol symbol="__LLACCUM_MAX__=0X7FFFFFFFFFFFFFFFP-31LLK"/>
<definedSymbol symbol="__LLACCUM_EPSILON__=0x1P-31LLK"/>
<definedSymbol symbol="__ULLACCUM_FBIT__=32"/>
<definedSymbol symbol="__ULLACCUM_IBIT__=32"/>
<definedSymbol symbol="__ULLACCUM_MIN__=0.0ULLK"/>
<definedSymbol symbol="__ULLACCUM_MAX__=0XFFFFFFFFFFFFFFFFP-32ULLK"/>
<definedSymbol symbol="__ULLACCUM_EPSILON__=0x1P-32ULLK"/>
<definedSymbol symbol="__QQ_FBIT__=7"/>
<definedSymbol symbol="__QQ_IBIT__=0"/>
<definedSymbol symbol="__HQ_FBIT__=15"/>
<definedSymbol symbol="__HQ_IBIT__=0"/>
<definedSymbol symbol="__SQ_FBIT__=31"/>
<definedSymbol symbol="__SQ_IBIT__=0"/>
<definedSymbol symbol="__DQ_FBIT__=63"/>
<definedSymbol symbol="__DQ_IBIT__=0"/>
<definedSymbol symbol="__TQ_FBIT__=127"/>
<definedSymbol symbol="__TQ_IBIT__=0"/>
<definedSymbol symbol="__UQQ_FBIT__=8"/>
<definedSymbol symbol="__UQQ_IBIT__=0"/>
<definedSymbol symbol="__UHQ_FBIT__=16"/>
<definedSymbol symbol="__UHQ_IBIT__=0"/>
<definedSymbol symbol="__USQ_FBIT__=32"/>
<definedSymbol symbol="__USQ_IBIT__=0"/>
<definedSymbol symbol="__UDQ_FBIT__=64"/>
<definedSymbol symbol="__UDQ_IBIT__=0"/>
<definedSymbol symbol="__UTQ_FBIT__=128"/>
<definedSymbol symbol="__UTQ_IBIT__=0"/>
<definedSymbol symbol="__HA_FBIT__=7"/>
<definedSymbol symbol="__HA_IBIT__=8"/>
<definedSymbol symbol="__SA_FBIT__=15"/>
<definedSymbol symbol="__SA_IBIT__=16"/>
<definedSymbol symbol="__DA_FBIT__=31"/>
<definedSymbol symbol="__DA_IBIT__=32"/>
<definedSymbol symbol="__TA_FBIT__=63"/>
<definedSymbol symbol="__TA_IBIT__=64"/>
<definedSymbol symbol="__UHA_FBIT__=8"/>
<definedSymbol symbol="__UHA_IBIT__=8"/>
<definedSymbol symbol="__USA_FBIT__=16"/>
<definedSymbol symbol="__USA_IBIT__=16"/>
<definedSymbol symbol="__UDA_FBIT__=32"/>
<definedSymbol symbol="__UDA_IBIT__=32"/>
<definedSymbol symbol="__UTA_FBIT__=64"/>
<definedSymbol symbol="__UTA_IBIT__=64"/>
<definedSymbol symbol="__REGISTER_PREFIX__="/>
<definedSymbol symbol="__USER_LABEL_PREFIX__="/>
<definedSymbol symbol="__GNUC_STDC_INLINE__=1"/>
<definedSymbol symbol="__NO_INLINE__=1"/>
<definedSymbol symbol="__CHAR_UNSIGNED__=1"/>
<definedSymbol symbol="__GCC_ATOMIC_BOOL_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR16_T_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR32_T_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_WCHAR_T_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_SHORT_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_INT_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_LONG_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_LLONG_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1"/>
<definedSymbol symbol="__GCC_ATOMIC_POINTER_LOCK_FREE=1"/>
<definedSymbol symbol="__PRAGMA_REDEFINE_EXTNAME=1"/>
<definedSymbol symbol="__SIZEOF_WCHAR_T__=4"/>
<definedSymbol symbol="__SIZEOF_WINT_T__=4"/>
<definedSymbol symbol="__SIZEOF_PTRDIFF_T__=4"/>
<definedSymbol symbol="__ARM_32BIT_STATE=1"/>
<definedSymbol symbol="__ARM_SIZEOF_MINIMAL_ENUM=1"/>
<definedSymbol symbol="__ARM_SIZEOF_WCHAR_T=4"/>
<definedSymbol symbol="__arm__=1"/>
<definedSymbol symbol="__ARM_ARCH=4"/>
<definedSymbol symbol="__ARM_ARCH_ISA_ARM=1"/>
<definedSymbol symbol="__APCS_32__=1"/>
<definedSymbol symbol="__ARM_ARCH_ISA_THUMB=1"/>
<definedSymbol symbol="__ARMEL__=1"/>
<definedSymbol symbol="__SOFTFP__=1"/>
<definedSymbol symbol="__VFP_FP__=1"/>
<definedSymbol symbol="__THUMB_INTERWORK__=1"/>
<definedSymbol symbol="__ARM_ARCH_4T__=1"/>
<definedSymbol symbol="__ARM_PCS=1"/>
<definedSymbol symbol="__ARM_EABI__=1"/>
<definedSymbol symbol="__GXX_TYPEINFO_EQUALITY_INLINE=0"/>
<definedSymbol symbol="__ELF__=1"/>
<definedSymbol symbol="__USES_INITFINI__=1"/>
</collector>
</instance>
<instance id="xilinx.gnu.armv7.exe.debug.456566226;xilinx.gnu.armv7.exe.debug.456566226.;xilinx.gnu.armv7.c.toolchain.compiler.debug.1114804754;xilinx.gnu.armv7.c.compiler.input.1949974799">
<collector id="org.eclipse.cdt.make.core.PerProjectSICollector">
<includePath path="d:\xilinx\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/include"/>
<includePath path="d:\xilinx\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/include-fixed"/>
<includePath path="d:\xilinx\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/../../../../arm-none-eabi/include"/>
<includePath path="d:\xilinx\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../arm-none-eabi/libc/usr/include"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/include"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/include-fixed"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../lib/gcc/arm-none-eabi/5.2.1/../../../../arm-none-eabi/include"/>
<includePath path="d:\xilinx\2016.4\sdk\2016.4\gnu\aarch32\nt\gcc-arm-none-eabi\bin\../arm-none-eabi/libc/usr/include"/>
<definedSymbol symbol="__STDC__=1"/>
<definedSymbol symbol="__STDC_VERSION__=201112L"/>
<definedSymbol symbol="__STDC_UTF_16__=1"/>
<definedSymbol symbol="__STDC_UTF_32__=1"/>
<definedSymbol symbol="__STDC_HOSTED__=1"/>
<definedSymbol symbol="__GNUC__=5"/>
<definedSymbol symbol="__GNUC_MINOR__=2"/>
<definedSymbol symbol="__GNUC_PATCHLEVEL__=1"/>
<definedSymbol symbol="__VERSION__=&quot;5.2.1 20151005&quot;"/>
<definedSymbol symbol="__LINARO_RELEASE__=201511"/>
<definedSymbol symbol="__LINARO_SPIN__=2"/>
<definedSymbol symbol="__ATOMIC_RELAXED=0"/>
<definedSymbol symbol="__ATOMIC_SEQ_CST=5"/>
<definedSymbol symbol="__ATOMIC_ACQUIRE=2"/>
<definedSymbol symbol="__ATOMIC_RELEASE=3"/>
<definedSymbol symbol="__ATOMIC_ACQ_REL=4"/>
<definedSymbol symbol="__ATOMIC_CONSUME=1"/>
<definedSymbol symbol="__FINITE_MATH_ONLY__=0"/>
<definedSymbol symbol="__SIZEOF_INT__=4"/>
<definedSymbol symbol="__SIZEOF_LONG__=4"/>
<definedSymbol symbol="__SIZEOF_LONG_LONG__=8"/>
<definedSymbol symbol="__SIZEOF_SHORT__=2"/>
<definedSymbol symbol="__SIZEOF_FLOAT__=4"/>
<definedSymbol symbol="__SIZEOF_DOUBLE__=8"/>
<definedSymbol symbol="__SIZEOF_LONG_DOUBLE__=8"/>
<definedSymbol symbol="__SIZEOF_SIZE_T__=4"/>
<definedSymbol symbol="__CHAR_BIT__=8"/>
<definedSymbol symbol="__BIGGEST_ALIGNMENT__=8"/>
<definedSymbol symbol="__ORDER_LITTLE_ENDIAN__=1234"/>
<definedSymbol symbol="__ORDER_BIG_ENDIAN__=4321"/>
<definedSymbol symbol="__ORDER_PDP_ENDIAN__=3412"/>
<definedSymbol symbol="__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__"/>
<definedSymbol symbol="__FLOAT_WORD_ORDER__=__ORDER_LITTLE_ENDIAN__"/>
<definedSymbol symbol="__SIZEOF_POINTER__=4"/>
<definedSymbol symbol="__SIZE_TYPE__=unsigned int"/>
<definedSymbol symbol="__PTRDIFF_TYPE__=int"/>
<definedSymbol symbol="__WCHAR_TYPE__=unsigned int"/>
<definedSymbol symbol="__WINT_TYPE__=unsigned int"/>
<definedSymbol symbol="__INTMAX_TYPE__=long long int"/>
<definedSymbol symbol="__UINTMAX_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__CHAR16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__CHAR32_TYPE__=long unsigned int"/>
<definedSymbol symbol="__SIG_ATOMIC_TYPE__=int"/>
<definedSymbol symbol="__INT8_TYPE__=signed char"/>
<definedSymbol symbol="__INT16_TYPE__=short int"/>
<definedSymbol symbol="__INT32_TYPE__=long int"/>
<definedSymbol symbol="__INT64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT32_TYPE__=long unsigned int"/>
<definedSymbol symbol="__UINT64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INT_LEAST8_TYPE__=signed char"/>
<definedSymbol symbol="__INT_LEAST16_TYPE__=short int"/>
<definedSymbol symbol="__INT_LEAST32_TYPE__=long int"/>
<definedSymbol symbol="__INT_LEAST64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT_LEAST8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT_LEAST16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT_LEAST32_TYPE__=long unsigned int"/>
<definedSymbol symbol="__UINT_LEAST64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INT_FAST8_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST16_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST32_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT_FAST8_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST16_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST32_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INTPTR_TYPE__=int"/>
<definedSymbol symbol="__UINTPTR_TYPE__=unsigned int"/>
<definedSymbol symbol="__has_include(STR)=__has_include__(STR)"/>
<definedSymbol symbol="__has_include_next(STR)=__has_include_next__(STR)"/>
<definedSymbol symbol="__GXX_ABI_VERSION=1009"/>
<definedSymbol symbol="__SCHAR_MAX__=0x7f"/>
<definedSymbol symbol="__SHRT_MAX__=0x7fff"/>
<definedSymbol symbol="__INT_MAX__=0x7fffffff"/>
<definedSymbol symbol="__LONG_MAX__=0x7fffffffL"/>
<definedSymbol symbol="__LONG_LONG_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__WCHAR_MAX__=0xffffffffU"/>
<definedSymbol symbol="__WCHAR_MIN__=0U"/>
<definedSymbol symbol="__WINT_MAX__=0xffffffffU"/>
<definedSymbol symbol="__WINT_MIN__=0U"/>
<definedSymbol symbol="__PTRDIFF_MAX__=0x7fffffff"/>
<definedSymbol symbol="__SIZE_MAX__=0xffffffffU"/>
<definedSymbol symbol="__INTMAX_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__INTMAX_C(c)=c ## LL"/>
<definedSymbol symbol="__UINTMAX_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__UINTMAX_C(c)=c ## ULL"/>
<definedSymbol symbol="__SIG_ATOMIC_MAX__=0x7fffffff"/>
<definedSymbol symbol="__SIG_ATOMIC_MIN__=(-__SIG_ATOMIC_MAX__ - 1)"/>
<definedSymbol symbol="__INT8_MAX__=0x7f"/>
<definedSymbol symbol="__INT16_MAX__=0x7fff"/>
<definedSymbol symbol="__INT32_MAX__=0x7fffffffL"/>
<definedSymbol symbol="__INT64_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__UINT8_MAX__=0xff"/>
<definedSymbol symbol="__UINT16_MAX__=0xffff"/>
<definedSymbol symbol="__UINT32_MAX__=0xffffffffUL"/>
<definedSymbol symbol="__UINT64_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__INT_LEAST8_MAX__=0x7f"/>
<definedSymbol symbol="__INT8_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST16_MAX__=0x7fff"/>
<definedSymbol symbol="__INT16_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST32_MAX__=0x7fffffffL"/>
<definedSymbol symbol="__INT32_C(c)=c ## L"/>
<definedSymbol symbol="__INT_LEAST64_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__INT64_C(c)=c ## LL"/>
<definedSymbol symbol="__UINT_LEAST8_MAX__=0xff"/>
<definedSymbol symbol="__UINT8_C(c)=c"/>
<definedSymbol symbol="__UINT_LEAST16_MAX__=0xffff"/>
<definedSymbol symbol="__UINT16_C(c)=c"/>
<definedSymbol symbol="__UINT_LEAST32_MAX__=0xffffffffUL"/>
<definedSymbol symbol="__UINT32_C(c)=c ## UL"/>
<definedSymbol symbol="__UINT_LEAST64_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__UINT64_C(c)=c ## ULL"/>
<definedSymbol symbol="__INT_FAST8_MAX__=0x7fffffff"/>
<definedSymbol symbol="__INT_FAST16_MAX__=0x7fffffff"/>
<definedSymbol symbol="__INT_FAST32_MAX__=0x7fffffff"/>
<definedSymbol symbol="__INT_FAST64_MAX__=0x7fffffffffffffffLL"/>
<definedSymbol symbol="__UINT_FAST8_MAX__=0xffffffffU"/>
<definedSymbol symbol="__UINT_FAST16_MAX__=0xffffffffU"/>
<definedSymbol symbol="__UINT_FAST32_MAX__=0xffffffffU"/>
<definedSymbol symbol="__UINT_FAST64_MAX__=0xffffffffffffffffULL"/>
<definedSymbol symbol="__INTPTR_MAX__=0x7fffffff"/>
<definedSymbol symbol="__UINTPTR_MAX__=0xffffffffU"/>
<definedSymbol symbol="__GCC_IEC_559=0"/>
<definedSymbol symbol="__GCC_IEC_559_COMPLEX=0"/>
<definedSymbol symbol="__FLT_EVAL_METHOD__=0"/>
<definedSymbol symbol="__DEC_EVAL_METHOD__=2"/>
<definedSymbol symbol="__FLT_RADIX__=2"/>
<definedSymbol symbol="__FLT_MANT_DIG__=24"/>
<definedSymbol symbol="__FLT_DIG__=6"/>
<definedSymbol symbol="__FLT_MIN_EXP__=(-125)"/>
<definedSymbol symbol="__FLT_MIN_10_EXP__=(-37)"/>
<definedSymbol symbol="__FLT_MAX_EXP__=128"/>
<definedSymbol symbol="__FLT_MAX_10_EXP__=38"/>
<definedSymbol symbol="__FLT_DECIMAL_DIG__=9"/>
<definedSymbol symbol="__FLT_MAX__=3.4028234663852886e+38F"/>
<definedSymbol symbol="__FLT_MIN__=1.1754943508222875e-38F"/>
<definedSymbol symbol="__FLT_EPSILON__=1.1920928955078125e-7F"/>
<definedSymbol symbol="__FLT_DENORM_MIN__=1.4012984643248171e-45F"/>
<definedSymbol symbol="__FLT_HAS_DENORM__=1"/>
<definedSymbol symbol="__FLT_HAS_INFINITY__=1"/>
<definedSymbol symbol="__FLT_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__DBL_MANT_DIG__=53"/>
<definedSymbol symbol="__DBL_DIG__=15"/>
<definedSymbol symbol="__DBL_MIN_EXP__=(-1021)"/>
<definedSymbol symbol="__DBL_MIN_10_EXP__=(-307)"/>
<definedSymbol symbol="__DBL_MAX_EXP__=1024"/>
<definedSymbol symbol="__DBL_MAX_10_EXP__=308"/>
<definedSymbol symbol="__DBL_DECIMAL_DIG__=17"/>
<definedSymbol symbol="__DBL_MAX__=((double)1.7976931348623157e+308L)"/>
<definedSymbol symbol="__DBL_MIN__=((double)2.2250738585072014e-308L)"/>
<definedSymbol symbol="__DBL_EPSILON__=((double)2.2204460492503131e-16L)"/>
<definedSymbol symbol="__DBL_DENORM_MIN__=((double)4.9406564584124654e-324L)"/>
<definedSymbol symbol="__DBL_HAS_DENORM__=1"/>
<definedSymbol symbol="__DBL_HAS_INFINITY__=1"/>
<definedSymbol symbol="__DBL_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__LDBL_MANT_DIG__=53"/>
<definedSymbol symbol="__LDBL_DIG__=15"/>
<definedSymbol symbol="__LDBL_MIN_EXP__=(-1021)"/>
<definedSymbol symbol="__LDBL_MIN_10_EXP__=(-307)"/>
<definedSymbol symbol="__LDBL_MAX_EXP__=1024"/>
<definedSymbol symbol="__LDBL_MAX_10_EXP__=308"/>
<definedSymbol symbol="__DECIMAL_DIG__=17"/>
<definedSymbol symbol="__LDBL_MAX__=1.7976931348623157e+308L"/>
<definedSymbol symbol="__LDBL_MIN__=2.2250738585072014e-308L"/>
<definedSymbol symbol="__LDBL_EPSILON__=2.2204460492503131e-16L"/>
<definedSymbol symbol="__LDBL_DENORM_MIN__=4.9406564584124654e-324L"/>
<definedSymbol symbol="__LDBL_HAS_DENORM__=1"/>
<definedSymbol symbol="__LDBL_HAS_INFINITY__=1"/>
<definedSymbol symbol="__LDBL_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__DEC32_MANT_DIG__=7"/>
<definedSymbol symbol="__DEC32_MIN_EXP__=(-94)"/>
<definedSymbol symbol="__DEC32_MAX_EXP__=97"/>
<definedSymbol symbol="__DEC32_MIN__=1E-95DF"/>
<definedSymbol symbol="__DEC32_MAX__=9.999999E96DF"/>
<definedSymbol symbol="__DEC32_EPSILON__=1E-6DF"/>
<definedSymbol symbol="__DEC32_SUBNORMAL_MIN__=0.000001E-95DF"/>
<definedSymbol symbol="__DEC64_MANT_DIG__=16"/>
<definedSymbol symbol="__DEC64_MIN_EXP__=(-382)"/>
<definedSymbol symbol="__DEC64_MAX_EXP__=385"/>
<definedSymbol symbol="__DEC64_MIN__=1E-383DD"/>
<definedSymbol symbol="__DEC64_MAX__=9.999999999999999E384DD"/>
<definedSymbol symbol="__DEC64_EPSILON__=1E-15DD"/>
<definedSymbol symbol="__DEC64_SUBNORMAL_MIN__=0.000000000000001E-383DD"/>
<definedSymbol symbol="__DEC128_MANT_DIG__=34"/>
<definedSymbol symbol="__DEC128_MIN_EXP__=(-6142)"/>
<definedSymbol symbol="__DEC128_MAX_EXP__=6145"/>
<definedSymbol symbol="__DEC128_MIN__=1E-6143DL"/>
<definedSymbol symbol="__DEC128_MAX__=9.999999999999999999999999999999999E6144DL"/>
<definedSymbol symbol="__DEC128_EPSILON__=1E-33DL"/>
<definedSymbol symbol="__DEC128_SUBNORMAL_MIN__=0.000000000000000000000000000000001E-6143DL"/>
<definedSymbol symbol="__SFRACT_FBIT__=7"/>
<definedSymbol symbol="__SFRACT_IBIT__=0"/>
<definedSymbol symbol="__SFRACT_MIN__=(-0.5HR-0.5HR)"/>
<definedSymbol symbol="__SFRACT_MAX__=0X7FP-7HR"/>
<definedSymbol symbol="__SFRACT_EPSILON__=0x1P-7HR"/>
<definedSymbol symbol="__USFRACT_FBIT__=8"/>
<definedSymbol symbol="__USFRACT_IBIT__=0"/>
<definedSymbol symbol="__USFRACT_MIN__=0.0UHR"/>
<definedSymbol symbol="__USFRACT_MAX__=0XFFP-8UHR"/>
<definedSymbol symbol="__USFRACT_EPSILON__=0x1P-8UHR"/>
<definedSymbol symbol="__FRACT_FBIT__=15"/>
<definedSymbol symbol="__FRACT_IBIT__=0"/>
<definedSymbol symbol="__FRACT_MIN__=(-0.5R-0.5R)"/>
<definedSymbol symbol="__FRACT_MAX__=0X7FFFP-15R"/>
<definedSymbol symbol="__FRACT_EPSILON__=0x1P-15R"/>
<definedSymbol symbol="__UFRACT_FBIT__=16"/>
<definedSymbol symbol="__UFRACT_IBIT__=0"/>
<definedSymbol symbol="__UFRACT_MIN__=0.0UR"/>
<definedSymbol symbol="__UFRACT_MAX__=0XFFFFP-16UR"/>
<definedSymbol symbol="__UFRACT_EPSILON__=0x1P-16UR"/>
<definedSymbol symbol="__LFRACT_FBIT__=31"/>
<definedSymbol symbol="__LFRACT_IBIT__=0"/>
<definedSymbol symbol="__LFRACT_MIN__=(-0.5LR-0.5LR)"/>
<definedSymbol symbol="__LFRACT_MAX__=0X7FFFFFFFP-31LR"/>
<definedSymbol symbol="__LFRACT_EPSILON__=0x1P-31LR"/>
<definedSymbol symbol="__ULFRACT_FBIT__=32"/>
<definedSymbol symbol="__ULFRACT_IBIT__=0"/>
<definedSymbol symbol="__ULFRACT_MIN__=0.0ULR"/>
<definedSymbol symbol="__ULFRACT_MAX__=0XFFFFFFFFP-32ULR"/>
<definedSymbol symbol="__ULFRACT_EPSILON__=0x1P-32ULR"/>
<definedSymbol symbol="__LLFRACT_FBIT__=63"/>
<definedSymbol symbol="__LLFRACT_IBIT__=0"/>
<definedSymbol symbol="__LLFRACT_MIN__=(-0.5LLR-0.5LLR)"/>
<definedSymbol symbol="__LLFRACT_MAX__=0X7FFFFFFFFFFFFFFFP-63LLR"/>
<definedSymbol symbol="__LLFRACT_EPSILON__=0x1P-63LLR"/>
<definedSymbol symbol="__ULLFRACT_FBIT__=64"/>
<definedSymbol symbol="__ULLFRACT_IBIT__=0"/>
<definedSymbol symbol="__ULLFRACT_MIN__=0.0ULLR"/>
<definedSymbol symbol="__ULLFRACT_MAX__=0XFFFFFFFFFFFFFFFFP-64ULLR"/>
<definedSymbol symbol="__ULLFRACT_EPSILON__=0x1P-64ULLR"/>
<definedSymbol symbol="__SACCUM_FBIT__=7"/>
<definedSymbol symbol="__SACCUM_IBIT__=8"/>
<definedSymbol symbol="__SACCUM_MIN__=(-0X1P7HK-0X1P7HK)"/>
<definedSymbol symbol="__SACCUM_MAX__=0X7FFFP-7HK"/>
<definedSymbol symbol="__SACCUM_EPSILON__=0x1P-7HK"/>
<definedSymbol symbol="__USACCUM_FBIT__=8"/>
<definedSymbol symbol="__USACCUM_IBIT__=8"/>
<definedSymbol symbol="__USACCUM_MIN__=0.0UHK"/>
<definedSymbol symbol="__USACCUM_MAX__=0XFFFFP-8UHK"/>
<definedSymbol symbol="__USACCUM_EPSILON__=0x1P-8UHK"/>
<definedSymbol symbol="__ACCUM_FBIT__=15"/>
<definedSymbol symbol="__ACCUM_IBIT__=16"/>
<definedSymbol symbol="__ACCUM_MIN__=(-0X1P15K-0X1P15K)"/>
<definedSymbol symbol="__ACCUM_MAX__=0X7FFFFFFFP-15K"/>
<definedSymbol symbol="__ACCUM_EPSILON__=0x1P-15K"/>
<definedSymbol symbol="__UACCUM_FBIT__=16"/>
<definedSymbol symbol="__UACCUM_IBIT__=16"/>
<definedSymbol symbol="__UACCUM_MIN__=0.0UK"/>
<definedSymbol symbol="__UACCUM_MAX__=0XFFFFFFFFP-16UK"/>
<definedSymbol symbol="__UACCUM_EPSILON__=0x1P-16UK"/>
<definedSymbol symbol="__LACCUM_FBIT__=31"/>
<definedSymbol symbol="__LACCUM_IBIT__=32"/>
<definedSymbol symbol="__LACCUM_MIN__=(-0X1P31LK-0X1P31LK)"/>
<definedSymbol symbol="__LACCUM_MAX__=0X7FFFFFFFFFFFFFFFP-31LK"/>
<definedSymbol symbol="__LACCUM_EPSILON__=0x1P-31LK"/>
<definedSymbol symbol="__ULACCUM_FBIT__=32"/>
<definedSymbol symbol="__ULACCUM_IBIT__=32"/>
<definedSymbol symbol="__ULACCUM_MIN__=0.0ULK"/>
<definedSymbol symbol="__ULACCUM_MAX__=0XFFFFFFFFFFFFFFFFP-32ULK"/>
<definedSymbol symbol="__ULACCUM_EPSILON__=0x1P-32ULK"/>
<definedSymbol symbol="__LLACCUM_FBIT__=31"/>
<definedSymbol symbol="__LLACCUM_IBIT__=32"/>
<definedSymbol symbol="__LLACCUM_MIN__=(-0X1P31LLK-0X1P31LLK)"/>
<definedSymbol symbol="__LLACCUM_MAX__=0X7FFFFFFFFFFFFFFFP-31LLK"/>
<definedSymbol symbol="__LLACCUM_EPSILON__=0x1P-31LLK"/>
<definedSymbol symbol="__ULLACCUM_FBIT__=32"/>
<definedSymbol symbol="__ULLACCUM_IBIT__=32"/>
<definedSymbol symbol="__ULLACCUM_MIN__=0.0ULLK"/>
<definedSymbol symbol="__ULLACCUM_MAX__=0XFFFFFFFFFFFFFFFFP-32ULLK"/>
<definedSymbol symbol="__ULLACCUM_EPSILON__=0x1P-32ULLK"/>
<definedSymbol symbol="__QQ_FBIT__=7"/>
<definedSymbol symbol="__QQ_IBIT__=0"/>
<definedSymbol symbol="__HQ_FBIT__=15"/>
<definedSymbol symbol="__HQ_IBIT__=0"/>
<definedSymbol symbol="__SQ_FBIT__=31"/>
<definedSymbol symbol="__SQ_IBIT__=0"/>
<definedSymbol symbol="__DQ_FBIT__=63"/>
<definedSymbol symbol="__DQ_IBIT__=0"/>
<definedSymbol symbol="__TQ_FBIT__=127"/>
<definedSymbol symbol="__TQ_IBIT__=0"/>
<definedSymbol symbol="__UQQ_FBIT__=8"/>
<definedSymbol symbol="__UQQ_IBIT__=0"/>
<definedSymbol symbol="__UHQ_FBIT__=16"/>
<definedSymbol symbol="__UHQ_IBIT__=0"/>
<definedSymbol symbol="__USQ_FBIT__=32"/>
<definedSymbol symbol="__USQ_IBIT__=0"/>
<definedSymbol symbol="__UDQ_FBIT__=64"/>
<definedSymbol symbol="__UDQ_IBIT__=0"/>
<definedSymbol symbol="__UTQ_FBIT__=128"/>
<definedSymbol symbol="__UTQ_IBIT__=0"/>
<definedSymbol symbol="__HA_FBIT__=7"/>
<definedSymbol symbol="__HA_IBIT__=8"/>
<definedSymbol symbol="__SA_FBIT__=15"/>
<definedSymbol symbol="__SA_IBIT__=16"/>
<definedSymbol symbol="__DA_FBIT__=31"/>
<definedSymbol symbol="__DA_IBIT__=32"/>
<definedSymbol symbol="__TA_FBIT__=63"/>
<definedSymbol symbol="__TA_IBIT__=64"/>
<definedSymbol symbol="__UHA_FBIT__=8"/>
<definedSymbol symbol="__UHA_IBIT__=8"/>
<definedSymbol symbol="__USA_FBIT__=16"/>
<definedSymbol symbol="__USA_IBIT__=16"/>
<definedSymbol symbol="__UDA_FBIT__=32"/>
<definedSymbol symbol="__UDA_IBIT__=32"/>
<definedSymbol symbol="__UTA_FBIT__=64"/>
<definedSymbol symbol="__UTA_IBIT__=64"/>
<definedSymbol symbol="__REGISTER_PREFIX__="/>
<definedSymbol symbol="__USER_LABEL_PREFIX__="/>
<definedSymbol symbol="__GNUC_STDC_INLINE__=1"/>
<definedSymbol symbol="__NO_INLINE__=1"/>
<definedSymbol symbol="__CHAR_UNSIGNED__=1"/>
<definedSymbol symbol="__GCC_ATOMIC_BOOL_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR16_T_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR32_T_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_WCHAR_T_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_SHORT_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_INT_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_LONG_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_LLONG_LOCK_FREE=1"/>
<definedSymbol symbol="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1"/>
<definedSymbol symbol="__GCC_ATOMIC_POINTER_LOCK_FREE=1"/>
<definedSymbol symbol="__PRAGMA_REDEFINE_EXTNAME=1"/>
<definedSymbol symbol="__SIZEOF_WCHAR_T__=4"/>
<definedSymbol symbol="__SIZEOF_WINT_T__=4"/>
<definedSymbol symbol="__SIZEOF_PTRDIFF_T__=4"/>
<definedSymbol symbol="__ARM_32BIT_STATE=1"/>
<definedSymbol symbol="__ARM_SIZEOF_MINIMAL_ENUM=1"/>
<definedSymbol symbol="__ARM_SIZEOF_WCHAR_T=4"/>
<definedSymbol symbol="__arm__=1"/>
<definedSymbol symbol="__ARM_ARCH=4"/>
<definedSymbol symbol="__ARM_ARCH_ISA_ARM=1"/>
<definedSymbol symbol="__APCS_32__=1"/>
<definedSymbol symbol="__ARM_ARCH_ISA_THUMB=1"/>
<definedSymbol symbol="__ARMEL__=1"/>
<definedSymbol symbol="__SOFTFP__=1"/>
<definedSymbol symbol="__VFP_FP__=1"/>
<definedSymbol symbol="__THUMB_INTERWORK__=1"/>
<definedSymbol symbol="__ARM_ARCH_4T__=1"/>
<definedSymbol symbol="__ARM_PCS=1"/>
<definedSymbol symbol="__ARM_EABI__=1"/>
<definedSymbol symbol="__GXX_TYPEINFO_EQUALITY_INLINE=0"/>
<definedSymbol symbol="__ELF__=1"/>
<definedSymbol symbol="__USES_INITFINI__=1"/>
</collector>
</instance>
</scannerInfo>
