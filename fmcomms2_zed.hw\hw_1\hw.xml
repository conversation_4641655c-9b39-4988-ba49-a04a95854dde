<?xml version="1.0" encoding="UTF-8"?>
<!-- Product Version: Vivado v2016.4 (64-bit)                     -->
<!--                                                              -->
<!-- Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.        -->

<hwsession version="1" minor="2">
  <device name="xc7z035_1" gui_info="dashboard1=hw_ila_1[xc7z035_1/hw_ila_1/Trigger Setup=ILA_TRIGGER_1;xc7z035_1/hw_ila_1/Status=ILA_STATUS_1;xc7z035_1/hw_ila_1/Settings=ILA_SETTINGS_1;xc7z035_1/hw_ila_1/Capture Setup=ILA_CAPTURE_1;xc7z035_1/hw_ila_1/Waveform=ILA_WAVE_1;],dashboard2=hw_ila_2[xc7z035_1/hw_ila_2/Status=ILA_STATUS_1;xc7z035_1/hw_ila_2/Trigger Setup=ILA_TRIGGER_1;xc7z035_1/hw_ila_2/Waveform=ILA_WAVE_1;xc7z035_1/hw_ila_2/Capture Setup=ILA_CAPTURE_1;xc7z035_1/hw_ila_2/Settings=ILA_SETTINGS_1;]"/>
  <ObjectList object_type="hw_device" gui_info="">
    <Object name="xc7k325t_0" gui_info="">
      <Properties Property="PROBES.FILE" value=""/>
      <Properties Property="PROGRAM.HW_BITSTREAM" value="D:/FPGA/K7_fpga/kc705/fmcomms2_kc705.runs/impl_1/system_top.bit"/>
    </Object>
    <Object name="xc7z035_1" gui_info="">
      <Properties Property="PROBES.FILE" value="$_project_name_.runs/impl_3/debug_nets.ltx"/>
      <Properties Property="PROGRAM.HW_BITSTREAM" value="$_project_name_.runs/impl_3/system_top.bit"/>
    </Object>
    <Object name="xc7z045_1" gui_info="">
      <Properties Property="PROBES.FILE" value="C:/Users/<USER>/Desktop/hdl-2016_r12016_z7045/projects/fmcomms2/zed/$_project_name_.runs/impl_2/debug_nets.ltx"/>
      <Properties Property="PROGRAM.HW_BITSTREAM" value="C:/Users/<USER>/Desktop/hdl-2016_r12016_z7045/projects/fmcomms2/zed/$_project_name_.runs/impl_2/system_top.bit"/>
    </Object>
  </ObjectList>
  <ObjectList object_type="hw_ila" gui_info="">
    <Object name="hw_ila_1" gui_info="">
      <Properties Property="CONTROL.TRIGGER_CONDITION" value="AND"/>
      <Properties Property="CORE_REFRESH_RATE_MS" value="500"/>
    </Object>
    <Object name="hw_ila_2" gui_info="">
      <Properties Property="CONTROL.TRIGGER_CONDITION" value="AND"/>
      <Properties Property="CORE_REFRESH_RATE_MS" value="500"/>
    </Object>
  </ObjectList>
  <ObjectList object_type="hw_probe" gui_info="">
    <Object name="BK_CS1_OBUF" gui_info="hw_vios/hw_vio_1=4,dashboard_1/hw_vio_1=3"/>
    <Object name="BK_IRQ_OBUF" gui_info="hw_vios/hw_vio_1=9,dashboard_1/hw_vio_1=4"/>
    <Object name="BK_LOAD_OBUF" gui_info="hw_vios/hw_vio_1=10,dashboard_1/hw_vio_1=5"/>
    <Object name="BK_MISO1_OBUF" gui_info="hw_vios/hw_vio_1=3,dashboard_1/hw_vio_1=6"/>
    <Object name="BK_MOSI1_OBUF" gui_info="hw_vios/hw_vio_1=11,dashboard_1/hw_vio_1=7"/>
    <Object name="BK_RST_OBUF" gui_info="hw_vios/hw_vio_1=12,dashboard_1/hw_vio_1=8"/>
    <Object name="BK_SCLK1_OBUF" gui_info="hw_vios/hw_vio_1=13,dashboard_1/hw_vio_1=21"/>
    <Object name="BK_TR_SW_OBUF" gui_info="hw_vios/hw_vio_1=14,dashboard_1/hw_vio_1=11"/>
    <Object name="IO1_OBUF" gui_info="hw_vios/hw_vio_1=15,dashboard_1/hw_vio_1=9"/>
    <Object name="IO2_OBUF" gui_info="hw_vios/hw_vio_1=16,dashboard_1/hw_vio_1=20"/>
    <Object name="PL_CTRL_A_OBUF" gui_info="hw_vios/hw_vio_1=17,dashboard_1/hw_vio_1=22"/>
    <Object name="PL_CTRL_B_OBUF" gui_info="hw_vios/hw_vio_1=18,dashboard_1/hw_vio_1=23"/>
    <Object name="PL_UART1_RX_IBUF" gui_info="hw_vios/hw_vio_1=7,dashboard_1/hw_vio_1=10"/>
    <Object name="PL_UART1_TX_OBUF" gui_info="hw_vios/hw_vio_1=19,dashboard_1/hw_vio_1=24"/>
    <Object name="PL_UART2_RX_IBUF" gui_info="hw_vios/hw_vio_1=8,dashboard_1/hw_vio_1=0"/>
    <Object name="PL_UART2_TX_OBUF" gui_info="hw_vios/hw_vio_1=20,dashboard_1/hw_vio_1=25"/>
    <Object name="PL_UART4_RX_IBUF" gui_info="hw_vios/hw_vio_1=5,dashboard_1/hw_vio_1=1"/>
    <Object name="PL_UART4_TX_OBUF" gui_info="hw_vios/hw_vio_1=21,dashboard_1/hw_vio_1=26"/>
    <Object name="PL_UART_RX_IBUF" gui_info="hw_vios/hw_vio_1=6,dashboard_1/hw_vio_1=2"/>
    <Object name="PL_UART_TX_OBUF" gui_info="dashboard_1/hw_vio_1=27"/>
    <Object name="TSELF_OBUF" gui_info="hw_vios/hw_vio_1=22,dashboard_1/hw_vio_1=12"/>
    <Object name="XD_HX_R_OBUF" gui_info="hw_vios/hw_vio_1=23,dashboard_1/hw_vio_1=13"/>
    <Object name="XD_R_B_OBUF" gui_info="hw_vios/hw_vio_1=1,dashboard_1/hw_vio_1=14"/>
    <Object name="XD_T_P1_OBUF" gui_info="hw_vios/hw_vio_1=25,dashboard_1/hw_vio_1=15"/>
    <Object name="XD_T_P_OBUF" gui_info="hw_vios/hw_vio_1=27,dashboard_1/hw_vio_1=16"/>
    <Object name="XD_T_R_OBUF" gui_info="hw_vios/hw_vio_1=0,dashboard_1/hw_vio_1=17"/>
    <Object name="XD_XS_OBUF" gui_info="hw_vios/hw_vio_1=2,dashboard_1/hw_vio_1=18"/>
    <Object name="s_test[6:1]" gui_info="hw_vios/hw_vio_1=24"/>
    <Object name="test_OBUF[6:1]" gui_info="dashboard_1/hw_vio_1=19"/>
    <Object name="test_vio_0/probe_out1[0:0]" gui_info="hw_vios/hw_vio_1=26"/>
  </ObjectList>
  <probeset name="hw project" active="false">
    <probe type="vio_input" busType="net" source="netlist" spec="VIO_INPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="ACTIVITY_PERSISTENCE" value="SHORT"/>
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="INPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="PL_UART1_RX_IBUF"/>
      </nets>
    </probe>
    <probe type="vio_input" busType="net" source="netlist" spec="VIO_INPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="ACTIVITY_PERSISTENCE" value="SHORT"/>
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="INPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="PL_UART2_RX_IBUF"/>
      </nets>
    </probe>
    <probe type="vio_input" busType="net" source="netlist" spec="VIO_INPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="ACTIVITY_PERSISTENCE" value="SHORT"/>
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="INPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="PL_UART4_RX_IBUF"/>
      </nets>
    </probe>
    <probe type="vio_input" busType="net" source="netlist" spec="VIO_INPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="ACTIVITY_PERSISTENCE" value="SHORT"/>
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="INPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="PL_UART_RX_IBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="BK_CS1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="BK_IRQ_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="BK_LOAD_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="BK_MISO1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="BK_MOSI1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="BK_RST_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="BK_SCLK1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="BK_TR_SW_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="1"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="IO1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="1"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="IO2_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="PL_CTRL_A_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="1"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="PL_CTRL_B_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="PL_UART1_TX_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="PL_UART2_TX_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="PL_UART4_TX_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="PL_UART_TX_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="TSELF_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="XD_HX_R_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="XD_R_B_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="XD_T_P1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="XD_T_P_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="XD_T_R_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="XD_XS_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="00"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="HEX"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="s_test[6]"/>
        <net name="s_test[5]"/>
        <net name="s_test[4]"/>
        <net name="s_test[3]"/>
        <net name="s_test[2]"/>
        <net name="s_test[1]"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="bus" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="00"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="HEX"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="test_OBUF[6]"/>
        <net name="test_OBUF[5]"/>
        <net name="test_OBUF[4]"/>
        <net name="test_OBUF[3]"/>
        <net name="test_OBUF[2]"/>
        <net name="test_OBUF[1]"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="DISPLAY_HINT" value=""/>
        <Option Id="DISPLAY_VISIBILITY" value=""/>
        <Option Id="HW_VIO" value="hw_vio_1"/>
        <Option Id="NAME.CUSTOM" value=""/>
        <Option Id="NAME.SELECT" value="Long"/>
        <Option Id="OUTPUT_VALUE" value="0"/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="SOURCE" value="netlist"/>
      </probeOptions>
      <nets>
        <net name="test_vio_0/probe_out1[0]"/>
      </nets>
    </probe>
  </probeset>
</hwsession>
