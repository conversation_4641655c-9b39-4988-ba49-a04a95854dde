#-----------------------------------------------------------
# Webtalk v2016.4 (64-bit)
# SW Build 1756540 on Mon Jan 23 19:11:23 MST 2017
# 
# Start of session at: Mon May 11 17:48:51 2020
# Process ID: 4000
# Current directory: D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/webtalk
# Command line: wbtcv.exe -mode batch -source sdk_webtalk.tcl
# Log file: D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/webtalk/webtalk.log
# Journal file: D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/webtalk\webtalk.jou
#-----------------------------------------------------------
source sdk_webtalk.tcl
# webtalk_init -webtalk_dir D:\\xiaoE\\R75_Z7035_0320\\fmcomms2_zed.sdk\\webtalk
# webtalk_register_client -client project
# webtalk_add_data -client project -key date_generated -value "2020-05-11 17:48:50" -context "software_version_and_target_device"
# webtalk_add_data -client project -key product_version -value "SDK v2016.4" -context "software_version_and_target_device"
# webtalk_add_data -client project -key build_version -value "2016.4" -context "software_version_and_target_device"
# webtalk_add_data -client project -key os_platform -value "amd64" -context "software_version_and_target_device"
# webtalk_add_data -client project -key registration_id -value "" -context "software_version_and_target_device"
# webtalk_add_data -client project -key tool_flow -value "SDK" -context "software_version_and_target_device"
# webtalk_add_data -client project -key beta -value "false" -context "software_version_and_target_device"
# webtalk_add_data -client project -key route_design -value "NA" -context "software_version_and_target_device"
# webtalk_add_data -client project -key target_family -value "NA" -context "software_version_and_target_device"
# webtalk_add_data -client project -key target_device -value "NA" -context "software_version_and_target_device"
# webtalk_add_data -client project -key target_package -value "NA" -context "software_version_and_target_device"
# webtalk_add_data -client project -key target_speed -value "NA" -context "software_version_and_target_device"
# webtalk_add_data -client project -key random_id -value "tsbh39hee9u30a2l54b63sntvj" -context "software_version_and_target_device"
# webtalk_add_data -client project -key project_id -value "2016.4_23" -context "software_version_and_target_device"
# webtalk_add_data -client project -key project_iteration -value "23" -context "software_version_and_target_device"
# webtalk_add_data -client project -key os_name -value "Microsoft Windows 8 or later , 64-bit" -context "user_environment"
# webtalk_add_data -client project -key os_release -value "major release  (build 9200)" -context "user_environment"
# webtalk_add_data -client project -key cpu_name -value "Intel(R) Core(TM) i7-6700 CPU @ 3.40GHz" -context "user_environment"
# webtalk_add_data -client project -key cpu_speed -value "3408 MHz" -context "user_environment"
# webtalk_add_data -client project -key total_processors -value "1" -context "user_environment"
# webtalk_add_data -client project -key system_ram -value "17.000 GB" -context "user_environment"
# webtalk_register_client -client sdk
# webtalk_add_data -client sdk -key uid -value "1589186846291" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key isZynq -value "true" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key isZynqMP -value "false" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key Processors -value "2" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key VivadoVersion -value "2016.4" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key Arch -value "zynq" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key Device -value "7z035" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key IsHandoff -value "true" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key os -value "NA" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key apptemplate -value "NA" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key RecordType -value "HWCreation" -context "sdk\\\\hardware/1589186846291"
# webtalk_add_data -client sdk -key uid -value "NA" -context "sdk\\\\bsp/1589190530338"
# webtalk_add_data -client sdk -key RecordType -value "ToolUsage" -context "sdk\\\\bsp/1589190530338"
# webtalk_add_data -client sdk -key BootgenCount -value "0" -context "sdk\\\\bsp/1589190530338"
# webtalk_add_data -client sdk -key DebugCount -value "0" -context "sdk\\\\bsp/1589190530338"
# webtalk_add_data -client sdk -key PerfCount -value "0" -context "sdk\\\\bsp/1589190530338"
# webtalk_add_data -client sdk -key FlashCount -value "0" -context "sdk\\\\bsp/1589190530338"
# webtalk_add_data -client sdk -key CrossTriggCount -value "0" -context "sdk\\\\bsp/1589190530338"
# webtalk_add_data -client sdk -key QemuDebugCount -value "0" -context "sdk\\\\bsp/1589190530338"
# webtalk_transmit -clientid 2136086442 -regid "" -xml D:\\xiaoE\\R75_Z7035_0320\\fmcomms2_zed.sdk\\webtalk\\usage_statistics_ext_sdk.xml -html D:\\xiaoE\\R75_Z7035_0320\\fmcomms2_zed.sdk\\webtalk\\usage_statistics_ext_sdk.html -wdm D:\\xiaoE\\R75_Z7035_0320\\fmcomms2_zed.sdk\\webtalk\\sdk_webtalk.wdm -intro "<H3>SDK Usage Report</H3><BR>"
# webtalk_terminate
INFO: [Common 17-206] Exiting Webtalk at Mon May 11 17:49:01 2020...
