<?xml version="1.0" encoding="UTF-8"?>
<!-- Product Version: Vivado v2018.3 (64-bit)              -->
<!--                                                         -->
<!-- Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.   -->

<Project Version="7" Minor="39" Path="D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/fmcomms2_zed.xpr">
  <DefaultLaunch Dir="$PRUNDIR"/>
  <Configuration>
    <Option Name="Id" Val="d99595073c624624ae85d6bca6a02b61"/>
    <Option Name="Part" Val="xc7z035ffg676-2"/>
    <Option Name="CompiledLibDir" Val="$PCACHEDIR/compile_simlib"/>
    <Option Name="CompiledLibDirXSim" Val=""/>
    <Option Name="CompiledLibDirModelSim" Val="$PCACHEDIR/compile_simlib/modelsim"/>
    <Option Name="CompiledLibDirQuesta" Val="$PCACHEDIR/compile_simlib/questa"/>
    <Option Name="CompiledLibDirIES" Val="$PCACHEDIR/compile_simlib/ies"/>
    <Option Name="CompiledLibDirXcelium" Val="$PCACHEDIR/compile_simlib/xcelium"/>
    <Option Name="CompiledLibDirVCS" Val="$PCACHEDIR/compile_simlib/vcs"/>
    <Option Name="CompiledLibDirRiviera" Val="$PCACHEDIR/compile_simlib/riviera"/>
    <Option Name="CompiledLibDirActivehdl" Val="$PCACHEDIR/compile_simlib/activehdl"/>
    <Option Name="BoardPart" Val=""/>
    <Option Name="ActiveSimSet" Val="sim_1"/>
    <Option Name="DefaultLib" Val="xil_defaultlib"/>
    <Option Name="ProjectType" Val="Default"/>
    <Option Name="IPRepoPath" Val="$PPRDIR/../../hdl-hdl_2017_r1/hdl-hdl_2017_r1/library"/>
    <Option Name="IPOutputRepo" Val="$PCACHEDIR/ip"/>
    <Option Name="IPCachePermission" Val="read"/>
    <Option Name="IPCachePermission" Val="write"/>
    <Option Name="EnableCoreContainer" Val="FALSE"/>
    <Option Name="CreateRefXciForCoreContainers" Val="FALSE"/>
    <Option Name="IPUserFilesDir" Val="$PIPUSERFILESDIR"/>
    <Option Name="IPStaticSourceDir" Val="$PIPUSERFILESDIR/ipstatic"/>
    <Option Name="EnableBDX" Val="FALSE"/>
    <Option Name="DSAVendor" Val="xilinx"/>
    <Option Name="DSABoardId" Val="zed"/>
    <Option Name="DSANumComputeUnits" Val="16"/>
    <Option Name="WTXSimLaunchSim" Val="0"/>
    <Option Name="WTModelSimLaunchSim" Val="0"/>
    <Option Name="WTQuestaLaunchSim" Val="0"/>
    <Option Name="WTIesLaunchSim" Val="0"/>
    <Option Name="WTVcsLaunchSim" Val="0"/>
    <Option Name="WTRivieraLaunchSim" Val="0"/>
    <Option Name="WTActivehdlLaunchSim" Val="0"/>
    <Option Name="WTXSimExportSim" Val="3"/>
    <Option Name="WTModelSimExportSim" Val="3"/>
    <Option Name="WTQuestaExportSim" Val="3"/>
    <Option Name="WTIesExportSim" Val="3"/>
    <Option Name="WTVcsExportSim" Val="3"/>
    <Option Name="WTRivieraExportSim" Val="3"/>
    <Option Name="WTActivehdlExportSim" Val="3"/>
    <Option Name="GenerateIPUpgradeLog" Val="TRUE"/>
    <Option Name="XSimRadix" Val="hex"/>
    <Option Name="XSimTimeUnit" Val="ns"/>
    <Option Name="XSimArrayDisplayLimit" Val="64"/>
    <Option Name="XSimTraceLimit" Val="65536"/>
    <Option Name="MEMEnableMemoryMapGeneration" Val="TRUE"/>
  </Configuration>
  <FileSets Version="1" Minor="31">
    <FileSet Name="sources_1" Type="DesignSrcs" RelSrcDir="$PSRCDIR/sources_1">
      <Filter Type="Srcs"/>
      <File Path="$PSRCDIR/sources_1/bd/system/system.bd">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PSRCDIR/sources_1/ip/vio_0/vio_0.xci">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/library/xilinx/common/ad_iobuf.v">
        <FileInfo>
          <Attr Name="ImportPath" Val="$PPRDIR/../../hdl-hdl_2017_r1/hdl-hdl_2017_r1/library/xilinx/common/ad_iobuf.v"/>
          <Attr Name="ImportTime" Val="1518442275"/>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PSRCDIR/sources_1/imports/hdl/system_wrapper.v">
        <FileInfo>
          <Attr Name="ImportPath" Val="$PSRCDIR/sources_1/bd/system/hdl/system_wrapper.v"/>
          <Attr Name="ImportTime" Val="1584428448"/>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_top.v">
        <FileInfo>
          <Attr Name="ImportPath" Val="$PPRDIR/system_top.v"/>
          <Attr Name="ImportTime" Val="1572310677"/>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc">
        <FileInfo>
          <Attr Name="ImportPath" Val="$PPRDIR/system_constr.xdc"/>
          <Attr Name="ImportTime" Val="1572310673"/>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
        </FileInfo>
      </File>
      <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc">
        <FileInfo>
          <Attr Name="ImportPath" Val="$PPRDIR/../../hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc"/>
          <Attr Name="ImportTime" Val="1572255971"/>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
        </FileInfo>
      </File>
      <Config>
        <Option Name="DesignMode" Val="RTL"/>
        <Option Name="TopModule" Val="system_top"/>
      </Config>
    </FileSet>
    <FileSet Name="constrs_1" Type="Constrs" RelSrcDir="$PSRCDIR/constrs_1">
      <Filter Type="Constrs"/>
      <File Path="$PSRCDIR/constrs_1/new/cdc.xdc">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
        </FileInfo>
      </File>
      <Config>
        <Option Name="TargetConstrsFile" Val="$PSRCDIR/constrs_1/new/cdc.xdc"/>
        <Option Name="ConstrsType" Val="XDC"/>
      </Config>
    </FileSet>
    <FileSet Name="sim_1" Type="SimulationSrcs" RelSrcDir="$PSRCDIR/sim_1">
      <Filter Type="Srcs"/>
      <Config>
        <Option Name="DesignMode" Val="RTL"/>
        <Option Name="TopModule" Val="system_top"/>
        <Option Name="TopLib" Val="xil_defaultlib"/>
        <Option Name="TopAutoSet" Val="TRUE"/>
        <Option Name="TransportPathDelay" Val="0"/>
        <Option Name="TransportIntDelay" Val="0"/>
        <Option Name="SrcSet" Val="sources_1"/>
      </Config>
    </FileSet>
    <FileSet Name="utils_1" Type="Utils" RelSrcDir="$PSRCDIR/utils_1">
      <Filter Type="Utils"/>
      <Config>
        <Option Name="TopAutoSet" Val="TRUE"/>
      </Config>
    </FileSet>
  </FileSets>
  <Simulators>
    <Simulator Name="XSim">
      <Option Name="Description" Val="Vivado Simulator"/>
      <Option Name="CompiledLib" Val="0"/>
    </Simulator>
    <Simulator Name="ModelSim">
      <Option Name="Description" Val="ModelSim Simulator"/>
    </Simulator>
    <Simulator Name="Questa">
      <Option Name="Description" Val="Questa Advanced Simulator"/>
    </Simulator>
    <Simulator Name="Riviera">
      <Option Name="Description" Val="Riviera-PRO Simulator"/>
    </Simulator>
    <Simulator Name="ActiveHDL">
      <Option Name="Description" Val="Active-HDL Simulator"/>
    </Simulator>
  </Simulators>
  <Runs Version="1" Minor="10">
    <Run Id="synth_1" Type="Ft3:Synth" SrcSet="sources_1" Part="xc7z020clg484-1" ConstrsSet="constrs_1" Description="Higher performance designs, resource sharing is turned off, the global fanout guide is set to a lower number, FSM extraction forced to one-hot, LUT combining is disabled, equivalent registers are preserved, SRL are inferred  with a larger threshold" AutoIncrementalCheckpoint="false" WriteIncrSynthDcp="false" IncludeInArchive="true">
      <Strategy Version="1" Minor="2">
        <StratHandle Name="Flow_PerfOptimized_high" Flow="Vivado Synthesis 2016"/>
        <Step Id="synth_design">
          <Option Id="ResourceSharing">2</Option>
          <Option Id="ShregMinSize">5</Option>
          <Option Id="KeepEquivalentRegisters">1</Option>
          <Option Id="RepFanoutThreshold">400</Option>
          <Option Id="NoCombineLuts">1</Option>
          <Option Id="FsmExtraction">1</Option>
        </Step>
      </Strategy>
      <ReportStrategy Name="Vivado Synthesis Default Reports" Flow="Vivado Synthesis 2017"/>
      <Report Name="ROUTE_DESIGN.REPORT_METHODOLOGY" Enabled="1"/>
    </Run>
    <Run Id="synth_2" Type="Ft3:Synth" SrcSet="sources_1" Part="xc7z045ffg900-2" ConstrsSet="constrs_1" Description="Higher performance designs, resource sharing is turned off, the global fanout guide is set to a lower number, FSM extraction forced to one-hot, LUT combining is disabled, equivalent registers are preserved, SRL are inferred  with a larger threshold" AutoIncrementalCheckpoint="false" WriteIncrSynthDcp="false" Dir="$PRUNDIR/synth_2" IncludeInArchive="true">
      <Strategy Version="1" Minor="2">
        <StratHandle Name="Flow_PerfOptimized_high" Flow="Vivado Synthesis 2016"/>
        <Step Id="synth_design">
          <Option Id="ResourceSharing">2</Option>
          <Option Id="ShregMinSize">5</Option>
          <Option Id="KeepEquivalentRegisters">1</Option>
          <Option Id="RepFanoutThreshold">400</Option>
          <Option Id="NoCombineLuts">1</Option>
          <Option Id="FsmExtraction">1</Option>
        </Step>
      </Strategy>
      <GeneratedRun Dir="$PRUNDIR" File="gen_run.xml"/>
      <ReportStrategy Name="Vivado Synthesis Default Reports" Flow="Vivado Synthesis 2017"/>
      <Report Name="ROUTE_DESIGN.REPORT_METHODOLOGY" Enabled="1"/>
    </Run>
    <Run Id="synth_3" Type="Ft3:Synth" SrcSet="sources_1" Part="xc7z035ffg676-2" ConstrsSet="constrs_1" Description="Higher performance designs, resource sharing is turned off, the global fanout guide is set to a lower number, FSM extraction forced to one-hot, LUT combining is disabled, equivalent registers are preserved, SRL are inferred  with a larger threshold" AutoIncrementalCheckpoint="false" WriteIncrSynthDcp="false" State="current" Dir="$PRUNDIR/synth_3" IncludeInArchive="true">
      <Strategy Version="1" Minor="2">
        <StratHandle Name="Flow_PerfOptimized_high" Flow="Vivado Synthesis 2016"/>
        <Step Id="synth_design">
          <Option Id="ResourceSharing">2</Option>
          <Option Id="ShregMinSize">5</Option>
          <Option Id="KeepEquivalentRegisters">1</Option>
          <Option Id="RepFanoutThreshold">400</Option>
          <Option Id="NoCombineLuts">1</Option>
          <Option Id="FsmExtraction">1</Option>
        </Step>
      </Strategy>
      <GeneratedRun Dir="$PRUNDIR" File="gen_run.xml"/>
      <ReportStrategy Name="Vivado Synthesis Default Reports" Flow="Vivado Synthesis 2017"/>
      <Report Name="ROUTE_DESIGN.REPORT_METHODOLOGY" Enabled="1"/>
    </Run>
    <Run Id="impl_1" Type="Ft2:EntireDesign" Part="xc7z020clg484-1" ConstrsSet="constrs_1" Description="Includes alternate algorithms for timing-driven optimization" AutoIncrementalCheckpoint="false" WriteIncrSynthDcp="false" SynthRun="synth_1" IncludeInArchive="true" GenFullBitstream="true">
      <Strategy Version="1" Minor="2">
        <StratHandle Name="Performance_ExtraTimingOpt" Flow="Vivado Implementation 2016"/>
        <Step Id="init_design"/>
        <Step Id="opt_design"/>
        <Step Id="power_opt_design"/>
        <Step Id="place_design">
          <Option Id="Directive">8</Option>
        </Step>
        <Step Id="post_place_power_opt_design"/>
        <Step Id="phys_opt_design" EnableStepBool="1">
          <Option Id="Directive">0</Option>
        </Step>
        <Step Id="route_design">
          <Option Id="Directive">0</Option>
        </Step>
        <Step Id="post_route_phys_opt_design"/>
        <Step Id="write_bitstream"/>
      </Strategy>
      <ReportStrategy Name="Vivado Implementation Default Reports" Flow="Vivado Implementation 2017"/>
      <Report Name="ROUTE_DESIGN.REPORT_METHODOLOGY" Enabled="1"/>
    </Run>
    <Run Id="impl_2" Type="Ft2:EntireDesign" Part="xc7z045ffg900-2" ConstrsSet="constrs_1" Description="Default settings for Implementation." AutoIncrementalCheckpoint="false" WriteIncrSynthDcp="false" Dir="$PRUNDIR/impl_2" SynthRun="synth_2" IncludeInArchive="true" GenFullBitstream="true">
      <Strategy Version="1" Minor="2">
        <StratHandle Name="Vivado Implementation Defaults" Flow="Vivado Implementation 2016"/>
        <Step Id="init_design"/>
        <Step Id="opt_design"/>
        <Step Id="power_opt_design"/>
        <Step Id="place_design"/>
        <Step Id="post_place_power_opt_design"/>
        <Step Id="phys_opt_design"/>
        <Step Id="route_design"/>
        <Step Id="post_route_phys_opt_design"/>
        <Step Id="write_bitstream"/>
      </Strategy>
      <GeneratedRun Dir="$PRUNDIR" File="gen_run.xml"/>
      <ReportStrategy Name="Vivado Implementation Default Reports" Flow="Vivado Implementation 2017"/>
      <Report Name="ROUTE_DESIGN.REPORT_METHODOLOGY" Enabled="1"/>
    </Run>
    <Run Id="impl_3" Type="Ft2:EntireDesign" Part="xc7z035ffg676-2" ConstrsSet="constrs_1" Description="Default settings for Implementation." AutoIncrementalCheckpoint="false" WriteIncrSynthDcp="false" State="current" Dir="$PRUNDIR/impl_3" SynthRun="synth_3" IncludeInArchive="true" GenFullBitstream="true">
      <Strategy Version="1" Minor="2">
        <StratHandle Name="Vivado Implementation Defaults" Flow="Vivado Implementation 2016"/>
        <Step Id="init_design"/>
        <Step Id="opt_design"/>
        <Step Id="power_opt_design"/>
        <Step Id="place_design"/>
        <Step Id="post_place_power_opt_design"/>
        <Step Id="phys_opt_design"/>
        <Step Id="route_design"/>
        <Step Id="post_route_phys_opt_design"/>
        <Step Id="write_bitstream"/>
      </Strategy>
      <GeneratedRun Dir="$PRUNDIR" File="gen_run.xml"/>
      <ReportStrategy Name="Vivado Implementation Default Reports" Flow="Vivado Implementation 2017"/>
      <Report Name="ROUTE_DESIGN.REPORT_METHODOLOGY" Enabled="1"/>
    </Run>
  </Runs>
  <MsgRule>
    <MsgAttr Name="RuleType" Val="1"/>
    <MsgAttr Name="Limit" Val="-1"/>
    <MsgAttr Name="NewSeverity" Val="INFO"/>
    <MsgAttr Name="Id" Val="BD 41-1348"/>
    <MsgAttr Name="Severity" Val="ANY"/>
    <MsgAttr Name="ShowRule" Val="1"/>
    <MsgAttr Name="RuleSource" Val="2"/>
    <MsgAttr Name="StringIsRegExp" Val="0"/>
    <MsgAttr Name="RuleId" Val="271"/>
    <MsgAttr Name="Note" Val=""/>
    <MsgAttr Name="Author" Val=""/>
    <MsgAttr Name="CreatedTimestamp" Val=""/>
  </MsgRule>
  <MsgRule>
    <MsgAttr Name="RuleType" Val="1"/>
    <MsgAttr Name="Limit" Val="-1"/>
    <MsgAttr Name="NewSeverity" Val="INFO"/>
    <MsgAttr Name="Id" Val="BD 41-1343"/>
    <MsgAttr Name="Severity" Val="ANY"/>
    <MsgAttr Name="ShowRule" Val="1"/>
    <MsgAttr Name="RuleSource" Val="2"/>
    <MsgAttr Name="StringIsRegExp" Val="0"/>
    <MsgAttr Name="RuleId" Val="272"/>
    <MsgAttr Name="Note" Val=""/>
    <MsgAttr Name="Author" Val=""/>
    <MsgAttr Name="CreatedTimestamp" Val=""/>
  </MsgRule>
  <MsgRule>
    <MsgAttr Name="RuleType" Val="1"/>
    <MsgAttr Name="Limit" Val="-1"/>
    <MsgAttr Name="NewSeverity" Val="INFO"/>
    <MsgAttr Name="Id" Val="BD 41-1306"/>
    <MsgAttr Name="Severity" Val="ANY"/>
    <MsgAttr Name="ShowRule" Val="1"/>
    <MsgAttr Name="RuleSource" Val="2"/>
    <MsgAttr Name="StringIsRegExp" Val="0"/>
    <MsgAttr Name="RuleId" Val="273"/>
    <MsgAttr Name="Note" Val=""/>
    <MsgAttr Name="Author" Val=""/>
    <MsgAttr Name="CreatedTimestamp" Val=""/>
  </MsgRule>
  <MsgRule>
    <MsgAttr Name="RuleType" Val="1"/>
    <MsgAttr Name="Limit" Val="-1"/>
    <MsgAttr Name="NewSeverity" Val="INFO"/>
    <MsgAttr Name="Id" Val="IP_Flow 19-1687"/>
    <MsgAttr Name="Severity" Val="ANY"/>
    <MsgAttr Name="ShowRule" Val="1"/>
    <MsgAttr Name="RuleSource" Val="2"/>
    <MsgAttr Name="StringIsRegExp" Val="0"/>
    <MsgAttr Name="RuleId" Val="274"/>
    <MsgAttr Name="Note" Val=""/>
    <MsgAttr Name="Author" Val=""/>
    <MsgAttr Name="CreatedTimestamp" Val=""/>
  </MsgRule>
  <MsgRule>
    <MsgAttr Name="RuleType" Val="1"/>
    <MsgAttr Name="Limit" Val="-1"/>
    <MsgAttr Name="NewSeverity" Val="INFO"/>
    <MsgAttr Name="Id" Val="filemgmt 20-1763"/>
    <MsgAttr Name="Severity" Val="ANY"/>
    <MsgAttr Name="ShowRule" Val="1"/>
    <MsgAttr Name="RuleSource" Val="2"/>
    <MsgAttr Name="StringIsRegExp" Val="0"/>
    <MsgAttr Name="RuleId" Val="275"/>
    <MsgAttr Name="Note" Val=""/>
    <MsgAttr Name="Author" Val=""/>
    <MsgAttr Name="CreatedTimestamp" Val=""/>
  </MsgRule>
  <MsgRule>
    <MsgAttr Name="RuleType" Val="1"/>
    <MsgAttr Name="Limit" Val="-1"/>
    <MsgAttr Name="NewSeverity" Val="ERROR"/>
    <MsgAttr Name="Id" Val="BD 41-1276"/>
    <MsgAttr Name="Severity" Val="CRITICAL WARNING"/>
    <MsgAttr Name="ShowRule" Val="1"/>
    <MsgAttr Name="RuleSource" Val="2"/>
    <MsgAttr Name="StringIsRegExp" Val="0"/>
    <MsgAttr Name="RuleId" Val="276"/>
    <MsgAttr Name="Note" Val=""/>
    <MsgAttr Name="Author" Val=""/>
    <MsgAttr Name="CreatedTimestamp" Val=""/>
  </MsgRule>
  <Board/>
  <DashboardSummary Version="1" Minor="0">
    <Dashboards>
      <Dashboard Name="default_dashboard">
        <Gadgets>
          <Gadget Name="drc_1" Type="drc" Version="1" Row="2" Column="0">
            <GadgetParam Name="REPORTS" Type="string_list" Value="impl_3#impl_3_route_report_drc_0 "/>
          </Gadget>
          <Gadget Name="methodology_1" Type="methodology" Version="1" Row="2" Column="1">
            <GadgetParam Name="REPORTS" Type="string_list" Value="impl_3#impl_3_route_report_methodology_0 "/>
          </Gadget>
          <Gadget Name="power_1" Type="power" Version="1" Row="1" Column="0">
            <GadgetParam Name="REPORTS" Type="string_list" Value="impl_3#impl_3_route_report_power_0 "/>
          </Gadget>
          <Gadget Name="timing_1" Type="timing" Version="1" Row="0" Column="1">
            <GadgetParam Name="REPORTS" Type="string_list" Value="impl_3#impl_3_route_report_timing_summary_0 "/>
          </Gadget>
          <Gadget Name="utilization_1" Type="utilization" Version="1" Row="0" Column="0">
            <GadgetParam Name="REPORTS" Type="string_list" Value="synth_3#synth_3_synth_report_utilization_0 "/>
            <GadgetParam Name="RUN.STEP" Type="string" Value="synth_design"/>
            <GadgetParam Name="RUN.TYPE" Type="string" Value="synthesis"/>
          </Gadget>
          <Gadget Name="utilization_2" Type="utilization" Version="1" Row="1" Column="1">
            <GadgetParam Name="REPORTS" Type="string_list" Value="impl_3#impl_3_place_report_utilization_0 "/>
          </Gadget>
        </Gadgets>
      </Dashboard>
      <CurrentDashboard>default_dashboard</CurrentDashboard>
    </Dashboards>
  </DashboardSummary>
  <BootPmcSettings Version="1" Minor="0">
    <Parameters/>
  </BootPmcSettings>
</Project>
