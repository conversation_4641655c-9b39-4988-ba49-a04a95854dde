set_property IOSTANDARD LVCMOS33 [get_ports {test[6]}]
set_property IOSTANDARD LVCMOS33 [get_ports {test[5]}]
set_property IOSTANDARD LVCMOS33 [get_ports {test[4]}]
set_property IOSTANDARD LVCMOS33 [get_ports {test[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {test[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports BK_CS1]
set_property IOSTANDARD LVCMOS33 [get_ports BK_IRQ]
set_property IOSTANDARD LVCMOS33 [get_ports BK_LOAD]
set_property IOSTANDARD LVCMOS33 [get_ports BK_MISO1]
set_property IOSTANDARD LVCMOS33 [get_ports BK_MOSI1]
set_property IOSTANDARD LVCMOS33 [get_ports BK_RST]
set_property IOSTANDARD LVCMOS33 [get_ports BK_SCLK1]
set_property IOSTANDARD LVCMOS33 [get_ports BK_TR_SW]
set_property IOSTANDARD LVCMOS33 [get_ports IO1]
set_property IOSTANDARD LVCMOS33 [get_ports IO2]
set_property IOSTANDARD LVCMOS33 [get_ports PL_CTRL_A]
set_property IOSTANDARD LVCMOS33 [get_ports PL_CTRL_B]
set_property IOSTANDARD LVCMOS33 [get_ports PL_QSPI_CCLK]
set_property IOSTANDARD LVCMOS33 [get_ports PL_QSPI_CS_N]
set_property IOSTANDARD LVCMOS33 [get_ports PL_UART1_RX]
set_property IOSTANDARD LVCMOS33 [get_ports PL_QSPI_IO0]
set_property IOSTANDARD LVCMOS33 [get_ports PL_QSPI_IO1]
set_property IOSTANDARD LVCMOS33 [get_ports PL_QSPI_IO2]
set_property IOSTANDARD LVCMOS33 [get_ports PL_QSPI_IO3]
set_property IOSTANDARD LVCMOS33 [get_ports PL_UART1_TX]
set_property IOSTANDARD LVCMOS33 [get_ports PL_UART2_RX]
set_property IOSTANDARD LVCMOS33 [get_ports PL_UART2_TX]
set_property IOSTANDARD LVCMOS33 [get_ports PL_UART4_RX]
set_property IOSTANDARD LVCMOS33 [get_ports PL_UART4_TX]
set_property IOSTANDARD LVCMOS33 [get_ports PL_UART_RX]
set_property IOSTANDARD LVCMOS33 [get_ports PL_UART_TX]
set_property IOSTANDARD LVCMOS33 [get_ports TSELF]
set_property IOSTANDARD LVCMOS33 [get_ports XD_HX_R]
set_property IOSTANDARD LVCMOS33 [get_ports XD_R_B]
set_property IOSTANDARD LVCMOS33 [get_ports XD_T_P]
set_property IOSTANDARD LVCMOS33 [get_ports XD_T_P1]
set_property IOSTANDARD LVCMOS33 [get_ports XD_T_R]
set_property IOSTANDARD LVCMOS33 [get_ports XD_XS]
set_property PACKAGE_PIN AE25 [get_ports BK_CS1]
set_property PACKAGE_PIN AE23 [get_ports BK_IRQ]
set_property PACKAGE_PIN AF24 [get_ports BK_LOAD]
set_property PACKAGE_PIN AE26 [get_ports BK_MISO1]
set_property PACKAGE_PIN AD25 [get_ports BK_MOSI1]
set_property PACKAGE_PIN AF23 [get_ports BK_RST]
set_property PACKAGE_PIN AD26 [get_ports BK_SCLK1]
set_property PACKAGE_PIN AC24 [get_ports BK_TR_SW]
set_property PACKAGE_PIN AD24 [get_ports IO1]
set_property PACKAGE_PIN AF20 [get_ports IO2]
set_property PACKAGE_PIN AA22 [get_ports PL_CTRL_A]
set_property PACKAGE_PIN AA23 [get_ports PL_CTRL_B]
set_property PACKAGE_PIN AE22 [get_ports PL_QSPI_CCLK]
set_property PACKAGE_PIN AD18 [get_ports PL_QSPI_CS_N]
set_property PACKAGE_PIN AF22 [get_ports PL_QSPI_IO0]
set_property PACKAGE_PIN AD19 [get_ports PL_QSPI_IO1]
set_property PACKAGE_PIN AE18 [get_ports PL_QSPI_IO2]
set_property PACKAGE_PIN AF18 [get_ports PL_QSPI_IO3]
set_property PACKAGE_PIN AB26 [get_ports PL_UART1_RX]
set_property PACKAGE_PIN AC26 [get_ports PL_UART1_TX]
set_property PACKAGE_PIN AF25 [get_ports PL_UART2_RX]
set_property PACKAGE_PIN AB24 [get_ports PL_UART2_TX]
set_property PACKAGE_PIN AE21 [get_ports PL_UART4_RX]
set_property PACKAGE_PIN AE20 [get_ports PL_UART4_TX]
set_property PACKAGE_PIN Y20 [get_ports PL_UART_RX]
set_property PACKAGE_PIN AB20 [get_ports PL_UART_TX]
set_property PACKAGE_PIN AF19 [get_ports TSELF]
set_property PACKAGE_PIN AB21 [get_ports XD_R_B]
set_property PACKAGE_PIN AC21 [get_ports XD_T_P]
set_property PACKAGE_PIN W20 [get_ports XD_XS]
set_property PACKAGE_PIN AA20 [get_ports XD_HX_R]
set_property PACKAGE_PIN AB19 [get_ports XD_T_R]
set_property PACKAGE_PIN Y18 [get_ports XD_T_P1]
set_property PACKAGE_PIN AA25 [get_ports {test[2]}]
set_property PACKAGE_PIN W18 [get_ports {test[3]}]
set_property PACKAGE_PIN W19 [get_ports {test[5]}]
set_property PACKAGE_PIN AA19 [get_ports {test[6]}]
set_property PACKAGE_PIN AC23 [get_ports {test[4]}]
set_property C_CLK_INPUT_FREQ_HZ 300000000 [get_debug_cores dbg_hub]
set_property C_ENABLE_CLK_DIVIDER false [get_debug_cores dbg_hub]
set_property C_USER_SCAN_CHAIN 1 [get_debug_cores dbg_hub]
connect_debug_port dbg_hub/clk [get_nets FCLK_CLK0]
