################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/platform_xilinx/adc_core.c \
../src/platform_xilinx/dac_core.c \
../src/platform_xilinx/platform.c 

OBJS += \
./src/platform_xilinx/adc_core.o \
./src/platform_xilinx/dac_core.o \
./src/platform_xilinx/platform.o 

C_DEPS += \
./src/platform_xilinx/adc_core.d \
./src/platform_xilinx/dac_core.d \
./src/platform_xilinx/platform.d 


# Each subdirectory must supply rules for building sources it contributes
src/platform_xilinx/%.o: ../src/platform_xilinx/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DXILINX_PLATFORM -DCONSOLE_COMMANDS -DADC_DMA_EXAMPLE -DDAC_DMA_EXAMPLE -Wall -O0 -g3 -I"D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/src/platform_xilinx" -I"D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/src" -I"D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361./src/console_commands" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -I../../fsbl_bsp/ps7_cortexa9_0/include -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


