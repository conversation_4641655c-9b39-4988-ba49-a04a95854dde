#-----------------------------------------------------------
# Vivado v2016.4 (64-bit)
# SW Build 1756540 on Mon Jan 23 19:11:23 MST 2017
# IP Build 1755317 on Mon Jan 23 20:30:07 MST 2017
# Start of session at: Fri Apr 24 17:02:50 2020
# Process ID: 10736
# Current directory: E:/FPGA/R75_Z7035_0320/R75_Z7035_0320
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent9428 E:\FPGA\R75_Z7035_0320\R75_Z7035_0320\fmcomms2_zed.xpr
# Log file: E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/vivado.log
# Journal file: E:/FPGA/R75_Z7035_0320/R75_Z7035_0320\vivado.jou
#-----------------------------------------------------------
start_gui
open_project E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.xpr
Scanning sources...
Finished scanning sources
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1700] Loaded user IP repository 'e:/FPGA/hdl-hdl_2017_r1/hdl-hdl_2017_r1/library'.
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'C:/Xilinx/Vivado/2016.4/data/ip'.
WARNING: [BD 41-1661] One or more IPs have been locked in the design 'system.bd'. Please run report_ip_status for more details and recommendations on how to fix this issue.
List of locked IPs:
system_axi_ad9361_0
system_axi_ad9361_dac_dma_0
system_util_ad9361_adc_fifo_0
system_util_ad9361_tdd_sync_0
system_util_ad9361_dac_upack_0
system_util_ad9361_divclk_0
system_util_ad9361_adc_pack_0
system_axi_ad9361_adc_dma_0
system_axi_ad9361_dac_fifo_0

open_project: Time (s): cpu = 00:00:16 ; elapsed = 00:00:34 . Memory (MB): peak = 972.410 ; gain = 223.809
launch_sdk -workspace E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk -hwspec E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top.hdf
INFO: [Vivado 12-393] Launching SDK...
INFO: [Vivado 12-417] Running xsdk -workspace E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk -hwspec E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top.hdf
INFO: [Vivado 12-3157] SDK launch initiated. Please check console for any further messages.
open_hw
connect_hw_server
INFO: [Labtools 27-2285] Connecting to hw_server url TCP:localhost:3121
open_hw_target
INFO: [Labtoolstcl 44-466] Opening hw_target localhost:3121/xilinx_tcf/Digilent/210251838408
ERROR: [Labtools 27-2269] No devices detected on target localhost:3121/xilinx_tcf/Digilent/210251838408.
Check cable connectivity and that the target board is powered up then
use the disconnect_hw_server and connect_hw_server to re-register this hardware target.
ERROR: [Common 17-39] 'open_hw_target' failed due to earlier errors.
close_hw
open_hw
connect_hw_server
INFO: [Labtools 27-2285] Connecting to hw_server url TCP:localhost:3121
INFO: [Labtools 27-2222] Launching hw_server...
INFO: [Labtools 27-2221] Launch Output:

****** Xilinx hw_server v2016.4
  **** Build date : Jan 23 2017-19:37:29
    ** Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.


open_hw_target
INFO: [Labtoolstcl 44-466] Opening hw_target localhost:3121/xilinx_tcf/Digilent/210251838408
ERROR: [Labtools 27-2269] No devices detected on target localhost:3121/xilinx_tcf/Digilent/210251838408.
Check cable connectivity and that the target board is powered up then
use the disconnect_hw_server and connect_hw_server to re-register this hardware target.
ERROR: [Common 17-39] 'open_hw_target' failed due to earlier errors.
close_hw
open_hw
connect_hw_server
INFO: [Labtools 27-2285] Connecting to hw_server url TCP:localhost:3121
INFO: [Labtools 27-2222] Launching hw_server...
INFO: [Labtools 27-2221] Launch Output:

****** Xilinx hw_server v2016.4
  **** Build date : Jan 23 2017-19:37:29
    ** Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.


connect_hw_server: Time (s): cpu = 00:00:04 ; elapsed = 00:00:06 . Memory (MB): peak = 1045.273 ; gain = 0.000
open_hw_target
INFO: [Labtoolstcl 44-466] Opening hw_target localhost:3121/xilinx_tcf/Digilent/210251838408
ERROR: [Labtools 27-2269] No devices detected on target localhost:3121/xilinx_tcf/Digilent/210251838408.
Check cable connectivity and that the target board is powered up then
use the disconnect_hw_server and connect_hw_server to re-register this hardware target.
ERROR: [Common 17-39] 'open_hw_target' failed due to earlier errors.
close_hw_target {localhost:3121/xilinx_tcf/Digilent/210251838408}
INFO: [Labtoolstcl 44-464] Closing hw_target localhost:3121/xilinx_tcf/Digilent/210251838408
set_property PARAM.FREQUENCY 6000000 [get_hw_targets localhost:3121/xilinx_tcf/Digilent/210251838408]
INFO: [Labtoolstcl 44-466] Opening hw_target localhost:3121/xilinx_tcf/Digilent/210251838408
INFO: [Labtools 27-2302] Device xc7z035 (JTAG device index = 1) is programmed with a design that has 1 VIO core(s).
WARNING: [Labtoolstcl 44-130] No matching hw_ilas were found.
WARNING: [Labtoolstcl 44-130] No matching hw_ilas were found.
WARNING: [Labtoolstcl 44-130] No matching hw_ilas were found.
WARNING: [Labtoolstcl 44-130] No matching hw_ilas were found.
WARNING: [Labtools 27-1952] VIO hw_probe OUTPUT_VALUE properties for hw_vio(s) [hw_vio_1] differ from output values in the VIO core(s).
Resolution: 
To synchronize the hw_probes properties and the VIO core outputs choose one of the following alternatives:
  1) Execute the command 'Commit Output Values to VIO Core', to write down the hw_probe values to the core.
  2) Execute the command 'Refresh Input and Output Values from VIO Core', to update the hw_probe properties with the core values.
  3) First restore initial values in the core with the command 'Reset VIO Core Outputs', and then execute the command 'Refresh Input and Output Values from VIO Core'.
close_hw
exit
INFO: [Common 17-206] Exiting Vivado at Fri Apr 24 17:30:30 2020...
