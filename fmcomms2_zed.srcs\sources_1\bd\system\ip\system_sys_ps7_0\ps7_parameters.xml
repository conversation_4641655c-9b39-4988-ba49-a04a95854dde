<?xml version="1.0" encoding="UTF-8" ?> 
<!DOCTYPE designInfo PUBLIC "designInfo" "designInfo.dtd" >
<designInfo version="1.0" >
  <MODULE IP_TYPE="SOC" MOD_CLASS="CONFIGURABLE" MODTYPE="processing_system7" >
    <PARAMETERS >
      <PARAMETER NAME="PCW_APU_CLK_RATIO_ENABLE" VALUE="6:2:1" />
      <PARAMETER NAME="PCW_APU_PERIPHERAL_FREQMHZ" VALUE="666.666667" />
      <PARAMETER NAME="PCW_ARMPLL_CTRL_FBDIV" VALUE="27" />
      <PARAMETER NAME="PCW_CAN0_CAN0_IO" VALUE="" />
      <PARAMETER NAME="PCW_CAN0_GRP_CLK_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_CAN0_GRP_CLK_IO" VALUE="" />
      <PARAMETER NAME="PCW_CAN0_PERIPHERAL_CLKSRC" VALUE="External" />
      <PARAMETER NAME="PCW_CAN0_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_CAN0_PERIPHERAL_FREQMHZ" VALUE="" />
      <PARAMETER NAME="PCW_CAN1_CAN1_IO" VALUE="" />
      <PARAMETER NAME="PCW_CAN1_GRP_CLK_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_CAN1_GRP_CLK_IO" VALUE="" />
      <PARAMETER NAME="PCW_CAN1_PERIPHERAL_CLKSRC" VALUE="External" />
      <PARAMETER NAME="PCW_CAN1_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_CAN1_PERIPHERAL_FREQMHZ" VALUE="" />
      <PARAMETER NAME="PCW_CAN_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_CAN_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_CAN_PERIPHERAL_DIVISOR1" VALUE="1" />
      <PARAMETER NAME="PCW_CAN_PERIPHERAL_FREQMHZ" VALUE="100" />
      <PARAMETER NAME="PCW_CPU_CPU_PLL_FREQMHZ" VALUE="1350.000" />
      <PARAMETER NAME="PCW_CPU_PERIPHERAL_CLKSRC" VALUE="ARM PLL" />
      <PARAMETER NAME="PCW_CPU_PERIPHERAL_DIVISOR0" VALUE="2" />
      <PARAMETER NAME="PCW_CRYSTAL_PERIPHERAL_FREQMHZ" VALUE="50" />
      <PARAMETER NAME="PCW_DCI_PERIPHERAL_CLKSRC" VALUE="DDR PLL" />
      <PARAMETER NAME="PCW_DCI_PERIPHERAL_DIVISOR0" VALUE="52" />
      <PARAMETER NAME="PCW_DCI_PERIPHERAL_DIVISOR1" VALUE="2" />
      <PARAMETER NAME="PCW_DCI_PERIPHERAL_FREQMHZ" VALUE="10.159" />
      <PARAMETER NAME="PCW_DDRPLL_CTRL_FBDIV" VALUE="21" />
      <PARAMETER NAME="PCW_DDR_DDR_PLL_FREQMHZ" VALUE="1050.000" />
      <PARAMETER NAME="PCW_DDR_HPRLPR_QUEUE_PARTITION" VALUE="HPR(0)/LPR(32)" />
      <PARAMETER NAME="PCW_DDR_HPR_TO_CRITICAL_PRIORITY_LEVEL" VALUE="15" />
      <PARAMETER NAME="PCW_DDR_LPR_TO_CRITICAL_PRIORITY_LEVEL" VALUE="2" />
      <PARAMETER NAME="PCW_DDR_PERIPHERAL_CLKSRC" VALUE="DDR PLL" />
      <PARAMETER NAME="PCW_DDR_PERIPHERAL_DIVISOR0" VALUE="2" />
      <PARAMETER NAME="PCW_DDR_PORT0_HPR_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PORT1_HPR_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PORT2_HPR_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PORT3_HPR_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PRIORITY_READPORT_0" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PRIORITY_READPORT_1" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PRIORITY_READPORT_2" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PRIORITY_READPORT_3" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PRIORITY_WRITEPORT_0" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PRIORITY_WRITEPORT_1" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PRIORITY_WRITEPORT_2" VALUE="" />
      <PARAMETER NAME="PCW_DDR_PRIORITY_WRITEPORT_3" VALUE="" />
      <PARAMETER NAME="PCW_DDR_WRITE_TO_CRITICAL_PRIORITY_LEVEL" VALUE="2" />
      <PARAMETER NAME="PCW_ENET0_ENET0_IO" VALUE="MIO 16 .. 27" />
      <PARAMETER NAME="PCW_ENET0_GRP_MDIO_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_ENET0_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_ENET0_PERIPHERAL_DIVISOR0" VALUE="8" />
      <PARAMETER NAME="PCW_ENET0_PERIPHERAL_DIVISOR1" VALUE="1" />
      <PARAMETER NAME="PCW_ENET0_PERIPHERAL_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_ENET0_PERIPHERAL_FREQMHZ" VALUE="1000 Mbps" />
      <PARAMETER NAME="PCW_ENET0_RESET_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_ENET0_RESET_IO" VALUE="MIO 40" />
      <PARAMETER NAME="PCW_ENET1_ENET1_IO" VALUE="" />
      <PARAMETER NAME="PCW_ENET1_GRP_MDIO_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_ENET1_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_ENET1_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_ENET1_PERIPHERAL_DIVISOR1" VALUE="1" />
      <PARAMETER NAME="PCW_ENET1_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_ENET1_PERIPHERAL_FREQMHZ" VALUE="1000 Mbps" />
      <PARAMETER NAME="PCW_ENET1_RESET_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_ENET1_RESET_IO" VALUE="" />
      <PARAMETER NAME="PCW_ENET_RESET_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_ENET_RESET_POLARITY" VALUE="Active Low" />
      <PARAMETER NAME="PCW_ENET_RESET_SELECT" VALUE="Share reset pin" />
      <PARAMETER NAME="PCW_EN_4K_TIMER" VALUE="0" />
      <PARAMETER NAME="PCW_EN_CLK0_PORT" VALUE="1" />
      <PARAMETER NAME="PCW_EN_CLK1_PORT" VALUE="1" />
      <PARAMETER NAME="PCW_EN_CLK2_PORT" VALUE="0" />
      <PARAMETER NAME="PCW_EN_CLK3_PORT" VALUE="0" />
      <PARAMETER NAME="PCW_FCLK0_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_FCLK0_PERIPHERAL_DIVISOR0" VALUE="5" />
      <PARAMETER NAME="PCW_FCLK0_PERIPHERAL_DIVISOR1" VALUE="2" />
      <PARAMETER NAME="PCW_FCLK1_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_FCLK1_PERIPHERAL_DIVISOR0" VALUE="5" />
      <PARAMETER NAME="PCW_FCLK1_PERIPHERAL_DIVISOR1" VALUE="1" />
      <PARAMETER NAME="PCW_FCLK2_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_FCLK2_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_FCLK2_PERIPHERAL_DIVISOR1" VALUE="1" />
      <PARAMETER NAME="PCW_FCLK3_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_FCLK3_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_FCLK3_PERIPHERAL_DIVISOR1" VALUE="1" />
      <PARAMETER NAME="PCW_FCLK_CLK0_BUF" VALUE="TRUE" />
      <PARAMETER NAME="PCW_FCLK_CLK1_BUF" VALUE="TRUE" />
      <PARAMETER NAME="PCW_FCLK_CLK2_BUF" VALUE="FALSE" />
      <PARAMETER NAME="PCW_FCLK_CLK3_BUF" VALUE="FALSE" />
      <PARAMETER NAME="PCW_FPGA0_PERIPHERAL_FREQMHZ" VALUE="100.0" />
      <PARAMETER NAME="PCW_FPGA1_PERIPHERAL_FREQMHZ" VALUE="200.0" />
      <PARAMETER NAME="PCW_FPGA2_PERIPHERAL_FREQMHZ" VALUE="50" />
      <PARAMETER NAME="PCW_FPGA3_PERIPHERAL_FREQMHZ" VALUE="50" />
      <PARAMETER NAME="PCW_FTM_CTI_IN0" VALUE="" />
      <PARAMETER NAME="PCW_FTM_CTI_IN1" VALUE="" />
      <PARAMETER NAME="PCW_FTM_CTI_IN2" VALUE="" />
      <PARAMETER NAME="PCW_FTM_CTI_IN3" VALUE="" />
      <PARAMETER NAME="PCW_FTM_CTI_OUT0" VALUE="" />
      <PARAMETER NAME="PCW_FTM_CTI_OUT1" VALUE="" />
      <PARAMETER NAME="PCW_FTM_CTI_OUT2" VALUE="" />
      <PARAMETER NAME="PCW_FTM_CTI_OUT3" VALUE="" />
      <PARAMETER NAME="PCW_GPIO_EMIO_GPIO_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_GPIO_EMIO_GPIO_IO" VALUE="64" />
      <PARAMETER NAME="PCW_GPIO_MIO_GPIO_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_GPIO_MIO_GPIO_IO" VALUE="MIO" />
      <PARAMETER NAME="PCW_GPIO_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_I2C0_GRP_INT_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_I2C0_GRP_INT_IO" VALUE="" />
      <PARAMETER NAME="PCW_I2C0_I2C0_IO" VALUE="" />
      <PARAMETER NAME="PCW_I2C0_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_I2C0_RESET_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_I2C0_RESET_IO" VALUE="" />
      <PARAMETER NAME="PCW_I2C1_GRP_INT_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_I2C1_GRP_INT_IO" VALUE="" />
      <PARAMETER NAME="PCW_I2C1_I2C1_IO" VALUE="" />
      <PARAMETER NAME="PCW_I2C1_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_I2C1_RESET_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_I2C1_RESET_IO" VALUE="" />
      <PARAMETER NAME="PCW_I2C_RESET_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_I2C_RESET_POLARITY" VALUE="Active Low" />
      <PARAMETER NAME="PCW_I2C_RESET_SELECT" VALUE="" />
      <PARAMETER NAME="PCW_IOPLL_CTRL_FBDIV" VALUE="20" />
      <PARAMETER NAME="PCW_IO_IO_PLL_FREQMHZ" VALUE="1000.000" />
      <PARAMETER NAME="PCW_IRQ_F2P_MODE" VALUE="REVERSE" />
      <PARAMETER NAME="PCW_MIO_0_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_0_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_0_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_0_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_10_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_10_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_10_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_10_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_11_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_11_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_11_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_11_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_12_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_12_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_12_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_12_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_13_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_13_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_13_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_13_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_14_DIRECTION" VALUE="in" />
      <PARAMETER NAME="PCW_MIO_14_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_14_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_14_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_15_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_15_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_15_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_15_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_16_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_16_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_16_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_16_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_17_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_17_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_17_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_17_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_18_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_18_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_18_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_18_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_19_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_19_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_19_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_19_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_1_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_1_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_1_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_1_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_20_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_20_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_20_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_20_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_21_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_21_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_21_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_21_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_22_DIRECTION" VALUE="in" />
      <PARAMETER NAME="PCW_MIO_22_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_22_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_22_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_23_DIRECTION" VALUE="in" />
      <PARAMETER NAME="PCW_MIO_23_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_23_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_23_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_24_DIRECTION" VALUE="in" />
      <PARAMETER NAME="PCW_MIO_24_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_24_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_24_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_25_DIRECTION" VALUE="in" />
      <PARAMETER NAME="PCW_MIO_25_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_25_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_25_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_26_DIRECTION" VALUE="in" />
      <PARAMETER NAME="PCW_MIO_26_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_26_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_26_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_27_DIRECTION" VALUE="in" />
      <PARAMETER NAME="PCW_MIO_27_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_27_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_27_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_28_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_28_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_28_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_28_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_29_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_29_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_29_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_29_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_2_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_2_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_2_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_2_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_30_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_30_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_30_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_30_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_31_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_31_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_31_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_31_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_32_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_32_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_32_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_32_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_33_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_33_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_33_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_33_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_34_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_34_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_34_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_34_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_35_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_35_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_35_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_35_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_36_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_36_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_36_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_36_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_37_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_37_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_37_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_37_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_38_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_38_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_38_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_38_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_39_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_39_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_39_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_39_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_3_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_3_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_3_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_3_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_40_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_40_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_40_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_40_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_41_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_41_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_41_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_41_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_42_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_42_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_42_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_42_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_43_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_43_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_43_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_43_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_44_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_44_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_44_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_44_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_45_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_45_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_45_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_45_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_46_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_46_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_46_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_46_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_47_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_47_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_47_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_47_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_48_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_48_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_48_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_48_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_49_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_49_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_49_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_49_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_4_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_4_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_4_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_4_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_50_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_50_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_50_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_50_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_51_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_51_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_51_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_51_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_52_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_52_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_52_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_52_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_53_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_53_IOTYPE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_MIO_53_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_53_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_5_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_5_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_5_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_5_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_6_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_6_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_6_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_6_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_7_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_7_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_7_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_7_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_MIO_8_DIRECTION" VALUE="out" />
      <PARAMETER NAME="PCW_MIO_8_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_8_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_8_SLEW" VALUE="fast" />
      <PARAMETER NAME="PCW_MIO_9_DIRECTION" VALUE="inout" />
      <PARAMETER NAME="PCW_MIO_9_IOTYPE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_MIO_9_PULLUP" VALUE="disabled" />
      <PARAMETER NAME="PCW_MIO_9_SLEW" VALUE="slow" />
      <PARAMETER NAME="PCW_NAND_CYCLES_T_AR" VALUE="1" />
      <PARAMETER NAME="PCW_NAND_CYCLES_T_CLR" VALUE="1" />
      <PARAMETER NAME="PCW_NAND_CYCLES_T_RC" VALUE="11" />
      <PARAMETER NAME="PCW_NAND_CYCLES_T_REA" VALUE="1" />
      <PARAMETER NAME="PCW_NAND_CYCLES_T_RR" VALUE="1" />
      <PARAMETER NAME="PCW_NAND_CYCLES_T_WC" VALUE="11" />
      <PARAMETER NAME="PCW_NAND_CYCLES_T_WP" VALUE="1" />
      <PARAMETER NAME="PCW_NAND_GRP_D8_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_NAND_GRP_D8_IO" VALUE="" />
      <PARAMETER NAME="PCW_NAND_NAND_IO" VALUE="" />
      <PARAMETER NAME="PCW_NAND_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_NOR_CS0_T_CEOE" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_CS0_T_PC" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_CS0_T_RC" VALUE="11" />
      <PARAMETER NAME="PCW_NOR_CS0_T_TR" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_CS0_T_WC" VALUE="11" />
      <PARAMETER NAME="PCW_NOR_CS0_T_WP" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_CS0_WE_TIME" VALUE="0" />
      <PARAMETER NAME="PCW_NOR_CS1_T_CEOE" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_CS1_T_PC" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_CS1_T_RC" VALUE="11" />
      <PARAMETER NAME="PCW_NOR_CS1_T_TR" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_CS1_T_WC" VALUE="11" />
      <PARAMETER NAME="PCW_NOR_CS1_T_WP" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_CS1_WE_TIME" VALUE="0" />
      <PARAMETER NAME="PCW_NOR_GRP_A25_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_A25_IO" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_CS0_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_CS0_IO" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_CS1_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_CS1_IO" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_SRAM_CS0_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_SRAM_CS0_IO" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_SRAM_CS1_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_SRAM_CS1_IO" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_SRAM_INT_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_NOR_GRP_SRAM_INT_IO" VALUE="" />
      <PARAMETER NAME="PCW_NOR_NOR_IO" VALUE="" />
      <PARAMETER NAME="PCW_NOR_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_CEOE" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_PC" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_RC" VALUE="11" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_TR" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_WC" VALUE="11" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_WP" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS0_WE_TIME" VALUE="0" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_CEOE" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_PC" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_RC" VALUE="11" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_TR" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_WC" VALUE="11" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_WP" VALUE="1" />
      <PARAMETER NAME="PCW_NOR_SRAM_CS1_WE_TIME" VALUE="0" />
      <PARAMETER NAME="PCW_OVERRIDE_BASIC_CLOCK" VALUE="0" />
      <PARAMETER NAME="PCW_PCAP_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_PCAP_PERIPHERAL_DIVISOR0" VALUE="5" />
      <PARAMETER NAME="PCW_PCAP_PERIPHERAL_FREQMHZ" VALUE="200" />
      <PARAMETER NAME="PCW_PJTAG_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_PJTAG_PJTAG_IO" VALUE="" />
      <PARAMETER NAME="PCW_PLL_BYPASSMODE_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_PRESET_BANK0_VOLTAGE" VALUE="LVCMOS 3.3V" />
      <PARAMETER NAME="PCW_PRESET_BANK1_VOLTAGE" VALUE="LVCMOS 2.5V" />
      <PARAMETER NAME="PCW_QSPI_GRP_FBCLK_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_QSPI_GRP_FBCLK_IO" VALUE="" />
      <PARAMETER NAME="PCW_QSPI_GRP_IO1_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_QSPI_GRP_IO1_IO" VALUE="" />
      <PARAMETER NAME="PCW_QSPI_GRP_SINGLE_SS_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_QSPI_GRP_SINGLE_SS_IO" VALUE="MIO 1 .. 6" />
      <PARAMETER NAME="PCW_QSPI_GRP_SS1_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_QSPI_GRP_SS1_IO" VALUE="" />
      <PARAMETER NAME="PCW_QSPI_INTERNAL_HIGHADDRESS" VALUE="0xFCFFFFFF" />
      <PARAMETER NAME="PCW_QSPI_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_QSPI_PERIPHERAL_DIVISOR0" VALUE="5" />
      <PARAMETER NAME="PCW_QSPI_PERIPHERAL_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_QSPI_PERIPHERAL_FREQMHZ" VALUE="200" />
      <PARAMETER NAME="PCW_QSPI_QSPI_IO" VALUE="MIO 1 .. 6" />
      <PARAMETER NAME="PCW_SD0_GRP_CD_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_SD0_GRP_CD_IO" VALUE="" />
      <PARAMETER NAME="PCW_SD0_GRP_POW_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_SD0_GRP_POW_IO" VALUE="" />
      <PARAMETER NAME="PCW_SD0_GRP_WP_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_SD0_GRP_WP_IO" VALUE="" />
      <PARAMETER NAME="PCW_SD0_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_SD0_SD0_IO" VALUE="" />
      <PARAMETER NAME="PCW_SD1_GRP_CD_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_SD1_GRP_CD_IO" VALUE="" />
      <PARAMETER NAME="PCW_SD1_GRP_POW_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_SD1_GRP_POW_IO" VALUE="" />
      <PARAMETER NAME="PCW_SD1_GRP_WP_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_SD1_GRP_WP_IO" VALUE="" />
      <PARAMETER NAME="PCW_SD1_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_SD1_SD1_IO" VALUE="" />
      <PARAMETER NAME="PCW_SDIO_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_SDIO_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_SDIO_PERIPHERAL_FREQMHZ" VALUE="100" />
      <PARAMETER NAME="PCW_SMC_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_SMC_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_SMC_PERIPHERAL_FREQMHZ" VALUE="100" />
      <PARAMETER NAME="PCW_SPI0_GRP_SS0_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_SPI0_GRP_SS0_IO" VALUE="EMIO" />
      <PARAMETER NAME="PCW_SPI0_GRP_SS1_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_SPI0_GRP_SS1_IO" VALUE="EMIO" />
      <PARAMETER NAME="PCW_SPI0_GRP_SS2_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_SPI0_GRP_SS2_IO" VALUE="EMIO" />
      <PARAMETER NAME="PCW_SPI0_PERIPHERAL_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_SPI0_SPI0_IO" VALUE="EMIO" />
      <PARAMETER NAME="PCW_SPI1_GRP_SS0_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_SPI1_GRP_SS0_IO" VALUE="EMIO" />
      <PARAMETER NAME="PCW_SPI1_GRP_SS1_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_SPI1_GRP_SS1_IO" VALUE="EMIO" />
      <PARAMETER NAME="PCW_SPI1_GRP_SS2_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_SPI1_GRP_SS2_IO" VALUE="EMIO" />
      <PARAMETER NAME="PCW_SPI1_PERIPHERAL_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_SPI1_SPI1_IO" VALUE="EMIO" />
      <PARAMETER NAME="PCW_SPI_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_SPI_PERIPHERAL_DIVISOR0" VALUE="6" />
      <PARAMETER NAME="PCW_SPI_PERIPHERAL_FREQMHZ" VALUE="166.666666" />
      <PARAMETER NAME="PCW_S_AXI_HP0_DATA_WIDTH" VALUE="64" />
      <PARAMETER NAME="PCW_S_AXI_HP1_DATA_WIDTH" VALUE="64" />
      <PARAMETER NAME="PCW_S_AXI_HP2_DATA_WIDTH" VALUE="64" />
      <PARAMETER NAME="PCW_S_AXI_HP3_DATA_WIDTH" VALUE="64" />
      <PARAMETER NAME="PCW_TPIU_PERIPHERAL_CLKSRC" VALUE="External" />
      <PARAMETER NAME="PCW_TPIU_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_TPIU_PERIPHERAL_FREQMHZ" VALUE="200" />
      <PARAMETER NAME="PCW_TRACE_GRP_16BIT_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_TRACE_GRP_16BIT_IO" VALUE="" />
      <PARAMETER NAME="PCW_TRACE_GRP_2BIT_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_TRACE_GRP_2BIT_IO" VALUE="" />
      <PARAMETER NAME="PCW_TRACE_GRP_32BIT_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_TRACE_GRP_32BIT_IO" VALUE="" />
      <PARAMETER NAME="PCW_TRACE_GRP_4BIT_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_TRACE_GRP_4BIT_IO" VALUE="" />
      <PARAMETER NAME="PCW_TRACE_GRP_8BIT_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_TRACE_GRP_8BIT_IO" VALUE="" />
      <PARAMETER NAME="PCW_TRACE_INTERNAL_WIDTH" VALUE="2" />
      <PARAMETER NAME="PCW_TRACE_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_TRACE_TRACE_IO" VALUE="" />
      <PARAMETER NAME="PCW_TTC0_CLK0_PERIPHERAL_CLKSRC" VALUE="CPU_1X" />
      <PARAMETER NAME="PCW_TTC0_CLK0_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_TTC0_CLK0_PERIPHERAL_FREQMHZ" VALUE="133.333333" />
      <PARAMETER NAME="PCW_TTC0_CLK1_PERIPHERAL_CLKSRC" VALUE="CPU_1X" />
      <PARAMETER NAME="PCW_TTC0_CLK1_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_TTC0_CLK1_PERIPHERAL_FREQMHZ" VALUE="133.333333" />
      <PARAMETER NAME="PCW_TTC0_CLK2_PERIPHERAL_CLKSRC" VALUE="CPU_1X" />
      <PARAMETER NAME="PCW_TTC0_CLK2_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_TTC0_CLK2_PERIPHERAL_FREQMHZ" VALUE="133.333333" />
      <PARAMETER NAME="PCW_TTC0_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_TTC0_TTC0_IO" VALUE="" />
      <PARAMETER NAME="PCW_TTC1_CLK0_PERIPHERAL_CLKSRC" VALUE="CPU_1X" />
      <PARAMETER NAME="PCW_TTC1_CLK0_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_TTC1_CLK0_PERIPHERAL_FREQMHZ" VALUE="133.333333" />
      <PARAMETER NAME="PCW_TTC1_CLK1_PERIPHERAL_CLKSRC" VALUE="CPU_1X" />
      <PARAMETER NAME="PCW_TTC1_CLK1_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_TTC1_CLK1_PERIPHERAL_FREQMHZ" VALUE="133.333333" />
      <PARAMETER NAME="PCW_TTC1_CLK2_PERIPHERAL_CLKSRC" VALUE="CPU_1X" />
      <PARAMETER NAME="PCW_TTC1_CLK2_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_TTC1_CLK2_PERIPHERAL_FREQMHZ" VALUE="133.333333" />
      <PARAMETER NAME="PCW_TTC1_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_TTC1_TTC1_IO" VALUE="" />
      <PARAMETER NAME="PCW_TTC_PERIPHERAL_FREQMHZ" VALUE="50" />
      <PARAMETER NAME="PCW_UART0_BAUD_RATE" VALUE="115200" />
      <PARAMETER NAME="PCW_UART0_GRP_FULL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_UART0_GRP_FULL_IO" VALUE="" />
      <PARAMETER NAME="PCW_UART0_PERIPHERAL_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_UART0_UART0_IO" VALUE="MIO 14 .. 15" />
      <PARAMETER NAME="PCW_UART1_BAUD_RATE" VALUE="115200" />
      <PARAMETER NAME="PCW_UART1_GRP_FULL_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_UART1_GRP_FULL_IO" VALUE="" />
      <PARAMETER NAME="PCW_UART1_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_UART1_UART1_IO" VALUE="" />
      <PARAMETER NAME="PCW_UART_PERIPHERAL_CLKSRC" VALUE="IO PLL" />
      <PARAMETER NAME="PCW_UART_PERIPHERAL_DIVISOR0" VALUE="20" />
      <PARAMETER NAME="PCW_UART_PERIPHERAL_FREQMHZ" VALUE="50" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_ADV_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_AL" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_BANK_ADDR_COUNT" VALUE="3" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_BL" VALUE="8" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_BOARD_DELAY0" VALUE="0.41" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_BOARD_DELAY1" VALUE="0.411" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_BOARD_DELAY2" VALUE="0.341" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_BOARD_DELAY3" VALUE="0.358" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_BUS_WIDTH" VALUE="32 Bit" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CL" VALUE="7" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_0_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_0_PACKAGE_LENGTH" VALUE="61.0905" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_0_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_1_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_1_PACKAGE_LENGTH" VALUE="61.0905" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_1_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_2_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_2_PACKAGE_LENGTH" VALUE="61.0905" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_2_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_3_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_3_PACKAGE_LENGTH" VALUE="61.0905" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_3_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_STOP_EN" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_COL_ADDR_COUNT" VALUE="10" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_CWL" VALUE="6" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DEVICE_CAPACITY" VALUE="4096 MBits" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_0_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_0_PACKAGE_LENGTH" VALUE="68.4725" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_0_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_1_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_1_PACKAGE_LENGTH" VALUE="71.086" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_1_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_2_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_2_PACKAGE_LENGTH" VALUE="66.794" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_2_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_3_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_3_PACKAGE_LENGTH" VALUE="108.7385" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_3_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_0" VALUE="0.025" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_1" VALUE="0.028" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_2" VALUE="-0.009" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_3" VALUE="-0.061" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_0_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_0_PACKAGE_LENGTH" VALUE="64.1705" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_0_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_1_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_1_PACKAGE_LENGTH" VALUE="63.686" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_1_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_2_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_2_PACKAGE_LENGTH" VALUE="68.46" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_2_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_3_LENGTH_MM" VALUE="0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_3_PACKAGE_LENGTH" VALUE="105.4895" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_3_PROPOGATION_DELAY" VALUE="160" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_DRAM_WIDTH" VALUE="16 Bits" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_ECC" VALUE="Disabled" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_FREQ_MHZ" VALUE="533.333313" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_HIGH_TEMP" VALUE="Normal (0-85)" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_MEMORY_TYPE" VALUE="DDR 3" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_PARTNO" VALUE="MT41K256M16 RE-125" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_ROW_ADDR_COUNT" VALUE="15" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_SPEED_BIN" VALUE="DDR3_1066F" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_TRAIN_DATA_EYE" VALUE="1" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_TRAIN_READ_GATE" VALUE="1" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_TRAIN_WRITE_LEVEL" VALUE="1" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_T_FAW" VALUE="40.0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_T_RAS_MIN" VALUE="35.0" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_T_RC" VALUE="48.75" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_T_RCD" VALUE="7" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_T_RP" VALUE="7" />
      <PARAMETER NAME="PCW_UIPARAM_DDR_USE_INTERNAL_VREF" VALUE="1" />
      <PARAMETER NAME="PCW_USB0_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_USB0_PERIPHERAL_FREQMHZ" VALUE="60" />
      <PARAMETER NAME="PCW_USB0_RESET_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_USB0_RESET_IO" VALUE="" />
      <PARAMETER NAME="PCW_USB0_USB0_IO" VALUE="" />
      <PARAMETER NAME="PCW_USB1_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_USB1_PERIPHERAL_FREQMHZ" VALUE="60" />
      <PARAMETER NAME="PCW_USB1_RESET_ENABLE" VALUE="" />
      <PARAMETER NAME="PCW_USB1_RESET_IO" VALUE="" />
      <PARAMETER NAME="PCW_USB1_USB1_IO" VALUE="" />
      <PARAMETER NAME="PCW_USB_RESET_ENABLE" VALUE="1" />
      <PARAMETER NAME="PCW_USB_RESET_POLARITY" VALUE="Active Low" />
      <PARAMETER NAME="PCW_USB_RESET_SELECT" VALUE="" />
      <PARAMETER NAME="PCW_USE_AXI_NONSECURE" VALUE="0" />
      <PARAMETER NAME="PCW_USE_CROSS_TRIGGER" VALUE="0" />
      <PARAMETER NAME="PCW_USE_M_AXI_GP0" VALUE="1" />
      <PARAMETER NAME="PCW_USE_M_AXI_GP1" VALUE="0" />
      <PARAMETER NAME="PCW_USE_S_AXI_ACP" VALUE="0" />
      <PARAMETER NAME="PCW_USE_S_AXI_GP0" VALUE="0" />
      <PARAMETER NAME="PCW_USE_S_AXI_GP1" VALUE="0" />
      <PARAMETER NAME="PCW_USE_S_AXI_HP0" VALUE="1" />
      <PARAMETER NAME="PCW_USE_S_AXI_HP1" VALUE="1" />
      <PARAMETER NAME="PCW_USE_S_AXI_HP2" VALUE="1" />
      <PARAMETER NAME="PCW_USE_S_AXI_HP3" VALUE="0" />
      <PARAMETER NAME="PCW_WDT_PERIPHERAL_CLKSRC" VALUE="CPU_1X" />
      <PARAMETER NAME="PCW_WDT_PERIPHERAL_DIVISOR0" VALUE="1" />
      <PARAMETER NAME="PCW_WDT_PERIPHERAL_ENABLE" VALUE="0" />
      <PARAMETER NAME="PCW_WDT_PERIPHERAL_FREQMHZ" VALUE="133.333333" />
      <PARAMETER NAME="PCW_WDT_WDT_IO" VALUE="" />
    </PARAMETERS>
    <BUSINTERFACES >
      <BUSINTERFACE NAME="M_AXI_GP0" TYPE="MASTER" WIDTH="32" PARAMTOENABLE="PCW_USE_M_AXI_GP0" VALUE="1" />
      <BUSINTERFACE NAME="M_AXI_GP1" TYPE="MASTER" WIDTH="32" PARAMTOENABLE="PCW_USE_M_AXI_GP1" VALUE="0" />
      <BUSINTERFACE NAME="S_AXI_GP0" TYPE="TARGET" WIDTH="32" PARAMTOENABLE="PCW_USE_S_AXI_GP0" VALUE="0" />
      <BUSINTERFACE NAME="S_AXI_GP0" TYPE="TARGET" WIDTH="32" PARAMTOENABLE="PCW_USE_S_AXI_GP1" VALUE="0" />
      <BUSINTERFACE NAME="S_AXI_HP0" TYPE="TARGET" WIDTH="64" PARAMTOENABLE="PCW_USE_S_AXI_HP0" VALUE="1" />
      <BUSINTERFACE NAME="S_AXI_HP1" TYPE="TARGET" WIDTH="64" PARAMTOENABLE="PCW_USE_S_AXI_HP1" VALUE="1" />
      <BUSINTERFACE NAME="S_AXI_HP2" TYPE="TARGET" WIDTH="64" PARAMTOENABLE="PCW_USE_S_AXI_HP2" VALUE="1" />
      <BUSINTERFACE NAME="S_AXI_HP3" TYPE="TARGET" WIDTH="64" PARAMTOENABLE="PCW_USE_S_AXI_HP1" VALUE="0" />
    </BUSINTERFACES>
    <CLOCKOUTS >
      <CLOCKOUT NAME="FCLK_CLK0" FREQUENCY="100.000000" />
      <CLOCKOUT NAME="FCLK_CLK1" FREQUENCY="200.000000" />
      <CLOCKOUT NAME="FCLK_CLK2" FREQUENCY="10.000000" />
      <CLOCKOUT NAME="FCLK_CLK3" FREQUENCY="10.000000" />
    </CLOCKOUTS>
  </MODULE>
</designInfo>
