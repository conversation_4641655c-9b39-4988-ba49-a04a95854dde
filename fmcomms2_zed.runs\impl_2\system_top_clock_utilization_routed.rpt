Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
----------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 13:26:10 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : report_clock_utilization -file system_top_clock_utilization_routed.rpt
| Design       : system_top
| Device       : 7z045-ffg900
| Speed File   : -2  PRODUCTION 1.11 2014-09-11
----------------------------------------------------------------------------------------

Clock Utilization Report

Table of Contents
-----------------
1. Clock Primitive Utilization
2. Global Clock Resources
3. Global Clock Source Details
4. Clock Regions: Key Resource Utilization
5. Clock Regions : Global Clock Summary
6. Cell Type Counts per Global Clock: Region X0Y3
7. Cell Type Counts per Global Clock: Region X1Y3
8. Cell Type Counts per Global Clock: Region X0Y4
9. Cell Type Counts per Global Clock: Region X1Y4
10. Cell Type Counts per Global Clock: Region X0Y5
11. Cell Type Counts per Global Clock: Region X1Y5
12. Cell Type Counts per Global Clock: Region X0Y6
13. Cell Type Counts per Global Clock: Region X1Y6
14. Load Cell Placement Summary for Global Clock g0
15. Load Cell Placement Summary for Global Clock g1
16. Load Cell Placement Summary for Global Clock g2
17. Load Cell Placement Summary for Global Clock g3
18. Load Cell Placement Summary for Global Clock g4
19. Load Cell Placement Summary for Global Clock g5

1. Clock Primitive Utilization
------------------------------

+----------+------+-----------+-----+--------------+--------+
| Type     | Used | Available | LOC | Clock Region | Pblock |
+----------+------+-----------+-----+--------------+--------+
| BUFGCTRL |    4 |        32 |   0 |            0 |      0 |
| BUFH     |    0 |       168 |   0 |            0 |      0 |
| BUFIO    |    0 |        32 |   0 |            0 |      0 |
| BUFMR    |    0 |        16 |   0 |            0 |      0 |
| BUFR     |    2 |        32 |   0 |            0 |      0 |
| MMCM     |    0 |         8 |   0 |            0 |      0 |
| PLL      |    0 |         8 |   0 |            0 |      0 |
+----------+------+-----------+-----+--------------+--------+


2. Global Clock Resources
-------------------------

+-----------+-----------+-----------------+------------+----------------+--------------+------+-------------------+-------------------+-------------+-----------------+--------------+-----------------+----------------------------------------------------------------------------+-------------------------------------------------------------------+
| Global Id | Source Id | Driver Type/Pin | Constraint | Site           | Clock Region | Root | Clock Delay Group | Load Clock Region | Clock Loads | Non-Clock Loads | Clock Period | Clock           | Driver Pin                                                                 | Net                                                               |
+-----------+-----------+-----------------+------------+----------------+--------------+------+-------------------+-------------------+-------------+-----------------+--------------+-----------------+----------------------------------------------------------------------------+-------------------------------------------------------------------+
| g0        | src0      | BUFG/O          | None       | BUFGCTRL_X0Y16 | n/a          |      |                   |                 7 |       10501 |               2 |        4.000 | rx_clk          | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/i_clk_gbuf/O      | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk      |
| g1        | src1      | BUFG/O          | None       | BUFGCTRL_X0Y17 | n/a          |      |                   |                 7 |        8905 |               0 |       10.000 | clk_fpga_0      | i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0                  |
| g2        | src2      | BUFGCTRL/O      | None       | BUFGCTRL_X0Y19 | n/a          |      |                   |                 2 |        3115 |               0 |       16.000 | Multiple        | i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf/O         | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_out         |
| g3        | src1      | BUFG/O          | None       | BUFGCTRL_X0Y18 | n/a          |      |                   |                 1 |           4 |               0 |        5.000 | clk_fpga_1      | i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG/O | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK1                  |
| g4        | src3      | BUFR/O          | None       | BUFR_X1Y25     | X1Y6         |      |                   |                 1 |           1 |               0 |       16.000 | clk_div_sel_0_s | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_divide_sel_0/O       | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_div_sel_0_s |
| g5        | src3      | BUFR/O          | None       | BUFR_X1Y24     | X1Y6         |      |                   |                 1 |           1 |               0 |        8.000 | clk_div_sel_1_s | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_divide_sel_1/O       | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_div_sel_1_s |
+-----------+-----------+-----------------+------------+----------------+--------------+------+-------------------+-------------------+-------------+-----------------+--------------+-----------------+----------------------------------------------------------------------------+-------------------------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)


3. Global Clock Source Details
------------------------------

+-----------+-----------+-----------------+------------+----------------+--------------+-------------+-----------------+---------------------+-----------------+--------------------------------------------------------------------------+---------------------------------------------------------------------+
| Source Id | Global Id | Driver Type/Pin | Constraint | Site           | Clock Region | Clock Loads | Non-Clock Loads | Source Clock Period | Source Clock    | Driver Pin                                                               | Net                                                                 |
+-----------+-----------+-----------------+------------+----------------+--------------+-------------+-----------------+---------------------+-----------------+--------------------------------------------------------------------------+---------------------------------------------------------------------+
| src0      | g0        | IBUFDS/O        | IOB_X1Y226 | IOB_X1Y226     | X1Y4         |           1 |               0 |               4.000 | rx_clk          | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/i_rx_clk_ibuf/O | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk_ibuf_s |
| src1      | g1        | PS7/FCLKCLK[0]  | PS7_X0Y0   | PS7_X0Y0       | X0Y6         |           1 |               0 |              10.000 | clk_fpga_0      | i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[0]                  | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0]       |
| src1      | g3        | PS7/FCLKCLK[1]  | PS7_X0Y0   | PS7_X0Y0       | X0Y6         |           1 |               0 |               5.000 | clk_fpga_1      | i_system_wrapper/system_i/sys_ps7/inst/PS7_i/FCLKCLK[1]                  | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1]       |
| src2      | g2        | BUFR/O          | None       | BUFR_X1Y25     | X1Y6         |           1 |               0 |              16.000 | clk_div_sel_0_s | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_divide_sel_0/O     | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_div_sel_0_s   |
| src3      | g4, g5    | BUFG/O          | None       | BUFGCTRL_X0Y16 | n/a          |       10503 |               0 |               4.000 | rx_clk          | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/i_clk_gbuf/O    | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk        |
+-----------+-----------+-----------------+------------+----------------+--------------+-------------+-----------------+---------------------+-----------------+--------------------------------------------------------------------------+---------------------------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)


4. Clock Regions: Key Resource Utilization
------------------------------------------

+-------------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+
|                   | Global Clock |     BUFRs    |    BUFMRs    |    BUFIOs    |     MMCM     |      PLL     |      GT      |      PCI     |    ILOGIC    |    OLOGIC    |      FF      |     LUTM     |    RAMB18    |    RAMB36    |    DSP48E2   |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| Clock Region Name | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| X0Y0              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  4500 |    0 |  1600 |    0 |    80 |    0 |    40 |    0 |    80 |
| X1Y0              |    0 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     4 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |  3700 |    0 |  1050 |    0 |    80 |    0 |    40 |    0 |    60 |
| X0Y1              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  4500 |    0 |  1600 |    0 |    80 |    0 |    40 |    0 |    80 |
| X1Y1              |    0 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     4 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |  3700 |    0 |  1050 |    0 |    80 |    0 |    40 |    0 |    60 |
| X0Y2              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  4500 |    0 |  1600 |    0 |    80 |    0 |    40 |    0 |    80 |
| X1Y2              |    0 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     4 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |  3700 |    0 |  1050 |    0 |    80 |    0 |    40 |    0 |    60 |
| X0Y3              |    2 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |  120 |  4500 |   56 |  1600 |    0 |    80 |    0 |    40 |    1 |    80 |
| X1Y3              |    2 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     4 |    0 |     1 |    0 |     0 |    0 |     0 | 1228 |  3550 |  519 |  1000 |    0 |    70 |    0 |    35 |   17 |    60 |
| X0Y4              |    3 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 | 8484 |  4500 | 2968 |  1600 |    0 |    80 |    3 |    40 |   11 |    80 |
| X1Y4              |    3 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    7 |    50 |    8 |    50 | 3950 |  4100 | 1592 |  1150 |    0 |   100 |    0 |    50 |   31 |    60 |
| X0Y5              |    3 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 | 7572 |  3200 | 2641 |  1000 |    0 |    40 |    1 |    20 |    0 |    40 |
| X1Y5              |    2 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    2 |    50 |  105 |  3500 |   62 |  1150 |    0 |   100 |    0 |    50 |    0 |    60 |
| X0Y6              |    1 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |  690 |  3200 |  280 |  1000 |    0 |    40 |    0 |    20 |    0 |    40 |
| X1Y6              |    1 |    12 |    2 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  3500 |    0 |  1150 |    0 |   100 |    0 |    50 |    0 |    60 |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
* Global Clock column represents track count; while other columns represents cell counts


5. Clock Regions : Global Clock Summary
---------------------------------------

+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y6 |  1 |  1 |
| Y5 |  3 |  2 |
| Y4 |  3 |  3 |
| Y3 |  2 |  2 |
| Y2 |  0 |  0 |
| Y1 |  0 |  0 |
| Y0 |  0 |  0 |
+----+----+----+


6. Cell Type Counts per Global Clock: Region X0Y3
-------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |          79 |               0 | 78 |      0 |    0 |   1 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk |
| g1        | n/a   | BUFG/O          | None       |          42 |               0 | 42 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0             |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


7. Cell Type Counts per Global Clock: Region X1Y3
-------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF   | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |        1274 |               0 | 1212 |     45 |    0 |  17 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk |
| g1        | n/a   | BUFG/O          | None       |          16 |               0 |   16 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0             |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


8. Cell Type Counts per Global Clock: Region X0Y4
-------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF   | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |        3498 |               0 | 3477 |      8 |    2 |  11 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk |
| g1        | n/a   | BUFG/O          | None       |        2390 |               0 | 2389 |      0 |    1 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0             |
| g2        | n/a   | BUFGCTRL/O      | None       |        2622 |               0 | 2618 |      1 |    3 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_out    |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


9. Cell Type Counts per Global Clock: Region X1Y4
-------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF   | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |        2979 |               0 | 2858 |     75 |    0 |  31 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk |
| g1        | n/a   | BUFG/O          | None       |        1096 |               0 | 1089 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0             |
| g3        | n/a   | BUFG/O          | None       |           4 |               0 |    3 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK1             |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


10. Cell Type Counts per Global Clock: Region X0Y5
--------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF   | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |        2608 |               0 | 2608 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk |
| g1        | n/a   | BUFG/O          | None       |        4620 |               0 | 4472 |    147 |    1 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0             |
| g2        | n/a   | BUFGCTRL/O      | None       |         493 |               0 |  492 |      0 |    1 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_out    |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


11. Cell Type Counts per Global Clock: Region X1Y5
--------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |          63 |               0 | 61 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk |
| g1        | n/a   | BUFG/O          | None       |          44 |               0 | 44 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0             |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


12. Cell Type Counts per Global Clock: Region X0Y6
--------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+--------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF  | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                              |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+--------------------------------------------------+
| g1        | n/a   | BUFG/O          | None       |         691 |               0 | 690 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0 |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+--------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


13. Cell Type Counts per Global Clock: Region X1Y6
--------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |           0 |               2 |  0 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


14. Load Cell Placement Summary for Global Clock g0
---------------------------------------------------

+-----------+-----------------+-------------------+--------+-------------+---------------+----------+-------------+----------+----------------+----------+--------------------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock  | Period (ns) | Waveform (ns) | Root (R) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                                          |
+-----------+-----------------+-------------------+--------+-------------+---------------+----------+-------------+----------+----------------+----------+--------------------------------------------------------------+
| g0        | BUFG/O          | n/a               | rx_clk |       4.000 | {0.000 2.000} |          |       10501 |        0 |              2 |        0 | i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk |
+-----------+-----------------+-------------------+--------+-------------+---------------+----------+-------------+----------+----------------+----------+--------------------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+-------+-------+
|    | X0    | X1    |
+----+-------+-------+
| Y6 |     0 |     2 |
| Y5 |  2608 |    63 |
| Y4 |  3498 |  2979 |
| Y3 |    79 |  1274 |
| Y2 |     0 |     0 |
| Y1 |     0 |     0 |
| Y0 |     0 |     0 |
+----+-------+-------+


15. Load Cell Placement Summary for Global Clock g1
---------------------------------------------------

+-----------+-----------------+-------------------+------------+-------------+---------------+----------+-------------+----------+----------------+----------+--------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock      | Period (ns) | Waveform (ns) | Root (R) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                              |
+-----------+-----------------+-------------------+------------+-------------+---------------+----------+-------------+----------+----------------+----------+--------------------------------------------------+
| g1        | BUFG/O          | n/a               | clk_fpga_0 |      10.000 | {0.000 5.000} |          |        8899 |        0 |              0 |        0 | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0 |
+-----------+-----------------+-------------------+------------+-------------+---------------+----------+-------------+----------+----------------+----------+--------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+-------+-------+
|    | X0    | X1    |
+----+-------+-------+
| Y6 |   691 |     0 |
| Y5 |  4620 |    44 |
| Y4 |  2390 |  1096 |
| Y3 |    42 |    16 |
| Y2 |     0 |     0 |
| Y1 |     0 |     0 |
| Y0 |     0 |     0 |
+----+-------+-------+


16. Load Cell Placement Summary for Global Clock g2
---------------------------------------------------

+-----------+-----------------+-------------------+----------+-------------+---------------+----------+-------------+----------+----------------+----------+-----------------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock    | Period (ns) | Waveform (ns) | Root (R) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                                       |
+-----------+-----------------+-------------------+----------+-------------+---------------+----------+-------------+----------+----------------+----------+-----------------------------------------------------------+
| g2        | BUFGCTRL/O      | n/a               | Multiple |      16.000 | {0.000 8.000} |          |        3115 |        0 |              0 |        0 | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_out |
+-----------+-----------------+-------------------+----------+-------------+---------------+----------+-------------+----------+----------------+----------+-----------------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+-------+----+
|    | X0    | X1 |
+----+-------+----+
| Y6 |     0 |  0 |
| Y5 |   493 |  0 |
| Y4 |  2622 |  0 |
| Y3 |     0 |  0 |
| Y2 |     0 |  0 |
| Y1 |     0 |  0 |
| Y0 |     0 |  0 |
+----+-------+----+


17. Load Cell Placement Summary for Global Clock g3
---------------------------------------------------

+-----------+-----------------+-------------------+------------+-------------+---------------+----------+-------------+----------+----------------+----------+--------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock      | Period (ns) | Waveform (ns) | Root (R) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                              |
+-----------+-----------------+-------------------+------------+-------------+---------------+----------+-------------+----------+----------------+----------+--------------------------------------------------+
| g3        | BUFG/O          | n/a               | clk_fpga_1 |       5.000 | {0.000 2.500} |          |           4 |        0 |              0 |        0 | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK1 |
+-----------+-----------------+-------------------+------------+-------------+---------------+----------+-------------+----------+----------------+----------+--------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y6 |  0 |  0 |
| Y5 |  0 |  0 |
| Y4 |  0 |  4 |
| Y3 |  0 |  0 |
| Y2 |  0 |  0 |
| Y1 |  0 |  0 |
| Y0 |  0 |  0 |
+----+----+----+


18. Load Cell Placement Summary for Global Clock g4
---------------------------------------------------

+-----------+-----------------+-------------------+-----------------+-------------+---------------+----------+-------------+----------+----------------+----------+-------------------------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock           | Period (ns) | Waveform (ns) | Root (R) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                                               |
+-----------+-----------------+-------------------+-----------------+-------------+---------------+----------+-------------+----------+----------------+----------+-------------------------------------------------------------------+
| g4        | BUFR/O          | X1Y6              | clk_div_sel_0_s |      16.000 | {0.000 8.000} |          |           0 |        0 |              1 |        0 | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_div_sel_0_s |
+-----------+-----------------+-------------------+-----------------+-------------+---------------+----------+-------------+----------+----------------+----------+-------------------------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+--------+
|    | X0 | X1     |
+----+----+--------+
| Y6 |  0 |  (D) 0 |
| Y5 |  0 |      0 |
| Y4 |  0 |      0 |
| Y3 |  0 |      0 |
| Y2 |  0 |      0 |
| Y1 |  0 |      0 |
| Y0 |  0 |      0 |
+----+----+--------+


19. Load Cell Placement Summary for Global Clock g5
---------------------------------------------------

+-----------+-----------------+-------------------+-----------------+-------------+---------------+----------+-------------+----------+----------------+----------+-------------------------------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock           | Period (ns) | Waveform (ns) | Root (R) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                                               |
+-----------+-----------------+-------------------+-----------------+-------------+---------------+----------+-------------+----------+----------------+----------+-------------------------------------------------------------------+
| g5        | BUFR/O          | X1Y6              | clk_div_sel_1_s |       8.000 | {0.000 4.000} |          |           0 |        0 |              1 |        0 | i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_div_sel_1_s |
+-----------+-----------------+-------------------+-----------------+-------------+---------------+----------+-------------+----------+----------------+----------+-------------------------------------------------------------------+
* Slice Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+--------+
|    | X0 | X1     |
+----+----+--------+
| Y6 |  0 |  (D) 0 |
| Y5 |  0 |      0 |
| Y4 |  0 |      0 |
| Y3 |  0 |      0 |
| Y2 |  0 |      0 |
| Y1 |  0 |      0 |
| Y0 |  0 |      0 |
+----+----+--------+



# Location of BUFG Primitives 
set_property LOC BUFGCTRL_X0Y19 [get_cells i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf]
set_property LOC BUFGCTRL_X0Y18 [get_cells i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG]
set_property LOC BUFGCTRL_X0Y17 [get_cells i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG]
set_property LOC BUFGCTRL_X0Y16 [get_cells i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/i_clk_gbuf]

# Location of BUFR Primitives 
set_property LOC BUFR_X1Y24 [get_cells i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_divide_sel_1]
set_property LOC BUFR_X1Y25 [get_cells i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_divide_sel_0]

# Location of IO Primitives which is load of clock spine

# Location of clock ports
set_property LOC IOB_X1Y225 [get_ports rx_clk_in_n]
set_property LOC IOB_X1Y226 [get_ports rx_clk_in_p]

# Clock net "i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_out" driven by instance "i_system_wrapper/system_i/util_ad9361_divclk/inst/i_div_clk_gbuf" located at site "BUFGCTRL_X0Y19"
#startgroup
create_pblock {CLKAG_i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_out}
add_cells_to_pblock [get_pblocks  {CLKAG_i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_out}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_out"}]]]
resize_pblock [get_pblocks {CLKAG_i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_out}] -add {CLOCKREGION_X0Y4:CLOCKREGION_X0Y4 CLOCKREGION_X0Y5:CLOCKREGION_X0Y5}
#endgroup

# Clock net "i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK1" driven by instance "i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_1.FCLK_CLK_1_BUFG" located at site "BUFGCTRL_X0Y18"
#startgroup
create_pblock {CLKAG_i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK1}
add_cells_to_pblock [get_pblocks  {CLKAG_i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK1}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK1"}]]]
resize_pblock [get_pblocks {CLKAG_i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK1}] -add {CLOCKREGION_X1Y4:CLOCKREGION_X1Y4}
#endgroup

# Clock net "i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0" driven by instance "i_system_wrapper/system_i/sys_ps7/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG" located at site "BUFGCTRL_X0Y17"
#startgroup
create_pblock {CLKAG_i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0}
add_cells_to_pblock [get_pblocks  {CLKAG_i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0"}]]]
resize_pblock [get_pblocks {CLKAG_i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK0}] -add {CLOCKREGION_X0Y3:CLOCKREGION_X0Y3 CLOCKREGION_X0Y4:CLOCKREGION_X0Y4 CLOCKREGION_X0Y5:CLOCKREGION_X0Y5 CLOCKREGION_X0Y6:CLOCKREGION_X0Y6 CLOCKREGION_X1Y3:CLOCKREGION_X1Y3 CLOCKREGION_X1Y4:CLOCKREGION_X1Y4 CLOCKREGION_X1Y5:CLOCKREGION_X1Y5}
#endgroup

# Clock net "i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk" driven by instance "i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/i_clk_gbuf" located at site "BUFGCTRL_X0Y16"
#startgroup
create_pblock {CLKAG_i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk}
add_cells_to_pblock [get_pblocks  {CLKAG_i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL && NAME!=i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_divide_sel_1 && NAME!=i_system_wrapper/system_i/util_ad9361_divclk/inst/clk_divide_sel_0} -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk"}]]]
resize_pblock [get_pblocks {CLKAG_i_system_wrapper/system_i/axi_ad9361/inst/i_dev_if/i_clk/clk}] -add {CLOCKREGION_X0Y3:CLOCKREGION_X0Y3 CLOCKREGION_X0Y4:CLOCKREGION_X0Y4 CLOCKREGION_X0Y5:CLOCKREGION_X0Y5 CLOCKREGION_X1Y3:CLOCKREGION_X1Y3 CLOCKREGION_X1Y4:CLOCKREGION_X1Y4 CLOCKREGION_X1Y5:CLOCKREGION_X1Y5}
#endgroup
