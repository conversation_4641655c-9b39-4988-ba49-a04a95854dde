15:56:03 **** Incremental Build of configuration Debug for project fsbl ****
make pre-build main-build 
a9-linaro-pre-build-step
' '
'Building file: ../src/fsbl_hooks.c'
'Invoking: ARM v7 gcc compiler'
arm-none-eabi-gcc -Wall -O0 -g3 -I"D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_hw_platform_0" -c -fmessage-length=0 -MT"src/fsbl_hooks.o" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -I../../fsbl_bsp/ps7_cortexa9_0/include -MMD -MP -MF"src/fsbl_hooks.d" -MT"src/fsbl_hooks.o" -o "src/fsbl_hooks.o" "../src/fsbl_hooks.c"
'Finished building: ../src/fsbl_hooks.c'
' '
'Building file: ../src/image_mover.c'
'Invoking: ARM v7 gcc compiler'
arm-none-eabi-gcc -Wall -O0 -g3 -I"D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_hw_platform_0" -c -fmessage-length=0 -MT"src/image_mover.o" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -I../../fsbl_bsp/ps7_cortexa9_0/include -MMD -MP -MF"src/image_mover.d" -MT"src/image_mover.o" -o "src/image_mover.o" "../src/image_mover.c"
'Finished building: ../src/image_mover.c'
' '
'Building file: ../src/main.c'
'Invoking: ARM v7 gcc compiler'
arm-none-eabi-gcc -Wall -O0 -g3 -I"D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_hw_platform_0" -c -fmessage-length=0 -MT"src/main.o" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -I../../fsbl_bsp/ps7_cortexa9_0/include -MMD -MP -MF"src/main.d" -MT"src/main.o" -o "src/main.o" "../src/main.c"
'Finished building: ../src/main.c'
' '
'Building file: ../src/nand.c'
'Invoking: ARM v7 gcc compiler'
arm-none-eabi-gcc -Wall -O0 -g3 -I"D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_hw_platform_0" -c -fmessage-length=0 -MT"src/nand.o" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -I../../fsbl_bsp/ps7_cortexa9_0/include -MMD -MP -MF"src/nand.d" -MT"src/nand.o" -o "src/nand.o" "../src/nand.c"
'Finished building: ../src/nand.c'
' '
'Building file: ../src/nor.c'
'Invoking: ARM v7 gcc compiler'
arm-none-eabi-gcc -Wall -O0 -g3 -I"D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_hw_platform_0" -c -fmessage-length=0 -MT"src/nor.o" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -I../../fsbl_bsp/ps7_cortexa9_0/include -MMD -MP -MF"src/nor.d" -MT"src/nor.o" -o "src/nor.o" "../src/nor.c"
'Finished building: ../src/nor.c'
' '
'Building file: ../src/pcap.c'
'Invoking: ARM v7 gcc compiler'
arm-none-eabi-gcc -Wall -O0 -g3 -I"D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_hw_platform_0" -c -fmessage-length=0 -MT"src/pcap.o" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -I../../fsbl_bsp/ps7_cortexa9_0/include -MMD -MP -MF"src/pcap.d" -MT"src/pcap.o" -o "src/pcap.o" "../src/pcap.c"
'Finished building: ../src/pcap.c'
' '
'Building file: D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.c'
'Invoking: ARM v7 gcc compiler'
arm-none-eabi-gcc -Wall -O0 -g3 -I"D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_hw_platform_0" -c -fmessage-length=0 -MT"src/ps7_init.o" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -I../../fsbl_bsp/ps7_cortexa9_0/include -MMD -MP -MF"src/ps7_init.d" -MT"src/ps7_init.o" -o "src/ps7_init.o" "D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.c"
'Finished building: D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.c'
' '
'Building file: ../src/qspi.c'
'Invoking: ARM v7 gcc compiler'
arm-none-eabi-gcc -Wall -O0 -g3 -I"D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_hw_platform_0" -c -fmessage-length=0 -MT"src/qspi.o" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -I../../fsbl_bsp/ps7_cortexa9_0/include -MMD -MP -MF"src/qspi.d" -MT"src/qspi.o" -o "src/qspi.o" "../src/qspi.c"
'Finished building: ../src/qspi.c'
' '
'Building file: ../src/sd.c'
'Invoking: ARM v7 gcc compiler'
arm-none-eabi-gcc -Wall -O0 -g3 -I"D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_hw_platform_0" -c -fmessage-length=0 -MT"src/sd.o" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -I../../fsbl_bsp/ps7_cortexa9_0/include -MMD -MP -MF"src/sd.d" -MT"src/sd.o" -o "src/sd.o" "../src/sd.c"
'Finished building: ../src/sd.c'
' '
'Building target: fsbl.elf'
'Invoking: ARM v7 gcc linker'
arm-none-eabi-gcc -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -Wl,-build-id=none -specs=Xilinx.spec -Wl,-T -Wl,../src/lscript.ld -L../../fsbl_bsp/ps7_cortexa9_0/lib -o "fsbl.elf"  ./src/fsbl_handoff.o ./src/fsbl_hooks.o ./src/image_mover.o ./src/main.o ./src/md5.o ./src/mioInit.o ./src/nand.o ./src/nor.o ./src/pcap.o ./src/ps7_init.o ./src/qspi.o ./src/rsa.o ./src/sd.o   -Wl,--start-group,-lxil,-lgcc,-lc,--end-group -Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group -Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group
'Finished building target: fsbl.elf'
' '
'Invoking: ARM v7 Print Size'
arm-none-eabi-size fsbl.elf  |tee "fsbl.elf.size"
   text	   data	    bss	    dec	    hex	filename
  62276	  11692	  71848	 145816	  23998	fsbl.elf
'Finished building: fsbl.elf.size'
' '

15:56:07 Build Finished (took 3s.600ms)

