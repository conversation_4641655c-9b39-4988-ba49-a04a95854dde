<HTML><HEAD><TITLE>Device Usage Statistics Report</TITLE></HEAD>
<BODY TEXT='#000000' BGCOLOR='#FFFFFF' LINK='#0000EE' VLINK='#551A8B' ALINK='#FF0000'><H3>Device Usage Page (usage_statistics_webtalk.html)</H3>This HTML page displays the device usage statistics that will be sent to Xilinx.<BR>To see the actual file transmitted to Xilinx, please click <A HREF="./usage_statistics_webtalk.xml">here</A>.<BR><BR><HR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>software_version_and_target_device</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>beta</B></TD><TD>FALSE</TD>
  <TD BGCOLOR='#DBE5F1'><B>build_version</B></TD><TD>1756540</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>date_generated</B></TD><TD>Tue Oct 29 13:27:15 2019</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_platform</B></TD><TD>WIN64</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>product_version</B></TD><TD>Vivado v2016.4 (64-bit)</TD>
  <TD BGCOLOR='#DBE5F1'><B>project_id</B></TD><TD>d99595073c624624ae85d6bca6a02b61</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>project_iteration</B></TD><TD>3</TD>
  <TD BGCOLOR='#DBE5F1'><B>random_id</B></TD><TD>d5aa0efbea9552bb80be422e35c8ba78</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>registration_id</B></TD><TD>205589051_15690819_210558907_866</TD>
  <TD BGCOLOR='#DBE5F1'><B>route_design</B></TD><TD>TRUE</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_device</B></TD><TD>xc7z045</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_family</B></TD><TD>zynq</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_package</B></TD><TD>ffg900</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_speed</B></TD><TD>-2</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>tool_flow</B></TD><TD>Vivado</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>user_environment</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>cpu_name</B></TD><TD>AMD Ryzen 5 2400G with Radeon Vega Graphics    </TD>
  <TD BGCOLOR='#DBE5F1'><B>cpu_speed</B></TD><TD>3593 MHz</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>os_name</B></TD><TD>Microsoft Windows 8 or later , 64-bit</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_release</B></TD><TD>major release  (build 9200)</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>system_ram</B></TD><TD>16.000 GB</TD>
  <TD BGCOLOR='#DBE5F1'><B>total_processors</B></TD><TD>1</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>vivado_usage</B></TD></TR>
<TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>java_command_handlers</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>createtophdl=4</TD>
   <TD>customizersbblock=10</TD>
   <TD>editdelete=39</TD>
   <TD>managecompositetargets=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>newexporthardware=1</TD>
   <TD>newlaunchhardware=2</TD>
   <TD>openblockdesign=1</TD>
   <TD>projectsettingscmdhandler=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>reportipstatus=2</TD>
   <TD>runbitgen=10</TD>
   <TD>runsynthesis=1</TD>
   <TD>savedesign=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>saversbdesign=6</TD>
   <TD>showview=6</TD>
   <TD>timingconstraintswizard=1</TD>
   <TD>upgradeip=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>validatersbdesign=7</TD>
</TR>  </TABLE>
  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>other_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>guimode=3</TD>
   <TD>tclmode=1</TD>
</TR>  </TABLE>
</TR><TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>project_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>constraintsetcount=1</TD>
   <TD>core_container=false</TD>
   <TD>currentimplrun=impl_2</TD>
   <TD>currentsynthesisrun=synth_2</TD>
</TR><TR ALIGN='LEFT'>   <TD>default_library=xil_defaultlib</TD>
   <TD>designmode=RTL</TD>
   <TD>export_simulation_activehdl=1</TD>
   <TD>export_simulation_ies=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_modelsim=1</TD>
   <TD>export_simulation_questa=1</TD>
   <TD>export_simulation_riviera=1</TD>
   <TD>export_simulation_vcs=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_xsim=1</TD>
   <TD>implstrategy=Vivado Implementation Defaults</TD>
   <TD>launch_simulation_activehdl=0</TD>
   <TD>launch_simulation_ies=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_modelsim=0</TD>
   <TD>launch_simulation_questa=0</TD>
   <TD>launch_simulation_riviera=0</TD>
   <TD>launch_simulation_vcs=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_xsim=0</TD>
   <TD>simulator_language=Mixed</TD>
   <TD>srcsetcount=6</TD>
   <TD>synthesisstrategy=Flow_PerfOptimized_high</TD>
</TR><TR ALIGN='LEFT'>   <TD>target_language=Verilog</TD>
   <TD>target_simulator=XSim</TD>
   <TD>totalimplruns=2</TD>
   <TD>totalsynthesisruns=2</TD>
</TR>  </TABLE>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>unisim_transformation</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>post_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bibuf=130</TD>
    <TD>bufg=3</TD>
    <TD>bufgctrl=1</TD>
    <TD>bufr=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>carry4=777</TD>
    <TD>dsp48e1=60</TD>
    <TD>fdce=7232</TD>
    <TD>fdpe=16</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdre=18915</TD>
    <TD>fdse=328</TD>
    <TD>gnd=395</TD>
    <TD>ibuf=16</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibufds=8</TD>
    <TD>iddr=7</TD>
    <TD>idelayctrl=1</TD>
    <TD>idelaye2=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut1=1219</TD>
    <TD>lut2=2402</TD>
    <TD>lut3=2946</TD>
    <TD>lut4=1953</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut5=2450</TD>
    <TD>lut6=6167</TD>
    <TD>muxf7=426</TD>
    <TD>muxf8=102</TD>
</TR><TR ALIGN='LEFT'>    <TD>obuf=7</TD>
    <TD>obufds=8</TD>
    <TD>obuft=15</TD>
    <TD>oddr=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>ps7=1</TD>
    <TD>ramb36e1=4</TD>
    <TD>srl16e=135</TD>
    <TD>srlc32e=141</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcc=455</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>pre_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bibuf=130</TD>
    <TD>bufg=3</TD>
    <TD>bufgctrl=1</TD>
    <TD>bufr=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>carry4=777</TD>
    <TD>dsp48e1=60</TD>
    <TD>fdce=7232</TD>
    <TD>fdpe=16</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdre=18915</TD>
    <TD>fdse=328</TD>
    <TD>gnd=395</TD>
    <TD>ibuf=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibufds=8</TD>
    <TD>iddr=7</TD>
    <TD>idelayctrl=1</TD>
    <TD>idelaye2=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>iobuf=15</TD>
    <TD>lut1=1219</TD>
    <TD>lut2=2402</TD>
    <TD>lut3=2946</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut4=1953</TD>
    <TD>lut5=2450</TD>
    <TD>lut6=6167</TD>
    <TD>muxf7=426</TD>
</TR><TR ALIGN='LEFT'>    <TD>muxf8=102</TD>
    <TD>obuf=7</TD>
    <TD>obufds=8</TD>
    <TD>oddr=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>ps7=1</TD>
    <TD>ramb36e1=4</TD>
    <TD>srl16e=135</TD>
    <TD>srlc32e=141</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcc=455</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>power_opt_design</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options_spo</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-cell_types=default::all</TD>
    <TD>-clocks=default::[not_specified]</TD>
    <TD>-exclude_cells=default::[not_specified]</TD>
    <TD>-include_cells=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bram_ports_augmented=1</TD>
    <TD>bram_ports_newly_gated=6</TD>
    <TD>bram_ports_total=8</TD>
    <TD>flow_state=default</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_augmented=0</TD>
    <TD>slice_registers_newly_gated=0</TD>
    <TD>slice_registers_total=22149</TD>
    <TD>srls_augmented=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>srls_newly_gated=0</TD>
    <TD>srls_total=276</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>ip_statistics</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>IP_Integrator/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bdsource=USER</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>maxhierdepth=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>numblks=38</TD>
    <TD>numhdlrefblks=0</TD>
    <TD>numhierblks=18</TD>
    <TD>numhlsblks=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>numnonxlnxblks=9</TD>
    <TD>numpkgbdblks=0</TD>
    <TD>numreposblks=20</TD>
    <TD>numsysgenblks=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>synth_mode=Global</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=BlockDiagram</TD>
    <TD>x_ipname=system</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.00.a</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_crossbar_v2_1_12_axi_crossbar/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=32</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_protocol=0</TD>
    <TD>c_axi_ruser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_connectivity_mode=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_addr_width=0x0000000c0000000c0000001000000000000000000000000000000000000000000000000000000000</TD>
    <TD>c_m_axi_base_addr=0x000000007c420000000000007c4000000000000079020000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff</TD>
    <TD>c_m_axi_read_connectivity=0x00000001000000010000000100000001000000010000000100000001000000010000000100000001</TD>
    <TD>c_m_axi_read_issuing=0x00000008000000080000000200000008000000080000000800000008000000080000000800000008</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_secure=0x00000000000000000000000000000000000000000000000000000000000000000000000000000000</TD>
    <TD>c_m_axi_write_connectivity=0x00000001000000010000000100000001000000010000000100000001000000010000000100000001</TD>
    <TD>c_m_axi_write_issuing=0x00000008000000080000000200000008000000080000000800000008000000080000000800000008</TD>
    <TD>c_num_addr_ranges=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_master_slots=10</TD>
    <TD>c_num_slave_slots=1</TD>
    <TD>c_r_register=0</TD>
    <TD>c_s_axi_arb_priority=0x00000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_base_id=0x00000000</TD>
    <TD>c_s_axi_read_acceptance=0x00000008</TD>
    <TD>c_s_axi_single_thread=0x00000000</TD>
    <TD>c_s_axi_thread_id_width=0x0000000c</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_write_acceptance=0x00000008</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=12</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_crossbar</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_protocol_converter_v2_1_11_axi_protocol_converter/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=16</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_ruser_width=1</TD>
    <TD>c_axi_supports_read=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_supports_write=1</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_ignore_id=0</TD>
    <TD>c_m_axi_protocol=2</TD>
    <TD>c_s_axi_protocol=0</TD>
    <TD>c_translation_mode=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=11</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_protocol_converter</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_protocol_converter_v2_1_11_axi_protocol_converter/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=12</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_ruser_width=1</TD>
    <TD>c_axi_supports_read=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_supports_write=1</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_ignore_id=0</TD>
    <TD>c_m_axi_protocol=2</TD>
    <TD>c_s_axi_protocol=0</TD>
    <TD>c_translation_mode=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=11</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_protocol_converter</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_protocol_converter_v2_1_11_axi_protocol_converter/3</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=12</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_ruser_width=1</TD>
    <TD>c_axi_supports_read=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_supports_write=1</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_ignore_id=0</TD>
    <TD>c_m_axi_protocol=2</TD>
    <TD>c_s_axi_protocol=0</TD>
    <TD>c_translation_mode=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=11</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_protocol_converter</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_protocol_converter_v2_1_11_axi_protocol_converter/4</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=32</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_ruser_width=1</TD>
    <TD>c_axi_supports_read=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_supports_write=1</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_ignore_id=0</TD>
    <TD>c_m_axi_protocol=0</TD>
    <TD>c_s_axi_protocol=1</TD>
    <TD>c_translation_mode=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=11</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_protocol_converter</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>proc_sys_reset/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aux_reset_high=0</TD>
    <TD>c_aux_rst_width=4</TD>
    <TD>c_ext_reset_high=0</TD>
    <TD>c_ext_rst_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_family=zynq</TD>
    <TD>c_num_bus_rst=1</TD>
    <TD>c_num_interconnect_aresetn=1</TD>
    <TD>c_num_perp_aresetn=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_perp_rst=1</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=proc_sys_reset</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=5.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>proc_sys_reset/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aux_reset_high=0</TD>
    <TD>c_aux_rst_width=4</TD>
    <TD>c_ext_reset_high=0</TD>
    <TD>c_ext_rst_width=4</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_family=zynq</TD>
    <TD>c_num_bus_rst=1</TD>
    <TD>c_num_interconnect_aresetn=1</TD>
    <TD>c_num_perp_aresetn=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_perp_rst=1</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=proc_sys_reset</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=5.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>processing_system7_v5.5_user_configuration/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>pcw_apu_clk_ratio_enable=6:2:1</TD>
    <TD>pcw_apu_peripheral_freqmhz=666.666667</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_armpll_ctrl_fbdiv=40</TD>
    <TD>pcw_can0_grp_clk_enable=0</TD>
    <TD>pcw_can0_peripheral_clksrc=External</TD>
    <TD>pcw_can0_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_can0_peripheral_freqmhz=-1</TD>
    <TD>pcw_can1_grp_clk_enable=0</TD>
    <TD>pcw_can1_peripheral_clksrc=External</TD>
    <TD>pcw_can1_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_can1_peripheral_freqmhz=-1</TD>
    <TD>pcw_can_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_can_peripheral_freqmhz=100</TD>
    <TD>pcw_cpu_cpu_pll_freqmhz=1333.333</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_cpu_peripheral_clksrc=ARM PLL</TD>
    <TD>pcw_crystal_peripheral_freqmhz=33.333333</TD>
    <TD>pcw_dci_peripheral_clksrc=DDR PLL</TD>
    <TD>pcw_dci_peripheral_freqmhz=10.159</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ddr_ddr_pll_freqmhz=1066.667</TD>
    <TD>pcw_ddr_hpr_to_critical_priority_level=15</TD>
    <TD>pcw_ddr_hprlpr_queue_partition=HPR(0)/LPR(32)</TD>
    <TD>pcw_ddr_lpr_to_critical_priority_level=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ddr_peripheral_clksrc=DDR PLL</TD>
    <TD>pcw_ddr_port0_hpr_enable=0</TD>
    <TD>pcw_ddr_port1_hpr_enable=0</TD>
    <TD>pcw_ddr_port2_hpr_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ddr_port3_hpr_enable=0</TD>
    <TD>pcw_ddr_write_to_critical_priority_level=2</TD>
    <TD>pcw_ddrpll_ctrl_fbdiv=32</TD>
    <TD>pcw_enet0_grp_mdio_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_enet0_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_enet0_peripheral_enable=0</TD>
    <TD>pcw_enet0_peripheral_freqmhz=1000 Mbps</TD>
    <TD>pcw_enet0_reset_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_enet0_reset_io=MIO 40</TD>
    <TD>pcw_enet1_enet1_io=MIO 28 .. 39</TD>
    <TD>pcw_enet1_grp_mdio_enable=1</TD>
    <TD>pcw_enet1_peripheral_clksrc=IO PLL</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_enet1_peripheral_enable=1</TD>
    <TD>pcw_enet1_peripheral_freqmhz=1000 Mbps</TD>
    <TD>pcw_enet1_reset_enable=1</TD>
    <TD>pcw_enet1_reset_io=MIO 40</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_enet_reset_polarity=Active Low</TD>
    <TD>pcw_fclk0_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_fclk1_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_fclk2_peripheral_clksrc=IO PLL</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_fclk3_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_fpga0_peripheral_freqmhz=100.0</TD>
    <TD>pcw_fpga1_peripheral_freqmhz=200.0</TD>
    <TD>pcw_fpga2_peripheral_freqmhz=50</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_fpga3_peripheral_freqmhz=50</TD>
    <TD>pcw_fpga_fclk0_enable=1</TD>
    <TD>pcw_fpga_fclk1_enable=1</TD>
    <TD>pcw_fpga_fclk2_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_fpga_fclk3_enable=0</TD>
    <TD>pcw_gpio_emio_gpio_enable=1</TD>
    <TD>pcw_gpio_emio_gpio_io=64</TD>
    <TD>pcw_gpio_mio_gpio_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_gpio_mio_gpio_io=MIO</TD>
    <TD>pcw_gpio_peripheral_enable=0</TD>
    <TD>pcw_i2c0_grp_int_enable=0</TD>
    <TD>pcw_i2c0_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_i2c0_reset_enable=0</TD>
    <TD>pcw_i2c1_grp_int_enable=0</TD>
    <TD>pcw_i2c1_peripheral_enable=0</TD>
    <TD>pcw_i2c1_reset_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_i2c_reset_polarity=Active Low</TD>
    <TD>pcw_io_io_pll_freqmhz=1000.000</TD>
    <TD>pcw_iopll_ctrl_fbdiv=30</TD>
    <TD>pcw_irq_f2p_mode=REVERSE</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_m_axi_gp0_freqmhz=100</TD>
    <TD>pcw_m_axi_gp1_freqmhz=10</TD>
    <TD>pcw_nand_cycles_t_ar=1</TD>
    <TD>pcw_nand_cycles_t_clr=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nand_cycles_t_rc=11</TD>
    <TD>pcw_nand_cycles_t_rea=1</TD>
    <TD>pcw_nand_cycles_t_rr=1</TD>
    <TD>pcw_nand_cycles_t_wc=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nand_cycles_t_wp=1</TD>
    <TD>pcw_nand_grp_d8_enable=0</TD>
    <TD>pcw_nand_peripheral_enable=0</TD>
    <TD>pcw_nor_cs0_t_ceoe=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_cs0_t_pc=1</TD>
    <TD>pcw_nor_cs0_t_rc=11</TD>
    <TD>pcw_nor_cs0_t_tr=1</TD>
    <TD>pcw_nor_cs0_t_wc=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_cs0_t_wp=1</TD>
    <TD>pcw_nor_cs0_we_time=0</TD>
    <TD>pcw_nor_cs1_t_ceoe=1</TD>
    <TD>pcw_nor_cs1_t_pc=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_cs1_t_rc=11</TD>
    <TD>pcw_nor_cs1_t_tr=1</TD>
    <TD>pcw_nor_cs1_t_wc=11</TD>
    <TD>pcw_nor_cs1_t_wp=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_cs1_we_time=0</TD>
    <TD>pcw_nor_grp_a25_enable=0</TD>
    <TD>pcw_nor_grp_cs0_enable=0</TD>
    <TD>pcw_nor_grp_cs1_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_grp_sram_cs0_enable=0</TD>
    <TD>pcw_nor_grp_sram_cs1_enable=0</TD>
    <TD>pcw_nor_grp_sram_int_enable=0</TD>
    <TD>pcw_nor_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs0_t_ceoe=1</TD>
    <TD>pcw_nor_sram_cs0_t_pc=1</TD>
    <TD>pcw_nor_sram_cs0_t_rc=11</TD>
    <TD>pcw_nor_sram_cs0_t_tr=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs0_t_wc=11</TD>
    <TD>pcw_nor_sram_cs0_t_wp=1</TD>
    <TD>pcw_nor_sram_cs0_we_time=0</TD>
    <TD>pcw_nor_sram_cs1_t_ceoe=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs1_t_pc=1</TD>
    <TD>pcw_nor_sram_cs1_t_rc=11</TD>
    <TD>pcw_nor_sram_cs1_t_tr=1</TD>
    <TD>pcw_nor_sram_cs1_t_wc=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs1_t_wp=1</TD>
    <TD>pcw_nor_sram_cs1_we_time=0</TD>
    <TD>pcw_override_basic_clock=0</TD>
    <TD>pcw_pcap_peripheral_clksrc=IO PLL</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_pcap_peripheral_freqmhz=200</TD>
    <TD>pcw_pjtag_peripheral_enable=0</TD>
    <TD>pcw_preset_bank0_voltage=LVCMOS 3.3V</TD>
    <TD>pcw_preset_bank1_voltage=LVCMOS 2.5V</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_qspi_grp_fbclk_enable=0</TD>
    <TD>pcw_qspi_grp_io1_enable=0</TD>
    <TD>pcw_qspi_grp_single_ss_enable=1</TD>
    <TD>pcw_qspi_grp_single_ss_io=MIO 1 .. 6</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_qspi_grp_ss1_enable=0</TD>
    <TD>pcw_qspi_internal_highaddress=0xFCFFFFFF</TD>
    <TD>pcw_qspi_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_qspi_peripheral_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_qspi_peripheral_freqmhz=200</TD>
    <TD>pcw_qspi_qspi_io=MIO 1 .. 6</TD>
    <TD>pcw_s_axi_acp_freqmhz=10</TD>
    <TD>pcw_s_axi_gp0_freqmhz=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_s_axi_gp1_freqmhz=10</TD>
    <TD>pcw_s_axi_hp0_data_width=64</TD>
    <TD>pcw_s_axi_hp0_freqmhz=100</TD>
    <TD>pcw_s_axi_hp1_data_width=64</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_s_axi_hp1_freqmhz=100</TD>
    <TD>pcw_s_axi_hp2_data_width=64</TD>
    <TD>pcw_s_axi_hp2_freqmhz=100</TD>
    <TD>pcw_s_axi_hp3_data_width=64</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_s_axi_hp3_freqmhz=10</TD>
    <TD>pcw_sd0_grp_cd_enable=0</TD>
    <TD>pcw_sd0_grp_pow_enable=0</TD>
    <TD>pcw_sd0_grp_wp_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_sd0_peripheral_enable=0</TD>
    <TD>pcw_sd1_grp_cd_enable=0</TD>
    <TD>pcw_sd1_grp_pow_enable=0</TD>
    <TD>pcw_sd1_grp_wp_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_sd1_peripheral_enable=0</TD>
    <TD>pcw_sdio_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_sdio_peripheral_freqmhz=100</TD>
    <TD>pcw_smc_peripheral_clksrc=IO PLL</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_smc_peripheral_freqmhz=100</TD>
    <TD>pcw_spi0_grp_ss0_enable=1</TD>
    <TD>pcw_spi0_grp_ss0_io=EMIO</TD>
    <TD>pcw_spi0_grp_ss1_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi0_grp_ss1_io=EMIO</TD>
    <TD>pcw_spi0_grp_ss2_enable=1</TD>
    <TD>pcw_spi0_grp_ss2_io=EMIO</TD>
    <TD>pcw_spi0_peripheral_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi0_spi0_io=EMIO</TD>
    <TD>pcw_spi1_grp_ss0_enable=1</TD>
    <TD>pcw_spi1_grp_ss0_io=EMIO</TD>
    <TD>pcw_spi1_grp_ss1_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi1_grp_ss1_io=EMIO</TD>
    <TD>pcw_spi1_grp_ss2_enable=1</TD>
    <TD>pcw_spi1_grp_ss2_io=EMIO</TD>
    <TD>pcw_spi1_peripheral_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi1_spi1_io=EMIO</TD>
    <TD>pcw_spi_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_spi_peripheral_freqmhz=166.666666</TD>
    <TD>pcw_tpiu_peripheral_clksrc=External</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_tpiu_peripheral_freqmhz=200</TD>
    <TD>pcw_trace_grp_16bit_enable=0</TD>
    <TD>pcw_trace_grp_2bit_enable=0</TD>
    <TD>pcw_trace_grp_32bit_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_trace_grp_4bit_enable=0</TD>
    <TD>pcw_trace_grp_8bit_enable=0</TD>
    <TD>pcw_trace_peripheral_enable=0</TD>
    <TD>pcw_ttc0_clk0_peripheral_clksrc=CPU_1X</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc0_clk0_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc0_clk1_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc0_clk1_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc0_clk2_peripheral_clksrc=CPU_1X</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc0_clk2_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc0_peripheral_enable=0</TD>
    <TD>pcw_ttc1_clk0_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc1_clk0_peripheral_freqmhz=133.333333</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc1_clk1_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc1_clk1_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc1_clk2_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc1_clk2_peripheral_freqmhz=133.333333</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc1_peripheral_enable=0</TD>
    <TD>pcw_ttc_peripheral_freqmhz=50</TD>
    <TD>pcw_uart0_baud_rate=115200</TD>
    <TD>pcw_uart0_grp_full_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uart0_peripheral_enable=0</TD>
    <TD>pcw_uart1_baud_rate=115200</TD>
    <TD>pcw_uart1_grp_full_enable=0</TD>
    <TD>pcw_uart1_peripheral_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uart1_uart1_io=MIO 12 .. 13</TD>
    <TD>pcw_uart_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_uart_peripheral_freqmhz=50</TD>
    <TD>pcw_uiparam_ddr_adv_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_al=0</TD>
    <TD>pcw_uiparam_ddr_bank_addr_count=3</TD>
    <TD>pcw_uiparam_ddr_bl=8</TD>
    <TD>pcw_uiparam_ddr_board_delay0=0.41</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_board_delay1=0.411</TD>
    <TD>pcw_uiparam_ddr_board_delay2=0.341</TD>
    <TD>pcw_uiparam_ddr_board_delay3=0.358</TD>
    <TD>pcw_uiparam_ddr_bus_width=32 Bit</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_cl=7</TD>
    <TD>pcw_uiparam_ddr_clock_0_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_clock_0_package_length=61.0905</TD>
    <TD>pcw_uiparam_ddr_clock_0_propogation_delay=160</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_clock_1_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_clock_1_package_length=61.0905</TD>
    <TD>pcw_uiparam_ddr_clock_1_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_clock_2_length_mm=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_clock_2_package_length=61.0905</TD>
    <TD>pcw_uiparam_ddr_clock_2_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_clock_3_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_clock_3_package_length=61.0905</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_clock_3_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_clock_stop_en=0</TD>
    <TD>pcw_uiparam_ddr_col_addr_count=10</TD>
    <TD>pcw_uiparam_ddr_cwl=6</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_device_capacity=1024 MBits</TD>
    <TD>pcw_uiparam_ddr_dq_0_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dq_0_package_length=64.1705</TD>
    <TD>pcw_uiparam_ddr_dq_0_propogation_delay=160</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dq_1_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dq_1_package_length=63.686</TD>
    <TD>pcw_uiparam_ddr_dq_1_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dq_2_length_mm=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dq_2_package_length=68.46</TD>
    <TD>pcw_uiparam_ddr_dq_2_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dq_3_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dq_3_package_length=105.4895</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dq_3_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_0_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dqs_0_package_length=68.4725</TD>
    <TD>pcw_uiparam_ddr_dqs_0_propogation_delay=160</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_1_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dqs_1_package_length=71.086</TD>
    <TD>pcw_uiparam_ddr_dqs_1_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_2_length_mm=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_2_package_length=66.794</TD>
    <TD>pcw_uiparam_ddr_dqs_2_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_3_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dqs_3_package_length=108.7385</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_3_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_0=0.025</TD>
    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_1=0.028</TD>
    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_2=-0.009</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_3=-0.061</TD>
    <TD>pcw_uiparam_ddr_dram_width=16 Bits</TD>
    <TD>pcw_uiparam_ddr_ecc=Disabled</TD>
    <TD>pcw_uiparam_ddr_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_freq_mhz=533.333313</TD>
    <TD>pcw_uiparam_ddr_high_temp=Normal (0-85)</TD>
    <TD>pcw_uiparam_ddr_memory_type=DDR 3</TD>
    <TD>pcw_uiparam_ddr_partno=MT41J64M16 JT-125G</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_row_addr_count=13</TD>
    <TD>pcw_uiparam_ddr_speed_bin=DDR3_1066F</TD>
    <TD>pcw_uiparam_ddr_t_faw=40.0</TD>
    <TD>pcw_uiparam_ddr_t_ras_min=35.0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_t_rc=48.75</TD>
    <TD>pcw_uiparam_ddr_t_rcd=7</TD>
    <TD>pcw_uiparam_ddr_t_rp=7</TD>
    <TD>pcw_uiparam_ddr_train_data_eye=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_train_read_gate=1</TD>
    <TD>pcw_uiparam_ddr_train_write_level=1</TD>
    <TD>pcw_uiparam_ddr_use_internal_vref=1</TD>
    <TD>pcw_usb0_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_usb0_peripheral_freqmhz=60</TD>
    <TD>pcw_usb0_reset_enable=0</TD>
    <TD>pcw_usb1_peripheral_enable=0</TD>
    <TD>pcw_usb1_peripheral_freqmhz=60</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_usb1_reset_enable=0</TD>
    <TD>pcw_usb_reset_polarity=Active Low</TD>
    <TD>pcw_use_cross_trigger=0</TD>
    <TD>pcw_use_m_axi_gp0=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_use_m_axi_gp1=0</TD>
    <TD>pcw_use_s_axi_acp=0</TD>
    <TD>pcw_use_s_axi_gp0=0</TD>
    <TD>pcw_use_s_axi_gp1=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_use_s_axi_hp0=1</TD>
    <TD>pcw_use_s_axi_hp1=1</TD>
    <TD>pcw_use_s_axi_hp2=1</TD>
    <TD>pcw_use_s_axi_hp3=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_wdt_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_wdt_peripheral_enable=0</TD>
    <TD>pcw_wdt_peripheral_freqmhz=133.333333</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>processing_system7_v5_5_processing_system7/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_dm_width=4</TD>
    <TD>c_dq_width=32</TD>
    <TD>c_dqs_width=4</TD>
    <TD>c_emio_gpio_width=64</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_en_emio_enet0=0</TD>
    <TD>c_en_emio_enet1=0</TD>
    <TD>c_en_emio_pjtag=0</TD>
    <TD>c_en_emio_trace=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fclk_clk0_buf=TRUE</TD>
    <TD>c_fclk_clk1_buf=TRUE</TD>
    <TD>c_fclk_clk2_buf=FALSE</TD>
    <TD>c_fclk_clk3_buf=FALSE</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gp0_en_modifiable_txn=0</TD>
    <TD>c_gp1_en_modifiable_txn=0</TD>
    <TD>c_include_acp_trans_check=0</TD>
    <TD>c_include_trace_buffer=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_irq_f2p_mode=REVERSE</TD>
    <TD>c_m_axi_gp0_enable_static_remap=0</TD>
    <TD>c_m_axi_gp0_id_width=12</TD>
    <TD>c_m_axi_gp0_thread_id_width=12</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_gp1_enable_static_remap=0</TD>
    <TD>c_m_axi_gp1_id_width=12</TD>
    <TD>c_m_axi_gp1_thread_id_width=12</TD>
    <TD>c_mio_primitive=54</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_f2p_intr_inputs=16</TD>
    <TD>c_package_name=clg484</TD>
    <TD>c_ps7_si_rev=PRODUCTION</TD>
    <TD>c_s_axi_acp_aruser_val=31</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_acp_awuser_val=31</TD>
    <TD>c_s_axi_acp_id_width=3</TD>
    <TD>c_s_axi_gp0_id_width=6</TD>
    <TD>c_s_axi_gp1_id_width=6</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_hp0_data_width=64</TD>
    <TD>c_s_axi_hp0_id_width=6</TD>
    <TD>c_s_axi_hp1_data_width=64</TD>
    <TD>c_s_axi_hp1_id_width=6</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_hp2_data_width=64</TD>
    <TD>c_s_axi_hp2_id_width=6</TD>
    <TD>c_s_axi_hp3_data_width=64</TD>
    <TD>c_s_axi_hp3_id_width=6</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_trace_buffer_clock_delay=12</TD>
    <TD>c_trace_buffer_fifo_size=128</TD>
    <TD>c_trace_internal_width=2</TD>
    <TD>c_trace_pipeline_width=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_use_axi_nonsecure=0</TD>
    <TD>c_use_default_acp_user_val=0</TD>
    <TD>c_use_m_axi_gp0=1</TD>
    <TD>c_use_m_axi_gp1=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_use_s_axi_acp=0</TD>
    <TD>c_use_s_axi_gp0=0</TD>
    <TD>c_use_s_axi_hp0=1</TD>
    <TD>c_use_s_axi_hp1=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_use_s_axi_hp2=1</TD>
    <TD>c_use_s_axi_hp3=0</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>use_trace_data_edge_detector=0</TD>
    <TD>x_ipcorerevision=3</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipname=processing_system7</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipversion=5.5</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>util_reduced_logic/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_operation=and</TD>
    <TD>c_size=2</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=2</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=util_reduced_logic</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xlconcat/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>dout_width=16</TD>
    <TD>in0_width=1</TD>
    <TD>in10_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in11_width=1</TD>
    <TD>in12_width=1</TD>
    <TD>in13_width=1</TD>
    <TD>in14_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in15_width=1</TD>
    <TD>in16_width=1</TD>
    <TD>in17_width=1</TD>
    <TD>in18_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in19_width=1</TD>
    <TD>in1_width=1</TD>
    <TD>in20_width=1</TD>
    <TD>in21_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in22_width=1</TD>
    <TD>in23_width=1</TD>
    <TD>in24_width=1</TD>
    <TD>in25_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in26_width=1</TD>
    <TD>in27_width=1</TD>
    <TD>in28_width=1</TD>
    <TD>in29_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in2_width=1</TD>
    <TD>in30_width=1</TD>
    <TD>in31_width=1</TD>
    <TD>in3_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in4_width=1</TD>
    <TD>in5_width=1</TD>
    <TD>in6_width=1</TD>
    <TD>in7_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in8_width=1</TD>
    <TD>in9_width=1</TD>
    <TD>iptotal=1</TD>
    <TD>num_ports=16</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=2</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=xlconcat</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xlconcat/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>dout_width=2</TD>
    <TD>in0_width=1</TD>
    <TD>in10_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in11_width=1</TD>
    <TD>in12_width=1</TD>
    <TD>in13_width=1</TD>
    <TD>in14_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in15_width=1</TD>
    <TD>in16_width=1</TD>
    <TD>in17_width=1</TD>
    <TD>in18_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in19_width=1</TD>
    <TD>in1_width=1</TD>
    <TD>in20_width=1</TD>
    <TD>in21_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in22_width=1</TD>
    <TD>in23_width=1</TD>
    <TD>in24_width=1</TD>
    <TD>in25_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in26_width=1</TD>
    <TD>in27_width=1</TD>
    <TD>in28_width=1</TD>
    <TD>in29_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in2_width=1</TD>
    <TD>in30_width=1</TD>
    <TD>in31_width=1</TD>
    <TD>in3_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in4_width=1</TD>
    <TD>in5_width=1</TD>
    <TD>in6_width=1</TD>
    <TD>in7_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in8_width=1</TD>
    <TD>in9_width=1</TD>
    <TD>iptotal=1</TD>
    <TD>num_ports=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=2</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=xlconcat</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_drc</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-append=default::[not_specified]</TD>
    <TD>-checks=default::[not_specified]</TD>
    <TD>-fail_on=default::[not_specified]</TD>
    <TD>-force=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-format=default::[not_specified]</TD>
    <TD>-messages=default::[not_specified]</TD>
    <TD>-name=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-ruledecks=default::[not_specified]</TD>
    <TD>-upgrade_cw=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>results</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>aval-4=56</TD>
    <TD>check-3=1</TD>
    <TD>dpop-1=4</TD>
    <TD>reqp-1839=20</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_methodology</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-append=default::[not_specified]</TD>
    <TD>-checks=default::[not_specified]</TD>
    <TD>-fail_on=default::[not_specified]</TD>
    <TD>-force=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-format=default::[not_specified]</TD>
    <TD>-messages=default::[not_specified]</TD>
    <TD>-name=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>results</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>pdrc-190=4</TD>
    <TD>timing-10=1</TD>
    <TD>timing-18=32</TD>
    <TD>timing-28=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>timing-9=1</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_power</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-advisory=default::[not_specified]</TD>
    <TD>-append=default::[not_specified]</TD>
    <TD>-file=[specified]</TD>
    <TD>-format=default::text</TD>
</TR><TR ALIGN='LEFT'>    <TD>-hier=default::power</TD>
    <TD>-l=default::[not_specified]</TD>
    <TD>-name=default::[not_specified]</TD>
    <TD>-no_propagation=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-return_string=default::[not_specified]</TD>
    <TD>-rpx=[specified]</TD>
    <TD>-verbose=default::[not_specified]</TD>
    <TD>-vid=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-xpe=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>airflow=250 (LFM)</TD>
    <TD>ambient_temp=25.0 (C)</TD>
    <TD>bi-dir_toggle=12.500000</TD>
    <TD>bidir_output_enable=1.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>board_layers=12to15 (12 to 15 Layers)</TD>
    <TD>board_selection=medium (10&quot;x10&quot;)</TD>
    <TD>bram=0.009336</TD>
    <TD>clocks=0.184502</TD>
</TR><TR ALIGN='LEFT'>    <TD>confidence_level_clock_activity=High</TD>
    <TD>confidence_level_design_state=High</TD>
    <TD>confidence_level_device_models=High</TD>
    <TD>confidence_level_internal_activity=Medium</TD>
</TR><TR ALIGN='LEFT'>    <TD>confidence_level_io_activity=Low</TD>
    <TD>confidence_level_overall=Low</TD>
    <TD>customer=TBD</TD>
    <TD>customer_class=TBD</TD>
</TR><TR ALIGN='LEFT'>    <TD>devstatic=0.256536</TD>
    <TD>die=xc7z045ffg900-2</TD>
    <TD>dsp=0.130862</TD>
    <TD>dsp_output_toggle=12.500000</TD>
</TR><TR ALIGN='LEFT'>    <TD>dynamic=2.260907</TD>
    <TD>effective_thetaja=1.8</TD>
    <TD>enable_probability=0.990000</TD>
    <TD>family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>ff_toggle=12.500000</TD>
    <TD>flow_state=routed</TD>
    <TD>heatsink=medium (Medium Profile)</TD>
    <TD>i/o=0.213962</TD>
</TR><TR ALIGN='LEFT'>    <TD>input_toggle=12.500000</TD>
    <TD>junction_temp=29.5 (C)</TD>
    <TD>logic=0.071252</TD>
    <TD>mgtavcc_dynamic_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>mgtavcc_static_current=0.000000</TD>
    <TD>mgtavcc_total_current=0.000000</TD>
    <TD>mgtavcc_voltage=1.000000</TD>
    <TD>mgtavtt_dynamic_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>mgtavtt_static_current=0.000000</TD>
    <TD>mgtavtt_total_current=0.000000</TD>
    <TD>mgtavtt_voltage=1.200000</TD>
    <TD>mgtvccaux_dynamic_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>mgtvccaux_static_current=0.000000</TD>
    <TD>mgtvccaux_total_current=0.000000</TD>
    <TD>mgtvccaux_voltage=1.800000</TD>
    <TD>netlist_net_matched=NA</TD>
</TR><TR ALIGN='LEFT'>    <TD>off-chip_power=0.009800</TD>
    <TD>on-chip_power=2.517444</TD>
    <TD>output_enable=1.000000</TD>
    <TD>output_load=5.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>output_toggle=12.500000</TD>
    <TD>package=ffg900</TD>
    <TD>pct_clock_constrained=17.000000</TD>
    <TD>pct_inputs_defined=4</TD>
</TR><TR ALIGN='LEFT'>    <TD>platform=nt64</TD>
    <TD>process=typical</TD>
    <TD>ps7=1.542892</TD>
    <TD>ram_enable=50.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>ram_write=50.000000</TD>
    <TD>read_saif=False</TD>
    <TD>set/reset_probability=0.000000</TD>
    <TD>signal_rate=False</TD>
</TR><TR ALIGN='LEFT'>    <TD>signals=0.128192</TD>
    <TD>simulation_file=None</TD>
    <TD>speedgrade=-2</TD>
    <TD>static_prob=False</TD>
</TR><TR ALIGN='LEFT'>    <TD>temp_grade=commercial</TD>
    <TD>thetajb=2.7 (C/W)</TD>
    <TD>thetasa=3.3 (C/W)</TD>
    <TD>toggle_rate=False</TD>
</TR><TR ALIGN='LEFT'>    <TD>user_board_temp=25.0 (C)</TD>
    <TD>user_effective_thetaja=1.8</TD>
    <TD>user_junc_temp=29.5 (C)</TD>
    <TD>user_thetajb=2.7 (C/W)</TD>
</TR><TR ALIGN='LEFT'>    <TD>user_thetasa=3.3 (C/W)</TD>
    <TD>vccadc_dynamic_current=0.000000</TD>
    <TD>vccadc_static_current=0.020000</TD>
    <TD>vccadc_total_current=0.020000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccadc_voltage=1.800000</TD>
    <TD>vccaux_dynamic_current=0.023912</TD>
    <TD>vccaux_io_dynamic_current=0.000000</TD>
    <TD>vccaux_io_static_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccaux_io_total_current=0.000000</TD>
    <TD>vccaux_io_voltage=1.800000</TD>
    <TD>vccaux_static_current=0.054019</TD>
    <TD>vccaux_total_current=0.077931</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccaux_voltage=1.800000</TD>
    <TD>vccbram_dynamic_current=0.000348</TD>
    <TD>vccbram_static_current=0.002011</TD>
    <TD>vccbram_total_current=0.002359</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccbram_voltage=1.000000</TD>
    <TD>vccint_dynamic_current=0.513368</TD>
    <TD>vccint_static_current=0.060857</TD>
    <TD>vccint_total_current=0.574225</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccint_voltage=1.000000</TD>
    <TD>vcco12_dynamic_current=0.000000</TD>
    <TD>vcco12_static_current=0.000000</TD>
    <TD>vcco12_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco12_voltage=1.200000</TD>
    <TD>vcco135_dynamic_current=0.000000</TD>
    <TD>vcco135_static_current=0.000000</TD>
    <TD>vcco135_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco135_voltage=1.350000</TD>
    <TD>vcco15_dynamic_current=0.000000</TD>
    <TD>vcco15_static_current=0.001000</TD>
    <TD>vcco15_total_current=0.001000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco15_voltage=1.500000</TD>
    <TD>vcco18_dynamic_current=0.095032</TD>
    <TD>vcco18_static_current=0.001000</TD>
    <TD>vcco18_total_current=0.096032</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco18_voltage=1.800000</TD>
    <TD>vcco25_dynamic_current=0.000000</TD>
    <TD>vcco25_static_current=0.001000</TD>
    <TD>vcco25_total_current=0.001000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco25_voltage=2.500000</TD>
    <TD>vcco33_dynamic_current=0.000000</TD>
    <TD>vcco33_static_current=0.001000</TD>
    <TD>vcco33_total_current=0.001000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco33_voltage=3.300000</TD>
    <TD>vcco_ddr_dynamic_current=0.456904</TD>
    <TD>vcco_ddr_static_current=0.002000</TD>
    <TD>vcco_ddr_total_current=0.458904</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco_ddr_voltage=1.500000</TD>
    <TD>vcco_mio0_dynamic_current=0.001500</TD>
    <TD>vcco_mio0_static_current=0.001000</TD>
    <TD>vcco_mio0_total_current=0.002500</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco_mio0_voltage=3.300000</TD>
    <TD>vcco_mio1_dynamic_current=0.002187</TD>
    <TD>vcco_mio1_static_current=0.001000</TD>
    <TD>vcco_mio1_total_current=0.003187</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco_mio1_voltage=2.500000</TD>
    <TD>vccpaux_dynamic_current=0.051022</TD>
    <TD>vccpaux_static_current=0.010330</TD>
    <TD>vccpaux_total_current=0.061352</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccpaux_voltage=1.800000</TD>
    <TD>vccpint_dynamic_current=0.730298</TD>
    <TD>vccpint_static_current=0.018540</TD>
    <TD>vccpint_total_current=0.748838</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccpint_voltage=1.000000</TD>
    <TD>vccpll_dynamic_current=0.013878</TD>
    <TD>vccpll_static_current=0.003000</TD>
    <TD>vccpll_total_current=0.016878</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccpll_voltage=1.800000</TD>
    <TD>version=2016.4</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_utilization</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>clocking</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufgctrl_available=32</TD>
    <TD>bufgctrl_fixed=0</TD>
    <TD>bufgctrl_used=4</TD>
    <TD>bufgctrl_util_percentage=12.50</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufhce_available=168</TD>
    <TD>bufhce_fixed=0</TD>
    <TD>bufhce_used=0</TD>
    <TD>bufhce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufio_available=32</TD>
    <TD>bufio_fixed=0</TD>
    <TD>bufio_used=0</TD>
    <TD>bufio_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufmrce_available=16</TD>
    <TD>bufmrce_fixed=0</TD>
    <TD>bufmrce_used=0</TD>
    <TD>bufmrce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufr_available=32</TD>
    <TD>bufr_fixed=0</TD>
    <TD>bufr_used=2</TD>
    <TD>bufr_util_percentage=6.25</TD>
</TR><TR ALIGN='LEFT'>    <TD>mmcme2_adv_available=8</TD>
    <TD>mmcme2_adv_fixed=0</TD>
    <TD>mmcme2_adv_used=0</TD>
    <TD>mmcme2_adv_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>plle2_adv_available=8</TD>
    <TD>plle2_adv_fixed=0</TD>
    <TD>plle2_adv_used=0</TD>
    <TD>plle2_adv_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>dsp</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>dsp48e1_only_used=60</TD>
    <TD>dsps_available=900</TD>
    <TD>dsps_fixed=0</TD>
    <TD>dsps_used=60</TD>
</TR><TR ALIGN='LEFT'>    <TD>dsps_util_percentage=6.67</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>io_standard</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>blvds_25=0</TD>
    <TD>diff_hstl_i=0</TD>
    <TD>diff_hstl_i_18=0</TD>
    <TD>diff_hstl_i_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_hstl_i_dci_18=0</TD>
    <TD>diff_hstl_ii=0</TD>
    <TD>diff_hstl_ii_18=0</TD>
    <TD>diff_hstl_ii_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_hstl_ii_dci_18=0</TD>
    <TD>diff_hstl_ii_t_dci=0</TD>
    <TD>diff_hstl_ii_t_dci_18=0</TD>
    <TD>diff_hsul_12=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_hsul_12_dci=0</TD>
    <TD>diff_mobile_ddr=0</TD>
    <TD>diff_sstl12=0</TD>
    <TD>diff_sstl12_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl12_t_dci=0</TD>
    <TD>diff_sstl135=0</TD>
    <TD>diff_sstl135_dci=0</TD>
    <TD>diff_sstl135_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl135_t_dci=0</TD>
    <TD>diff_sstl15=1</TD>
    <TD>diff_sstl15_dci=0</TD>
    <TD>diff_sstl15_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl15_t_dci=1</TD>
    <TD>diff_sstl18_i=0</TD>
    <TD>diff_sstl18_i_dci=0</TD>
    <TD>diff_sstl18_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl18_ii_dci=0</TD>
    <TD>diff_sstl18_ii_t_dci=0</TD>
    <TD>hslvdci_15=0</TD>
    <TD>hslvdci_18=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hstl_i=0</TD>
    <TD>hstl_i_12=0</TD>
    <TD>hstl_i_18=0</TD>
    <TD>hstl_i_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hstl_i_dci_18=0</TD>
    <TD>hstl_ii=0</TD>
    <TD>hstl_ii_18=0</TD>
    <TD>hstl_ii_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hstl_ii_dci_18=0</TD>
    <TD>hstl_ii_t_dci=0</TD>
    <TD>hstl_ii_t_dci_18=0</TD>
    <TD>hsul_12=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hsul_12_dci=0</TD>
    <TD>lvcmos12=0</TD>
    <TD>lvcmos15=0</TD>
    <TD>lvcmos18=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvcmos25=1</TD>
    <TD>lvcmos33=1</TD>
    <TD>lvdci_15=0</TD>
    <TD>lvdci_18=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvdci_dv2_15=0</TD>
    <TD>lvdci_dv2_18=0</TD>
    <TD>lvds=1</TD>
    <TD>lvds_25=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvttl=0</TD>
    <TD>mini_lvds_25=0</TD>
    <TD>mobile_ddr=0</TD>
    <TD>pci33_3=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>ppds_25=0</TD>
    <TD>rsds_25=0</TD>
    <TD>sstl12=0</TD>
    <TD>sstl12_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl12_t_dci=0</TD>
    <TD>sstl135=0</TD>
    <TD>sstl135_dci=0</TD>
    <TD>sstl135_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl135_t_dci=0</TD>
    <TD>sstl15=1</TD>
    <TD>sstl15_dci=0</TD>
    <TD>sstl15_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl15_t_dci=1</TD>
    <TD>sstl18_i=0</TD>
    <TD>sstl18_i_dci=0</TD>
    <TD>sstl18_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl18_ii_dci=0</TD>
    <TD>sstl18_ii_t_dci=0</TD>
    <TD>tmds_33=0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>memory</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>block_ram_tile_available=545</TD>
    <TD>block_ram_tile_fixed=0</TD>
    <TD>block_ram_tile_used=4</TD>
    <TD>block_ram_tile_util_percentage=0.73</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb18_available=1090</TD>
    <TD>ramb18_fixed=0</TD>
    <TD>ramb18_used=0</TD>
    <TD>ramb18_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36_fifo_available=545</TD>
    <TD>ramb36_fifo_fixed=0</TD>
    <TD>ramb36_fifo_used=4</TD>
    <TD>ramb36_fifo_util_percentage=0.73</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36e1_only_used=4</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>primitives</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bibuf_functional_category=IO</TD>
    <TD>bibuf_used=130</TD>
    <TD>bufg_functional_category=Clock</TD>
    <TD>bufg_used=3</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufgctrl_functional_category=Clock</TD>
    <TD>bufgctrl_used=1</TD>
    <TD>bufr_functional_category=Clock</TD>
    <TD>bufr_used=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>carry4_functional_category=CarryLogic</TD>
    <TD>carry4_used=777</TD>
    <TD>dsp48e1_functional_category=Block Arithmetic</TD>
    <TD>dsp48e1_used=60</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdce_functional_category=Flop &amp; Latch</TD>
    <TD>fdce_used=7042</TD>
    <TD>fdpe_functional_category=Flop &amp; Latch</TD>
    <TD>fdpe_used=13</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdre_functional_category=Flop &amp; Latch</TD>
    <TD>fdre_used=14768</TD>
    <TD>fdse_functional_category=Flop &amp; Latch</TD>
    <TD>fdse_used=326</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibuf_functional_category=IO</TD>
    <TD>ibuf_used=16</TD>
    <TD>ibufds_functional_category=IO</TD>
    <TD>ibufds_used=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>iddr_functional_category=IO</TD>
    <TD>iddr_used=7</TD>
    <TD>idelayctrl_functional_category=IO</TD>
    <TD>idelayctrl_used=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>idelaye2_functional_category=IO</TD>
    <TD>idelaye2_used=7</TD>
    <TD>lut1_functional_category=LUT</TD>
    <TD>lut1_used=835</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut2_functional_category=LUT</TD>
    <TD>lut2_used=2177</TD>
    <TD>lut3_functional_category=LUT</TD>
    <TD>lut3_used=2490</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut4_functional_category=LUT</TD>
    <TD>lut4_used=1225</TD>
    <TD>lut5_functional_category=LUT</TD>
    <TD>lut5_used=1295</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut6_functional_category=LUT</TD>
    <TD>lut6_used=3260</TD>
    <TD>muxf7_functional_category=MuxFx</TD>
    <TD>muxf7_used=297</TD>
</TR><TR ALIGN='LEFT'>    <TD>muxf8_functional_category=MuxFx</TD>
    <TD>muxf8_used=85</TD>
    <TD>obuf_functional_category=IO</TD>
    <TD>obuf_used=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>obufds_functional_category=IO</TD>
    <TD>obufds_used=8</TD>
    <TD>obuft_functional_category=IO</TD>
    <TD>obuft_used=15</TD>
</TR><TR ALIGN='LEFT'>    <TD>oddr_functional_category=IO</TD>
    <TD>oddr_used=10</TD>
    <TD>ps7_functional_category=Specialized Resource</TD>
    <TD>ps7_used=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36e1_functional_category=Block Memory</TD>
    <TD>ramb36e1_used=4</TD>
    <TD>srl16e_functional_category=Distributed Memory</TD>
    <TD>srl16e_used=135</TD>
</TR><TR ALIGN='LEFT'>    <TD>srlc32e_functional_category=Distributed Memory</TD>
    <TD>srlc32e_used=141</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>slice_logic</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>f7_muxes_available=109300</TD>
    <TD>f7_muxes_fixed=0</TD>
    <TD>f7_muxes_used=297</TD>
    <TD>f7_muxes_util_percentage=0.27</TD>
</TR><TR ALIGN='LEFT'>    <TD>f8_muxes_available=54650</TD>
    <TD>f8_muxes_fixed=0</TD>
    <TD>f8_muxes_used=85</TD>
    <TD>f8_muxes_util_percentage=0.16</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_distributed_ram_fixed=0</TD>
    <TD>lut_as_distributed_ram_used=0</TD>
    <TD>lut_as_logic_available=218600</TD>
    <TD>lut_as_logic_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_used=10981</TD>
    <TD>lut_as_logic_util_percentage=5.02</TD>
    <TD>lut_as_memory_available=70400</TD>
    <TD>lut_as_memory_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_used=220</TD>
    <TD>lut_as_memory_util_percentage=0.31</TD>
    <TD>lut_as_shift_register_fixed=0</TD>
    <TD>lut_as_shift_register_used=220</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_flip_flop_available=437200</TD>
    <TD>register_as_flip_flop_fixed=0</TD>
    <TD>register_as_flip_flop_used=22149</TD>
    <TD>register_as_flip_flop_util_percentage=5.07</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_latch_available=437200</TD>
    <TD>register_as_latch_fixed=0</TD>
    <TD>register_as_latch_used=0</TD>
    <TD>register_as_latch_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_luts_available=218600</TD>
    <TD>slice_luts_fixed=0</TD>
    <TD>slice_luts_used=11201</TD>
    <TD>slice_luts_util_percentage=5.12</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_available=437200</TD>
    <TD>slice_registers_fixed=0</TD>
    <TD>slice_registers_used=22149</TD>
    <TD>slice_registers_util_percentage=5.07</TD>
</TR><TR ALIGN='LEFT'>    <TD>fully_used_lut_ff_pairs_fixed=5.07</TD>
    <TD>fully_used_lut_ff_pairs_used=56</TD>
    <TD>lut_as_distributed_ram_fixed=0</TD>
    <TD>lut_as_distributed_ram_used=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_available=218600</TD>
    <TD>lut_as_logic_fixed=0</TD>
    <TD>lut_as_logic_used=10981</TD>
    <TD>lut_as_logic_util_percentage=5.02</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_available=70400</TD>
    <TD>lut_as_memory_fixed=0</TD>
    <TD>lut_as_memory_used=220</TD>
    <TD>lut_as_memory_util_percentage=0.31</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_shift_register_fixed=0</TD>
    <TD>lut_as_shift_register_used=220</TD>
    <TD>lut_ff_pairs_with_one_unused_flip_flop_fixed=220</TD>
    <TD>lut_ff_pairs_with_one_unused_flip_flop_used=5222</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_ff_pairs_with_one_unused_lut_output_fixed=5222</TD>
    <TD>lut_ff_pairs_with_one_unused_lut_output_used=6628</TD>
    <TD>lut_flip_flop_pairs_available=218600</TD>
    <TD>lut_flip_flop_pairs_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_flip_flop_pairs_used=6948</TD>
    <TD>lut_flip_flop_pairs_util_percentage=3.18</TD>
    <TD>slice_available=54650</TD>
    <TD>slice_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_used=6389</TD>
    <TD>slice_util_percentage=11.69</TD>
    <TD>slicel_fixed=0</TD>
    <TD>slicel_used=4154</TD>
</TR><TR ALIGN='LEFT'>    <TD>slicem_fixed=0</TD>
    <TD>slicem_used=2235</TD>
    <TD>unique_control_sets_used=627</TD>
    <TD>using_o5_and_o6_fixed=627</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o5_and_o6_used=56</TD>
    <TD>using_o5_output_only_fixed=56</TD>
    <TD>using_o5_output_only_used=8</TD>
    <TD>using_o6_output_only_fixed=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o6_output_only_used=156</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>specific_feature</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bscane2_available=4</TD>
    <TD>bscane2_fixed=0</TD>
    <TD>bscane2_used=0</TD>
    <TD>bscane2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>capturee2_available=1</TD>
    <TD>capturee2_fixed=0</TD>
    <TD>capturee2_used=0</TD>
    <TD>capturee2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>dna_port_available=1</TD>
    <TD>dna_port_fixed=0</TD>
    <TD>dna_port_used=0</TD>
    <TD>dna_port_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>efuse_usr_available=1</TD>
    <TD>efuse_usr_fixed=0</TD>
    <TD>efuse_usr_used=0</TD>
    <TD>efuse_usr_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>frame_ecce2_available=1</TD>
    <TD>frame_ecce2_fixed=0</TD>
    <TD>frame_ecce2_used=0</TD>
    <TD>frame_ecce2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>icape2_available=2</TD>
    <TD>icape2_fixed=0</TD>
    <TD>icape2_used=0</TD>
    <TD>icape2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcie_2_1_available=1</TD>
    <TD>pcie_2_1_fixed=0</TD>
    <TD>pcie_2_1_used=0</TD>
    <TD>pcie_2_1_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>startupe2_available=1</TD>
    <TD>startupe2_fixed=0</TD>
    <TD>startupe2_used=0</TD>
    <TD>startupe2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>xadc_available=1</TD>
    <TD>xadc_fixed=0</TD>
    <TD>xadc_used=0</TD>
    <TD>xadc_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>router</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>actual_expansions=18681140</TD>
    <TD>bogomips=0</TD>
    <TD>bram18=0</TD>
    <TD>bram36=4</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufg=0</TD>
    <TD>bufr=2</TD>
    <TD>congestion_level=0</TD>
    <TD>ctrls=627</TD>
</TR><TR ALIGN='LEFT'>    <TD>dsp=60</TD>
    <TD>effort=2</TD>
    <TD>estimated_expansions=24001722</TD>
    <TD>ff=22149</TD>
</TR><TR ALIGN='LEFT'>    <TD>global_clocks=4</TD>
    <TD>high_fanout_nets=12</TD>
    <TD>iob=55</TD>
    <TD>lut=11281</TD>
</TR><TR ALIGN='LEFT'>    <TD>movable_instances=36035</TD>
    <TD>nets=45013</TD>
    <TD>pins=214415</TD>
    <TD>pll=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>router_runtime=2.000000</TD>
    <TD>router_timing_driven=1</TD>
    <TD>threads=8</TD>
    <TD>timing_constraints_exist=1</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>synthesis</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-assert=default::[not_specified]</TD>
    <TD>-bufg=default::12</TD>
    <TD>-cascade_dsp=default::auto</TD>
    <TD>-constrset=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-control_set_opt_threshold=default::auto</TD>
    <TD>-directive=default::default</TD>
    <TD>-fanout_limit=400</TD>
    <TD>-flatten_hierarchy=default::rebuilt</TD>
</TR><TR ALIGN='LEFT'>    <TD>-fsm_extraction=one_hot</TD>
    <TD>-gated_clock_conversion=default::off</TD>
    <TD>-generic=default::[not_specified]</TD>
    <TD>-include_dirs=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-keep_equivalent_registers=[specified]</TD>
    <TD>-max_bram=default::-1</TD>
    <TD>-max_bram_cascade_height=default::-1</TD>
    <TD>-max_dsp=default::-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>-max_uram=default::-1</TD>
    <TD>-max_uram_cascade_height=default::-1</TD>
    <TD>-mode=default::default</TD>
    <TD>-name=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-no_lc=[specified]</TD>
    <TD>-no_srlextract=default::[not_specified]</TD>
    <TD>-no_timing_driven=default::[not_specified]</TD>
    <TD>-part=xc7z045ffg900-2</TD>
</TR><TR ALIGN='LEFT'>    <TD>-resource_sharing=off</TD>
    <TD>-retiming=default::[not_specified]</TD>
    <TD>-rtl=default::[not_specified]</TD>
    <TD>-rtl_skip_constraints=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-rtl_skip_ip=default::[not_specified]</TD>
    <TD>-seu_protect=default::none</TD>
    <TD>-shreg_min_size=5</TD>
    <TD>-top=system_top</TD>
</TR><TR ALIGN='LEFT'>    <TD>-verilog_define=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>elapsed=00:04:19s</TD>
    <TD>hls_ip=0</TD>
    <TD>memory_gain=1103.555MB</TD>
    <TD>memory_peak=1413.684MB</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
</BODY>
</HTML>
