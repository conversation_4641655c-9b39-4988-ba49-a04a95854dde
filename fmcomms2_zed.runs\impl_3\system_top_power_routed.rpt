Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
----------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version     : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date             : Tue Mar 17 17:08:33 2020
| Host             : cduser1 running 64-bit major release  (build 9200)
| Command          : report_power -file system_top_power_routed.rpt -pb system_top_power_summary_routed.pb -rpx system_top_power_routed.rpx
| Design           : system_top
| Device           : xc7z035ffg676-2
| Design State     : routed
| Grade            : commercial
| Process          : typical
| Characterization : Production
----------------------------------------------------------------------------------------------------------------------------------------------------

Power Report

Table of Contents
-----------------
1. Summary
1.1 On-Chip Components
1.2 Power Supply Summary
1.3 Confidence Level
2. Settings
2.1 Environment
2.2 Clock Constraints
3. Detailed Reports
3.1 By Hierarchy

1. Summary
----------

+--------------------------+-------+
| Total On-Chip Power (W)  | 2.346 |
| Dynamic (W)              | 2.090 |
| Device Static (W)        | 0.256 |
| Total Off-Chip Power (W) | 0.010 |
| Effective TJA (C/W)      | 1.9   |
| Max Ambient (C)          | 80.6  |
| Junction Temperature (C) | 29.4  |
| Confidence Level         | Low   |
| Setting File             | ---   |
| Simulation Activity File | ---   |
| Design Nets Matched      | NA    |
+--------------------------+-------+


1.1 On-Chip Components
----------------------

+--------------------------+-----------+----------+-----------+-----------------+
| On-Chip                  | Power (W) | Used     | Available | Utilization (%) |
+--------------------------+-----------+----------+-----------+-----------------+
| Clocks                   |     0.036 |        5 |       --- |             --- |
| Slice Logic              |     0.073 |    37532 |       --- |             --- |
|   LUT as Logic           |     0.057 |    11520 |    171900 |            6.70 |
|   Register               |     0.011 |    23001 |    343800 |            6.69 |
|   CARRY4                 |     0.005 |      789 |     54650 |            1.44 |
|   LUT as Shift Register  |    <0.001 |      220 |     70400 |            0.31 |
|   F7/F8 Muxes            |    <0.001 |      386 |    218600 |            0.18 |
|   BUFG                   |    <0.001 |        2 |        32 |            6.25 |
|   LUT as Distributed RAM |    <0.001 |       24 |     70400 |            0.03 |
|   Others                 |     0.000 |     1135 |       --- |             --- |
|   BUFR                   |     0.000 |        2 |       200 |            1.00 |
| Signals                  |     0.126 |    29963 |       --- |             --- |
| Block RAM                |     0.002 |        4 |       500 |            0.80 |
| DSPs                     |     0.106 |       60 |       900 |            6.67 |
| I/O                      |     0.203 |       88 |       250 |           35.20 |
| PS7                      |     1.543 |        1 |       --- |             --- |
| Static Power             |     0.256 |          |           |                 |
| Total                    |     2.346 |          |           |                 |
+--------------------------+-----------+----------+-----------+-----------------+


1.2 Power Supply Summary
------------------------

+-----------+-------------+-----------+-------------+------------+
| Source    | Voltage (V) | Total (A) | Dynamic (A) | Static (A) |
+-----------+-------------+-----------+-------------+------------+
| Vccint    |       1.000 |     0.414 |       0.353 |      0.061 |
| Vccaux    |       1.800 |     0.076 |       0.022 |      0.054 |
| Vcco33    |       3.300 |     0.002 |       0.001 |      0.001 |
| Vcco25    |       2.500 |     0.001 |       0.000 |      0.001 |
| Vcco18    |       1.800 |     0.090 |       0.089 |      0.001 |
| Vcco15    |       1.500 |     0.001 |       0.000 |      0.001 |
| Vcco135   |       1.350 |     0.000 |       0.000 |      0.000 |
| Vcco12    |       1.200 |     0.000 |       0.000 |      0.000 |
| Vccaux_io |       1.800 |     0.000 |       0.000 |      0.000 |
| Vccbram   |       1.000 |     0.002 |       0.000 |      0.002 |
| MGTAVcc   |       1.000 |     0.000 |       0.000 |      0.000 |
| MGTAVtt   |       1.200 |     0.000 |       0.000 |      0.000 |
| MGTVccaux |       1.800 |     0.000 |       0.000 |      0.000 |
| Vccpint   |       1.000 |     0.750 |       0.731 |      0.019 |
| Vccpaux   |       1.800 |     0.061 |       0.051 |      0.010 |
| Vccpll    |       1.800 |     0.017 |       0.014 |      0.003 |
| Vcco_ddr  |       1.500 |     0.459 |       0.457 |      0.002 |
| Vcco_mio0 |       3.300 |     0.003 |       0.002 |      0.001 |
| Vcco_mio1 |       2.500 |     0.003 |       0.002 |      0.001 |
| Vccadc    |       1.800 |     0.020 |       0.000 |      0.020 |
+-----------+-------------+-----------+-------------+------------+


1.3 Confidence Level
--------------------

+-----------------------------+------------+--------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------+
| User Input Data             | Confidence | Details                                                | Action                                                                                                             |
+-----------------------------+------------+--------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------+
| Design implementation state | High       | Design is routed                                       |                                                                                                                    |
| Clock nodes activity        | Low        | User specified less than 75% of clocks                 | Provide missing clock activity with a constraint file, simulation results or by editing the "By Clock Domain" view |
| I/O nodes activity          | Low        | More than 75% of inputs are missing user specification | Provide missing input activity with simulation results or by editing the "By Resource Type -> I/Os" view           |
| Internal nodes activity     | Medium     | User specified less than 25% of internal nodes         | Provide missing internal nodes activity with simulation results or by editing the "By Resource Type" views         |
| Device models               | High       | Device models are Production                           |                                                                                                                    |
|                             |            |                                                        |                                                                                                                    |
| Overall confidence level    | Low        |                                                        |                                                                                                                    |
+-----------------------------+------------+--------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------+


2. Settings
-----------

2.1 Environment
---------------

+-----------------------+--------------------------+
| Ambient Temp (C)      | 25.0                     |
| ThetaJA (C/W)         | 1.9                      |
| Airflow (LFM)         | 250                      |
| Heat Sink             | medium (Medium Profile)  |
| ThetaSA (C/W)         | 3.4                      |
| Board Selection       | medium (10"x10")         |
| # of Board Layers     | 12to15 (12 to 15 Layers) |
| Board Temperature (C) | 25.0                     |
+-----------------------+--------------------------+


2.2 Clock Constraints
---------------------

+-------------------------------------------------------------------------+---------------------------------------------------------------+-----------------+
| Clock                                                                   | Domain                                                        | Constraint (ns) |
+-------------------------------------------------------------------------+---------------------------------------------------------------+-----------------+
| clk_fpga_0                                                              | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[0] |            10.0 |
| clk_fpga_1                                                              | i_system_wrapper/system_i/sys_ps7/inst/FCLK_CLK_unbuffered[1] |             5.0 |
| dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/SERIES7_BSCAN.bscan_inst/TCK | dbg_hub/inst/SWITCH_N_EXT_BSCAN.bscan_inst/tck_bs             |            33.0 |
+-------------------------------------------------------------------------+---------------------------------------------------------------+-----------------+


3. Detailed Reports
-------------------

3.1 By Hierarchy
----------------

+----------------------------------------------------------------------------------+-----------+
| Name                                                                             | Power (W) |
+----------------------------------------------------------------------------------+-----------+
| system_top                                                                       |     2.090 |
|   dbg_hub                                                                        |     0.003 |
|     inst                                                                         |     0.003 |
|       CORE_XSDB.UUT_MASTER                                                       |     0.002 |
|         U_ICON_INTERFACE                                                         |     0.001 |
|           U_CMD1                                                                 |    <0.001 |
|           U_CMD2                                                                 |    <0.001 |
|           U_CMD3                                                                 |    <0.001 |
|           U_CMD4                                                                 |    <0.001 |
|           U_CMD5                                                                 |    <0.001 |
|           U_CMD6_RD                                                              |    <0.001 |
|             U_RD_FIFO                                                            |    <0.001 |
|               SUBCORE_FIFO.xsdbm_v2_0_2_rdfifo_inst                              |    <0.001 |
|                 inst_fifo_gen                                                    |    <0.001 |
|                   gconvfifo.rf                                                   |    <0.001 |
|                     grf.rf                                                       |    <0.001 |
|                       gntv_or_sync_fifo.gcx.clkx                                 |    <0.001 |
|                         gnxpm_cdc.gsync_stage[1].rd_stg_inst                     |    <0.001 |
|                         gnxpm_cdc.gsync_stage[1].wr_stg_inst                     |    <0.001 |
|                         gnxpm_cdc.gsync_stage[2].rd_stg_inst                     |    <0.001 |
|                         gnxpm_cdc.gsync_stage[2].wr_stg_inst                     |    <0.001 |
|                       gntv_or_sync_fifo.gl0.rd                                   |    <0.001 |
|                         gr1.gr1_int.rfwft                                        |    <0.001 |
|                         gras.rsts                                                |    <0.001 |
|                         rpntr                                                    |    <0.001 |
|                       gntv_or_sync_fifo.gl0.wr                                   |    <0.001 |
|                         gwas.wsts                                                |    <0.001 |
|                         wpntr                                                    |    <0.001 |
|                       gntv_or_sync_fifo.mem                                      |    <0.001 |
|                         gdm.dm_gen.dm                                            |    <0.001 |
|                           RAM_reg_0_15_0_5                                       |    <0.001 |
|                           RAM_reg_0_15_12_15                                     |    <0.001 |
|                           RAM_reg_0_15_6_11                                      |    <0.001 |
|                       rstblk                                                     |    <0.001 |
|                         ngwrdrst.grst.g7serrst.gwrrd_rst_sync_stage[1].rrst_inst |    <0.001 |
|                         ngwrdrst.grst.g7serrst.gwrrd_rst_sync_stage[1].wrst_inst |    <0.001 |
|                         ngwrdrst.grst.g7serrst.gwrrd_rst_sync_stage[2].rrst_inst |    <0.001 |
|                         ngwrdrst.grst.g7serrst.gwrrd_rst_sync_stage[2].wrst_inst |    <0.001 |
|           U_CMD6_WR                                                              |    <0.001 |
|             U_WR_FIFO                                                            |    <0.001 |
|               SUBCORE_FIFO.xsdbm_v2_0_2_wrfifo_inst                              |    <0.001 |
|                 inst_fifo_gen                                                    |    <0.001 |
|                   gconvfifo.rf                                                   |    <0.001 |
|                     grf.rf                                                       |    <0.001 |
|                       gntv_or_sync_fifo.gcx.clkx                                 |    <0.001 |
|                         gnxpm_cdc.gsync_stage[1].rd_stg_inst                     |    <0.001 |
|                         gnxpm_cdc.gsync_stage[1].wr_stg_inst                     |    <0.001 |
|                         gnxpm_cdc.gsync_stage[2].rd_stg_inst                     |    <0.001 |
|                         gnxpm_cdc.gsync_stage[2].wr_stg_inst                     |    <0.001 |
|                       gntv_or_sync_fifo.gl0.rd                                   |    <0.001 |
|                         gras.rsts                                                |    <0.001 |
|                         rpntr                                                    |    <0.001 |
|                       gntv_or_sync_fifo.gl0.wr                                   |    <0.001 |
|                         gwas.wsts                                                |    <0.001 |
|                         wpntr                                                    |    <0.001 |
|                       gntv_or_sync_fifo.mem                                      |    <0.001 |
|                         gdm.dm_gen.dm                                            |    <0.001 |
|                           RAM_reg_0_15_0_5                                       |    <0.001 |
|                           RAM_reg_0_15_12_15                                     |    <0.001 |
|                           RAM_reg_0_15_6_11                                      |    <0.001 |
|                       rstblk                                                     |    <0.001 |
|                         ngwrdrst.grst.g7serrst.gwrrd_rst_sync_stage[1].rrst_inst |    <0.001 |
|                         ngwrdrst.grst.g7serrst.gwrrd_rst_sync_stage[1].wrst_inst |    <0.001 |
|                         ngwrdrst.grst.g7serrst.gwrrd_rst_sync_stage[2].rrst_inst |    <0.001 |
|                         ngwrdrst.grst.g7serrst.gwrrd_rst_sync_stage[2].wrst_inst |    <0.001 |
|           U_CMD7_CTL                                                             |    <0.001 |
|           U_CMD7_STAT                                                            |    <0.001 |
|           U_STATIC_STATUS                                                        |    <0.001 |
|         U_XSDB_ADDRESS_CONTROLLER                                                |    <0.001 |
|         U_XSDB_BURST_WD_LEN_CONTROLLER                                           |    <0.001 |
|         U_XSDB_BUS_CONTROLLER                                                    |    <0.001 |
|           U_RD_ABORT_FLAG                                                        |    <0.001 |
|           U_RD_REQ_FLAG                                                          |    <0.001 |
|           U_TIMER                                                                |    <0.001 |
|         U_XSDB_BUS_MSTR2SL_PORT_IFACE                                            |    <0.001 |
|       CORE_XSDB.U_ICON                                                           |    <0.001 |
|         U_CMD                                                                    |    <0.001 |
|         U_STAT                                                                   |    <0.001 |
|         U_SYNC                                                                   |    <0.001 |
|       SWITCH_N_EXT_BSCAN.bscan_inst                                              |    <0.001 |
|       SWITCH_N_EXT_BSCAN.bscan_switch                                            |    <0.001 |
|   gpio_ctl_IOBUF[0]_inst                                                         |    <0.001 |
|   gpio_ctl_IOBUF[1]_inst                                                         |    <0.001 |
|   gpio_ctl_IOBUF[2]_inst                                                         |    <0.001 |
|   gpio_ctl_IOBUF[3]_inst                                                         |    <0.001 |
|   gpio_en_agc_IOBUF_inst                                                         |    <0.001 |
|   gpio_resetb_IOBUF_inst                                                         |    <0.001 |
|   gpio_status_IOBUF[0]_inst                                                      |    <0.001 |
|   gpio_status_IOBUF[1]_inst                                                      |    <0.001 |
|   gpio_status_IOBUF[2]_inst                                                      |    <0.001 |
|   gpio_status_IOBUF[3]_inst                                                      |    <0.001 |
|   gpio_status_IOBUF[4]_inst                                                      |    <0.001 |
|   gpio_status_IOBUF[5]_inst                                                      |    <0.001 |
|   gpio_status_IOBUF[6]_inst                                                      |    <0.001 |
|   gpio_status_IOBUF[7]_inst                                                      |    <0.001 |
|   gpio_sync_IOBUF_inst                                                           |    <0.001 |
|   i_system_wrapper                                                               |     2.072 |
|     system_i                                                                     |     2.072 |
|       axi_ad9361                                                                 |     0.468 |
|         inst                                                                     |     0.468 |
|           i_dev_if                                                               |     0.211 |
|             g_rx_data[0].i_rx_data                                               |     0.009 |
|             g_rx_data[1].i_rx_data                                               |     0.009 |
|             g_rx_data[2].i_rx_data                                               |     0.009 |
|             g_rx_data[3].i_rx_data                                               |     0.009 |
|             g_rx_data[4].i_rx_data                                               |     0.009 |
|             g_rx_data[5].i_rx_data                                               |     0.009 |
|             g_tx_data[0].i_tx_data                                               |     0.016 |
|             g_tx_data[1].i_tx_data                                               |     0.016 |
|             g_tx_data[2].i_tx_data                                               |     0.016 |
|             g_tx_data[3].i_tx_data                                               |     0.016 |
|             g_tx_data[4].i_tx_data                                               |     0.016 |
|             g_tx_data[5].i_tx_data                                               |     0.016 |
|             i_clk                                                                |     0.007 |
|             i_enable                                                             |    <0.001 |
|             i_rx_frame                                                           |     0.017 |
|             i_tx_clk                                                             |     0.016 |
|             i_tx_frame                                                           |     0.016 |
|             i_txnrx                                                              |    <0.001 |
|           i_rx                                                                   |     0.055 |
|             i_delay_cntrl                                                        |     0.002 |
|               i_delay_rst_reg                                                    |     0.002 |
|             i_rx_channel_0                                                       |     0.013 |
|               i_ad_datafmt                                                       |    <0.001 |
|               i_ad_dcfilter                                                      |     0.006 |
|               i_ad_iqcor                                                         |     0.005 |
|                 i_mul_i                                                          |     0.002 |
|                   i_mult_macro                                                   |     0.002 |
|                 i_mul_q                                                          |     0.002 |
|                   i_mult_macro                                                   |     0.002 |
|               i_rx_pnmon                                                         |    <0.001 |
|                 i_pnmon                                                          |    <0.001 |
|               i_up_adc_channel                                                   |     0.001 |
|                 i_xfer_cntrl                                                     |    <0.001 |
|                 i_xfer_status                                                    |    <0.001 |
|             i_rx_channel_1                                                       |     0.013 |
|               i_ad_datafmt                                                       |    <0.001 |
|               i_ad_dcfilter                                                      |     0.005 |
|               i_ad_iqcor                                                         |     0.005 |
|                 i_mul_i                                                          |     0.002 |
|                   i_mult_macro                                                   |     0.002 |
|                 i_mul_q                                                          |     0.002 |
|                   i_mult_macro                                                   |     0.002 |
|               i_rx_pnmon                                                         |    <0.001 |
|                 i_pnmon                                                          |    <0.001 |
|               i_up_adc_channel                                                   |     0.001 |
|                 i_xfer_cntrl                                                     |    <0.001 |
|                 i_xfer_status                                                    |    <0.001 |
|             i_rx_channel_2                                                       |     0.013 |
|               i_ad_datafmt                                                       |    <0.001 |
|               i_ad_dcfilter                                                      |     0.006 |
|               i_ad_iqcor                                                         |     0.005 |
|                 i_mul_i                                                          |     0.002 |
|                   i_mult_macro                                                   |     0.002 |
|                 i_mul_q                                                          |     0.002 |
|                   i_mult_macro                                                   |     0.002 |
|               i_rx_pnmon                                                         |    <0.001 |
|                 i_pnmon                                                          |    <0.001 |
|               i_up_adc_channel                                                   |     0.001 |
|                 i_xfer_cntrl                                                     |    <0.001 |
|                 i_xfer_status                                                    |    <0.001 |
|             i_rx_channel_3                                                       |     0.013 |
|               i_ad_datafmt                                                       |    <0.001 |
|               i_ad_dcfilter                                                      |     0.006 |
|               i_ad_iqcor                                                         |     0.005 |
|                 i_mul_i                                                          |     0.002 |
|                   i_mult_macro                                                   |     0.002 |
|                 i_mul_q                                                          |     0.002 |
|                   i_mult_macro                                                   |     0.002 |
|               i_rx_pnmon                                                         |    <0.001 |
|                 i_pnmon                                                          |    <0.001 |
|               i_up_adc_channel                                                   |     0.001 |
|                 i_xfer_cntrl                                                     |    <0.001 |
|                 i_xfer_status                                                    |    <0.001 |
|             i_up_adc_common                                                      |     0.002 |
|               i_clock_mon                                                        |    <0.001 |
|               i_core_rst_reg                                                     |     0.000 |
|               i_xfer_cntrl                                                       |    <0.001 |
|               i_xfer_status                                                      |    <0.001 |
|           i_tdd                                                                  |     0.008 |
|             i_tdd_control                                                        |     0.001 |
|               i_rx_off_1_comp                                                    |    <0.001 |
|               i_rx_off_2_comp                                                    |    <0.001 |
|               i_rx_on_1_comp                                                     |    <0.001 |
|               i_rx_on_2_comp                                                     |    <0.001 |
|               i_tx_dp_off_1_comp                                                 |    <0.001 |
|               i_tx_dp_off_2_comp                                                 |    <0.001 |
|               i_tx_dp_on_1_comp                                                  |    <0.001 |
|               i_tx_dp_on_2_comp                                                  |    <0.001 |
|               i_tx_off_1_comp                                                    |    <0.001 |
|               i_tx_off_2_comp                                                    |    <0.001 |
|               i_tx_on_1_comp                                                     |    <0.001 |
|               i_tx_on_2_comp                                                     |    <0.001 |
|               i_vco_rx_off_1_comp                                                |    <0.001 |
|               i_vco_rx_off_2_comp                                                |    <0.001 |
|               i_vco_rx_on_1_comp                                                 |    <0.001 |
|               i_vco_rx_on_2_comp                                                 |    <0.001 |
|               i_vco_tx_off_1_comp                                                |    <0.001 |
|               i_vco_tx_off_2_comp                                                |    <0.001 |
|               i_vco_tx_on_1_comp                                                 |    <0.001 |
|               i_vco_tx_on_2_comp                                                 |    <0.001 |
|             i_up_tdd_cntrl                                                       |     0.006 |
|               i_xfer_tdd_control                                                 |    <0.001 |
|               i_xfer_tdd_counter_values                                          |     0.003 |
|               i_xfer_tdd_status                                                  |    <0.001 |
|           i_tdd_if                                                               |    <0.001 |
|           i_tx                                                                   |     0.190 |
|             i_tx_channel_0                                                       |     0.048 |
|               i_ad_iqcor                                                         |     0.003 |
|                 i_mul_i                                                          |     0.001 |
|                   i_mult_macro                                                   |     0.001 |
|                 i_mul_q                                                          |     0.001 |
|                   i_mult_macro                                                   |     0.001 |
|               i_dds                                                              |     0.041 |
|                 i_dds_1_0                                                        |     0.020 |
|                   i_dds_scale                                                    |     0.002 |
|                     i_mult_macro                                                 |     0.002 |
|                   i_dds_sine                                                     |     0.018 |
|                     i_mul_s1                                                     |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s2                                                     |     0.003 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_1                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_2                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                 i_dds_1_1                                                        |     0.020 |
|                   i_dds_scale                                                    |     0.002 |
|                     i_mult_macro                                                 |     0.002 |
|                   i_dds_sine                                                     |     0.018 |
|                     i_mul_s1                                                     |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s2                                                     |     0.004 |
|                       i_mult_macro                                               |     0.003 |
|                     i_mul_s3_1                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_2                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|               i_up_dac_channel                                                   |     0.002 |
|                 i_xfer_cntrl                                                     |     0.001 |
|             i_tx_channel_1                                                       |     0.047 |
|               i_ad_iqcor                                                         |     0.003 |
|                 i_mul_i                                                          |     0.001 |
|                   i_mult_macro                                                   |     0.001 |
|                 i_mul_q                                                          |     0.001 |
|                   i_mult_macro                                                   |     0.001 |
|               i_dds                                                              |     0.040 |
|                 i_dds_1_0                                                        |     0.020 |
|                   i_dds_scale                                                    |     0.002 |
|                     i_mult_macro                                                 |     0.002 |
|                   i_dds_sine                                                     |     0.017 |
|                     i_mul_s1                                                     |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s2                                                     |     0.003 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_1                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_2                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                 i_dds_1_1                                                        |     0.020 |
|                   i_dds_scale                                                    |     0.002 |
|                     i_mult_macro                                                 |     0.002 |
|                   i_dds_sine                                                     |     0.017 |
|                     i_mul_s1                                                     |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s2                                                     |     0.003 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_1                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_2                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|               i_up_dac_channel                                                   |     0.002 |
|                 i_xfer_cntrl                                                     |     0.001 |
|             i_tx_channel_2                                                       |     0.046 |
|               i_ad_iqcor                                                         |     0.003 |
|                 i_mul_i                                                          |    <0.001 |
|                   i_mult_macro                                                   |    <0.001 |
|                 i_mul_q                                                          |    <0.001 |
|                   i_mult_macro                                                   |    <0.001 |
|               i_dds                                                              |     0.040 |
|                 i_dds_1_0                                                        |     0.020 |
|                   i_dds_scale                                                    |     0.002 |
|                     i_mult_macro                                                 |     0.002 |
|                   i_dds_sine                                                     |     0.017 |
|                     i_mul_s1                                                     |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s2                                                     |     0.003 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_1                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_2                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                 i_dds_1_1                                                        |     0.019 |
|                   i_dds_scale                                                    |     0.002 |
|                     i_mult_macro                                                 |     0.002 |
|                   i_dds_sine                                                     |     0.017 |
|                     i_mul_s1                                                     |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s2                                                     |     0.003 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_1                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_2                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|               i_up_dac_channel                                                   |     0.002 |
|                 i_xfer_cntrl                                                     |     0.001 |
|             i_tx_channel_3                                                       |     0.047 |
|               i_ad_iqcor                                                         |     0.003 |
|                 i_mul_i                                                          |    <0.001 |
|                   i_mult_macro                                                   |    <0.001 |
|                 i_mul_q                                                          |    <0.001 |
|                   i_mult_macro                                                   |    <0.001 |
|               i_dds                                                              |     0.041 |
|                 i_dds_1_0                                                        |     0.020 |
|                   i_dds_scale                                                    |     0.002 |
|                     i_mult_macro                                                 |     0.002 |
|                   i_dds_sine                                                     |     0.017 |
|                     i_mul_s1                                                     |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s2                                                     |     0.004 |
|                       i_mult_macro                                               |     0.003 |
|                     i_mul_s3_1                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_2                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                 i_dds_1_1                                                        |     0.020 |
|                   i_dds_scale                                                    |     0.002 |
|                     i_mult_macro                                                 |     0.002 |
|                   i_dds_sine                                                     |     0.017 |
|                     i_mul_s1                                                     |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s2                                                     |     0.003 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_1                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|                     i_mul_s3_2                                                   |     0.002 |
|                       i_mult_macro                                               |     0.002 |
|               i_up_dac_channel                                                   |     0.002 |
|                 i_xfer_cntrl                                                     |     0.001 |
|             i_up_dac_common                                                      |     0.002 |
|               i_clock_mon                                                        |    <0.001 |
|               i_core_rst_reg                                                     |     0.000 |
|               i_xfer_cntrl                                                       |    <0.001 |
|               i_xfer_status                                                      |    <0.001 |
|           i_up_axi                                                               |     0.004 |
|       axi_ad9361_adc_dma                                                         |     0.004 |
|         inst                                                                     |     0.004 |
|           i_request_arb                                                          |     0.003 |
|             i_dest_dma_mm                                                        |    <0.001 |
|               i_addr_gen                                                         |    <0.001 |
|               i_data_mover                                                       |    <0.001 |
|               i_req_splitter                                                     |    <0.001 |
|               i_response_handler                                                 |    <0.001 |
|             i_dest_req_fifo                                                      |    <0.001 |
|             i_dest_response_fifo                                                 |    <0.001 |
|             i_fifo                                                               |     0.002 |
|               i_address_gray                                                     |    <0.001 |
|                 i_raddr_sync                                                     |    <0.001 |
|                 i_waddr_sync                                                     |    <0.001 |
|             i_req_gen                                                            |    <0.001 |
|             i_req_splitter                                                       |    <0.001 |
|             i_src_dma_fifo                                                       |    <0.001 |
|               i_data_mover                                                       |    <0.001 |
|             i_src_req_fifo                                                       |    <0.001 |
|               i_raddr_sync                                                       |    <0.001 |
|               i_waddr_sync                                                       |    <0.001 |
|             i_sync_control_src                                                   |    <0.001 |
|             i_sync_dest_request_id                                               |    <0.001 |
|             i_sync_src_request_id                                                |     0.000 |
|             i_sync_status_src                                                    |    <0.001 |
|           i_up_axi                                                               |     0.001 |
|       axi_ad9361_dac_dma                                                         |     0.003 |
|         inst                                                                     |     0.003 |
|           i_request_arb                                                          |     0.001 |
|             i_dest_dma_fifo                                                      |    <0.001 |
|               i_data_mover                                                       |    <0.001 |
|               i_response_generator                                               |    <0.001 |
|             i_dest_req_fifo                                                      |    <0.001 |
|               i_raddr_sync                                                       |    <0.001 |
|               i_waddr_sync                                                       |    <0.001 |
|             i_dest_response_fifo                                                 |    <0.001 |
|               i_raddr_sync                                                       |    <0.001 |
|               i_waddr_sync                                                       |    <0.001 |
|             i_dest_slice                                                         |    <0.001 |
|             i_dest_slice2                                                        |    <0.001 |
|             i_fifo                                                               |    <0.001 |
|               i_address_gray                                                     |    <0.001 |
|                 i_raddr_sync                                                     |    <0.001 |
|                 i_waddr_sync                                                     |    <0.001 |
|             i_req_gen                                                            |    <0.001 |
|             i_req_splitter                                                       |    <0.001 |
|             i_src_dma_mm                                                         |    <0.001 |
|               i_addr_gen                                                         |    <0.001 |
|               i_data_mover                                                       |    <0.001 |
|               i_req_splitter                                                     |    <0.001 |
|             i_src_req_fifo                                                       |    <0.001 |
|             i_sync_control_dest                                                  |    <0.001 |
|             i_sync_dest_request_id                                               |    <0.001 |
|             i_sync_req_response_id                                               |    <0.001 |
|             i_sync_status_dest                                                   |    <0.001 |
|           i_up_axi                                                               |     0.001 |
|       axi_ad9361_dac_fifo                                                        |    <0.001 |
|         inst                                                                     |    <0.001 |
|           i_mem                                                                  |    <0.001 |
|       axi_cpu_interconnect                                                       |     0.021 |
|         m07_couplers                                                             |     0.004 |
|           auto_pc                                                                |     0.004 |
|             inst                                                                 |     0.004 |
|               gen_axilite.gen_b2s_conv.axilite_b2s                               |     0.004 |
|                 RD.ar_channel_0                                                  |    <0.001 |
|                   ar_cmd_fsm_0                                                   |    <0.001 |
|                   cmd_translator_0                                               |    <0.001 |
|                     incr_cmd_0                                                   |    <0.001 |
|                     wrap_cmd_0                                                   |    <0.001 |
|                 RD.r_channel_0                                                   |    <0.001 |
|                   rd_data_fifo_0                                                 |    <0.001 |
|                   transaction_fifo_0                                             |    <0.001 |
|                 SI_REG                                                           |     0.002 |
|                   ar_pipe                                                        |    <0.001 |
|                   aw_pipe                                                        |    <0.001 |
|                   b_pipe                                                         |    <0.001 |
|                   r_pipe                                                         |    <0.001 |
|                 WR.aw_channel_0                                                  |    <0.001 |
|                   aw_cmd_fsm_0                                                   |    <0.001 |
|                   cmd_translator_0                                               |    <0.001 |
|                     incr_cmd_0                                                   |    <0.001 |
|                     wrap_cmd_0                                                   |    <0.001 |
|                 WR.b_channel_0                                                   |    <0.001 |
|                   bid_fifo_0                                                     |    <0.001 |
|                   bresp_fifo_0                                                   |    <0.001 |
|         m08_couplers                                                             |     0.005 |
|           auto_pc                                                                |     0.005 |
|             inst                                                                 |     0.005 |
|               gen_axilite.gen_b2s_conv.axilite_b2s                               |     0.005 |
|                 RD.ar_channel_0                                                  |    <0.001 |
|                   ar_cmd_fsm_0                                                   |    <0.001 |
|                   cmd_translator_0                                               |    <0.001 |
|                     incr_cmd_0                                                   |    <0.001 |
|                     wrap_cmd_0                                                   |    <0.001 |
|                 RD.r_channel_0                                                   |    <0.001 |
|                   rd_data_fifo_0                                                 |    <0.001 |
|                   transaction_fifo_0                                             |    <0.001 |
|                 SI_REG                                                           |     0.002 |
|                   ar_pipe                                                        |    <0.001 |
|                   aw_pipe                                                        |    <0.001 |
|                   b_pipe                                                         |    <0.001 |
|                   r_pipe                                                         |    <0.001 |
|                 WR.aw_channel_0                                                  |    <0.001 |
|                   aw_cmd_fsm_0                                                   |    <0.001 |
|                   cmd_translator_0                                               |    <0.001 |
|                     incr_cmd_0                                                   |    <0.001 |
|                     wrap_cmd_0                                                   |    <0.001 |
|                 WR.b_channel_0                                                   |    <0.001 |
|                   bid_fifo_0                                                     |    <0.001 |
|                   bresp_fifo_0                                                   |    <0.001 |
|         m09_couplers                                                             |     0.005 |
|           auto_pc                                                                |     0.005 |
|             inst                                                                 |     0.005 |
|               gen_axilite.gen_b2s_conv.axilite_b2s                               |     0.005 |
|                 RD.ar_channel_0                                                  |    <0.001 |
|                   ar_cmd_fsm_0                                                   |    <0.001 |
|                   cmd_translator_0                                               |    <0.001 |
|                     incr_cmd_0                                                   |    <0.001 |
|                     wrap_cmd_0                                                   |    <0.001 |
|                 RD.r_channel_0                                                   |    <0.001 |
|                   rd_data_fifo_0                                                 |    <0.001 |
|                   transaction_fifo_0                                             |    <0.001 |
|                 SI_REG                                                           |     0.002 |
|                   ar_pipe                                                        |    <0.001 |
|                   aw_pipe                                                        |    <0.001 |
|                   b_pipe                                                         |    <0.001 |
|                   r_pipe                                                         |    <0.001 |
|                 WR.aw_channel_0                                                  |    <0.001 |
|                   aw_cmd_fsm_0                                                   |    <0.001 |
|                   cmd_translator_0                                               |    <0.001 |
|                     incr_cmd_0                                                   |    <0.001 |
|                     wrap_cmd_0                                                   |    <0.001 |
|                 WR.b_channel_0                                                   |    <0.001 |
|                   bid_fifo_0                                                     |    <0.001 |
|                   bresp_fifo_0                                                   |    <0.001 |
|         s00_couplers                                                             |     0.000 |
|           auto_pc                                                                |     0.000 |
|         xbar                                                                     |     0.007 |
|           inst                                                                   |     0.007 |
|             gen_samd.crossbar_samd                                               |     0.007 |
|               addr_arbiter_ar                                                    |    <0.001 |
|               addr_arbiter_aw                                                    |    <0.001 |
|               gen_decerr_slave.decerr_slave_inst                                 |    <0.001 |
|               gen_master_slots[0].reg_slice_mi                                   |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_master_slots[10].reg_slice_mi                                  |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_master_slots[1].reg_slice_mi                                   |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_master_slots[2].reg_slice_mi                                   |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_master_slots[3].reg_slice_mi                                   |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_master_slots[4].reg_slice_mi                                   |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_master_slots[5].reg_slice_mi                                   |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_master_slots[6].reg_slice_mi                                   |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_master_slots[7].reg_slice_mi                                   |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_master_slots[8].reg_slice_mi                                   |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_master_slots[9].reg_slice_mi                                   |    <0.001 |
|                 b_pipe                                                           |    <0.001 |
|                 r_pipe                                                           |    <0.001 |
|               gen_slave_slots[0].gen_si_read.si_transactor_ar                    |     0.002 |
|                 gen_multi_thread.arbiter_resp_inst                               |    <0.001 |
|                 gen_multi_thread.mux_resp_multi_thread                           |    <0.001 |
|               gen_slave_slots[0].gen_si_write.si_transactor_aw                   |     0.002 |
|                 gen_multi_thread.arbiter_resp_inst                               |    <0.001 |
|                 gen_multi_thread.mux_resp_multi_thread                           |    <0.001 |
|               gen_slave_slots[0].gen_si_write.splitter_aw_si                     |    <0.001 |
|               gen_slave_slots[0].gen_si_write.wdata_router_w                     |    <0.001 |
|                 wrouter_aw_fifo                                                  |    <0.001 |
|                   gen_srls[0].gen_rep[0].srl_nx1                                 |    <0.001 |
|                   gen_srls[0].gen_rep[1].srl_nx1                                 |    <0.001 |
|                   gen_srls[0].gen_rep[2].srl_nx1                                 |    <0.001 |
|                   gen_srls[0].gen_rep[3].srl_nx1                                 |    <0.001 |
|                   gen_srls[0].gen_rep[4].srl_nx1                                 |    <0.001 |
|               splitter_aw_mi                                                     |    <0.001 |
|       axi_hp0_interconnect                                                       |     0.000 |
|       axi_hp1_interconnect                                                       |     0.000 |
|       axi_hp2_interconnect                                                       |     0.000 |
|       sys_concat_intc                                                            |     0.000 |
|       sys_ps7                                                                    |     1.546 |
|         inst                                                                     |     1.546 |
|       sys_rstgen                                                                 |    <0.001 |
|         U0                                                                       |    <0.001 |
|           EXT_LPF                                                                |    <0.001 |
|             ACTIVE_LOW_EXT.ACT_LO_EXT                                            |    <0.001 |
|           SEQ                                                                    |    <0.001 |
|             SEQ_COUNTER                                                          |    <0.001 |
|       util_ad9361_adc_fifo                                                       |     0.002 |
|         inst                                                                     |     0.002 |
|           i_mem                                                                  |     0.001 |
|       util_ad9361_adc_pack                                                       |     0.025 |
|         inst                                                                     |     0.025 |
|           g_dsf[0].i_dsf                                                         |     0.009 |
|           g_dsf[1].i_dsf                                                         |     0.003 |
|           g_dsf[2].i_dsf                                                         |     0.006 |
|           g_dsf[3].i_dsf                                                         |    <0.001 |
|           g_mux[0].i_mux                                                         |     0.004 |
|       util_ad9361_dac_upack                                                      |    <0.001 |
|         inst                                                                     |    <0.001 |
|           g_dmx[0].i_dmx                                                         |    <0.001 |
|           g_dsf[0].i_dsf                                                         |    <0.001 |
|           g_dsf[1].i_dsf                                                         |    <0.001 |
|           g_dsf[2].i_dsf                                                         |    <0.001 |
|           g_dsf[3].i_dsf                                                         |    <0.001 |
|       util_ad9361_divclk                                                         |    <0.001 |
|         inst                                                                     |    <0.001 |
|       util_ad9361_divclk_reset                                                   |    <0.001 |
|         U0                                                                       |    <0.001 |
|           EXT_LPF                                                                |     0.000 |
|             ACTIVE_LOW_EXT.ACT_LO_EXT                                            |     0.000 |
|           SEQ                                                                    |    <0.001 |
|             SEQ_COUNTER                                                          |     0.000 |
|       util_ad9361_divclk_sel                                                     |    <0.001 |
|       util_ad9361_divclk_sel_concat                                              |     0.000 |
|       util_ad9361_tdd_sync                                                       |    <0.001 |
|         inst                                                                     |    <0.001 |
|           i_tdd_sync                                                             |    <0.001 |
|   test_vio_0                                                                     |    <0.001 |
|     inst                                                                         |    <0.001 |
|       DECODER_INST                                                               |    <0.001 |
|       PROBE_IN_INST                                                              |    <0.001 |
|       PROBE_OUT_ALL_INST                                                         |    <0.001 |
|         G_PROBE_OUT[0].PROBE_OUT0_INST                                           |    <0.001 |
|         G_PROBE_OUT[10].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[11].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[12].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[13].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[14].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[15].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[16].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[17].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[18].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[19].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[1].PROBE_OUT0_INST                                           |    <0.001 |
|         G_PROBE_OUT[20].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[21].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[22].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[23].PROBE_OUT0_INST                                          |    <0.001 |
|         G_PROBE_OUT[2].PROBE_OUT0_INST                                           |    <0.001 |
|         G_PROBE_OUT[3].PROBE_OUT0_INST                                           |    <0.001 |
|         G_PROBE_OUT[4].PROBE_OUT0_INST                                           |    <0.001 |
|         G_PROBE_OUT[5].PROBE_OUT0_INST                                           |    <0.001 |
|         G_PROBE_OUT[6].PROBE_OUT0_INST                                           |    <0.001 |
|         G_PROBE_OUT[7].PROBE_OUT0_INST                                           |    <0.001 |
|         G_PROBE_OUT[8].PROBE_OUT0_INST                                           |    <0.001 |
|         G_PROBE_OUT[9].PROBE_OUT0_INST                                           |    <0.001 |
|       PROBE_OUT_WIDTH_INST                                                       |    <0.001 |
|       U_XSDB_SLAVE                                                               |    <0.001 |
+----------------------------------------------------------------------------------+-----------+


