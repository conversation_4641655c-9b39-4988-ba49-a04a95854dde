#-----------------------------------------------------------
# Vivado v2016.4 (64-bit)
# SW Build 1756540 on Mon Jan 23 19:11:23 MST 2017
# IP Build 1755317 on Mon Jan 23 20:30:07 MST 2017
# Start of session at: Fri May 08 14:36:52 2020
# Process ID: 3740
# Current directory: E:/wgfile/R75_Z7035_0320
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent4528 E:\wgfile\R75_Z7035_0320\fmcomms2_zed.xpr
# Log file: E:/wgfile/R75_Z7035_0320/vivado.log
# Journal file: E:/wgfile/R75_Z7035_0320\vivado.jou
#-----------------------------------------------------------
start_gui
open_project E:/wgfile/R75_Z7035_0320/fmcomms2_zed.xpr
INFO: [Project 1-313] Project file moved from 'E:/FPGA/R75_Z7035_0320/R75_Z7035_0320' since last save.
Scanning sources...
Finished scanning sources
WARNING: [filemgmt 56-2] IP Repository Path: Could not find the directory 'E:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/library', nor could it be found using path 'E:/FPGA/hdl-hdl_2017_r1/hdl-hdl_2017_r1/library'.
INFO: [IP_Flow 19-234] Refreshing IP repositories
WARNING: [IP_Flow 19-2248] Failed to load user IP repository 'e:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/library'; Can't find the specified path.
If this directory should no longer be in your list of user repositories, go to the IP Settings dialog and remove it.
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'D:/vivado2016/Vivado/2016.4/data/ip'.
WARNING: [BD 41-1661] One or more IPs have been locked in the design 'system.bd'. Please run report_ip_status for more details and recommendations on how to fix this issue.
List of locked IPs:
system_axi_ad9361_0
system_axi_ad9361_dac_dma_0
system_util_ad9361_adc_fifo_0
system_util_ad9361_tdd_sync_0
system_util_ad9361_dac_upack_0
system_util_ad9361_divclk_0
system_util_ad9361_adc_pack_0
system_axi_ad9361_adc_dma_0
system_axi_ad9361_dac_fifo_0

open_project: Time (s): cpu = 00:00:33 ; elapsed = 00:00:57 . Memory (MB): peak = 848.070 ; gain = 196.707
open_hw
connect_hw_server
INFO: [Labtools 27-2285] Connecting to hw_server url TCP:localhost:3121
INFO: [Labtools 27-2222] Launching hw_server...
INFO: [Labtools 27-2221] Launch Output:

****** Xilinx hw_server v2016.4
  **** Build date : Jan 23 2017-19:37:29
    ** Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.


connect_hw_server: Time (s): cpu = 00:00:05 ; elapsed = 00:00:06 . Memory (MB): peak = 834.988 ; gain = 0.000
open_hw_target
INFO: [Labtoolstcl 44-466] Opening hw_target localhost:3121/xilinx_tcf/Digilent/210251A08870
open_hw_target: Time (s): cpu = 00:00:05 ; elapsed = 00:00:06 . Memory (MB): peak = 909.359 ; gain = 74.371
set_property PROGRAM.FILE {E:/wgfile/R75_Z7035_0320/fmcomms2_zed.runs/impl_3/system_top.bit} [lindex [get_hw_devices xc7z035_1] 0]
set_property PROBES.FILE {E:/wgfile/R75_Z7035_0320/fmcomms2_zed.runs/impl_3/debug_nets.ltx} [lindex [get_hw_devices xc7z035_1] 0]
current_hw_device [lindex [get_hw_devices xc7z035_1] 0]
refresh_hw_device [lindex [get_hw_devices xc7z035_1] 0]
INFO: [Labtools 27-2302] Device xc7z035 (JTAG device index = 1) is programmed with a design that has 1 VIO core(s).
WARNING: [Labtoolstcl 44-130] No matching hw_ilas were found.
WARNING: [Labtoolstcl 44-130] No matching hw_ilas were found.
WARNING: [Labtoolstcl 44-130] No matching hw_ilas were found.
WARNING: [Labtoolstcl 44-130] No matching hw_ilas were found.
WARNING: [Labtools 27-1952] VIO hw_probe OUTPUT_VALUE properties for hw_vio(s) [hw_vio_1] differ from output values in the VIO core(s).
Resolution: 
To synchronize the hw_probes properties and the VIO core outputs choose one of the following alternatives:
  1) Execute the command 'Commit Output Values to VIO Core', to write down the hw_probe values to the core.
  2) Execute the command 'Refresh Input and Output Values from VIO Core', to update the hw_probe properties with the core values.
  3) First restore initial values in the core with the command 'Reset VIO Core Outputs', and then execute the command 'Refresh Input and Output Values from VIO Core'.
refresh_hw_device: Time (s): cpu = 00:00:09 ; elapsed = 00:00:08 . Memory (MB): peak = 924.355 ; gain = 14.996
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_A_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_A_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 0 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
set_property OUTPUT_VALUE 1 [get_hw_probes PL_CTRL_B_OBUF -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
commit_hw_vio [get_hw_probes {PL_CTRL_B_OBUF} -of_objects [get_hw_vios -of_objects [get_hw_devices xc7z035_1] -filter {CELL_NAME=~"test_vio_0"}]]
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Xicom 50-38] xicom: Unable to connect to debug core(s) on the target device. Check cable connectivity and that the target board is powered up then use the disconnect_hw_server and connect_hw_server to re-initialize the hardware target. Use open_hw_target to re-register the hardware device.
ERROR: [Labtools 27-2269] No devices detected on target localhost:3121/xilinx_tcf/Digilent/210251A08870.
Check cable connectivity and that the target board is powered up then
use the disconnect_hw_server and connect_hw_server to re-register this hardware target.
INFO: [Labtools 27-2302] Device xc7z035 (JTAG device index = 1) is programmed with a design that has 2 ILA core(s).
WARNING: [Labtools 27-1974] Mismatch between the design programmed into the device xc7z035_1 and the probes file(s) E:/wgfile/R75_Z7035_0320/fmcomms2_zed.runs/impl_3/debug_nets.ltx.
The device design has 2 ILA core(s) and 0 VIO core(s). The probes file(s) have 0 ILA core(s) and 1 VIO core(s).
Resolution: 
1. Reprogram device with the correct programming file and associated probes file(s) OR
2. Goto device properties and associate the correct probes file(s) with the programming file already programmed in the device.
refresh_hw_device: Time (s): cpu = 00:00:12 ; elapsed = 00:00:08 . Memory (MB): peak = 972.172 ; gain = 2.590
close_project
exit
INFO: [Common 17-206] Exiting Vivado at Fri May 08 15:05:54 2020...
