<?xml version="1.0" encoding="UTF-8"?>
<GenRun Id="impl_3" LaunchPart="xc7z035ffg676-2" LaunchTime="1584435154">
  <File Type="ROUTE-PWR-RPX" Name="system_top_power_routed.rpx"/>
  <File Type="BITSTR-BMM" Name="system_top_bd.bmm"/>
  <File Type="ROUTE-PWR" Name="system_top_power_routed.rpt"/>
  <File Type="PA-TCL" Name="system_top.tcl"/>
  <File Type="OPT-DRC" Name="system_top_drc_opted.rpt"/>
  <File Type="ROUTE-CLK" Name="system_top_clock_utilization_routed.rpt"/>
  <File Type="RDI-RDI" Name="system_top.vdi"/>
  <File Type="OPT-DCP" Name="system_top_opt.dcp"/>
  <File Type="OPT-HWDEF" Name="system_top.hwdef"/>
  <File Type="PWROPT-DCP" Name="system_top_pwropt.dcp"/>
  <File Type="BG-DRC" Name="system_top.drc"/>
  <File Type="PLACE-DCP" Name="system_top_placed.dcp"/>
  <File Type="PLACE-IO" Name="system_top_io_placed.rpt"/>
  <File Type="PLACE-UTIL" Name="system_top_utilization_placed.rpt"/>
  <File Type="PLACE-UTIL-PB" Name="system_top_utilization_placed.pb"/>
  <File Type="PLACE-CTRL" Name="system_top_control_sets_placed.rpt"/>
  <File Type="PLACE-PRE-SIMILARITY" Name="system_top_incremental_reuse_pre_placed.rpt"/>
  <File Type="BG-BGN" Name="system_top.bgn"/>
  <File Type="POSTPLACE-PWROPT-DCP" Name="system_top_postplace_pwropt.dcp"/>
  <File Type="BG-BIN" Name="system_top.bin"/>
  <File Type="PHYSOPT-DCP" Name="system_top_physopt.dcp"/>
  <File Type="BITSTR-MSK" Name="system_top.msk"/>
  <File Type="ROUTE-ERROR-DCP" Name="system_top_routed_error.dcp"/>
  <File Type="ROUTE-DCP" Name="system_top_routed.dcp"/>
  <File Type="ROUTE-BLACKBOX-DCP" Name="system_top_routed_bb.dcp"/>
  <File Type="ROUTE-DRC" Name="system_top_drc_routed.rpt"/>
  <File Type="ROUTE-DRC-PB" Name="system_top_drc_routed.pb"/>
  <File Type="ROUTE-DRC-RPX" Name="system_top_drc_routed.rpx"/>
  <File Type="BITSTR-MMI" Name="system_top.mmi"/>
  <File Type="ROUTE-METHODOLOGY-DRC" Name="system_top_methodology_drc_routed.rpt"/>
  <File Type="ROUTE-METHODOLOGY-DRC-RPX" Name="system_top_methodology_drc_routed.rpx"/>
  <File Type="BITSTR-SYSDEF" Name="system_top.sysdef"/>
  <File Type="ROUTE-METHODOLOGY-DRC-PB" Name="system_top_methodology_drc_routed.pb"/>
  <File Type="ROUTE-PWR-SUM" Name="system_top_power_summary_routed.pb"/>
  <File Type="ROUTE-STATUS" Name="system_top_route_status.rpt"/>
  <File Type="ROUTE-STATUS-PB" Name="system_top_route_status.pb"/>
  <File Type="ROUTE-TIMINGSUMMARY" Name="system_top_timing_summary_routed.rpt"/>
  <File Type="ROUTE-TIMING-PB" Name="system_top_timing_summary_routed.pb"/>
  <File Type="ROUTE-TIMING-RPX" Name="system_top_timing_summary_routed.rpx"/>
  <File Type="POSTROUTE-PHYSOPT-DCP" Name="system_top_postroute_physopt.dcp"/>
  <File Type="POSTROUTE-PHYSOPT-BLACKBOX-DCP" Name="system_top_postroute_physopt_bb.dcp"/>
  <File Type="BG-BIT" Name="system_top.bit"/>
  <File Type="BITSTR-RBT" Name="system_top.rbt"/>
  <File Type="BITSTR-NKY" Name="system_top.nky"/>
  <File Type="WBT-USG" Name="usage_statistics_webtalk.html"/>
  <FileSet Name="sources" Type="DesignSrcs" RelSrcDir="$PSRCDIR/sources_1">
    <Filter Type="Srcs"/>
    <File Path="$PSRCDIR/sources_1/bd/system/system.bd">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/library/xilinx/common/ad_iobuf.v">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PPRDIR/../../hdl-hdl_2017_r1/hdl-hdl_2017_r1/library/xilinx/common/ad_iobuf.v"/>
        <Attr Name="ImportTime" Val="1518442275"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/hdl/system_wrapper.v">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PSRCDIR/sources_1/bd/system/hdl/system_wrapper.v"/>
        <Attr Name="ImportTime" Val="1584428448"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_top.v">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PPRDIR/system_top.v"/>
        <Attr Name="ImportTime" Val="1572310677"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PPRDIR/system_constr.xdc"/>
        <Attr Name="ImportTime" Val="1572310673"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PPRDIR/../../hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc"/>
        <Attr Name="ImportTime" Val="1572255971"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="DesignMode" Val="RTL"/>
      <Option Name="TopModule" Val="system_top"/>
    </Config>
  </FileSet>
  <FileSet Name="constrs_in" Type="Constrs" RelSrcDir="$PSRCDIR/constrs_1">
    <Filter Type="Constrs"/>
    <File Path="$PSRCDIR/constrs_1/new/cdc.xdc">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="TargetConstrsFile" Val="$PSRCDIR/constrs_1/new/cdc.xdc"/>
      <Option Name="ConstrsType" Val="XDC"/>
    </Config>
  </FileSet>
  <Strategy Version="1" Minor="2">
    <StratHandle Name="Vivado Implementation Defaults" Flow="Vivado Implementation 2016"/>
    <Step Id="init_design"/>
    <Step Id="opt_design"/>
    <Step Id="power_opt_design"/>
    <Step Id="place_design"/>
    <Step Id="post_place_power_opt_design"/>
    <Step Id="phys_opt_design"/>
    <Step Id="route_design"/>
    <Step Id="post_route_phys_opt_design"/>
    <Step Id="write_bitstream"/>
  </Strategy>
  <BlockFileSet Type="BlockSrcs" Name="vio_0"/>
</GenRun>
