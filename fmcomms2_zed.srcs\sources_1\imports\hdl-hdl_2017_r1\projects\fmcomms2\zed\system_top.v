// ***************************************************************************
// ***************************************************************************
// Copyright 2014 - 2017 (c) Analog Devices, Inc. All rights reserved.
//
// In this HDL repository, there are many different and unique modules, consisting
// of various HDL (Verilog or VHDL) components. The individual modules are
// developed independently, and may be accompanied by separate and unique license
// terms.
//
// The user should read each of these license terms, and understand the
// freedoms and responsabilities that he or she has by using this source/core.
//
// This core is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
// A PARTICULAR PURPOSE.
//
// Redistribution and use of source or resulting binaries, with or without modification
// of this file, are permitted under one of the following two license terms:
//
//   1. The GNU General Public License version 2 as published by the
//      Free Software Foundation, which can be found in the top level directory
//      of this repository (LICENSE_GPL2), and also online at:
//      <https://www.gnu.org/licenses/old-licenses/gpl-2.0.html>
//
// OR
//
//   2. An ADI specific BSD license, which can be found in the top level directory
//      of this repository (LICENSE_ADIBSD), and also on-line at:
//      https://github.com/analogdevicesinc/hdl/blob/master/LICENSE_ADIBSD
//      This will allow to generate bit files and not release the source code,
//      as long as it attaches to an ADI device.
//
// ***************************************************************************
// ***************************************************************************

`timescale 1ns/100ps

module system_top (

  inout       [14:0]      ddr_addr,
  inout       [ 2:0]      ddr_ba,
  inout                   ddr_cas_n,
  inout                   ddr_ck_n,
  inout                   ddr_ck_p,
  inout                   ddr_cke,
  inout                   ddr_cs_n,
  inout       [ 3:0]      ddr_dm,
  inout       [31:0]      ddr_dq,
  inout       [ 3:0]      ddr_dqs_n,
  inout       [ 3:0]      ddr_dqs_p,
  inout                   ddr_odt,
  inout                   ddr_ras_n,
  inout                   ddr_reset_n,
  inout                   ddr_we_n,

  inout                   fixed_io_ddr_vrn,
  inout                   fixed_io_ddr_vrp,
  inout       [53:0]      fixed_io_mio,
  inout                   fixed_io_ps_clk,
  inout                   fixed_io_ps_porb,
  inout                   fixed_io_ps_srstb,

//  inout       [31:0]      gpio_bd,

//  output                  hdmi_out_clk,
//  output                  hdmi_vsync,
//  output                  hdmi_hsync,
//  output                  hdmi_data_e,
//  output      [15:0]      hdmi_data,

//  output                  i2s_mclk,
//  output                  i2s_bclk,
//  output                  i2s_lrclk,
//  output                  i2s_sdata_out,
//  input                   i2s_sdata_in,

//  output                  spdif,

//  inout                   iic_scl,
//  inout                   iic_sda,
//  inout       [ 1:0]      iic_mux_scl,
//  inout       [ 1:0]      iic_mux_sda,

//  input                   otg_vbusoc,

  input                   rx_clk_in_p,
  input                   rx_clk_in_n,
  input                   rx_frame_in_p,
  input                   rx_frame_in_n,
  input       [ 5:0]      rx_data_in_p,
  input       [ 5:0]      rx_data_in_n,
  output                  tx_clk_out_p,
  output                  tx_clk_out_n,
  output                  tx_frame_out_p,
  output                  tx_frame_out_n,
  output      [ 5:0]      tx_data_out_p,
  output      [ 5:0]      tx_data_out_n,

  output                  txnrx,
  output                  enable,


  inout                   gpio_resetb,
  inout                   gpio_sync,
  inout                   gpio_en_agc,
  inout       [ 3:0]      gpio_ctl,
  inout       [ 7:0]      gpio_status,

  output                  spi_csn,
  output                  spi_clk,
  output                  spi_mosi,
  input                   spi_miso,
  
  output      [ 6:1]      test,  
  output                  PL_UART_TX,
  input                   PL_UART_RX,
  output                  PL_UART1_TX,
  input                   PL_UART1_RX,
  output                  PL_UART2_TX,
  input                   PL_UART2_RX,
  output                  PL_UART4_TX,
  input                   PL_UART4_RX,

  output                  BK_CS1,
  output                  BK_MISO1,
  output                  BK_MOSI1,
  output                  BK_SCLK1,
  output                  BK_LOAD,
  output                  BK_IRQ,
  output                  BK_RST,
  output                  BK_TR_SW,
  
  output                  XD_R_B,
  output                  XD_T_P,
  output                  XD_XS,
  output                  XD_HX_R,
  output                  XD_T_R,
  output                  XD_T_P1,

  output                  PL_QSPI_CCLK,
  output                  PL_QSPI_CS_N,
  inout                   PL_QSPI_IO0,
  inout                   PL_QSPI_IO1,
  inout                   PL_QSPI_IO2,
  inout                   PL_QSPI_IO3,          

  output                  PL_CTRL_A,
  output                  PL_CTRL_B,
  output                  IO1,
  output                  IO2,
  output                  TSELF
);

  // internal signals

  wire    [63:0]  gpio_i;
  wire    [63:0]  gpio_o;
  wire    [63:0]  gpio_t;

  wire       [31:0]      gpio_bd;

  wire            FCLK_CLK0;

  // instantiations

  ad_iobuf #(.DATA_WIDTH(47)) i_iobuf_gpio (
    .dio_t (gpio_t[46:0]),
    .dio_i (gpio_o[46:0]),
    .dio_o (gpio_i[46:0]),
    .dio_p ({ gpio_resetb,
              gpio_sync,
              gpio_en_agc,
              gpio_ctl,
              gpio_status,
              gpio_bd}));


    vio_0 test_vio_0(
    .clk(FCLK_CLK0),
    .probe_in0(PL_UART_RX),
    .probe_in1(PL_UART1_RX),
    .probe_in2(PL_UART2_RX),
    .probe_in3(PL_UART4_RX),
    .probe_out0(test),
    .probe_out1(PL_UART_TX),
    .probe_out2(PL_UART1_TX),
    .probe_out3(PL_UART2_TX),
    .probe_out4(PL_UART4_TX),
    .probe_out5(BK_CS1),
    .probe_out6(BK_MISO1),
    .probe_out7(BK_MOSI1),
    .probe_out8(BK_SCLK1),
    .probe_out9(BK_LOAD),
    .probe_out10(BK_IRQ),
    .probe_out11(BK_RST),
    .probe_out12(BK_TR_SW),
    .probe_out13(XD_R_B),
    .probe_out14(XD_T_P),
    .probe_out15(XD_XS),
    .probe_out16(XD_HX_R),
    .probe_out17(XD_T_R),
    .probe_out18(XD_T_P1),
    .probe_out19(PL_CTRL_A),
    .probe_out20(PL_CTRL_B),
    .probe_out21(IO1),
    .probe_out22(IO2),
    .probe_out23(TSELF)
    );     
              
  system_wrapper i_system_wrapper (
    .FCLK_CLK0(FCLK_CLK0),
    .ddr_addr (ddr_addr),
    .ddr_ba (ddr_ba),
    .ddr_cas_n (ddr_cas_n),
    .ddr_ck_n (ddr_ck_n),
    .ddr_ck_p (ddr_ck_p),
    .ddr_cke (ddr_cke),
    .ddr_cs_n (ddr_cs_n),
    .ddr_dm (ddr_dm),
    .ddr_dq (ddr_dq),
    .ddr_dqs_n (ddr_dqs_n),
    .ddr_dqs_p (ddr_dqs_p),
    .ddr_odt (ddr_odt),
    .ddr_ras_n (ddr_ras_n),
    .ddr_reset_n (ddr_reset_n),
    .ddr_we_n (ddr_we_n),
    .fixed_io_ddr_vrn (fixed_io_ddr_vrn),
    .fixed_io_ddr_vrp (fixed_io_ddr_vrp),
    .fixed_io_mio (fixed_io_mio),
    .fixed_io_ps_clk (fixed_io_ps_clk),
    .fixed_io_ps_porb (fixed_io_ps_porb),
    .fixed_io_ps_srstb (fixed_io_ps_srstb),
    .gpio_i (gpio_i),
    .gpio_o (gpio_o),
    .gpio_t (gpio_t),
    .ps_intr_00 (1'b0),
    .ps_intr_01 (1'b0),
    .ps_intr_02 (1'b0),
    .ps_intr_03 (1'b0),
    .ps_intr_04 (1'b0),
    .ps_intr_05 (1'b0),
    .ps_intr_06 (1'b0),
    .ps_intr_07 (1'b0),
    .ps_intr_08 (1'b0),
    .ps_intr_09 (1'b0),
    .ps_intr_10 (1'b0),
    .rx_clk_in_n (rx_clk_in_n),
    .rx_clk_in_p (rx_clk_in_p),
    .rx_data_in_n (rx_data_in_n),
    .rx_data_in_p (rx_data_in_p),
    .rx_frame_in_n (rx_frame_in_n),
    .rx_frame_in_p (rx_frame_in_p),
    .spi0_clk_i (1'b0),
    .spi0_clk_o (spi_clk),
    .spi0_csn_0_o (spi_csn),
    .spi0_csn_1_o (),
    .spi0_csn_2_o (),
    .spi0_csn_i (1'b1),
    .spi0_sdi_i (spi_miso),
    .spi0_sdo_i (1'b0),
    .spi0_sdo_o (spi_mosi),
    .tx_clk_out_n (tx_clk_out_n),
    .tx_clk_out_p (tx_clk_out_p),
    .tx_data_out_n (tx_data_out_n),
    .tx_data_out_p (tx_data_out_p),
    .tx_frame_out_n (tx_frame_out_n),
    .tx_frame_out_p (tx_frame_out_p),
    .enable (enable),
    .txnrx (txnrx),
    .up_enable (gpio_o[47]),
    .up_txnrx (gpio_o[48]));

endmodule

// ***************************************************************************
// ***************************************************************************
