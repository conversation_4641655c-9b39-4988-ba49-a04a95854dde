// ***************************************************************************
// ***************************************************************************
// Copyright 2014 - 2017 (c) Analog Devices, Inc. All rights reserved.
//
// In this HDL repository, there are many different and unique modules, consisting
// of various HDL (Verilog or VHDL) components. The individual modules are
// developed independently, and may be accompanied by separate and unique license
// terms.
//
// The user should read each of these license terms, and understand the
// freedoms and responsabilities that he or she has by using this source/core.
//
// This core is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
// A PARTICULAR PURPOSE.
//
// Redistribution and use of source or resulting binaries, with or without modification
// of this file, are permitted under one of the following two license terms:
//
//   1. The GNU General Public License version 2 as published by the
//      Free Software Foundation, which can be found in the top level directory
//      of this repository (LICENSE_GPL2), and also online at:
//      <https://www.gnu.org/licenses/old-licenses/gpl-2.0.html>
//
// OR
//
//   2. An ADI specific BSD license, which can be found in the top level directory
//      of this repository (LICENSE_ADIBSD), and also on-line at:
//      https://github.com/analogdevicesinc/hdl/blob/master/LICENSE_ADIBSD
//      This will allow to generate bit files and not release the source code,
//      as long as it attaches to an ADI device.
//
// ***************************************************************************
// ***************************************************************************

module dmac_dest_mm_axi #(

  parameter ID_WIDTH = 3,
  parameter DMA_DATA_WIDTH = 64,
  parameter DMA_ADDR_WIDTH = 32,
  parameter BYTES_PER_BEAT_WIDTH = $clog2(DMA_DATA_WIDTH/8),
  parameter BEATS_PER_BURST_WIDTH = 4,
  parameter AXI_LENGTH_WIDTH = 8)(

  input                               m_axi_aclk,
  input                               m_axi_aresetn,

  input                               req_valid,
  output                              req_ready,
  input [DMA_ADDR_WIDTH-1:BYTES_PER_BEAT_WIDTH] req_address,
  input [BEATS_PER_BURST_WIDTH-1:0] req_last_burst_length,

  input                               enable,
  output                              enabled,
  input                               pause,
  input                               sync_id,
  output                              sync_id_ret,

  output                              response_valid,
  input                               response_ready,
  output [1:0]                        response_resp,
  output                              response_resp_eot,

  input  [ID_WIDTH-1:0]             request_id,
  output [ID_WIDTH-1:0]             response_id,

  output [ID_WIDTH-1:0]             data_id,
  output [ID_WIDTH-1:0]             address_id,
  input                               data_eot,
  input                               address_eot,
  input                               response_eot,

  input                               fifo_valid,
  output                              fifo_ready,
  input [DMA_DATA_WIDTH-1:0]        fifo_data,

  // Write address
  input                               m_axi_awready,
  output                              m_axi_awvalid,
  output [DMA_ADDR_WIDTH-1:0]         m_axi_awaddr,
  output [AXI_LENGTH_WIDTH-1:0]       m_axi_awlen,
  output [ 2:0]                       m_axi_awsize,
  output [ 1:0]                       m_axi_awburst,
  output [ 2:0]                       m_axi_awprot,
  output [ 3:0]                       m_axi_awcache,

  // Write data
  output [DMA_DATA_WIDTH-1:0]     m_axi_wdata,
  output [(DMA_DATA_WIDTH/8)-1:0] m_axi_wstrb,
  input                               m_axi_wready,
  output                              m_axi_wvalid,
  output                              m_axi_wlast,

  // Write response
  input                               m_axi_bvalid,
  input  [ 1:0]                       m_axi_bresp,
  output                              m_axi_bready
);

wire address_req_valid;
wire address_req_ready;
wire data_req_valid;
wire data_req_ready;

wire address_enabled;
wire data_enabled;
assign sync_id_ret = sync_id;

wire _fifo_ready;
assign fifo_ready = _fifo_ready | ~enabled;

splitter #(
  .NUM_M(2)
) i_req_splitter (
  .clk(m_axi_aclk),
  .resetn(m_axi_aresetn),
  .s_valid(req_valid),
  .s_ready(req_ready),
  .m_valid({
    address_req_valid,
    data_req_valid
  }),
  .m_ready({
    address_req_ready,
    data_req_ready
  })
);

dmac_address_generator #(
  .ID_WIDTH(ID_WIDTH),
  .BEATS_PER_BURST_WIDTH(BEATS_PER_BURST_WIDTH),
  .BYTES_PER_BEAT_WIDTH(BYTES_PER_BEAT_WIDTH),
  .DMA_DATA_WIDTH(DMA_DATA_WIDTH),
  .LENGTH_WIDTH(AXI_LENGTH_WIDTH),
  .DMA_ADDR_WIDTH(DMA_ADDR_WIDTH)
) i_addr_gen (
  .clk(m_axi_aclk),
  .resetn(m_axi_aresetn),

  .enable(enable),
  .enabled(address_enabled),
  .pause(pause),

  .id(address_id),
  .request_id(request_id),
  .sync_id(sync_id),

  .req_valid(address_req_valid),
  .req_ready(address_req_ready),
  .req_address(req_address),
  .req_last_burst_length(req_last_burst_length),

  .eot(address_eot),

  .addr_ready(m_axi_awready),
  .addr_valid(m_axi_awvalid),
  .addr(m_axi_awaddr),
  .len(m_axi_awlen),
  .size(m_axi_awsize),
  .burst(m_axi_awburst),
  .prot(m_axi_awprot),
  .cache(m_axi_awcache)
);

dmac_data_mover # (
  .ID_WIDTH(ID_WIDTH),
  .DATA_WIDTH(DMA_DATA_WIDTH),
  .BEATS_PER_BURST_WIDTH(BEATS_PER_BURST_WIDTH)
) i_data_mover (
  .clk(m_axi_aclk),
  .resetn(m_axi_aresetn),

  .enable(address_enabled),
  .enabled(data_enabled),

  .xfer_req(),

  .request_id(address_id),
  .response_id(data_id),
  .sync_id(sync_id),
  .eot(data_eot),

  .req_valid(data_req_valid),
  .req_ready(data_req_ready),
  .req_last_burst_length(req_last_burst_length),

  .s_axi_valid(fifo_valid),
  .s_axi_ready(_fifo_ready),
  .s_axi_data(fifo_data),
  .m_axi_valid(m_axi_wvalid),
  .m_axi_ready(m_axi_wready),
  .m_axi_data(m_axi_wdata),
  .m_axi_last(m_axi_wlast)
);

assign m_axi_wstrb = {(DMA_DATA_WIDTH/8){1'b1}};

dmac_response_handler #(
  .ID_WIDTH(ID_WIDTH)
) i_response_handler (
  .clk(m_axi_aclk),
  .resetn(m_axi_aresetn),
  .bvalid(m_axi_bvalid),
  .bready(m_axi_bready),
  .bresp(m_axi_bresp),

  .enable(data_enabled),
  .enabled(enabled),

  .id(response_id),
  .request_id(data_id),
  .sync_id(sync_id),

  .eot(response_eot),

  .resp_valid(response_valid),
  .resp_ready(response_ready),
  .resp_resp(response_resp),
  .resp_eot(response_resp_eot)
);

endmodule
