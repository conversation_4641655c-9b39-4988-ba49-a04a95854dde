<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="com.xilinx.sdk.tcf.debug.LaunchConfigurationType">
<booleanAttribute key="com.xilinx.sdk.tcf.debug.ui.breakpoint.main" value="true"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.ui.exec.script" value="false"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.ui.exec.script.location" value=""/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uiapplication.path" value="Debug/ad9361.elf"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uicrosstrigger.bpcontainer" value="{&quot;arch&quot;:&quot;zynq&quot;,&quot;isSelected&quot;:false,&quot;breakPoints&quot;:[]}"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uidebug.type" value="STANDALONE_DEBUG"/>
<booleanAttribute key="com.xilinx.sdk.tcf.debug.uienable.crosstrigger" value="false"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uifpga.device" value="Auto Detect"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uihw.bit.file" value="E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_flash_50M.bit"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uihw.init.tcl" value="ps7_init.tcl"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uihw_platform.name" value="system_top_hw_platform_0"/>
<booleanAttribute key="com.xilinx.sdk.tcf.debug.uipl.powerup" value="false"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uiproc.appl.map" value="{&quot;ps7_cortexa9_1&quot;:{&quot;xilinx.tcf.application&quot;:&quot;&quot;,&quot;xilinx.tcf.datafiles&quot;:&quot;&quot;,&quot;xilinx.tcf.no_download&quot;:true,&quot;xilinx.tcf.profile_enabled&quot;:false,&quot;xilinx.tcf.profile_frequency&quot;:&quot;10000&quot;,&quot;xilinx.tcf.profile_non_int_frequency&quot;:&quot;100000000&quot;,&quot;xilinx.tcf.profile_non_int_high_addr&quot;:&quot;&quot;,&quot;xilinx.tcf.profile_non_int_low_addr&quot;:&quot;&quot;,&quot;xilinx.tcf.profile_non_int_use_count_instr&quot;:false,&quot;xilinx.tcf.profile_non_int_use_cumulate&quot;:false,&quot;xilinx.tcf.profile_non_intrusive_support&quot;:false,&quot;xilinx.tcf.profile_store_address&quot;:&quot;0x0&quot;,&quot;xilinx.tcf.profile_use_intrusive&quot;:false,&quot;xilinx.tcf.project&quot;:&quot;&quot;,&quot;xilinx.tcf.relocate&quot;:false,&quot;xilinx.tcf.relocate_addr&quot;:&quot;&quot;,&quot;xilinx.tcf.reset&quot;:true,&quot;xilinx.tcf.stop_at_entry&quot;:false},&quot;ps7_cortexa9_0&quot;:{&quot;xilinx.tcf.application&quot;:&quot;Debug/ad9361.elf&quot;,&quot;xilinx.tcf.datafiles&quot;:&quot;&quot;,&quot;xilinx.tcf.no_download&quot;:false,&quot;xilinx.tcf.profile_enabled&quot;:false,&quot;xilinx.tcf.profile_frequency&quot;:&quot;10000&quot;,&quot;xilinx.tcf.profile_non_int_frequency&quot;:&quot;100000000&quot;,&quot;xilinx.tcf.profile_non_int_high_addr&quot;:&quot;&quot;,&quot;xilinx.tcf.profile_non_int_low_addr&quot;:&quot;&quot;,&quot;xilinx.tcf.profile_non_int_use_count_instr&quot;:false,&quot;xilinx.tcf.profile_non_int_use_cumulate&quot;:false,&quot;xilinx.tcf.profile_non_intrusive_support&quot;:false,&quot;xilinx.tcf.profile_store_address&quot;:&quot;0x0&quot;,&quot;xilinx.tcf.profile_use_intrusive&quot;:false,&quot;xilinx.tcf.project&quot;:&quot;ad9361&quot;,&quot;xilinx.tcf.relocate&quot;:false,&quot;xilinx.tcf.relocate_addr&quot;:&quot;&quot;,&quot;xilinx.tcf.reset&quot;:true,&quot;xilinx.tcf.stop_at_entry&quot;:false}}"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uiproc.selection" value="ps7_cortexa9_0"/>
<booleanAttribute key="com.xilinx.sdk.tcf.debug.uiprogram.fpga" value="false"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uiproject.name" value="ad9361"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uips.device" value="Auto Detect"/>
<booleanAttribute key="com.xilinx.sdk.tcf.debug.uips7.init" value="true"/>
<booleanAttribute key="com.xilinx.sdk.tcf.debug.uips7.post" value="true"/>
<booleanAttribute key="com.xilinx.sdk.tcf.debug.uireset.apu" value="false"/>
<booleanAttribute key="com.xilinx.sdk.tcf.debug.uireset.lock.step" value="false"/>
<booleanAttribute key="com.xilinx.sdk.tcf.debug.uireset.rpu" value="false"/>
<booleanAttribute key="com.xilinx.sdk.tcf.debug.uireset.system" value="false"/>
<stringAttribute key="com.xilinx.sdk.tcf.debug.uitarget.peer" value="Local"/>
<listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_PATHS">
<listEntry value="/ad9361"/>
</listAttribute>
<listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_TYPES">
<listEntry value="4"/>
</listAttribute>
<booleanAttribute key="org.eclipse.tcf.debug.AttachChildren" value="true"/>
<booleanAttribute key="org.eclipse.tcf.debug.DisconnectOnCtxExit" value="false"/>
<booleanAttribute key="org.eclipse.tcf.debug.StopAtEntry" value="false"/>
<booleanAttribute key="org.eclipse.tcf.debug.StopAtMain" value="true"/>
<booleanAttribute key="org.eclipse.tcf.debug.UseTerminal" value="false"/>
</launchConfiguration>
