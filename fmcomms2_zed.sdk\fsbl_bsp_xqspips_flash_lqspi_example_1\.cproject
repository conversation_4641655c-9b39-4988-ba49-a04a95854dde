<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="xilinx.gnu.armv7.exe.debug.456566226">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="xilinx.gnu.armv7.exe.debug.456566226" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="" id="xilinx.gnu.armv7.exe.debug.456566226" name="Debug" parent="xilinx.gnu.armv7.exe.debug" prebuildStep="a9-linaro-pre-build-step">
					<folderInfo id="xilinx.gnu.armv7.exe.debug.456566226." name="/" resourcePath="">
						<toolChain id="xilinx.gnu.armv7.exe.debug.toolchain.927480622" name="Xilinx ARM v7 GNU Toolchain" superClass="xilinx.gnu.armv7.exe.debug.toolchain">
							<targetPlatform binaryParser="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" id="xilinx.armv7.target.gnu.base.debug.1950752692" isAbstract="false" name="Debug Platform" superClass="xilinx.armv7.target.gnu.base.debug"/>
							<builder buildPath="${workspace_loc:/fsbl_bsp_xqspips_flash_lqspi_example_1}/Debug" enableAutoBuild="true" id="xilinx.gnu.armv7.toolchain.builder.debug.406246199" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="GNU make" superClass="xilinx.gnu.armv7.toolchain.builder.debug"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.assembler.debug.1782827438" name="ARM v7 gcc assembler" superClass="xilinx.gnu.armv7.c.toolchain.assembler.debug">
								<inputType id="xilinx.gnu.assembler.input.599040643" superClass="xilinx.gnu.assembler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.c.toolchain.compiler.debug.1114804754" name="ARM v7 gcc compiler" superClass="xilinx.gnu.armv7.c.toolchain.compiler.debug">
								<option defaultValue="gnu.c.optimization.level.none" id="xilinx.gnu.compiler.option.optimization.level.386486697" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.922494822" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.768125194" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.1787304316" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
								<option id="xilinx.gnu.compiler.misc.other.406490063" name="Other flags" superClass="xilinx.gnu.compiler.misc.other" value="-c -fmessage-length=0 -MT&quot;$@&quot; -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard" valueType="string"/>
								<option id="xilinx.gnu.compiler.symbols.defined.2001164201" superClass="xilinx.gnu.compiler.symbols.defined" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="FSBL_DEBUG_INFO"/>
								</option>
								<inputType id="xilinx.gnu.armv7.c.compiler.input.1949974799" name="C source files" superClass="xilinx.gnu.armv7.c.compiler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.compiler.debug.609497012" name="ARM v7 g++ compiler" superClass="xilinx.gnu.armv7.cxx.toolchain.compiler.debug">
								<option defaultValue="gnu.c.optimization.level.none" id="xilinx.gnu.compiler.option.optimization.level.2112043822" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.1504058019" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.1697947743" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.1937469587" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.toolchain.archiver.1606244535" name="ARM v7 archiver" superClass="xilinx.gnu.armv7.toolchain.archiver"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.linker.debug.1153900804" name="ARM v7 gcc linker" superClass="xilinx.gnu.armv7.c.toolchain.linker.debug">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.1592322418" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.2003788454" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.1802623949" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
								<option id="xilinx.gnu.c.link.option.ldflags.1638111547" name="Linker Flags" superClass="xilinx.gnu.c.link.option.ldflags" value=" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -Wl,-build-id=none -specs=Xilinx.spec" valueType="string"/>
								<inputType id="xilinx.gnu.linker.input.1197635724" superClass="xilinx.gnu.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
								<inputType id="xilinx.gnu.linker.input.lscript.1192918783" name="Linker Script" superClass="xilinx.gnu.linker.input.lscript"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.linker.debug.982524129" name="ARM v7 g++ linker" superClass="xilinx.gnu.armv7.cxx.toolchain.linker.debug">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.774196881" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.715245398" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.2007001953" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.size.debug.**********" name="ARM v7 Print Size" superClass="xilinx.gnu.armv7.size.debug"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="xilinx.gnu.armv7.exe.release.647963491">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="xilinx.gnu.armv7.exe.release.647963491" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="rm -rf" description="" id="xilinx.gnu.armv7.exe.release.647963491" name="Release" parent="xilinx.gnu.armv7.exe.release" prebuildStep="a9-linaro-pre-build-step">
					<folderInfo id="xilinx.gnu.armv7.exe.release.647963491." name="/" resourcePath="">
						<toolChain id="xilinx.gnu.armv7.exe.release.toolchain.1417136321" name="Xilinx ARM v7 GNU Toolchain" superClass="xilinx.gnu.armv7.exe.release.toolchain">
							<targetPlatform binaryParser="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" id="xilinx.armv7.target.gnu.base.release.1837630970" isAbstract="false" name="Release Platform" superClass="xilinx.armv7.target.gnu.base.release"/>
							<builder buildPath="${workspace_loc:/fsbl_bsp_xqspips_flash_lqspi_example_1}/Release" enableAutoBuild="true" id="xilinx.gnu.armv7.toolchain.builder.release.960769283" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="GNU make" superClass="xilinx.gnu.armv7.toolchain.builder.release"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.assembler.release.1681132668" name="ARM v7 gcc assembler" superClass="xilinx.gnu.armv7.c.toolchain.assembler.release">
								<inputType id="xilinx.gnu.assembler.input.576526603" superClass="xilinx.gnu.assembler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.c.toolchain.compiler.release.**********" name="ARM v7 gcc compiler" superClass="xilinx.gnu.armv7.c.toolchain.compiler.release">
								<option defaultValue="gnu.c.optimization.level.more" id="xilinx.gnu.compiler.option.optimization.level.1075836785" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.2059794317" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.990291129" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.1709486204" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
								<option id="xilinx.gnu.compiler.misc.other.191827795" name="Other flags" superClass="xilinx.gnu.compiler.misc.other" value="-c -fmessage-length=0 -MT&quot;$@&quot; -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard" valueType="string"/>
								<inputType id="xilinx.gnu.armv7.c.compiler.input.84352853" name="C source files" superClass="xilinx.gnu.armv7.c.compiler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.compiler.release.357397460" name="ARM v7 g++ compiler" superClass="xilinx.gnu.armv7.cxx.toolchain.compiler.release">
								<option defaultValue="gnu.c.optimization.level.more" id="xilinx.gnu.compiler.option.optimization.level.267485464" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.1532916968" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.707745083" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.1204632726" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.toolchain.archiver.546791906" name="ARM v7 archiver" superClass="xilinx.gnu.armv7.toolchain.archiver"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.linker.release.1216307339" name="ARM v7 gcc linker" superClass="xilinx.gnu.armv7.c.toolchain.linker.release">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.1449542006" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.1164502374" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.5960786" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
								<option id="xilinx.gnu.c.link.option.ldflags.1465956755" name="Linker Flags" superClass="xilinx.gnu.c.link.option.ldflags" value=" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -Wl,-build-id=none -specs=Xilinx.spec" valueType="string"/>
								<inputType id="xilinx.gnu.linker.input.223992389" superClass="xilinx.gnu.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
								<inputType id="xilinx.gnu.linker.input.lscript.1012141387" name="Linker Script" superClass="xilinx.gnu.linker.input.lscript"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.linker.release.1773076114" name="ARM v7 g++ linker" superClass="xilinx.gnu.armv7.cxx.toolchain.linker.release">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.756836094" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.1170203302" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.1451025401" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.size.release.353009674" name="ARM v7 Print Size" superClass="xilinx.gnu.armv7.size.release"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="fsbl_bsp_xqspips_flash_lqspi_example_1.xilinx.gnu.armv7.exe.**********" name="Xilinx ARM v7 Executable" projectType="xilinx.gnu.armv7.exe"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.debug.456566226;xilinx.gnu.armv7.exe.debug.456566226.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.release.647963491;xilinx.gnu.armv7.exe.release.647963491.;xilinx.gnu.armv7.c.toolchain.compiler.release.**********;xilinx.gnu.armv7.c.compiler.input.84352853">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.debug.456566226;xilinx.gnu.armv7.exe.debug.456566226.;xilinx.gnu.armv7.c.toolchain.compiler.debug.1114804754;xilinx.gnu.armv7.c.compiler.input.1949974799">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.release.647963491;xilinx.gnu.armv7.exe.release.647963491.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="refreshScope"/>
</cproject>
