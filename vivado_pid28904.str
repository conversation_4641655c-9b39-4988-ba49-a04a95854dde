/*

Xilinx Vivado v2018.3 (64-bit) [Major: 2018, Minor: 3]
SW Build: 2405991 on Thu Dec  6 23:38:27 MST 2018
IP Build: 2404404 on Fri Dec  7 01:43:56 MST 2018

Process ID (PID): 28904
License: Customer

Current time: 	Sun Sep 28 10:46:16 CST 2025
Time zone: 	China Standard Time (Asia/Shanghai)

OS: Windows 10
OS Version: 10.0
OS Architecture: amd64
Available processors (cores): 18

Screen size: 3840x2160
Screen resolution (DPI): 144
Available screens: 1
Available disk space: 192 GB
Default font: family=Dialog,name=Dialog,style=plain,size=18

Java version: 	9.0.4 64-bit
Java home: 	C:/Xilinx/Vivado/2018.3/tps/win64/jre9.0.4
Java executable location: 	C:/Xilinx/Vivado/2018.3/tps/win64/jre9.0.4/bin/java.exe
Java initial memory (-Xms): 	128 MB
Java maximum memory (-Xmx):	 3 GB


User name: 	cdfuy
User home directory: C:/Users/<USER>
User working directory: D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035
User country: 	CN
User language: 	zh
User locale: 	zh_CN

RDI_BASEROOT: C:/Xilinx/Vivado
HDI_APPROOT: C:/Xilinx/Vivado/2018.3
RDI_DATADIR: C:/Xilinx/Vivado/2018.3/data
RDI_BINDIR: C:/Xilinx/Vivado/2018.3/bin

Vivado preferences file location: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2018.3/vivado.xml
Vivado preferences directory: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2018.3/
Vivado layouts directory: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2018.3/layouts
PlanAhead jar file location: 	C:/Xilinx/Vivado/2018.3/lib/classes/planAhead.jar
Vivado log file location: 	D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/vivado.log
Vivado journal file location: 	D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/vivado.jou
Engine tmp dir: 	D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/.Xil/Vivado-28904-voyager_nb

Xilinx Environment Variables
----------------------------
XILINX: C:/Xilinx/Vivado/2018.3/ids_lite/ISE
XILINX_DSP: C:/Xilinx/Vivado/2018.3/ids_lite/ISE
XILINX_PLANAHEAD: C:/Xilinx/Vivado/2018.3
XILINX_SDK: C:/Xilinx/SDK/2018.3
XILINX_VIVADO: C:/Xilinx/Vivado/2018.3
XILINX_VIVADO_HLS: C:/Xilinx/Vivado/2018.3


GUI allocated memory:	135 MB
GUI max memory:		3,072 MB
Engine allocated memory: 623 MB

Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.

*/

// TclEventType: START_GUI
// Tcl Message: start_gui 
// TclEventType: PROJECT_OPEN_DIALOG
// bx (cp):  Open Project : addNotify
// Opening Vivado Project: D:\WORK\fuyong\2025\AD9361_PRJ\AD9361_Relative\PL_PS\PS_Z7035\fmcomms2_zed.xpr. Version: Vivado v2018.3 
// TclEventType: DEBUG_PROBE_SET_CHANGE
// Tcl Message: open_project D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/fmcomms2_zed.xpr 
// TclEventType: MSGMGR_MOVEMSG
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: FILE_SET_NEW
// TclEventType: RUN_COMPLETED
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: RUN_COMPLETED
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: RUN_COMPLETED
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: RUN_COMPLETED
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: RUN_CURRENT
// TclEventType: MSGMGR_REFRESH_MSG
// TclEventType: PROJECT_DASHBOARD_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// HMemoryUtils.trashcanNow. Engine heap size: 623 MB. GUI used memory: 73 MB. Current time: 9/28/25, 10:46:18 AM CST
// TclEventType: FILE_SET_CHANGE
// TclEventType: IP_LOCK_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: IP_LOCK_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: IP_LOCK_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: IP_LOCK_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: IP_LOCK_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: IP_LOCK_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: IP_LOCK_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: IP_LOCK_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: IP_LOCK_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: PROJECT_NEW
// [GUI Memory]: 82 MB (+83668kb) [00:00:15]
// [Engine Memory]: 695 MB (+577415kb) [00:00:15]
// [GUI Memory]: 114 MB (+29161kb) [00:00:15]
// [Engine Memory]: 756 MB (+27119kb) [00:00:16]
// [GUI Memory]: 120 MB (+437kb) [00:00:16]
// [GUI Memory]: 136 MB (+9920kb) [00:00:16]
// WARNING: HEventQueue.dispatchEvent() is taking  2819 ms.
// Tcl Message: open_project D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/fmcomms2_zed.xpr 
// Tcl Message: Scanning sources... Finished scanning sources 
// Tcl Message: INFO: [IP_Flow 19-234] Refreshing IP repositories 
// Tcl Message: INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'C:/Xilinx/Vivado/2018.3/data/ip'. 
// Tcl Message: open_project: Time (s): cpu = 00:00:12 ; elapsed = 00:00:11 . Memory (MB): peak = 814.547 ; gain = 151.641 
// Project name: fmcomms2_zed; location: D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035; part: xc7z035ffg676-2
// [GUI Memory]: 149 MB (+6589kb) [00:00:18]
// [Engine Memory]: 800 MB (+7216kb) [00:00:18]
dismissDialog("Open Project"); // bx (cp)
// [Engine Memory]: 842 MB (+1243kb) [00:00:22]
// Tcl Message: update_compile_order -fileset sources_1 
// HMemoryUtils.trashcanNow. Engine heap size: 842 MB. GUI used memory: 88 MB. Current time: 9/28/25, 10:46:33 AM CST
// Elapsed time: 622 seconds
closeMainWindow("fmcomms2_zed - [D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/fmcomms2_zed.xpr] - Vivado 2018.3"); // cp
// A (cp): Exit Vivado: addNotify
selectButton(RDIResource.BaseDialog_CANCEL, "Cancel"); // a (A)
dismissDialog("Exit Vivado"); // A (cp)
selectTree(PAResourceEtoH.FlowNavigatorTreePanel_FLOW_NAVIGATOR_TREE, "[, IP Integrator, Open Block Design]", 7, false); // u (Q, cp)
// Run Command: PAResourceCommand.PACommandNames_OPEN_BLOCK_DESIGN
// bx (cp):  Open Block Design : addNotify
// TclEventType: LOAD_FEATURE
// Tcl Message: open_bd_design {D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/fmcomms2_zed.srcs/sources_1/bd/system/system.bd} 
// TclEventType: RSB_CHANGE_CURRENT_DIAGRAM
// TclEventType: LOAD_FEATURE
// TclEventType: RSB_PROPERTY_CHANGE
// Tcl Message: Adding cell -- analog.com:user:axi_ad9361:1.0 - axi_ad9361 Adding cell -- analog.com:user:axi_dmac:1.0 - axi_ad9361_adc_dma 
// Tcl Message: Adding cell -- analog.com:user:axi_dmac:1.0 - axi_ad9361_dac_dma 
// Tcl Message: Adding cell -- analog.com:user:util_rfifo:1.0 - axi_ad9361_dac_fifo Adding cell -- analog.com:user:util_wfifo:1.0 - util_ad9361_adc_fifo Adding cell -- analog.com:user:util_cpack:1.0 - util_ad9361_adc_pack Adding cell -- analog.com:user:util_upack:1.0 - util_ad9361_dac_upack Adding cell -- analog.com:user:util_clkdiv:1.0 - util_ad9361_divclk Adding cell -- analog.com:user:util_tdd_sync:1.0 - util_ad9361_tdd_sync Adding cell -- xilinx.com:ip:axi_interconnect:2.1 - axi_cpu_interconnect 
// TclEventType: FILE_SET_CHANGE
// TclEventType: RSB_PROPERTY_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: RSB_PROPERTY_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: RSB_PROPERTY_CHANGE
// Tcl Message: Adding cell -- xilinx.com:ip:axi_crossbar:2.1 - xbar Adding cell -- xilinx.com:ip:axi_interconnect:2.1 - axi_hp0_interconnect Adding cell -- xilinx.com:ip:axi_interconnect:2.1 - axi_hp1_interconnect Adding cell -- xilinx.com:ip:axi_interconnect:2.1 - axi_hp2_interconnect Adding cell -- xilinx.com:ip:xlconcat:2.1 - sys_concat_intc Adding cell -- xilinx.com:ip:processing_system7:5.5 - sys_ps7 
// TclEventType: FILE_SET_CHANGE
// TclEventType: RSB_PROPERTY_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: RSB_PROPERTY_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: RSB_PROPERTY_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: RSB_PROPERTY_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: RSB_PROPERTY_CHANGE
// TclEventType: RSB_OPEN_DIAGRAM
// Tcl Message: Adding cell -- xilinx.com:ip:proc_sys_reset:5.0 - sys_rstgen Adding cell -- xilinx.com:ip:proc_sys_reset:5.0 - util_ad9361_divclk_reset Adding cell -- xilinx.com:ip:util_reduced_logic:2.0 - util_ad9361_divclk_sel Adding cell -- xilinx.com:ip:xlconcat:2.1 - util_ad9361_divclk_sel_concat 
// Tcl Message: Successfully read diagram <system> from BD file <D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/fmcomms2_zed.srcs/sources_1/bd/system/system.bd> 
// TclEventType: RSB_OPEN_DIAGRAM
// WARNING: HEventQueue.dispatchEvent() is taking  1387 ms.
// TclEventType: RSB_CONNECTION_CHANGE
closeView(PAResourceOtoP.PAViews_PROJECT_SUMMARY, "Project Summary"); // v
// HMemoryUtils.trashcanNow. Engine heap size: 873 MB. GUI used memory: 92 MB. Current time: 9/28/25, 10:56:59 AM CST
// TclEventType: RSB_CONNECTION_CHANGE
// TclEventType: RSB_LOCK_CHANGE
// Tcl Message: open_bd_design: Time (s): cpu = 00:00:04 ; elapsed = 00:00:06 . Memory (MB): peak = 886.191 ; gain = 12.148 
// 'bA' command handler elapsed time: 5 seconds
// a (cp): Critical Messages: addNotify
dismissDialog("Open Block Design"); // bx (cp)
// Elapsed time: 21 seconds
selectButton(PAResourceAtoD.CmdMsgDialog_OK, "OK"); // f (a)
dismissDialog("Critical Messages"); // a (cp)
// Run Command: PAResourceCommand.PACommandNames_CUSTOMIZE_RSB_BLOC
// [Engine Memory]: 885 MB (+1195kb) [00:11:52]
// r (cp): Re-customize IP: addNotify
// Elapsed time: 45 seconds
selectButton(PAResourceAtoD.CustomizeCoreDialog_IP_LOCATION, "IP Location"); // B (f, r)
selectButton(PAResourceAtoD.CustomizeCoreDialog_CHOOSE_IP_LOCATION, (String) null); // q (ak, ResizableWindow)
// Elapsed time: 21 seconds
selectButton(PAResourceAtoD.CustomizeCoreDialog_IP_LOCATION, "IP Location"); // B (f, r)
selectButton(PAResourceAtoD.CustomizeCoreDialog_DOCUMENTATION, "Documentation"); // B (f, r)
/*
#--------------------------------------------------------------------------
# Xilinx Vivado v2018.3 (64-bit)
# SW Build: 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build: 2404404 on Fri Dec  7 01:43:56 MST 2018
# Current time: Sun Sep 28 10:58:39 CST 2025
# Process ID (PID): 28904
# OS: Windows 10
# User: cdfuy
#
# This file is an indication that an internal application error occurred.
# This information is useful for debugging. Please open a case with Xilinx.
# Technical Support with this file and a testcase attached.
#--------------------------------------------------------------------------
java.lang.NullPointerException (See D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/vivado_pid28904.debug)
*/
// WARNING: HEventQueue.dispatchEvent() is taking  5639 ms.
selectButton(PAResourceAtoD.CustomizeCoreDialog_DOCUMENTATION, "Documentation"); // B (f, r)
selectButton(PAResourceAtoD.CustomizeCoreDialog_DOCUMENTATION, "Documentation"); // B (f, r)
/*
#--------------------------------------------------------------------------
# Xilinx Vivado v2018.3 (64-bit)
# SW Build: 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build: 2404404 on Fri Dec  7 01:43:56 MST 2018
# Current time: Sun Sep 28 10:58:42 CST 2025
# Process ID (PID): 28904
# OS: Windows 10
# User: cdfuy
#
# This file is an indication that an internal application error occurred.
# This information is useful for debugging. Please open a case with Xilinx.
# Technical Support with this file and a testcase attached.
#--------------------------------------------------------------------------
java.lang.NullPointerException (See D:/WORK/fuyong/2025/AD9361_PRJ/AD9361_Relative/PL_PS/PS_Z7035/vivado_pid28904.debug)
*/
// WARNING: HEventQueue.dispatchEvent() is taking  3185 ms.
// Elapsed time: 30 seconds
selectButton(RDIResource.HExceptionDialog_CONTINUE, "Continue"); // a (ae, aa)
dismissDialog("Re-customize IP"); // r (cp)
// [Engine Memory]: 947 MB (+18809kb) [00:13:30]
// HMemoryUtils.trashcanNow. Engine heap size: 959 MB. GUI used memory: 95 MB. Current time: 9/28/25, 10:59:39 AM CST
// Run Command: PAResourceCommand.PACommandNames_CUSTOMIZE_RSB_BLOC
// r (cp): Re-customize IP: addNotify
// Elapsed time: 70 seconds
dismissDialog("Re-customize IP"); // r (cp)
// HMemoryUtils.trashcanNow. Engine heap size: 959 MB. GUI used memory: 96 MB. Current time: 9/28/25, 11:29:40 AM CST
// HMemoryUtils.trashcanNow. Engine heap size: 959 MB. GUI used memory: 92 MB. Current time: 9/28/25, 11:59:42 AM CST
// HMemoryUtils.trashcanNow. Engine heap size: 959 MB. GUI used memory: 91 MB. Current time: 9/28/25, 12:29:44 PM CST
// WARNING: HEventQueue.dispatchEvent() is taking  330542 ms.
// [GUI Memory]: 156 MB (+8kb) [02:11:18]
// [GUI Memory]: 168 MB (+4221kb) [02:12:13]
// [GUI Memory]: 180 MB (+4003kb) [02:13:08]
// HMemoryUtils.trashcanNow. Engine heap size: 959 MB. GUI used memory: 91 MB. Current time: 9/28/25, 12:59:46 PM CST
// Run Command: PAResourceCommand.PACommandNames_CUSTOMIZE_RSB_BLOC
// O (cp):  Re-customize IP : addNotify
// r (cp): Re-customize IP: addNotify
// cl (r): Configuration Presets: addNotify
// Elapsed time: 7601 seconds
dismissDialog("Re-customize IP"); // O (cp)
// Elapsed time: 22 seconds
selectButton("Peripheral I/O Pins", "Peripheral I/O Pins"); // k (ch, r)
// Elapsed time: 17 seconds
expandTreeTable(PAResourceItoN.MIOTablePagePanel_MIO_TABLE, (String) null, 22); // bt (Q, r)
collapseTreeTable(PAResourceItoN.MIOTablePagePanel_MIO_TABLE, (String) null, 22); // bt (Q, r)
// Elapsed time: 12 seconds
selectButton("PS-PL Configuration", "PS-PL Configuration"); // k (ch, r)
selectButton("Peripheral I/O Pins", "Peripheral I/O Pins"); // k (ch, r)
selectButton("MIO Configuration", "MIO Configuration"); // k (ch, r)
// WARNING: HEventQueue.dispatchEvent() is taking  1139 ms.
expandTreeTable(PAResourceItoN.MIOConfigTreeTablePanel_MIO_CONFIG_TREE_TABLE, "I/O Peripherals ; I/O Peripherals ; I/O Peripherals ; I/O Peripherals ; I/O Peripherals ; I/O Peripherals ; I/O Peripherals ; I/O Peripherals", 1); // aW (Q, r)
expandTreeTable(PAResourceItoN.MIOConfigTreeTablePanel_MIO_CONFIG_TREE_TABLE, "GPIO ; GPIO ; GPIO ; GPIO ; GPIO ; GPIO ; GPIO ; GPIO", 16); // aW (Q, r)
selectTreeTableHeader(PAResourceItoN.MIOConfigTreeTablePanel_MIO_CONFIG_TREE_TABLE, "Peripheral", 0); // aW (Q, r)
selectTreeTableHeader(PAResourceItoN.MIOConfigTreeTablePanel_MIO_CONFIG_TREE_TABLE, "Signal", 2); // aW (Q, r)
// [GUI Memory]: 204 MB (+15285kb) [02:22:55]
