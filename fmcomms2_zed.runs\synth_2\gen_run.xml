<?xml version="1.0" encoding="UTF-8"?>
<GenRun Id="synth_2" LaunchPart="xc7z045ffg900-2" LaunchTime="1572326124">
  <File Type="PA-TCL" Name="system_top.tcl"/>
  <File Type="RDS-RDS" Name="system_top.vds"/>
  <File Type="RDS-UTIL" Name="system_top_utilization_synth.rpt"/>
  <File Type="RDS-UTIL-PB" Name="system_top_utilization_synth.pb"/>
  <File Type="RDS-DCP" Name="system_top.dcp"/>
  <FileSet Name="sources" Type="DesignSrcs" RelSrcDir="$PSRCDIR/sources_1">
    <Filter Type="Srcs"/>
    <File Path="$PSRCDIR/sources_1/bd/system/system.bd">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/library/xilinx/common/ad_iobuf.v">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PPRDIR/../../hdl-hdl_2017_r1/hdl-hdl_2017_r1/library/xilinx/common/ad_iobuf.v"/>
        <Attr Name="ImportTime" Val="1518442275"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/hdl/system_wrapper.v">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PSRCDIR/sources_1/bd/system/hdl/system_wrapper.v"/>
        <Attr Name="ImportTime" Val="1584428448"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_top.v">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PPRDIR/system_top.v"/>
        <Attr Name="ImportTime" Val="1572310677"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PPRDIR/system_constr.xdc"/>
        <Attr Name="ImportTime" Val="1572310673"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PPRDIR/../../hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc"/>
        <Attr Name="ImportTime" Val="1572255971"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="DesignMode" Val="RTL"/>
      <Option Name="TopModule" Val="system_top"/>
    </Config>
  </FileSet>
  <FileSet Name="constrs_in" Type="Constrs" RelSrcDir="$PSRCDIR/constrs_1">
    <Filter Type="Constrs"/>
    <File Path="$PSRCDIR/constrs_1/new/cdc.xdc">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="TargetConstrsFile" Val="$PSRCDIR/constrs_1/new/cdc.xdc"/>
      <Option Name="ConstrsType" Val="XDC"/>
    </Config>
  </FileSet>
  <Strategy Version="1" Minor="2">
    <StratHandle Name="Flow_PerfOptimized_high" Flow="Vivado Synthesis 2016"/>
    <Step Id="synth_design">
      <Option Id="KeepEquivalentRegisters">1</Option>
      <Option Id="ShregMinSize">5</Option>
      <Option Id="ResourceSharing">2</Option>
      <Option Id="RepFanoutThreshold">400</Option>
      <Option Id="NoCombineLuts">1</Option>
      <Option Id="FsmExtraction">1</Option>
    </Step>
  </Strategy>
</GenRun>
