<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>customized_ip</spirit:library>
  <spirit:name>system_sys_rstgen_0</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:busInterfaces>
    <spirit:busInterface>
      <spirit:name>clock</spirit:name>
      <spirit:displayName>Clock</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>slowest_sync_clk</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.CLOCK.ASSOCIATED_RESET">mb_reset:bus_struct_reset:interconnect_aresetn:peripheral_aresetn:peripheral_reset</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:displayName>Slowest Sync clock frequency</spirit:displayName>
          <spirit:description>Slowest Synchronous clock frequency</spirit:description>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.CLOCK.FREQ_HZ">100000000</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK.PHASE">0.000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK.ASSOCIATED_BUSIF"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.CLOCK.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>ext_reset</spirit:name>
      <spirit:displayName>Ext_Reset</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>ext_reset_in</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BOARD.ASSOCIATED_PARAM</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.EXT_RESET.BOARD.ASSOCIATED_PARAM">RESET_BOARD_INTERFACE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:enablement>
                <xilinx:presence>required</xilinx:presence>
              </xilinx:enablement>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.EXT_RESET.POLARITY">ACTIVE_LOW</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.EXT_RESET.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>aux_reset</spirit:name>
      <spirit:displayName>aux_reset</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>aux_reset_in</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AUX_RESET.POLARITY">ACTIVE_LOW</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.AUX_RESET.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>dbg_reset</spirit:name>
      <spirit:displayName>DBG_Reset</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>mb_debug_sys_rst</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.DBG_RESET.POLARITY">ACTIVE_HIGH</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.DBG_RESET.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>mb_rst</spirit:name>
      <spirit:displayName>MB_rst</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>mb_reset</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.MB_RST.POLARITY">ACTIVE_HIGH</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TYPE</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.MB_RST.TYPE">PROCESSOR</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.MB_RST.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>bus_struct_reset</spirit:name>
      <spirit:displayName>bus_struct_reset</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>bus_struct_reset</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.BUS_STRUCT_RESET.POLARITY">ACTIVE_HIGH</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TYPE</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.BUS_STRUCT_RESET.TYPE">INTERCONNECT</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.BUS_STRUCT_RESET.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>interconnect_low_rst</spirit:name>
      <spirit:displayName>interconnect_low_rst</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>interconnect_aresetn</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.INTERCONNECT_LOW_RST.POLARITY">ACTIVE_LOW</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TYPE</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.INTERCONNECT_LOW_RST.TYPE">INTERCONNECT</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.INTERCONNECT_LOW_RST.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>peripheral_high_rst</spirit:name>
      <spirit:displayName>peripheral_high_rst</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>peripheral_reset</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.PERIPHERAL_HIGH_RST.POLARITY">ACTIVE_HIGH</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TYPE</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.PERIPHERAL_HIGH_RST.TYPE">PERIPHERAL</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.PERIPHERAL_HIGH_RST.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>peripheral_low_rst</spirit:name>
      <spirit:displayName>peripheral_low_rst</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>peripheral_aresetn</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.PERIPHERAL_LOW_RST.POLARITY">ACTIVE_LOW</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TYPE</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.PERIPHERAL_LOW_RST.TYPE">PERIPHERAL</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.PERIPHERAL_LOW_RST.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
  </spirit:busInterfaces>
  <spirit:model>
    <spirit:ports>
      <spirit:port>
        <spirit:name>slowest_sync_clk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>ext_reset_in</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>aux_reset_in</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>mb_debug_sys_rst</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>dcm_locked</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>mb_reset</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>bus_struct_reset</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">0</spirit:left>
            <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_BUS_RST&apos;)) - 1)">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>peripheral_reset</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">0</spirit:left>
            <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_PERP_RST&apos;)) - 1)">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>interconnect_aresetn</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">0</spirit:left>
            <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_INTERCONNECT_ARESETN&apos;)) - 1)">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>peripheral_aresetn</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">0</spirit:left>
            <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_PERP_ARESETN&apos;)) - 1)">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>dummy_view</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
    </spirit:ports>
    <spirit:modelParameters>
      <spirit:modelParameter xsi:type="spirit:nameValueTypeType" spirit:dataType="string">
        <spirit:name>C_FAMILY</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FAMILY">zynq</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_EXT_RST_WIDTH</spirit:name>
        <spirit:displayName>Ext Rst Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_EXT_RST_WIDTH" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_AUX_RST_WIDTH</spirit:name>
        <spirit:displayName>Aux Rst Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_AUX_RST_WIDTH" spirit:minimum="1" spirit:maximum="32" spirit:rangeType="long">4</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="std_logic">
        <spirit:name>C_EXT_RESET_HIGH</spirit:name>
        <spirit:displayName>Ext Reset High</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_EXT_RESET_HIGH">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="std_logic">
        <spirit:name>C_AUX_RESET_HIGH</spirit:name>
        <spirit:displayName>Aux Reset High</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_AUX_RESET_HIGH" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_NUM_BUS_RST</spirit:name>
        <spirit:displayName>No. of Bus Reset (Active High)</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_NUM_BUS_RST" spirit:minimum="1" spirit:maximum="32" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_NUM_PERP_RST</spirit:name>
        <spirit:displayName>No. of Peripheral Reset (Active High)</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_NUM_PERP_RST" spirit:minimum="1" spirit:maximum="32" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_NUM_INTERCONNECT_ARESETN</spirit:name>
        <spirit:displayName>No. of Interconnect Reset (Active Low)</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_NUM_INTERCONNECT_ARESETN" spirit:minimum="1" spirit:maximum="32" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_NUM_PERP_ARESETN</spirit:name>
        <spirit:displayName>No. of Peripheral Reset (Active Low)</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_NUM_PERP_ARESETN" spirit:minimum="1" spirit:maximum="31" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
    </spirit:modelParameters>
  </spirit:model>
  <spirit:choices>
    <spirit:choice>
      <spirit:name>choice_list_ac75ef1e</spirit:name>
      <spirit:enumeration>Custom</spirit:enumeration>
    </spirit:choice>
  </spirit:choices>
  <spirit:description>Processor Reset System</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>C_NUM_PERP_ARESETN</spirit:name>
      <spirit:displayName>No. of Peripheral Reset (Active Low)</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_NUM_PERP_ARESETN" spirit:order="1800" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_NUM_INTERCONNECT_ARESETN</spirit:name>
      <spirit:displayName>No. of Interconnect Reset (Active Low)</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_NUM_INTERCONNECT_ARESETN" spirit:order="1700" spirit:minimum="1" spirit:maximum="8" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_NUM_PERP_RST</spirit:name>
      <spirit:displayName>No. of Peripheral Reset (Active High)</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_NUM_PERP_RST" spirit:order="1600" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_NUM_BUS_RST</spirit:name>
      <spirit:displayName>No. of Bus Reset (Active High)</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_NUM_BUS_RST" spirit:order="1500" spirit:minimum="1" spirit:maximum="8" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_AUX_RESET_HIGH</spirit:name>
      <spirit:displayName>Aux Reset High</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_AUX_RESET_HIGH" spirit:order="1400" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_EXT_RESET_HIGH</spirit:name>
      <spirit:displayName>Ext Reset High</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_EXT_RESET_HIGH" spirit:order="1300" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_AUX_RST_WIDTH</spirit:name>
      <spirit:displayName>Aux Rst Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_AUX_RST_WIDTH" spirit:order="1200" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_EXT_RST_WIDTH</spirit:name>
      <spirit:displayName>Ext Rst Width</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_EXT_RST_WIDTH" spirit:order="1100" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="1">system_sys_rstgen_0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_BOARD_FLOW</spirit:name>
      <spirit:displayName>Generate Board based IO Constraints</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_BOARD_FLOW" spirit:order="2">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>RESET_BOARD_INTERFACE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.RESET_BOARD_INTERFACE" spirit:choiceRef="choice_list_ac75ef1e" spirit:order="3">Custom</spirit:value>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:displayName>Processor System Reset</xilinx:displayName>
      <xilinx:coreRevision>13</xilinx:coreRevision>
      <xilinx:configElementInfos>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AUX_RESET.POLARITY" xilinx:valueSource="propagated" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.CLOCK.FREQ_HZ" xilinx:valueSource="user_prop"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.C_AUX_RESET_HIGH" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.C_EXT_RESET_HIGH" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.C_EXT_RST_WIDTH" xilinx:valueSource="user"/>
      </xilinx:configElementInfos>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2018.3</xilinx:xilinxVersion>
      <xilinx:checksum xilinx:scope="busInterfaces" xilinx:value="26169568"/>
      <xilinx:checksum xilinx:scope="fileGroups" xilinx:value="af82d8e6"/>
      <xilinx:checksum xilinx:scope="ports" xilinx:value="f2ac9635"/>
      <xilinx:checksum xilinx:scope="hdlParameters" xilinx:value="404de108"/>
      <xilinx:checksum xilinx:scope="parameters" xilinx:value="8319b917"/>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
