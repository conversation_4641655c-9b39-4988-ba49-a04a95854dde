# This file is automatically generated.
# It contains project source information necessary for synthesis and implementation.

# XDC: new/cdc.xdc

# Block Designs: bd/system/system.bd
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system || ORIG_REF_NAME==system}]

# IP: bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_sys_ps7_0 || ORIG_REF_NAME==system_sys_ps7_0}]

# IP: bd/system/ip/system_sys_concat_intc_0/system_sys_concat_intc_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_sys_concat_intc_0 || ORIG_REF_NAME==system_sys_concat_intc_0}]

# IP: bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_sys_rstgen_0 || ORIG_REF_NAME==system_sys_rstgen_0}]

# IP: bd/system/ip/system_axi_cpu_interconnect_0/system_axi_cpu_interconnect_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_axi_cpu_interconnect_0 || ORIG_REF_NAME==system_axi_cpu_interconnect_0}]

# IP: bd/system/ip/system_axi_hp0_interconnect_0/system_axi_hp0_interconnect_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_axi_hp0_interconnect_0 || ORIG_REF_NAME==system_axi_hp0_interconnect_0}]

# IP: bd/system/ip/system_axi_ad9361_0/system_axi_ad9361_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_axi_ad9361_0 || ORIG_REF_NAME==system_axi_ad9361_0}]

# IP: bd/system/ip/system_util_ad9361_tdd_sync_0/system_util_ad9361_tdd_sync_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_util_ad9361_tdd_sync_0 || ORIG_REF_NAME==system_util_ad9361_tdd_sync_0}]

# IP: bd/system/ip/system_util_ad9361_divclk_sel_concat_0/system_util_ad9361_divclk_sel_concat_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_util_ad9361_divclk_sel_concat_0 || ORIG_REF_NAME==system_util_ad9361_divclk_sel_concat_0}]

# IP: bd/system/ip/system_util_ad9361_divclk_sel_0/system_util_ad9361_divclk_sel_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_util_ad9361_divclk_sel_0 || ORIG_REF_NAME==system_util_ad9361_divclk_sel_0}]

# IP: bd/system/ip/system_util_ad9361_divclk_0/system_util_ad9361_divclk_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_util_ad9361_divclk_0 || ORIG_REF_NAME==system_util_ad9361_divclk_0}]

# IP: bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_util_ad9361_divclk_reset_0 || ORIG_REF_NAME==system_util_ad9361_divclk_reset_0}]

# IP: bd/system/ip/system_util_ad9361_adc_fifo_0/system_util_ad9361_adc_fifo_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_util_ad9361_adc_fifo_0 || ORIG_REF_NAME==system_util_ad9361_adc_fifo_0}]

# IP: bd/system/ip/system_util_ad9361_adc_pack_0/system_util_ad9361_adc_pack_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_util_ad9361_adc_pack_0 || ORIG_REF_NAME==system_util_ad9361_adc_pack_0}]

# IP: bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_axi_ad9361_adc_dma_0 || ORIG_REF_NAME==system_axi_ad9361_adc_dma_0}]

# IP: bd/system/ip/system_axi_ad9361_dac_fifo_0/system_axi_ad9361_dac_fifo_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_axi_ad9361_dac_fifo_0 || ORIG_REF_NAME==system_axi_ad9361_dac_fifo_0}]

# IP: bd/system/ip/system_util_ad9361_dac_upack_0/system_util_ad9361_dac_upack_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_util_ad9361_dac_upack_0 || ORIG_REF_NAME==system_util_ad9361_dac_upack_0}]

# IP: bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_axi_ad9361_dac_dma_0 || ORIG_REF_NAME==system_axi_ad9361_dac_dma_0}]

# IP: bd/system/ip/system_axi_hp1_interconnect_0/system_axi_hp1_interconnect_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_axi_hp1_interconnect_0 || ORIG_REF_NAME==system_axi_hp1_interconnect_0}]

# IP: bd/system/ip/system_axi_hp2_interconnect_0/system_axi_hp2_interconnect_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_axi_hp2_interconnect_0 || ORIG_REF_NAME==system_axi_hp2_interconnect_0}]

# IP: bd/system/ip/system_xbar_0/system_xbar_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_xbar_0 || ORIG_REF_NAME==system_xbar_0}]

# IP: bd/system/ip/system_auto_pc_0/system_auto_pc_0.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_auto_pc_0 || ORIG_REF_NAME==system_auto_pc_0}]

# IP: bd/system/ip/system_auto_pc_1/system_auto_pc_1.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_auto_pc_1 || ORIG_REF_NAME==system_auto_pc_1}]

# IP: bd/system/ip/system_auto_pc_2/system_auto_pc_2.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_auto_pc_2 || ORIG_REF_NAME==system_auto_pc_2}]

# IP: bd/system/ip/system_auto_pc_3/system_auto_pc_3.xci
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==system_auto_pc_3 || ORIG_REF_NAME==system_auto_pc_3}]

# XDC: bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_sys_ps7_0 || ORIG_REF_NAME==system_sys_ps7_0}] {/inst }]/inst ]]

# XDC: bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0_board.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_sys_rstgen_0 || ORIG_REF_NAME==system_sys_rstgen_0}] {/U0 }]/U0 ]]

# XDC: bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0.xdc
#dup# set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_sys_rstgen_0 || ORIG_REF_NAME==system_sys_rstgen_0}] {/U0 }]/U0 ]]

# XDC: bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0_ooc.xdc

# XDC: bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==up_xfer_cntrl || ORIG_REF_NAME==up_xfer_cntrl}]

# XDC: bd/system/ip/xilinx/common/ad_rst_constr.xdc
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==ad_rst || ORIG_REF_NAME==ad_rst}]

# XDC: bd/system/ip/xilinx/common/up_xfer_status_constr.xdc
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==up_xfer_status || ORIG_REF_NAME==up_xfer_status}]

# XDC: bd/system/ip/xilinx/common/up_clock_mon_constr.xdc
set_property DONT_TOUCH TRUE [get_cells -hier -filter {REF_NAME==up_clock_mon || ORIG_REF_NAME==up_clock_mon}]

# XDC: bd/system/ip/system_axi_ad9361_0/axi_ad9361_constr.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_axi_ad9361_0 || ORIG_REF_NAME==system_axi_ad9361_0}] {/inst }]/inst ]]

# XDC: bd/system/ip/system_axi_ad9361_0/system_axi_ad9361_0_pps_constr.xdc
#dup# set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_axi_ad9361_0 || ORIG_REF_NAME==system_axi_ad9361_0}] {/inst }]/inst ]]

# XDC: bd/system/ip/system_util_ad9361_tdd_sync_0/util_tdd_sync_constr.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_util_ad9361_tdd_sync_0 || ORIG_REF_NAME==system_util_ad9361_tdd_sync_0}] {/inst }]/inst ]]

# XDC: bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_util_ad9361_divclk_0 || ORIG_REF_NAME==system_util_ad9361_divclk_0}] {/inst }]/inst ]]

# XDC: bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0_board.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_util_ad9361_divclk_reset_0 || ORIG_REF_NAME==system_util_ad9361_divclk_reset_0}] {/U0 }]/U0 ]]

# XDC: bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0.xdc
#dup# set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_util_ad9361_divclk_reset_0 || ORIG_REF_NAME==system_util_ad9361_divclk_reset_0}] {/U0 }]/U0 ]]

# XDC: bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0_ooc.xdc

# XDC: bd/system/ip/system_util_ad9361_adc_fifo_0/util_wfifo_constr.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_util_ad9361_adc_fifo_0 || ORIG_REF_NAME==system_util_ad9361_adc_fifo_0}] {/inst }]/inst ]]

# XDC: bd/system/ip/system_util_ad9361_adc_pack_0/util_cpack_constr.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_util_ad9361_adc_pack_0 || ORIG_REF_NAME==system_util_ad9361_adc_pack_0}] {/inst }]/inst ]]

# TCL: bd/system/ip/system_axi_ad9361_adc_dma_0/bd/bd.tcl

# XDC: bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_axi_ad9361_adc_dma_0 || ORIG_REF_NAME==system_axi_ad9361_adc_dma_0}] {/inst }]/inst ]]

# XDC: bd/system/ip/system_axi_ad9361_dac_fifo_0/util_rfifo_constr.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_axi_ad9361_dac_fifo_0 || ORIG_REF_NAME==system_axi_ad9361_dac_fifo_0}] {/inst }]/inst ]]

# XDC: bd/system/ip/system_util_ad9361_dac_upack_0/util_upack_constr.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_util_ad9361_dac_upack_0 || ORIG_REF_NAME==system_util_ad9361_dac_upack_0}] {/inst }]/inst ]]

# TCL: bd/system/ip/system_axi_ad9361_dac_dma_0/bd/bd.tcl

# XDC: bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc
set_property DONT_TOUCH TRUE [get_cells [split [join [get_cells -hier -filter {REF_NAME==system_axi_ad9361_dac_dma_0 || ORIG_REF_NAME==system_axi_ad9361_dac_dma_0}] {/inst }]/inst ]]

# XDC: bd/system/ip/system_xbar_0/system_xbar_0_ooc.xdc

# XDC: bd/system/ip/system_auto_pc_0/system_auto_pc_0_ooc.xdc

# XDC: bd/system/ip/system_auto_pc_1/system_auto_pc_1_ooc.xdc

# XDC: bd/system/ip/system_auto_pc_2/system_auto_pc_2_ooc.xdc

# XDC: bd/system/ip/system_auto_pc_3/system_auto_pc_3_ooc.xdc

# XDC: bd/system/system_ooc.xdc

# XDC: imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc

# XDC: imports/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc
