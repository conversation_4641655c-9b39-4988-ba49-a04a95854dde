<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>fsbl_bsp</name>
	<comment>Created by SDK v2016.4</comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.make.core.makeBuilder</name>
			<arguments>
				<dictionary>
					<key>org.eclipse.cdt.core.errorOutputParser</key>
					<value>org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.VCErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.MakeErrorParser;</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.append_environment</key>
					<value>true</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.build.arguments</key>
					<value></value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.build.command</key>
					<value>make</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.build.target.auto</key>
					<value>all</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.build.target.clean</key>
					<value>clean</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.build.target.inc</key>
					<value>all</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.enableAutoBuild</key>
					<value>true</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.enableCleanBuild</key>
					<value>true</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.enableFullBuild</key>
					<value>true</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.enabledIncrementalBuild</key>
					<value>true</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.environment</key>
					<value></value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.stopOnError</key>
					<value>false</value>
				</dictionary>
				<dictionary>
					<key>org.eclipse.cdt.make.core.useDefaultBuildCmd</key>
					<value>true</value>
				</dictionary>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.xilinx.sdk.sw.SwProjectNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.make.core.makeNature</nature>
	</natures>
</projectDescription>
