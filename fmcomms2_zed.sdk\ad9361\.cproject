<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="xilinx.gnu.armv7.exe.debug.256761281">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="xilinx.gnu.armv7.exe.debug.256761281" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="" id="xilinx.gnu.armv7.exe.debug.256761281" name="Debug" parent="xilinx.gnu.armv7.exe.debug" prebuildStep="a9-linaro-pre-build-step">
					<folderInfo id="xilinx.gnu.armv7.exe.debug.256761281." name="/" resourcePath="">
						<toolChain id="xilinx.gnu.armv7.exe.debug.toolchain.110664301" name="Xilinx ARM v7 GNU Toolchain" superClass="xilinx.gnu.armv7.exe.debug.toolchain">
							<targetPlatform binaryParser="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" id="xilinx.armv7.target.gnu.base.debug.1787386214" isAbstract="false" name="Debug Platform" superClass="xilinx.armv7.target.gnu.base.debug"/>
							<builder buildPath="${workspace_loc:/ad9361}/Debug" enableAutoBuild="true" id="xilinx.gnu.armv7.toolchain.builder.debug.431731958" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="GNU make" superClass="xilinx.gnu.armv7.toolchain.builder.debug"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.assembler.debug.2139662179" name="ARM v7 gcc assembler" superClass="xilinx.gnu.armv7.c.toolchain.assembler.debug">
								<inputType id="xilinx.gnu.assembler.input.109087742" superClass="xilinx.gnu.assembler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.c.toolchain.compiler.debug.**********" name="ARM v7 gcc compiler" superClass="xilinx.gnu.armv7.c.toolchain.compiler.debug">
								<option defaultValue="gnu.c.optimization.level.none" id="xilinx.gnu.compiler.option.optimization.level.1462434942" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.783737421" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.707849345" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.46623799" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
								<option id="xilinx.gnu.compiler.misc.other.2125418582" name="Other flags" superClass="xilinx.gnu.compiler.misc.other" value="-c -fmessage-length=0 -MT&quot;$@&quot; -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard" valueType="string"/>
								<option id="xilinx.gnu.compiler.symbols.defined.1341119845" name="Defined symbols (-D)" superClass="xilinx.gnu.compiler.symbols.defined" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="XILINX_PLATFORM"/>
									<listOptionValue builtIn="false" value="CONSOLE_COMMANDS"/>
									<listOptionValue builtIn="false" value="ADC_DMA_EXAMPLE"/>
									<listOptionValue builtIn="false" value="DAC_DMA_EXAMPLE"/>
								</option>
								<option id="xilinx.gnu.compiler.dircategory.includes.1476202842" name="Include Paths" superClass="xilinx.gnu.compiler.dircategory.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/src/platform_xilinx&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/src&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}./src/console_commands&quot;"/>
								</option>
								<inputType id="xilinx.gnu.armv7.c.compiler.input.**********" name="C source files" superClass="xilinx.gnu.armv7.c.compiler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.compiler.debug.1449880062" name="ARM v7 g++ compiler" superClass="xilinx.gnu.armv7.cxx.toolchain.compiler.debug">
								<option defaultValue="gnu.c.optimization.level.none" id="xilinx.gnu.compiler.option.optimization.level.1171859566" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.492740290" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.2096963081" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.642658965" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.toolchain.archiver.165829354" name="ARM v7 archiver" superClass="xilinx.gnu.armv7.toolchain.archiver"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.linker.debug.2029983559" name="ARM v7 gcc linker" superClass="xilinx.gnu.armv7.c.toolchain.linker.debug">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.2076141524" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.176712776" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.1957660410" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
								<option id="xilinx.gnu.c.link.option.ldflags.1091144804" name="Linker Flags" superClass="xilinx.gnu.c.link.option.ldflags" value=" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -Wl,-build-id=none -specs=Xilinx.spec" valueType="string"/>
								<inputType id="xilinx.gnu.linker.input.976399847" superClass="xilinx.gnu.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
								<inputType id="xilinx.gnu.linker.input.lscript.1092695579" name="Linker Script" superClass="xilinx.gnu.linker.input.lscript"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.linker.debug.999696594" name="ARM v7 g++ linker" superClass="xilinx.gnu.armv7.cxx.toolchain.linker.debug">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.637285111" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.1688130991" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.1190528759" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.size.debug.**********" name="ARM v7 Print Size" superClass="xilinx.gnu.armv7.size.debug"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="xilinx.gnu.armv7.exe.release.910006141">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="xilinx.gnu.armv7.exe.release.910006141" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="rm -rf" description="" id="xilinx.gnu.armv7.exe.release.910006141" name="Release" parent="xilinx.gnu.armv7.exe.release" prebuildStep="a9-linaro-pre-build-step">
					<folderInfo id="xilinx.gnu.armv7.exe.release.910006141." name="/" resourcePath="">
						<toolChain id="xilinx.gnu.armv7.exe.release.toolchain.965031928" name="Xilinx ARM v7 GNU Toolchain" superClass="xilinx.gnu.armv7.exe.release.toolchain">
							<targetPlatform binaryParser="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" id="xilinx.armv7.target.gnu.base.release.616460797" isAbstract="false" name="Release Platform" superClass="xilinx.armv7.target.gnu.base.release"/>
							<builder buildPath="${workspace_loc:/ad9361}/Release" enableAutoBuild="true" id="xilinx.gnu.armv7.toolchain.builder.release.112310027" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="GNU make" superClass="xilinx.gnu.armv7.toolchain.builder.release"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.assembler.release.1726882385" name="ARM v7 gcc assembler" superClass="xilinx.gnu.armv7.c.toolchain.assembler.release">
								<inputType id="xilinx.gnu.assembler.input.1916343471" superClass="xilinx.gnu.assembler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.c.toolchain.compiler.release.**********" name="ARM v7 gcc compiler" superClass="xilinx.gnu.armv7.c.toolchain.compiler.release">
								<option defaultValue="gnu.c.optimization.level.more" id="xilinx.gnu.compiler.option.optimization.level.1430558531" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.350612673" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.932993777" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.2123263119" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
								<option id="xilinx.gnu.compiler.misc.other.1714660915" name="Other flags" superClass="xilinx.gnu.compiler.misc.other" value="-c -fmessage-length=0 -MT&quot;$@&quot; -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard" valueType="string"/>
								<inputType id="xilinx.gnu.armv7.c.compiler.input.489553731" name="C source files" superClass="xilinx.gnu.armv7.c.compiler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.compiler.release.541798568" name="ARM v7 g++ compiler" superClass="xilinx.gnu.armv7.cxx.toolchain.compiler.release">
								<option defaultValue="gnu.c.optimization.level.more" id="xilinx.gnu.compiler.option.optimization.level.174180944" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.11741065" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.17451505" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.191967205" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.toolchain.archiver.886092612" name="ARM v7 archiver" superClass="xilinx.gnu.armv7.toolchain.archiver"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.linker.release.480725301" name="ARM v7 gcc linker" superClass="xilinx.gnu.armv7.c.toolchain.linker.release">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.1064936172" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.101692117" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.1575053799" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
								<option id="xilinx.gnu.c.link.option.ldflags.2116197633" name="Linker Flags" superClass="xilinx.gnu.c.link.option.ldflags" value=" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -Wl,-build-id=none -specs=Xilinx.spec" valueType="string"/>
								<inputType id="xilinx.gnu.linker.input.1979872035" superClass="xilinx.gnu.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
								<inputType id="xilinx.gnu.linker.input.lscript.193888318" name="Linker Script" superClass="xilinx.gnu.linker.input.lscript"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.linker.release.260534200" name="ARM v7 g++ linker" superClass="xilinx.gnu.armv7.cxx.toolchain.linker.release">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.787944573" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.1421882933" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.176914891" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.size.release.664264227" name="ARM v7 Print Size" superClass="xilinx.gnu.armv7.size.release"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="ad9361.xilinx.gnu.armv7.exe.**********" name="Xilinx ARM v7 Executable" projectType="xilinx.gnu.armv7.exe"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.debug.256761281;xilinx.gnu.armv7.exe.debug.256761281.;xilinx.gnu.armv7.c.toolchain.compiler.debug.**********;xilinx.gnu.armv7.c.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.release.910006141;xilinx.gnu.armv7.exe.release.910006141.;xilinx.gnu.armv7.c.toolchain.compiler.release.**********;xilinx.gnu.armv7.c.compiler.input.489553731">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.debug.256761281;xilinx.gnu.armv7.exe.debug.256761281.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.release.910006141;xilinx.gnu.armv7.exe.release.910006141.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="refreshScope"/>
</cproject>
