<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_8qzHsG2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_8qzHsW2xEeqhENMVS6WjVA" bindingContexts="_8q0W322xEeqhENMVS6WjVA">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;ad9361.c&quot; tooltip=&quot;ad9361/src/ad9361.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/ad9361.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;ad9361_api.c&quot; tooltip=&quot;ad9361/src/ad9361_api.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/ad9361_api.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;command.c&quot; tooltip=&quot;ad9361/src/console_commands/command.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/console_commands/command.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;console.c&quot; tooltip=&quot;ad9361/src/console_commands/console.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/console_commands/console.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;console.h&quot; tooltip=&quot;ad9361/src/console_commands/console.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/console_commands/console.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;dac_core.c&quot; tooltip=&quot;ad9361/src/platform_xilinx/dac_core.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/platform_xilinx/dac_core.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;ad9361/src/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;ad9361.h&quot; tooltip=&quot;ad9361/src/ad9361.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/ad9361.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;common.h&quot; tooltip=&quot;ad9361/src/common.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/common.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;command.h&quot; tooltip=&quot;ad9361/src/console_commands/command.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/console_commands/command.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;platform.h&quot; tooltip=&quot;ad9361/src/platform_xilinx/platform.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/platform_xilinx/platform.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;parameters.h&quot; tooltip=&quot;ad9361/src/platform_xilinx/parameters.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/platform_xilinx/parameters.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;ad9361_api.h&quot; tooltip=&quot;ad9361/src/ad9361_api.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/ad9361_api.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;config.h&quot; tooltip=&quot;ad9361/src/config.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/config.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;platform.c&quot; tooltip=&quot;ad9361/src/platform_xilinx/platform.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ad9361/src/platform_xilinx/platform.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <tags>ModelMigrationProcessor.001</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_8qzHsW2xEeqhENMVS6WjVA" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_8qzHsm2xEeqhENMVS6WjVA" x="49" y="335" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1572325589587"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_8qzHsm2xEeqhENMVS6WjVA" selectedElement="_8qzHs22xEeqhENMVS6WjVA" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_8qzHs22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_8qzHtG2xEeqhENMVS6WjVA">
        <children xsi:type="advanced:Perspective" xmi:id="_8qzHtG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_8qzHtW2xEeqhENMVS6WjVA" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif" tooltip="C/C++">
          <persistedState key="persp.hiddenItems" value="persp.hideMenuSC:org.eclipse.jdt.ui.refactoring.menu,persp.hideMenuSC:org.eclipse.jdt.ui.source.menu,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:com.xilinx.sdk.appwiz.AppWizard</tags>
          <tags>persp.newWizSC:com.xilinx.sdk.profile.ui.wizards.ZpeProjectWizard</tags>
          <tags>persp.newWizSC:com.xilinx.sdk.sw.ui.NewBspWizard</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_8qzHtW2xEeqhENMVS6WjVA" selectedElement="_8qzHvm2xEeqhENMVS6WjVA" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_8qzHtm2xEeqhENMVS6WjVA" containerData="1906" selectedElement="_8qzHt22xEeqhENMVS6WjVA">
              <children xsi:type="basic:PartStack" xmi:id="_8qzHt22xEeqhENMVS6WjVA" elementId="topLeft" containerData="7500" selectedElement="_8qzHuG2xEeqhENMVS6WjVA">
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzHuG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_8qzvRG2xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzHuW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_8qzvg22xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzHum2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_8qzvhG2xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzHu22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_8qzvhW2xEeqhENMVS6WjVA"/>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_8qzHvG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementViewMStack" containerData="2500" selectedElement="_8qzHvW2xEeqhENMVS6WjVA">
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzHvW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView" ref="_8qzvzG2xEeqhENMVS6WjVA"/>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_8qzHvm2xEeqhENMVS6WjVA" containerData="8094" selectedElement="_8qzHxW2xEeqhENMVS6WjVA">
              <children xsi:type="basic:PartSashContainer" xmi:id="_8qzHv22xEeqhENMVS6WjVA" containerData="2219" selectedElement="_8qzHwG2xEeqhENMVS6WjVA" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzHwG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_8qzvLG2xEeqhENMVS6WjVA"/>
                <children xsi:type="basic:PartStack" xmi:id="_8qzHwW2xEeqhENMVS6WjVA" elementId="topRight" containerData="2500" selectedElement="_8qzHwm2xEeqhENMVS6WjVA">
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzHwm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ContentOutline" ref="_8qzvum2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzHw22xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView" ref="_8qzv1G2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzHxG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_8qzv1W2xEeqhENMVS6WjVA"/>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_8qzHxW2xEeqhENMVS6WjVA" containerData="7781" selectedElement="_8qzHzG2xEeqhENMVS6WjVA" horizontal="true">
                <children xsi:type="basic:PartStack" xmi:id="_8qzHxm2xEeqhENMVS6WjVA" elementId="bottom" containerData="5000" selectedElement="_8qzHyW2xEeqhENMVS6WjVA">
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzHx22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ProblemView" ref="_8qzvhm2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzHyG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.TaskList" ref="_8qzvi22xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzHyW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.console.ConsoleView" ref="_8qzvjG2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzHym2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.PropertySheet" ref="_8qzvuW2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzHy22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.terminal.sdkterminal" ref="_8qzv022xEeqhENMVS6WjVA"/>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_8qzHzG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.logger.SdkLogViewMStack" containerData="5000" selectedElement="_JPYbIJQpEeqSJ5celE-NEg">
                  <tags>General</tags>
                  <tags>Xilinx</tags>
                  <tags>active</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzHzW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.logger.SdkLogView" ref="_8qzvx22xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_2nKlQXFqEeq57eTh4XJtOg" elementId="org.eclipse.search.ui.views.SearchView" ref="_2nKlQHFqEeq57eTh4XJtOg"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JPYbIJQpEeqSJ5celE-NEg" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView" ref="_8qzv622xEeqhENMVS6WjVA"/>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_8qzHzm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_8qzHz22xEeqhENMVS6WjVA" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.SignalsView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ModuleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.executablesView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.debug.ui.DisplayView</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_8qzHz22xEeqhENMVS6WjVA" selectedElement="_8qzH0G2xEeqhENMVS6WjVA">
            <children xsi:type="basic:PartSashContainer" xmi:id="_8qzH0G2xEeqhENMVS6WjVA" containerData="7500" selectedElement="_8qzH422xEeqhENMVS6WjVA">
              <children xsi:type="basic:PartSashContainer" xmi:id="_8qzH0W2xEeqhENMVS6WjVA" containerData="4693" selectedElement="_8qzH0m2xEeqhENMVS6WjVA" horizontal="true">
                <children xsi:type="basic:PartSashContainer" xmi:id="_8qzH0m2xEeqhENMVS6WjVA" containerData="5000" selectedElement="_8qzH022xEeqhENMVS6WjVA" horizontal="true">
                  <children xsi:type="basic:PartStack" xmi:id="_8qzH022xEeqhENMVS6WjVA" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="5000" selectedElement="_8qzH1G2xEeqhENMVS6WjVA">
                    <tags>org.eclipse.e4.primaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_8qzH1G2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.DebugView" ref="_8qzv1m2xEeqhENMVS6WjVA"/>
                    <children xsi:type="advanced:Placeholder" xmi:id="_8qzH1W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_8qzvRG2xEeqhENMVS6WjVA"/>
                    <children xsi:type="advanced:Placeholder" xmi:id="_8qzH1m2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.PackageExplorer" toBeRendered="false" ref="_8qzv-m2xEeqhENMVS6WjVA"/>
                    <children xsi:type="advanced:Placeholder" xmi:id="_8qzH122xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_8qzv-22xEeqhENMVS6WjVA"/>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_8qzH2G2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.viewMStack" toBeRendered="false" containerData="5000">
                    <children xsi:type="advanced:Placeholder" xmi:id="_8qzH2W2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" toBeRendered="false" ref="_8qzv922xEeqhENMVS6WjVA"/>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_8qzH2m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="5000" selectedElement="_8qzH222xEeqhENMVS6WjVA">
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH222xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.VariableView" ref="_8qzv222xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH3G2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.BreakpointView" ref="_8qzv4G2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH3W2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.ExpressionView" toBeRendered="false" ref="_8qzv5W2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH3m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.RegisterView" ref="_8qzv5m2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH322xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView" ref="_8qzv622xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH4G2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView" ref="_8qzv7m2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH4W2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.SignalsView" toBeRendered="false" ref="_8qzv8G2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH4m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.ModuleView" ref="_8qzv8W2xEeqhENMVS6WjVA"/>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_8qzH422xEeqhENMVS6WjVA" containerData="5307" selectedElement="_8qzH5G2xEeqhENMVS6WjVA" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH5G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_8qzvLG2xEeqhENMVS6WjVA"/>
                <children xsi:type="basic:PartStack" xmi:id="_8qzH5W2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="2500" selectedElement="_8qzH5m2xEeqhENMVS6WjVA">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH5m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ContentOutline" ref="_8qzvum2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH522xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_8qzv722xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH6G2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" toBeRendered="false" ref="_8qzv-G2xEeqhENMVS6WjVA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8qzH6W2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.ProfilerView" toBeRendered="false" ref="_8qzv_m2xEeqhENMVS6WjVA"/>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_8qzH6m2xEeqhENMVS6WjVA" containerData="2500" selectedElement="_8qzH622xEeqhENMVS6WjVA" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_8qzH622xEeqhENMVS6WjVA" elementId="org.eclipse.debug.internal.ui.ConsoleFolderView" containerData="6000" selectedElement="_8qzH7G2xEeqhENMVS6WjVA">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH7G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.console.ConsoleView" ref="_8qzvjG2xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH7W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.TaskList" ref="_8qzvi22xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH7m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_8qzvhW2xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH722xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_8qzvuW2xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH8G2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.terminal.sdkterminal" ref="_8qzv022xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH8W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ProblemView" ref="_8qzvhm2xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH8m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.executablesView" ref="_8qzv9m2xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH822xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.DisplayView" toBeRendered="false" ref="_8qzv-W2xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH9G2xEeqhENMVS6WjVA" elementId="org.eclipse.search.SearchResultView" toBeRendered="false" ref="_8qzv_G2xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH9W2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.TraceView" toBeRendered="false" ref="_8qzv_W2xEeqhENMVS6WjVA"/>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_8qzH9m2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.logger.SdkLogViewMStack" containerData="4000" selectedElement="_8qzH-G2xEeqhENMVS6WjVA">
                <tags>Debug</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH922xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.logger.SdkLogView" ref="_8qzvx22xEeqhENMVS6WjVA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_8qzH-G2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.MemoryView" ref="_8qzv_22xEeqhENMVS6WjVA"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_8qzH-W2xEeqhENMVS6WjVA" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_8qzH-m2xEeqhENMVS6WjVA" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_8qzvJ22xEeqhENMVS6WjVA"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_8qzH-22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_8qzvKG2xEeqhENMVS6WjVA"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_8qzH_G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_8qzvK22xEeqhENMVS6WjVA"/>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvJ22xEeqhENMVS6WjVA" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvKG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8qzvKW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8qzvKm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvK22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_8qzvLG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.editorss" selectedElement="_8qzvLW2xEeqhENMVS6WjVA">
      <children xsi:type="basic:PartStack" xmi:id="_8qzvLW2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_eUFa4HF1Eeq57eTh4XJtOg">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
        <children xsi:type="basic:Part" xmi:id="_8qzvLm2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; partName=&quot;main.c&quot; title=&quot;main.c&quot; tooltip=&quot;fsbl/src/main.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl/src/main.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;6342&quot; selectionTopPixel=&quot;8150&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvL22xEeqhENMVS6WjVA" elementId="#CEditorContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#CEditorContext</tags>
            <tags>popup:org.eclipse.cdt.ui.editor.CEditor.EditorContext</tags>
            <tags>popup:#AbstractTextEditorContext</tags>
          </menus>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvMG2xEeqhENMVS6WjVA" elementId="#CEditorRulerContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#CEditorRulerContext</tags>
            <tags>popup:org.eclipse.cdt.ui.editor.CEditor.RulerContext</tags>
            <tags>popup:#AbstractTextEditorRulerContext</tags>
          </menus>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvMW2xEeqhENMVS6WjVA" elementId="#OverviewRulerContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#OverviewRulerContext</tags>
          </menus>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvMm2xEeqhENMVS6WjVA" elementId="#CEditorContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#CEditorContext</tags>
            <tags>popup:org.eclipse.cdt.ui.editor.CEditor.EditorContext</tags>
            <tags>popup:#AbstractTextEditorContext</tags>
          </menus>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvM22xEeqhENMVS6WjVA" elementId="#CEditorRulerContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#CEditorRulerContext</tags>
            <tags>popup:org.eclipse.cdt.ui.editor.CEditor.RulerContext</tags>
            <tags>popup:#AbstractTextEditorRulerContext</tags>
          </menus>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvNG2xEeqhENMVS6WjVA" elementId="#OverviewRulerContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#OverviewRulerContext</tags>
          </menus>
        </children>
        <children xsi:type="basic:Part" xmi:id="_8qzvNW2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" closeable="true">
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.dsf.ui.disassembly</tags>
          <tags>removeOnHide</tags>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvNm2xEeqhENMVS6WjVA" elementId="#DisassemblyPartRulerContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#DisassemblyPartRulerContext</tags>
          </menus>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvN22xEeqhENMVS6WjVA" elementId="#DisassemblyPartContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#DisassemblyPartContext</tags>
          </menus>
        </children>
        <children xsi:type="basic:Part" xmi:id="_8qzvOG2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="fsbl_debug.h" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;fsbl_debug.h&quot; partName=&quot;fsbl_debug.h&quot; title=&quot;fsbl_debug.h&quot; tooltip=&quot;fsbl/src/fsbl_debug.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl/src/fsbl_debug.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2561&quot; selectionTopPixel=&quot;246&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvOW2xEeqhENMVS6WjVA" elementId="#CEditorContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#CEditorContext</tags>
            <tags>popup:org.eclipse.cdt.ui.editor.CEditor.EditorContext</tags>
            <tags>popup:#AbstractTextEditorContext</tags>
          </menus>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvOm2xEeqhENMVS6WjVA" elementId="#CEditorRulerContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#CEditorRulerContext</tags>
            <tags>popup:org.eclipse.cdt.ui.editor.CEditor.RulerContext</tags>
            <tags>popup:#AbstractTextEditorRulerContext</tags>
          </menus>
          <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvO22xEeqhENMVS6WjVA" elementId="#OverviewRulerContext">
            <tags>menuContribution:popup</tags>
            <tags>popup:#OverviewRulerContext</tags>
          </menus>
        </children>
        <children xsi:type="basic:Part" xmi:id="_DnTiIHFoEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="system.mss" iconURI="platform:/plugin/com.xilinx.sdk.sw.ui/icons/16x16/mss_file_icon.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;com.xilinx.sdk.sw.MSSEditor&quot; name=&quot;system.mss&quot; partName=&quot;system.mss&quot; title=&quot;system.mss&quot; tooltip=&quot;fsbl_bsp/system.mss&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp/system.mss&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>com.xilinx.sdk.sw.MSSEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_LHjngHFoEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="system.hdf" iconURI="platform:/plugin/com.xilinx.sdk.hw/icons/hwspec_file_icon.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;com.xilinx.sdk.hw.ui.HwSpecXMLEditor&quot; name=&quot;system.hdf&quot; partName=&quot;system.hdf&quot; title=&quot;system.hdf&quot; tooltip=&quot;system_top_hw_platform_0/system.hdf&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/system_top_hw_platform_0/system.hdf&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>com.xilinx.sdk.hw.ui.HwSpecXMLEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_68ZLAHFoEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="xqspips_flash_lqspi_example.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;xqspips_flash_lqspi_example.c&quot; partName=&quot;xqspips_flash_lqspi_example.c&quot; title=&quot;xqspips_flash_lqspi_example.c&quot; tooltip=&quot;fsbl_bsp_xqspips_flash_lqspi_example_1/src/xqspips_flash_lqspi_example.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp_xqspips_flash_lqspi_example_1/src/xqspips_flash_lqspi_example.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;20015&quot; selectionTopPixel=&quot;9800&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_8Q3DkHFoEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Xilinx.spec" iconURI="platform:/plugin/org.eclipse.ui.editors/icons/full/obj16/file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;Xilinx.spec&quot; partName=&quot;Xilinx.spec&quot; title=&quot;Xilinx.spec&quot; tooltip=&quot;fsbl_bsp_xqspips_flash_lqspi_example_1/src/Xilinx.spec&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp_xqspips_flash_lqspi_example_1/src/Xilinx.spec&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.ui.DefaultTextEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_847wMHFoEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="lscript.ld" iconURI="platform:/plugin/com.xilinx.sdk.lscript/icons/16x16/linkgen_16x16.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;com.xilinx.sdk.lscript.editor&quot; name=&quot;lscript.ld&quot; partName=&quot;lscript.ld&quot; title=&quot;lscript.ld&quot; tooltip=&quot;fsbl_bsp_xqspips_flash_lqspi_example_1/src/lscript.ld&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp_xqspips_flash_lqspi_example_1/src/lscript.ld&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>com.xilinx.sdk.lscript.editor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_Ah-6gHFpEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="xparameters.h" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;xparameters.h&quot; partName=&quot;xparameters.h&quot; title=&quot;xparameters.h&quot; tooltip=&quot;fsbl_bsp/ps7_cortexa9_0/include/xparameters.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp/ps7_cortexa9_0/include/xparameters.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;13155&quot; selectionTopPixel=&quot;6511&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_BIbaAHFqEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ps7_init.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;ps7_init.c&quot; partName=&quot;ps7_init.c&quot; title=&quot;ps7_init.c&quot; tooltip=&quot;fsbl/src/ps7_init.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl/src/ps7_init.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;8&quot; selectionOffset=&quot;574119&quot; selectionTopPixel=&quot;218229&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_B_xcQHFrEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="xqspips.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;xqspips.c&quot; partName=&quot;xqspips.c&quot; title=&quot;xqspips.c&quot; tooltip=&quot;fsbl_bsp/ps7_cortexa9_0/libsrc/qspips_v3_3/src/xqspips.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp/ps7_cortexa9_0/libsrc/qspips_v3_3/src/xqspips.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;21&quot; selectionOffset=&quot;9711&quot; selectionTopPixel=&quot;2822&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_EIdnEHFrEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="xqspips_sinit.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;xqspips_sinit.c&quot; partName=&quot;xqspips_sinit.c&quot; title=&quot;xqspips_sinit.c&quot; tooltip=&quot;fsbl_bsp/ps7_cortexa9_0/libsrc/qspips_v3_3/src/xqspips_sinit.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp/ps7_cortexa9_0/libsrc/qspips_v3_3/src/xqspips_sinit.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;20&quot; selectionOffset=&quot;3325&quot; selectionTopPixel=&quot;552&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_I0BXQHFrEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="xqspips_selftest.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;xqspips_selftest.c&quot; partName=&quot;xqspips_selftest.c&quot; title=&quot;xqspips_selftest.c&quot; tooltip=&quot;fsbl_bsp/ps7_cortexa9_0/libsrc/qspips_v3_3/src/xqspips_selftest.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp/ps7_cortexa9_0/libsrc/qspips_v3_3/src/xqspips_selftest.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4471&quot; selectionTopPixel=&quot;1215&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_K7dk4HFrEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="xqspips_options.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;xqspips_options.c&quot; partName=&quot;xqspips_options.c&quot; title=&quot;xqspips_options.c&quot; tooltip=&quot;fsbl_bsp/ps7_cortexa9_0/libsrc/qspips_v3_3/src/xqspips_options.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp/ps7_cortexa9_0/libsrc/qspips_v3_3/src/xqspips_options.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;23&quot; selectionOffset=&quot;9176&quot; selectionTopPixel=&quot;3808&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_sZ2VUHFtEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; partName=&quot;main.c&quot; title=&quot;main.c&quot; tooltip=&quot;ad9361/src/main.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/main.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;24630&quot; selectionTopPixel=&quot;7709&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_KdBOAHFuEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="platform.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;platform.c&quot; partName=&quot;platform.c&quot; title=&quot;platform.c&quot; tooltip=&quot;ad9361/src/platform_xilinx/platform.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/platform_xilinx/platform.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;8&quot; selectionOffset=&quot;3748&quot; selectionTopPixel=&quot;583&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_0wR_UHFuEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ad9361_api.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;ad9361_api.c&quot; partName=&quot;ad9361_api.c&quot; title=&quot;ad9361_api.c&quot; tooltip=&quot;ad9361/src/ad9361_api.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/ad9361_api.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;54394&quot; selectionTopPixel=&quot;26435&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_05ha4HFuEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ad9361.h" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;ad9361.h&quot; partName=&quot;ad9361.h&quot; title=&quot;ad9361.h&quot; tooltip=&quot;ad9361/src/ad9361.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/ad9361.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;25&quot; selectionOffset=&quot;3136&quot; selectionTopPixel=&quot;4556&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WdeD8HF1Eeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="xcpu_cortexa9.h" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;xcpu_cortexa9.h&quot; partName=&quot;xcpu_cortexa9.h&quot; title=&quot;xcpu_cortexa9.h&quot; tooltip=&quot;fsbl_bsp/ps7_cortexa9_0/libsrc/cpu_cortexa9_v2_3/src/xcpu_cortexa9.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp/ps7_cortexa9_0/libsrc/cpu_cortexa9_v2_3/src/xcpu_cortexa9.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;82&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_eUFa4HF1Eeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ad9361.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;ad9361.c&quot; partName=&quot;ad9361.c&quot; title=&quot;ad9361.c&quot; tooltip=&quot;ad9361/src/ad9361.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/ad9361.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;192005&quot; selectionTopPixel=&quot;98257&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_JaJogHF-Eeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="xparameters_ps.h" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;xparameters_ps.h&quot; partName=&quot;xparameters_ps.h&quot; title=&quot;xparameters_ps.h&quot; tooltip=&quot;fsbl_bsp/ps7_cortexa9_0/include/xparameters_ps.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp/ps7_cortexa9_0/include/xparameters_ps.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;21&quot; selectionOffset=&quot;2686&quot; selectionTopPixel=&quot;476&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_LL-LcHF-Eeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="xparameters_ps.h" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;xparameters_ps.h&quot; partName=&quot;xparameters_ps.h&quot; title=&quot;xparameters_ps.h&quot; tooltip=&quot;fsbl_bsp/ps7_cortexa9_0/libsrc/standalone_v6_1/src/xparameters_ps.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl_bsp/ps7_cortexa9_0/libsrc/standalone_v6_1/src/xparameters_ps.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;21&quot; selectionOffset=&quot;2686&quot; selectionTopPixel=&quot;476&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_Ci-M8HGMEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="dac_core.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;dac_core.c&quot; partName=&quot;dac_core.c&quot; title=&quot;dac_core.c&quot; tooltip=&quot;ad9361/src/platform_xilinx/dac_core.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/platform_xilinx/dac_core.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;10&quot; selectionOffset=&quot;8847&quot; selectionTopPixel=&quot;4590&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_nbIngHGMEeq57eTh4XJtOg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="command.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;command.c&quot; partName=&quot;command.c&quot; title=&quot;command.c&quot; tooltip=&quot;ad9361/src/console_commands/command.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/console_commands/command.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;16452&quot; selectionTopPixel=&quot;5151&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_ikR4QIROEeqiufKhdLKJ0Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="system.hdf" iconURI="platform:/plugin/com.xilinx.sdk.hw/icons/hwspec_file_icon.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;com.xilinx.sdk.hw.ui.HwSpecXMLEditor&quot; name=&quot;system.hdf&quot; partName=&quot;system.hdf&quot; title=&quot;system.hdf&quot; tooltip=&quot;system_top_hw_platform_1/system.hdf&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/system_top_hw_platform_1/system.hdf&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>com.xilinx.sdk.hw.ui.HwSpecXMLEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_Ch6TEJNkEequWrDyeI1hBw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="system.hdf" iconURI="platform:/plugin/com.xilinx.sdk.hw/icons/hwspec_file_icon.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;com.xilinx.sdk.hw.ui.HwSpecXMLEditor&quot; name=&quot;system.hdf&quot; partName=&quot;system.hdf&quot; title=&quot;system.hdf&quot; tooltip=&quot;system_top_hw_platform_2/system.hdf&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/system_top_hw_platform_2/system.hdf&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>com.xilinx.sdk.hw.ui.HwSpecXMLEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_7kPHkJNpEequWrDyeI1hBw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="mioInit.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;mioInit.c&quot; partName=&quot;mioInit.c&quot; title=&quot;mioInit.c&quot; tooltip=&quot;fsbl/src/mioInit.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/fsbl/src/mioInit.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;7&quot; selectionOffset=&quot;48&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_yKn3YJNqEequWrDyeI1hBw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="util.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;util.c&quot; partName=&quot;util.c&quot; title=&quot;util.c&quot; tooltip=&quot;ad9361/src/util.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/util.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2610&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_41P8UJNqEequWrDyeI1hBw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="util.h" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;util.h&quot; partName=&quot;util.h&quot; title=&quot;util.h&quot; tooltip=&quot;ad9361/src/util.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/util.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;82&quot; selectionTopPixel=&quot;1116&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_D6smwJNrEequWrDyeI1hBw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="console.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;console.c&quot; partName=&quot;console.c&quot; title=&quot;console.c&quot; tooltip=&quot;ad9361/src/console_commands/console.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/console_commands/console.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;12&quot; selectionOffset=&quot;10137&quot; selectionTopPixel=&quot;6324&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_0JXmkJNrEequWrDyeI1hBw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="adc_core.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;adc_core.c&quot; partName=&quot;adc_core.c&quot; title=&quot;adc_core.c&quot; tooltip=&quot;ad9361/src/platform_xilinx/adc_core.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/platform_xilinx/adc_core.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;82&quot; selectionTopPixel=&quot;969&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_nwNokJQbEeqSJ5celE-NEg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="config.h" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;config.h&quot; partName=&quot;config.h&quot; title=&quot;config.h&quot; tooltip=&quot;ad9361/src/config.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/config.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3240&quot; selectionTopPixel=&quot;470&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_kVrJcJQdEeqSJ5celE-NEg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="common.h" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;common.h&quot; partName=&quot;common.h&quot; title=&quot;common.h&quot; tooltip=&quot;ad9361/src/common.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/common.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;3&quot; selectionOffset=&quot;3647&quot; selectionTopPixel=&quot;249&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_OSmvUJQnEeqSJ5celE-NEg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="console.h" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;console.h&quot; partName=&quot;console.h&quot; title=&quot;console.h&quot; tooltip=&quot;ad9361/src/console_commands/console.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/ad9361/src/console_commands/console.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3311&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvRG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8qzvRW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvV22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.ProjectExplorer#PopupMenu">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.navigator.ProjectExplorer#PopupMenu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvWG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.ProjectExplorer#PopupMenu">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.navigator.ProjectExplorer#PopupMenu</tags>
      </menus>
      <toolbar xmi:id="_8qzvfW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvg22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvhG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvhW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvhm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot; partName=&quot;Problems&quot;>&#xD;&#xA;&lt;expanded>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xD;&#xA;&lt;/expanded>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8qzvh22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzviG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ProblemView">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.views.ProblemView</tags>
        <tags>popup:org.eclipse.ui.ide.MarkersView</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzviW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ProblemView">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.views.ProblemView</tags>
        <tags>popup:org.eclipse.ui.ide.MarkersView</tags>
      </menus>
      <toolbar xmi:id="_8qzvim2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvi22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.completionField&quot; categoryGroup=&quot;none&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.tasksGenerator&quot; partName=&quot;Tasks&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.completionField=&quot;40&quot; org.eclipse.ui.ide.descriptionField=&quot;300&quot; org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.priorityField=&quot;30&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.completionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.priorityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.descriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_hxEdsJQoEeqSJ5celE-NEg" elementId="org.eclipse.ui.views.TaskList">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_hxEdsZQoEeqSJ5celE-NEg" elementId="org.eclipse.ui.views.TaskList" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvjG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8qzvjW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvjm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CDTGlobalBuildConsole">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.cdt.ui.CDTGlobalBuildConsole</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvj22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CDTBuildConsole">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.cdt.ui.CDTBuildConsole</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvkG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.MessageConsole.#ContextMenu">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.MessageConsole.#ContextMenu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvkW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.MessageConsole.#ContextMenu">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.MessageConsole.#ContextMenu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvkm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.MessageConsole.#ContextMenu">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.MessageConsole.#ContextMenu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvk22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.MessageConsole.#ContextMenu">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.MessageConsole.#ContextMenu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvlG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CDTGlobalBuildConsole">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.cdt.ui.CDTGlobalBuildConsole</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvlW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CDTBuildConsole">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.cdt.ui.CDTBuildConsole</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvlm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.MessageConsole.#ContextMenu">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.MessageConsole.#ContextMenu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvl22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.MessageConsole.#ContextMenu">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.MessageConsole.#ContextMenu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvmG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.MessageConsole.#ContextMenu">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.ui.MessageConsole.#ContextMenu</tags>
      </menus>
      <toolbar xmi:id="_8qzvmW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvuW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_HmPTEJQpEeqSJ5celE-NEg" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_HmPTEZQpEeqSJ5celE-NEg" elementId="org.eclipse.ui.views.PropertySheet" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvum2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8qzvu22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvvG2xEeqhENMVS6WjVA" elementId="#TranslationUnitOutlinerContext">
        <tags>menuContribution:popup</tags>
        <tags>popup:#TranslationUnitOutlinerContext</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvvW2xEeqhENMVS6WjVA" elementId="#TranslationUnitOutlinerContext">
        <tags>menuContribution:popup</tags>
        <tags>popup:#TranslationUnitOutlinerContext</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvvm2xEeqhENMVS6WjVA" elementId="#TranslationUnitOutlinerContext">
        <tags>menuContribution:popup</tags>
        <tags>popup:#TranslationUnitOutlinerContext</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvv22xEeqhENMVS6WjVA" elementId="#TranslationUnitOutlinerContext">
        <tags>menuContribution:popup</tags>
        <tags>popup:#TranslationUnitOutlinerContext</tags>
      </menus>
      <toolbar xmi:id="_8qzvwG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ContentOutline"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvx22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.logger.SdkLogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="SDK Log" iconURI="platform:/plugin/com.xilinx.sdk.loggers/icons/icon.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_8qzvyG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.logger.SdkLogView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8qzvyW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.logger.SdkLogView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzvzG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Target Connections" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/target-mgmt-view.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_8qzvzW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvzm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView">
        <tags>menuContribution:popup</tags>
        <tags>popup:com.xilinx.sdk.targetmanager.ui.TargetManagementView</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzvz22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView">
        <tags>menuContribution:popup</tags>
        <tags>popup:com.xilinx.sdk.targetmanager.ui.TargetManagementView</tags>
      </menus>
      <toolbar xmi:id="_8qzv0G2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv022xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.terminal.sdkterminal" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="SDK Terminal" iconURI="platform:/plugin/com.xilinx.sdk.terminal/icons/console.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_HUJdgJQpEeqSJ5celE-NEg" elementId="com.xilinx.sdk.terminal.sdkterminal">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_HUJdgZQpEeqSJ5celE-NEg" elementId="com.xilinx.sdk.terminal.sdkterminal" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv1G2xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Documents" iconURI="platform:/plugin/ilg.gnuarmeclipse.managedbuild.packs/icons/pdficon_small.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:C/C++ Packs</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv1W2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Make Target" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv1m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_8qzv122xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzv2G2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.DebugView">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.DebugView</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzv2W2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.DebugView">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.DebugView</tags>
      </menus>
      <toolbar xmi:id="_8qzv2m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.DebugView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv222xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_8qzv3G2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzv3W2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.VariableView.detail">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.VariableView.detail</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzv3m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.VariableView">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.VariableView</tags>
      </menus>
      <toolbar xmi:id="_8qzv322xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.VariableView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv4G2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_8qzv4W2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzv4m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.VariableView.detail">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.VariableView.detail</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzv422xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.BreakpointView</tags>
      </menus>
      <toolbar xmi:id="_8qzv5G2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv5W2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv5m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_8qzv522xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzv6G2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.VariableView.detail">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.VariableView.detail</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzv6W2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.RegisterView</tags>
      </menus>
      <toolbar xmi:id="_8qzv6m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.RegisterView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv622xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="XSCT Console" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/xsdbconsole_icon.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_8qzv7G2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8qzv7W2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv7m2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="QEMU Console" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/xsdbconsole_icon.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv722xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv8G2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv8W2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_8qzv8m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.ModuleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzv822xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.VariableView.detail">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.VariableView.detail</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzv9G2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.ModuleView">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.ModuleView</tags>
      </menus>
      <toolbar xmi:id="_8qzv9W2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.ModuleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv9m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv922xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv-G2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv-W2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.DisplayView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Display" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv-m2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv-22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv_G2xEeqhENMVS6WjVA" elementId="org.eclipse.search.SearchResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv_W2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.TraceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TCF Trace" iconURI="platform:/plugin/org.eclipse.tcf.debug.ui/icons/tcf.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv_m2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.ProfilerView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TCF Profiler" iconURI="platform:/plugin/org.eclipse.tcf.debug.ui/icons/profiler.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8qzv_22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_8qzwAG2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <menus xsi:type="menu:PopupMenu" xmi:id="_8qzwAW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.MemoryView.MemoryBlocksTreeViewPane">
        <tags>menuContribution:popup</tags>
        <tags>popup:org.eclipse.debug.ui.MemoryView.MemoryBlocksTreeViewPane</tags>
      </menus>
      <toolbar xmi:id="_8qzwAm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.MemoryView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_2nKlQHFqEeq57eTh4XJtOg" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view isPinned=&quot;false&quot;>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_2neHQHFqEeq57eTh4XJtOg" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_2neHQXFqEeq57eTh4XJtOg" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_8qzwA22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwBG2xEeqhENMVS6WjVA" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8qzwBW2xEeqhENMVS6WjVA" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwBm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="__7mD8JQaEeqSJ5celE-NEg" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_8q1m422xEeqhENMVS6WjVA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwFm2xEeqhENMVS6WjVA" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8qzwF22xEeqhENMVS6WjVA" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwOG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwQ22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwTG2xEeqhENMVS6WjVA" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwVG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwX22xEeqhENMVS6WjVA" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8qzwYG2xEeqhENMVS6WjVA" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwYW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="__7ogMJQaEeqSJ5celE-NEg" elementId="org.eclipse.ui.window.pinEditor" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_8q1mk22xEeqhENMVS6WjVA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwbG2xEeqhENMVS6WjVA" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8qzwbW2xEeqhENMVS6WjVA" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwbm2xEeqhENMVS6WjVA" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8qzwb22xEeqhENMVS6WjVA" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8qzwcG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.workbench.help">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_8qzwc22xEeqhENMVS6WjVA" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_8qzwd22xEeqhENMVS6WjVA" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_8qzweG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_8qzweW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_8qzwem2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_8qzwe22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_8qzwfG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_8qzwfW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_8qzwfm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Right"/>
  </children>
  <bindingTables xmi:id="_8qzwf22xEeqhENMVS6WjVA" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_8q0W322xEeqhENMVS6WjVA">
    <bindings xmi:id="_8qzwgG2xEeqhENMVS6WjVA" keySequence="CTRL+A" command="_8q1llm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwgW2xEeqhENMVS6WjVA" keySequence="CTRL+C" command="_8q1l6G2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwgm2xEeqhENMVS6WjVA" keySequence="ALT+PAGE_UP" command="_8q1lwW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwg22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+D" command="_8q1nRG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwhG2xEeqhENMVS6WjVA" keySequence="ALT+PAGE_DOWN" command="_8q1ma22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwhW2xEeqhENMVS6WjVA" keySequence="SHIFT+INSERT" command="_8q1ke22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwhm2xEeqhENMVS6WjVA" keySequence="CTRL+V" command="_8q1ke22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwh22xEeqhENMVS6WjVA" keySequence="CTRL+F10" command="_8q1klW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwiG2xEeqhENMVS6WjVA" keySequence="CTRL+Y" command="_8q1ltG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwiW2xEeqhENMVS6WjVA" keySequence="CTRL+Z" command="_8q1lSG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwim2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+F3" command="_8q1m622xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwi22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+L" command="_8q1nHG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwjG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+I" command="_8q1k8G2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwjW2xEeqhENMVS6WjVA" keySequence="CTRL+INSERT" command="_8q1l6G2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwjm2xEeqhENMVS6WjVA" keySequence="CTRL+1" command="_8q1lC22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwj22xEeqhENMVS6WjVA" keySequence="CTRL+X" command="_8q1lUG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwkG2xEeqhENMVS6WjVA" keySequence="ALT+/" command="_8q1mtm2xEeqhENMVS6WjVA">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_8qzwkW2xEeqhENMVS6WjVA" keySequence="SHIFT+DEL" command="_8q1lUG2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8qzwkm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_8q0W6W2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8qzwk22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_8q1nF22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwlG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_8q2LG22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwlW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+ARROW_UP" command="_8q1myG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwlm2xEeqhENMVS6WjVA" keySequence="ALT+C" command="_8q1mEm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwl22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_UP" command="_8q1lem2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwmG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+/" command="_8q1nQ22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwmW2xEeqhENMVS6WjVA" keySequence="CTRL+#" command="_8q1mnG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwmm2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+G" command="_8q1kUm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwm22xEeqhENMVS6WjVA" keySequence="CTRL+G" command="_8q1nXm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwnG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+F" command="_8q2LGm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwnW2xEeqhENMVS6WjVA" keySequence="SHIFT+TAB" command="_8q1mNW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwnm2xEeqhENMVS6WjVA" keySequence="ALT+CTRL+H" command="_8q1lDm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwn22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+H" command="_8q1mM22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwoG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Z" command="_8q1m222xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwoW2xEeqhENMVS6WjVA" keySequence="F4" command="_8q1nP22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwom2xEeqhENMVS6WjVA" keySequence="ALT+CTRL+S" command="_8q2LA22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwo22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+L" command="_8q1lnG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwpG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+S" command="_8q1kmG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwpW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+O" command="_8q1lYm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwpm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+R" command="_8q1mw22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwp22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+T" command="_8q1lKW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwqG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+O" command="_8q1lVm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwqW2xEeqhENMVS6WjVA" keySequence="ALT+CTRL+I" command="_8q1mFG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwqm2xEeqhENMVS6WjVA" keySequence="CTRL+TAB" command="_8q2LGW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwq22xEeqhENMVS6WjVA" keySequence="CTRL+I" command="_8q1k6G2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwrG2xEeqhENMVS6WjVA" keySequence="CTRL+O" command="_8q1l-m2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwrW2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+T" command="_8q1mo22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwrm2xEeqhENMVS6WjVA" keySequence="CTRL+/" command="_8q1kfm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwr22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+P" command="_8q1lcG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwsG2xEeqhENMVS6WjVA" keySequence="CTRL+T" command="_8q1md22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwsW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+N" command="_8q1k9m2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwsm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+M" command="_8q1kcW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzws22xEeqhENMVS6WjVA" keySequence="F3" command="_8q2LIm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwtG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+\" command="_8q1mEG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwtW2xEeqhENMVS6WjVA" keySequence="CTRL+=" command="_8q1mnG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwtm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_8q1mt22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwt22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_8q1mbW2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8qzwuG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_8q0W5m2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8qzwuW2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_8q1ko22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwum2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+C" command="_8q1mAW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwu22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_8q1ky22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwvG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+ARROW_UP" command="_8q1lvm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwvW2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+B" command="_8q2LEm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwvm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_UP" command="_8q1lom2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwv22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+/" command="_8q1lsG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwwG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+F" command="_8q1nJ22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwwW2xEeqhENMVS6WjVA" keySequence="CTRL+F3" command="_8q1nV22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwwm2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+M" command="_8q1lF22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzww22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+O" command="_8q1lQ22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwxG2xEeqhENMVS6WjVA" keySequence="CTRL+I" command="_8q1lY22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwxW2xEeqhENMVS6WjVA" keySequence="CTRL+O" command="_8q1ll22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwxm2xEeqhENMVS6WjVA" keySequence="CTRL+/" command="_8q1mAW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwx22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+P" command="_8q1mdW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwyG2xEeqhENMVS6WjVA" keySequence="CTRL+T" command="_8q1mCW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwyW2xEeqhENMVS6WjVA" keySequence="CTRL+7" command="_8q1mAW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwym2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+U" command="_8q1mkm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwy22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+\" command="_8q1kqG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwzG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_8q1lbG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwzW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_8q1lmW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwzm2xEeqhENMVS6WjVA" keySequence="CTRL+2 F" command="_8q1nR22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzwz22xEeqhENMVS6WjVA" keySequence="CTRL+2 R" command="_8q1mom2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw0G2xEeqhENMVS6WjVA" keySequence="CTRL+2 L" command="_8q1knW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw0W2xEeqhENMVS6WjVA" keySequence="CTRL+2 M" command="_8q1lb22xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8qzw0m2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" bindingContext="_8q0W-22xEeqhENMVS6WjVA">
    <bindings xmi:id="_8qzw022xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_8q1mKW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw1G2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_8q1kbm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw1W2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_UP" command="_8q1lZm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw1m2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+HOME" command="_8q1nU22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw122xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+END" command="_8q1lPW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw2G2xEeqhENMVS6WjVA" keySequence="ALT+R" command="_8q1mtG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw2W2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_8q1l1m2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8qzw2m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_8q0W-W2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8qzw222xEeqhENMVS6WjVA" keySequence="ALT+ARROW_RIGHT" command="_8q1miG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw3G2xEeqhENMVS6WjVA" keySequence="ALT+ARROW_LEFT" command="_8q1meG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw3W2xEeqhENMVS6WjVA" keySequence="F3" command="_8q2LIm2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8qzw3m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.contexts.window" bindingContext="_8q0W4G2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8qzw322xEeqhENMVS6WjVA" keySequence="ALT+ARROW_RIGHT" command="_8q1lZW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw4G2xEeqhENMVS6WjVA" keySequence="CTRL+B" command="_8q1kgm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw4W2xEeqhENMVS6WjVA" keySequence="ALT+ARROW_LEFT" command="_8q1kmm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw4m2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+D Q" command="_8q1lo22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw422xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+D J" command="_8q1mjm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw5G2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+D A" command="_8q1mzm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw5W2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+D T" command="_8q1kXm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw5m2xEeqhENMVS6WjVA" keySequence="CTRL+M" command="_8q1mr22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8qzw522xEeqhENMVS6WjVA" keySequence="ALT+CTRL+G" command="_8q1mVW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V0G2xEeqhENMVS6WjVA" keySequence="CTRL+#" command="_8q1klm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V0W2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+G" command="_8q2LAG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V0m2xEeqhENMVS6WjVA" keySequence="CTRL+G" command="_8q1kV22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V022xEeqhENMVS6WjVA" keySequence="CTRL+F" command="_8q1ksW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V1G2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+E" command="_8q1k1W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V1W2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+F" command="_8q1m822xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V1m2xEeqhENMVS6WjVA" keySequence="CTRL+E" command="_8q1lPm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V122xEeqhENMVS6WjVA" keySequence="ALT+CTRL+H" command="_8q1kkm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V2G2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+H" command="_8q1l4G2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V2W2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+I" command="_8q1kl22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V2m2xEeqhENMVS6WjVA" keySequence="CTRL+H" command="_8q1mtW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V222xEeqhENMVS6WjVA" keySequence="F11" command="_8q1nOm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V3G2xEeqhENMVS6WjVA" keySequence="CTRL+U" command="_8q1lr22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V3W2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+F7" command="_8q1mdm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V3m2xEeqhENMVS6WjVA" keySequence="ALT+-" command="_8q1l_W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V322xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Z" command="_8q1l222xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V4G2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+F4" command="_8q1lTm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V4W2xEeqhENMVS6WjVA" keySequence="CTRL+F11" command="_8q1nAm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V4m2xEeqhENMVS6WjVA" keySequence="SHIFT+F9" command="_8q1lYG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V422xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+N" command="_8q1lS22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V5G2xEeqhENMVS6WjVA" keySequence="SHIFT+F5" command="_8q1lnm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V5W2xEeqhENMVS6WjVA" keySequence="F4" command="_8q1kiW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V5m2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+L" command="_8q1lB22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V522xEeqhENMVS6WjVA" keySequence="CTRL+N" command="_8q2LBW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V6G2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+S" command="_8q1l022xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V6W2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+O" command="_8q1nPm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V6m2xEeqhENMVS6WjVA" keySequence="CTRL+F4" command="_8q1lu22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V622xEeqhENMVS6WjVA" keySequence="SHIFT+F2" command="_8q1mNG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V7G2xEeqhENMVS6WjVA" keySequence="ALT+CR" command="_8q1mn22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V7W2xEeqhENMVS6WjVA" keySequence="F12" command="_8q1muW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V7m2xEeqhENMVS6WjVA" keySequence="CTRL+Q" command="_8q1m8G2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V722xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+R" command="_8q2LH22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V8G2xEeqhENMVS6WjVA" keySequence="F2" command="_8q1kgW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V8W2xEeqhENMVS6WjVA" keySequence="CTRL+3" command="_8q1lFW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V8m2xEeqhENMVS6WjVA" keySequence="CTRL+F7" command="_8q1l6W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V822xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_8q1lL22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V9G2xEeqhENMVS6WjVA" keySequence="CTRL+W" command="_8q1lu22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V9W2xEeqhENMVS6WjVA" keySequence="CTRL+." command="_8q1nWG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V9m2xEeqhENMVS6WjVA" keySequence="F9" command="_8q1lcm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V922xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+F7" command="_8q1nO22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V-G2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+R" command="_8q1luG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V-W2xEeqhENMVS6WjVA" keySequence="DEL" command="_8q1kv22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V-m2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+T" command="_8q1lT22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V-22xEeqhENMVS6WjVA" keySequence="CTRL+S" command="_8q1lg22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V_G2xEeqhENMVS6WjVA" keySequence="CTRL+P" command="_8q1m422xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V_W2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+W" command="_8q1nVG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V_m2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+J" command="_8q1lOm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0V_22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_8q1mRG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WAG2xEeqhENMVS6WjVA" keySequence="CTRL+," command="_8q1kf22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WAW2xEeqhENMVS6WjVA" keySequence="CTRL+F8" command="_8q1lDW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WAm2xEeqhENMVS6WjVA" keySequence="ALT+F7" command="_8q1mDm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WA22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+T" command="_8q1mC22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WBG2xEeqhENMVS6WjVA" keySequence="CTRL+F6" command="_8q1kum2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WBW2xEeqhENMVS6WjVA" keySequence="CTRL+{" command="_8q1lLW2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WBm2xEeqhENMVS6WjVA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_8q0WB22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+N" command="_8q1l_m2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WCG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+F8" command="_8q1lLG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WCW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+S" command="_8q1mRW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WCm2xEeqhENMVS6WjVA" keySequence="F5" command="_8q1lbW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WC22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+W" command="_8q1lTm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WDG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+M" command="_8q1nSW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WDW2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+V" command="_8q1lh22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WDm2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+F6" command="_8q1mTG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WD22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+U" command="_8q1k2W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WEG2xEeqhENMVS6WjVA" keySequence="F3" command="_8q1lBG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WEW2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+?" command="_8q1lKm2xEeqhENMVS6WjVA">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_8q0WEm2xEeqhENMVS6WjVA" keySequence="CTRL+_" command="_8q1lLW2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WE22xEeqhENMVS6WjVA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_8q0WFG2xEeqhENMVS6WjVA" keySequence="ALT+?" command="_8q1lKm2xEeqhENMVS6WjVA">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_8q0WFW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+B" command="_8q1kxm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WFm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+C" command="_8q1mum2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WF22xEeqhENMVS6WjVA" keySequence="ALT+CTRL+B" command="_8q1mZ22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WGG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q O" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WGW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_8q0WGm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q P" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WG22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_8q0WHG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q T" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WHW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_8q0WHm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q J" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WH22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_8q0WIG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q H" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WIW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_8q0WIm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q Q" command="_8q1mXm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WI22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q D" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WJG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_8q0WJW2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q V" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WJm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_8q0WJ22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+X T" command="_8q1llG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WKG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+X Q" command="_8q1k7G2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WKW2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+X A" command="_8q1kfW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WKm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q X" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WK22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_8q0WLG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q B" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WLW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_8q0WLm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q C" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WL22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_8q0WMG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q S" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WMW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_8q0WMm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+X J" command="_8q1mbm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WM22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q Z" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WNG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_8q0WNW2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+Q Y" command="_8q1mXm2xEeqhENMVS6WjVA">
      <parameters xmi:id="_8q0WNm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_8q0WN22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.textEditorScope" bindingContext="_8q0W5G2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WOG2xEeqhENMVS6WjVA" keySequence="CTRL+ARROW_DOWN" command="_8q2LHW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WOW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_8q1liG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WOm2xEeqhENMVS6WjVA" keySequence="CTRL+ARROW_UP" command="_8q1kyW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WO22xEeqhENMVS6WjVA" keySequence="ALT+CTRL+ARROW_UP" command="_8q1nVW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WPG2xEeqhENMVS6WjVA" keySequence="ALT+ARROW_DOWN" command="_8q1mcm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WPW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+DEL" command="_8q1mu22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WPm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+A" command="_8q1mCm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WP22xEeqhENMVS6WjVA" keySequence="CTRL+ARROW_LEFT" command="_8q1l422xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WQG2xEeqhENMVS6WjVA" keySequence="SHIFT+END" command="_8q1lhm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WQW2xEeqhENMVS6WjVA" keySequence="CTRL+HOME" command="_8q1kem2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WQm2xEeqhENMVS6WjVA" keySequence="CTRL+D" command="_8q1kpm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WQ22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_8q1k4G2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WRG2xEeqhENMVS6WjVA" keySequence="CTRL+ARROW_RIGHT" command="_8q1k922xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WRW2xEeqhENMVS6WjVA" keySequence="SHIFT+HOME" command="_8q1ld22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WRm2xEeqhENMVS6WjVA" keySequence="ALT+CTRL+J" command="_8q1lAm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WR22xEeqhENMVS6WjVA" keySequence="CTRL+END" command="_8q1mdG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WSG2xEeqhENMVS6WjVA" keySequence="CTRL+BS" command="_8q1kVm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WSW2xEeqhENMVS6WjVA" keySequence="SHIFT+CR" command="_8q1nKG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WSm2xEeqhENMVS6WjVA" keySequence="CTRL+NUMPAD_DIVIDE" command="_8q1kzG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WS22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+CR" command="_8q1m6m2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WTG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+X" command="_8q1l8W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WTW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+K" command="_8q1kxW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WTm2xEeqhENMVS6WjVA" keySequence="CTRL+NUMPAD_MULTIPLY" command="_8q1mg22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WT22xEeqhENMVS6WjVA" keySequence="END" command="_8q1nAG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WUG2xEeqhENMVS6WjVA" keySequence="CTRL+F10" command="_8q1m522xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WUW2xEeqhENMVS6WjVA" keySequence="CTRL+L" command="_8q1mzG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WUm2xEeqhENMVS6WjVA" keySequence="INSERT" command="_8q1mI22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WU22xEeqhENMVS6WjVA" keySequence="F2" command="_8q1lFm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WVG2xEeqhENMVS6WjVA" keySequence="CTRL+J" command="_8q1knG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WVW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_8q1mJW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WVm2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_8q1mim2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WV22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+J" command="_8q1k622xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WWG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+INSERT" command="_8q1k2m2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WWW2xEeqhENMVS6WjVA" keySequence="HOME" command="_8q1nLW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WWm2xEeqhENMVS6WjVA" keySequence="CTRL+NUMPAD_SUBTRACT" command="_8q1m6W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WW22xEeqhENMVS6WjVA" keySequence="ALT+CTRL+/" command="_8q1nMG2xEeqhENMVS6WjVA">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_8q0WXG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+Q" command="_8q1k-G2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WXW2xEeqhENMVS6WjVA" keySequence="CTRL+K" command="_8q1mam2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WXm2xEeqhENMVS6WjVA" keySequence="CTRL+NUMPAD_ADD" command="_8q1nSG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WX22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+Y" command="_8q1lgW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WYG2xEeqhENMVS6WjVA" keySequence="ALT+ARROW_UP" command="_8q2LEW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WYW2xEeqhENMVS6WjVA" keySequence="CTRL+DEL" command="_8q1lQG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WYm2xEeqhENMVS6WjVA" keySequence="ALT+CTRL+ARROW_DOWN" command="_8q1lm22xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0WY22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_8q0W6m2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WZG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+C" command="_8q1mAW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WZW2xEeqhENMVS6WjVA" keySequence="CTRL+/" command="_8q1mAW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WZm2xEeqhENMVS6WjVA" keySequence="CTRL+7" command="_8q1mAW2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0WZ22xEeqhENMVS6WjVA" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_8q0W4m2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WaG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+C" command="_8q1ml22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WaW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+V" command="_8q1lfG2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0Wam2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_8q0W_W2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0Wa22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+B" command="_8q2LEm2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0WbG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_8q0W922xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WbW2xEeqhENMVS6WjVA" keySequence="CTRL+C" command="_8q1lHm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wbm2xEeqhENMVS6WjVA" keySequence="CTRL+V" command="_8q1l722xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0Wb22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_8q0W9W2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WcG2xEeqhENMVS6WjVA" keySequence="CTRL+C" command="_8q1ku22xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0WcW2xEeqhENMVS6WjVA" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_8q0W_m2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0Wcm2xEeqhENMVS6WjVA" keySequence="ALT+E" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wc22xEeqhENMVS6WjVA" keySequence="ALT+H" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WdG2xEeqhENMVS6WjVA" keySequence="ALT+F" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WdW2xEeqhENMVS6WjVA" keySequence="ALT+G" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wdm2xEeqhENMVS6WjVA" keySequence="ALT+R" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wd22xEeqhENMVS6WjVA" keySequence="ALT+T" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WeG2xEeqhENMVS6WjVA" keySequence="ALT+N" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WeW2xEeqhENMVS6WjVA" keySequence="ALT+P" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wem2xEeqhENMVS6WjVA" keySequence="ALT+S" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0We22xEeqhENMVS6WjVA" keySequence="ALT+V" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WfG2xEeqhENMVS6WjVA" keySequence="ALT+W" command="_8q1l-W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WfW2xEeqhENMVS6WjVA" keySequence="ALT+A" command="_8q1l-W2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0Wfm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_8q0W9m2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0Wf22xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+G" command="_8q1kUm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WgG2xEeqhENMVS6WjVA" keySequence="CTRL+G" command="_8q1nXm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WgW2xEeqhENMVS6WjVA" keySequence="ALT+CTRL+H" command="_8q1lDm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wgm2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+H" command="_8q1mM22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wg22xEeqhENMVS6WjVA" keySequence="F4" command="_8q1nP22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WhG2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+R" command="_8q1mw22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WhW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+T" command="_8q1lKW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Whm2xEeqhENMVS6WjVA" keySequence="ALT+CTRL+I" command="_8q1mFG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wh22xEeqhENMVS6WjVA" keySequence="F3" command="_8q2LIm2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0WiG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_8q0W8m2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WiW2xEeqhENMVS6WjVA" keySequence="CTRL+G" command="_8q2LAW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wim2xEeqhENMVS6WjVA" keySequence="HOME" command="_8q1lGG2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0Wi22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_8q0W8G2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WjG2xEeqhENMVS6WjVA" keySequence="CTRL+G" command="_8q1mrW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WjW2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+." command="_8q1mq22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wjm2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+," command="_8q1m8m2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0Wj22xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_8q0W6G2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WkG2xEeqhENMVS6WjVA" keySequence="CTRL+SHIFT+F" command="_8q1nJ22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WkW2xEeqhENMVS6WjVA" keySequence="SHIFT+F2" command="_8q1mGG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wkm2xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+R" command="_8q1kiG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wk22xEeqhENMVS6WjVA" keySequence="ALT+SHIFT+O" command="_8q1kXG2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WlG2xEeqhENMVS6WjVA" keySequence="F3" command="_8q1kYW2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0WlW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_8q0W9G2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0Wlm2xEeqhENMVS6WjVA" keySequence="SHIFT+F5" command="_8q1lim2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wl22xEeqhENMVS6WjVA" keySequence="SHIFT+F7" command="_8q1mOm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WmG2xEeqhENMVS6WjVA" keySequence="CTRL+F5" command="_8q1nMW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WmW2xEeqhENMVS6WjVA" keySequence="SHIFT+F8" command="_8q1mqW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wmm2xEeqhENMVS6WjVA" keySequence="SHIFT+F6" command="_8q1khG2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0Wm22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_8q0W7m2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WnG2xEeqhENMVS6WjVA" keySequence="CTRL+N" command="_8q1mUm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WnW2xEeqhENMVS6WjVA" keySequence="CTRL+W" command="_8q1mJ22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wnm2xEeqhENMVS6WjVA" keySequence="ALT+CTRL+N" command="_8q1nS22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wn22xEeqhENMVS6WjVA" keySequence="CTRL+T" command="_8q1lGW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WoG2xEeqhENMVS6WjVA" keySequence="ALT+CTRL+M" command="_8q1lk22xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0WoW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_8q0W422xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0Wom2xEeqhENMVS6WjVA" keySequence="ALT+CR" command="_8q1l322xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0Wo22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.debugging" bindingContext="_8q0W722xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WpG2xEeqhENMVS6WjVA" keySequence="CTRL+F5" command="_8q1nN22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WpW2xEeqhENMVS6WjVA" keySequence="CTRL+F2" command="_8q1mv22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wpm2xEeqhENMVS6WjVA" keySequence="F8" command="_8q1mIW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wp22xEeqhENMVS6WjVA" keySequence="F7" command="_8q1nWm2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WqG2xEeqhENMVS6WjVA" keySequence="CTRL+R" command="_8q1l7m2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WqW2xEeqhENMVS6WjVA" keySequence="F6" command="_8q1liW2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wqm2xEeqhENMVS6WjVA" keySequence="F5" command="_8q1kkG2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0Wq22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.console" bindingContext="_8q0W622xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0WrG2xEeqhENMVS6WjVA" keySequence="CTRL+Z" command="_8q1nUW2xEeqhENMVS6WjVA">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_8q0WrW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_8q0W522xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0Wrm2xEeqhENMVS6WjVA" keySequence="CTRL+/" command="_8q1k0W2xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0Wr22xEeqhENMVS6WjVA" keySequence="F3" command="_8q1mO22xEeqhENMVS6WjVA"/>
    <bindings xmi:id="_8q0WsG2xEeqhENMVS6WjVA" keySequence="CTRL+\" command="_8q1kvW2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0WsW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_8q0W5W2xEeqhENMVS6WjVA">
    <bindings xmi:id="_8q0Wsm2xEeqhENMVS6WjVA" keySequence="CTRL+1" command="_8q1nYW2xEeqhENMVS6WjVA"/>
  </bindingTables>
  <bindingTables xmi:id="_8q0Ws22xEeqhENMVS6WjVA" bindingContext="_8q0W_22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WtG2xEeqhENMVS6WjVA" bindingContext="_8q0XAG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WtW2xEeqhENMVS6WjVA" bindingContext="_8q0XAW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wtm2xEeqhENMVS6WjVA" bindingContext="_8q0XAm2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wt22xEeqhENMVS6WjVA" bindingContext="_8q0XA22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WuG2xEeqhENMVS6WjVA" bindingContext="_8q0XBG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WuW2xEeqhENMVS6WjVA" bindingContext="_8q0XBW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wum2xEeqhENMVS6WjVA" bindingContext="_8q0XBm2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wu22xEeqhENMVS6WjVA" bindingContext="_8q0XB22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WvG2xEeqhENMVS6WjVA" bindingContext="_8q0XCG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WvW2xEeqhENMVS6WjVA" bindingContext="_8q0XCW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wvm2xEeqhENMVS6WjVA" bindingContext="_8q0XCm2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wv22xEeqhENMVS6WjVA" bindingContext="_8q0XC22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WwG2xEeqhENMVS6WjVA" bindingContext="_8q0XDG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WwW2xEeqhENMVS6WjVA" bindingContext="_8q0XDW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wwm2xEeqhENMVS6WjVA" bindingContext="_8q0XDm2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Ww22xEeqhENMVS6WjVA" bindingContext="_8q0XD22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WxG2xEeqhENMVS6WjVA" bindingContext="_8q0XEG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WxW2xEeqhENMVS6WjVA" bindingContext="_8q0XEW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wxm2xEeqhENMVS6WjVA" bindingContext="_8q0XEm2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wx22xEeqhENMVS6WjVA" bindingContext="_8q0XE22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WyG2xEeqhENMVS6WjVA" bindingContext="_8q0XFG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WyW2xEeqhENMVS6WjVA" bindingContext="_8q0XFW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wym2xEeqhENMVS6WjVA" bindingContext="_8q0XFm2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wy22xEeqhENMVS6WjVA" bindingContext="_8q0XF22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WzG2xEeqhENMVS6WjVA" bindingContext="_8q0XGG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0WzW2xEeqhENMVS6WjVA" bindingContext="_8q0XGW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wzm2xEeqhENMVS6WjVA" bindingContext="_8q0XGm2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0Wz22xEeqhENMVS6WjVA" bindingContext="_8q0XG22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W0G2xEeqhENMVS6WjVA" bindingContext="_8q0XHG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W0W2xEeqhENMVS6WjVA" bindingContext="_8q0XHW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W0m2xEeqhENMVS6WjVA" bindingContext="_8q0XHm2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W022xEeqhENMVS6WjVA" bindingContext="_8q0XH22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W1G2xEeqhENMVS6WjVA" bindingContext="_8q0XIG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W1W2xEeqhENMVS6WjVA" bindingContext="_8q0XIW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W1m2xEeqhENMVS6WjVA" bindingContext="_8q0XIm2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W122xEeqhENMVS6WjVA" bindingContext="_8q0XI22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W2G2xEeqhENMVS6WjVA" bindingContext="_8q0XJG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W2W2xEeqhENMVS6WjVA" bindingContext="_8q0XJW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W2m2xEeqhENMVS6WjVA" bindingContext="_8q0XJm2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W222xEeqhENMVS6WjVA" bindingContext="_8q0XJ22xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W3G2xEeqhENMVS6WjVA" bindingContext="_8q0XKG2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W3W2xEeqhENMVS6WjVA" bindingContext="_8q0XKW2xEeqhENMVS6WjVA"/>
  <bindingTables xmi:id="_8q0W3m2xEeqhENMVS6WjVA" bindingContext="_8q0XKm2xEeqhENMVS6WjVA"/>
  <rootContext xmi:id="_8q0W322xEeqhENMVS6WjVA" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_8q0W4G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_8q0W4W2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_8q0W4m2xEeqhENMVS6WjVA" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal widget context" description="Override ALT+x menu access keys"/>
      <children xmi:id="_8q0W422xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_8q0W5G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_8q0W5W2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_8q0W5m2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_8q0W522xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_8q0W6G2xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_8q0W6W2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
        <children xmi:id="_8q0W6m2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_8q0W622xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_8q0W7G2xEeqhENMVS6WjVA" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_8q0W7W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_8q0W7m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_8q0W722xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_8q0W8G2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_8q0W8W2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.debug.ui.debugging" name="Debugging using Target Communication Framework" description="Debugging using Target Communication Framework"/>
        <children xmi:id="_8q0W8m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_8q0W822xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
        <children xmi:id="_8q0W9G2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_8q0W9W2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_8q0W9m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_8q0W922xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
    </children>
    <children xmi:id="_8q0W-G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_8q0W-W2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_8q0W-m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_8q0W-22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" name="UML2 Sequence Diagram Viewer"/>
  <rootContext xmi:id="_8q0W_G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_8q0W_W2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_8q0W_m2xEeqhENMVS6WjVA" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal widget context" description="Override ALT+x menu access keys"/>
  <rootContext xmi:id="_8q0W_22xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_8q0XAG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_8q0XAW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_8q0XAm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_8q0XA22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_8q0XBG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_8q0XBW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_8q0XBm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_8q0XB22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_8q0XCG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_8q0XCW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_8q0XCm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_8q0XC22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_8q0XDG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_8q0XDW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_8q0XDm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_8q0XD22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_8q0XEG2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_8q0XEW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_8q0XEm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_8q0XE22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_8q0XFG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_8q0XFW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_8q0XFm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_8q0XF22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_8q0XGG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_8q0XGW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_8q0XGm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_8q0XG22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_8q0XHG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_8q0XHW2xEeqhENMVS6WjVA" elementId="org.eclipse.rse.core.search.searchActionSet" name="Auto::org.eclipse.rse.core.search.searchActionSet"/>
  <rootContext xmi:id="_8q0XHm2xEeqhENMVS6WjVA" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_8q0XH22xEeqhENMVS6WjVA" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_8q0XIG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_8q0XIW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_8q0XIm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_8q0XI22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_8q0XJG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_8q0XJW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_8q0XJm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_8q0XJ22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_8q0XKG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_8q0XKW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_8q0XKm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.tcf.debug.LaunchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::com.xilinx.sdk.tcf.debug.LaunchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_8q0XK22xEeqhENMVS6WjVA" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XLG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.logger.SdkLogView" label="SDK Log" iconURI="platform:/plugin/com.xilinx.sdk.loggers/icons/icon.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XLW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.oprofile.view" label="Oprofile View" iconURI="platform:/plugin/com.xilinx.sdk.oprofile/icons/gprof.gif" tooltip="" category="Oprofile" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Oprofile</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XLm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.performance.ui.views.PsPerfGraphsView" label="PS Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XL22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.performance.ui.views.PsPerfTableView" label="PS Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XMG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfTableView" label="APM Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XMW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfGraphsView" label="APM Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XMm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.performance.ui.views.PerfSessionManagerView" label="Performance Session Manager" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XM22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.performance.ui.views.MbPerfGraphsView" label="MicroBlaze Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XNG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.performance.ui.views.MbPerfTableView" label="MicroBlaze Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XNW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.performance.ui.views.TraceSessionManagerView" label="Trace Session Manager" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XNm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.performance.stm.freertos.trace.FreeRtosAnalysisView" label="FreeRTOS Analysis" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XN22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView" label="XSCT Console" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/xsdbconsole_icon.png" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XOG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView" label="Target Connections" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/target-mgmt-view.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XOW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView" label="QEMU Console" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/xsdbconsole_icon.png" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XOm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.terminal.sdkterminal" label="SDK Terminal" iconURI="platform:/plugin/com.xilinx.sdk.terminal/icons/console.gif" tooltip="" allowMultiple="true" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XO22xEeqhENMVS6WjVA" elementId="com.xilinx.sdsoc.trace.view.AxiEventsView" label="AXI State View" iconURI="platform:/plugin/com.xilinx.sdsoc.trace/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XPG2xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView" label="Documents" iconURI="platform:/plugin/ilg.gnuarmeclipse.managedbuild.packs/icons/pdficon_small.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XPW2xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.packs.ui.views.DevicesView" label="Devices" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/hardware_chip.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XPm2xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.packs.ui.views.BoardsView" label="Boards" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/board.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XP22xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.packs.ui.views.KeywordsView" label="Keywords" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/info_obj.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XQG2xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.packs.ui.views.PackagesView" label="Packs" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/packages.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XQW2xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.packs.ui.views.OutlineView" label="Outline" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/outline_co.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XQm2xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XQ22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XRG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XRW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XRm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XR22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XSG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XSW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Make Target" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XSm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XS22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XTG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XTW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XTm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XT22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XUG2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XUW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XUm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XU22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XVG2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XVW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XVm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XV22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XWG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XWW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XWm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XW22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XXG2xEeqhENMVS6WjVA" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XXW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Display" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XXm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XX22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XYG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XYW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XYm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XY22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XZG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XZW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XZm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XZ22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XaG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" tooltip="" allowMultiple="true" category="Charts" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XaW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.gprof.view" label="gprof" iconURI="platform:/plugin/org.eclipse.linuxtools.gprof/icons/toggle.gif" tooltip="Gprof view displays the profiling information contained in a gmon.out file" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xam2xEeqhENMVS6WjVA" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xa22xEeqhENMVS6WjVA" elementId="org.eclipse.rse.shells.ui.view.commandsView" label="Remote Shell" iconURI="platform:/plugin/org.eclipse.rse.shells.ui/icons/full/cview16/commands_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XbG2xEeqhENMVS6WjVA" elementId="org.eclipse.rse.terminals.ui.view.TerminalView" label="Terminals" iconURI="platform:/plugin/org.eclipse.rse.terminals.ui/icons/terminal_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XbW2xEeqhENMVS6WjVA" elementId="org.eclipse.rse.ui.view.systemView" label="Remote Systems" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xbm2xEeqhENMVS6WjVA" elementId="org.eclipse.rse.ui.view.teamView" label="Team" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/team_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xb22xEeqhENMVS6WjVA" elementId="org.eclipse.rse.ui.view.systemTableView" label="Remote System Details" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XcG2xEeqhENMVS6WjVA" elementId="org.eclipse.rse.ui.view.SystemSearchView" label="Remote Search" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/obj16/system_search.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XcW2xEeqhENMVS6WjVA" elementId="org.eclipse.rse.ui.view.scratchpad.SystemScratchpadViewPart" label="Remote Scratchpad" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/scratchpad_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xcm2xEeqhENMVS6WjVA" elementId="org.eclipse.rse.ui.view.monitorView" label="Remote Monitor" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xc22xEeqhENMVS6WjVA" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XdG2xEeqhENMVS6WjVA" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XdW2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.TraceView" label="TCF Trace" iconURI="platform:/plugin/org.eclipse.tcf.debug.ui/icons/tcf.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xdm2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.ProfilerView" label="TCF Profiler" iconURI="platform:/plugin/org.eclipse.tcf.debug.ui/icons/profiler.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xd22xEeqhENMVS6WjVA" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XeG2xEeqhENMVS6WjVA" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XeW2xEeqhENMVS6WjVA" elementId="org.eclipse.tm.terminal.view.TerminalView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view/icons/cview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xem2xEeqhENMVS6WjVA" elementId="org.eclipse.tracecompass.analysis.os.linux.views.controlflow" label="Control Flow" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/control_flow_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xe22xEeqhENMVS6WjVA" elementId="org.eclipse.tracecompass.analysis.os.linux.views.resources" label="Resources" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XfG2xEeqhENMVS6WjVA" elementId="org.eclipse.tracecompass.analysis.os.linux.views.cpuusage" label="CPU Usage" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XfW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.views.control" label="Control" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.control.ui/icons/eview16/control_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xfm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.lttng2.ust.memoryusage" label="UST Memory Usage" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xf22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.views.timegraph" label="XML Time Graph View" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XgG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.tmf.analysis.xml.ui.views.xyview" label="XML XY Chart View" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XgW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.pcap.ui.view.stream.list" label="Stream List" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.pcap.ui/icons/stream_list_view.gif" tooltip="" category="Network Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Network Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xgm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.timechart" label="Time Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/timechart_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xg22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.ssvisualizer" label="State System Explorer" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/events_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XhG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" label="Colors" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/colors_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XhW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" label="Filters" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/filters_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xhm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.tmfUml2SDSyncView" label="Sequence Diagram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/sequencediagram_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xh22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" label="Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XiG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" label="Histogram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XiW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.callstack" label="Call Stack" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/callstack_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xim2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.synchronization" label="Synchronization" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/synced.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xi22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XjG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XjW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xjm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xj22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XkG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XkW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xkm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xk22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XlG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0XlW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xlm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8q0Xl22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <snippets xsi:type="advanced:Perspective" xmi:id="_8q1kM22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_8q1kNG2xEeqhENMVS6WjVA" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif" tooltip="C/C++">
    <persistedState key="persp.hiddenItems" value="persp.hideMenuSC:org.eclipse.jdt.ui.refactoring.menu,persp.hideMenuSC:org.eclipse.jdt.ui.source.menu,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
    <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
    <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
    <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
    <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
    <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
    <tags>persp.viewSC:ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView</tags>
    <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
    <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
    <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
    <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
    <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
    <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
    <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
    <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
    <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
    <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
    <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
    <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
    <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
    <tags>persp.newWizSC:com.xilinx.sdk.appwiz.AppWizard</tags>
    <tags>persp.newWizSC:com.xilinx.sdk.profile.ui.wizards.ZpeProjectWizard</tags>
    <tags>persp.newWizSC:com.xilinx.sdk.sw.ui.NewBspWizard</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_8q1kNG2xEeqhENMVS6WjVA" selectedElement="_8q1kNW2xEeqhENMVS6WjVA" horizontal="true">
      <children xsi:type="basic:PartSashContainer" xmi:id="_8q1kNW2xEeqhENMVS6WjVA" containerData="2500" selectedElement="_8q1kNm2xEeqhENMVS6WjVA">
        <children xsi:type="basic:PartStack" xmi:id="_8q1kNm2xEeqhENMVS6WjVA" elementId="topLeft" containerData="7500" selectedElement="_8q1kN22xEeqhENMVS6WjVA">
          <tags>noFocus</tags>
          <children xsi:type="advanced:Placeholder" xmi:id="_8q1kN22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
          <children xsi:type="advanced:Placeholder" xmi:id="_8q1kOG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false"/>
          <children xsi:type="advanced:Placeholder" xmi:id="_8q1kOW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false"/>
          <children xsi:type="advanced:Placeholder" xmi:id="_8q1kOm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false"/>
        </children>
        <children xsi:type="basic:PartStack" xmi:id="_8q1kO22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementViewMStack" containerData="2500" selectedElement="_8q1kPG2xEeqhENMVS6WjVA">
          <children xsi:type="advanced:Placeholder" xmi:id="_8q1kPG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView"/>
        </children>
      </children>
      <children xsi:type="basic:PartSashContainer" xmi:id="_8q1kPW2xEeqhENMVS6WjVA" containerData="7500">
        <children xsi:type="basic:PartSashContainer" xmi:id="_8q1kPm2xEeqhENMVS6WjVA" containerData="7500" horizontal="true">
          <children xsi:type="advanced:Placeholder" xmi:id="_8q1kP22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.editorss" containerData="7500"/>
          <children xsi:type="basic:PartStack" xmi:id="_8q1kQG2xEeqhENMVS6WjVA" elementId="topRight" containerData="2500" selectedElement="_8q1kQW2xEeqhENMVS6WjVA">
            <children xsi:type="advanced:Placeholder" xmi:id="_8q1kQW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ContentOutline"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_8q1kQm2xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_8q1kQ22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.views.MakeView"/>
          </children>
        </children>
        <children xsi:type="basic:PartSashContainer" xmi:id="_8q1kRG2xEeqhENMVS6WjVA" containerData="2500" horizontal="true">
          <children xsi:type="basic:PartStack" xmi:id="_8q1kRW2xEeqhENMVS6WjVA" elementId="bottom" containerData="5000" selectedElement="_8q1kRm2xEeqhENMVS6WjVA">
            <children xsi:type="advanced:Placeholder" xmi:id="_8q1kRm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.ProblemView"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_8q1kR22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.TaskList"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_8q1kSG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.console.ConsoleView"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_8q1kSW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.PropertySheet"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_8q1kSm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.terminal.sdkterminal"/>
          </children>
          <children xsi:type="basic:PartStack" xmi:id="_8q1kS22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.logger.SdkLogViewMStack" containerData="5000" selectedElement="_8q1kTG2xEeqhENMVS6WjVA">
            <children xsi:type="advanced:Placeholder" xmi:id="_8q1kTG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.logger.SdkLogView"/>
          </children>
        </children>
      </children>
    </children>
  </snippets>
  <commands xmi:id="_8q1kTW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kTm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kT22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_8q2Lz22xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1kUG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_8q1kUW2xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.packs.commands.updateCommand" commandName="Refresh" category="_8q2Ls22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kUm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kU22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kVG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawarerefresh" commandName="Refresh" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kVW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kVm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kV22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kWG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kWW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kWm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kW22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.bootimage.commands.CreateBootImage" commandName="Create Boot Image" category="_8q2LwW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kXG2xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kXW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kXm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kX22xEeqhENMVS6WjVA" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_8q2Lym2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kYG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kYW2xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kYm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kY22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_8q2Lv22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kZG2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kZW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.commands.configureQEMU" commandName="Configure QEMU Settings" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kZm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.commands.viewXSDBConsole" commandName="XSCT Console" description="View XSCT Console" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kZ22xEeqhENMVS6WjVA" elementId="org.eclipse.tracecompass.tmf.ui.copy_to_clipboard" commandName="Copy to Clipboard" description="Copy to Clipboard" category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kaG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kaW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_8q2LzW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kam2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.internal.reflog.CheckoutCommand" commandName="Checkout" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ka22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kbG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kbW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.delete" commandName="Delete" description="Delete Target Node" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kbm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDDown" commandName="Scroll down" description="Scroll down the sequence diagram" category="_8q2LyG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kb22xEeqhENMVS6WjVA" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_8q2Ltm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kcG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kcW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_8q2Lum2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kcm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewRefresh" commandName="Refresh" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kc22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_8q2LtG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1kdG2xEeqhENMVS6WjVA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_8q1kdW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to" description="Go to a particular resource in the active view" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kdm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kd22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1keG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1keW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kem2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ke22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kfG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kfW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kfm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Comment/Uncomment" description="Comments/Uncomments the selected lines" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kf22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kgG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kgW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kgm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kg22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1khG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_8q2Lzm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1khW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1khm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kh22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kiG2xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kiW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kim2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ki22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kjG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences" commandName="Modify ATF Configuration"/>
  <commands xmi:id="_8q1kjW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kjm2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.debug.ui.commands.toggleFilterVariants" commandName="Filter Variants by Discriminant Value" category="_8q2L0W2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kj22xEeqhENMVS6WjVA" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_8q2Ly22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kkG2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kkW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kkm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kk22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1klG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnChannel" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1klW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1klm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kl22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kmG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kmW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kmm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1km22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1knG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1knW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1knm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kn22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1koG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1koW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.profile.ui.commands.configurefsbl" commandName="Configure FSBL Parameters" category="_8q2L0m2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kom2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ko22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kpG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kpW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kpm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kp22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.executeScript" commandName="Execute Command Script..." description="Execute Command Script" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kqG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kqW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method in its hierarchy" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kqm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnChannel" commandName="Enable Event..." description="Enable Event" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kq22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1krG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1krW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_8q2LtG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1krm2xEeqhENMVS6WjVA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_8q1kr22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.start" commandName="Start" description="Start Trace Session" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ksG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ksW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ksm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.openFile" commandName="Open File" description="Opens a file" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ks22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ktG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.utils.enablewebtalk" commandName="Enable Webtalk" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ktW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_8q2Lz22xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1ktm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_8q1kt22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kuG2xEeqhENMVS6WjVA" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kuW2xEeqhENMVS6WjVA" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_8q2Lqm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kum2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ku22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy SHA-1" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kvG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_8q2Lu22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kvW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.uncomment" commandName="Uncomment" description="Uncomment the selected # style comment lines" category="_8q2Lwm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kvm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kv22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kwG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kwW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kwm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kw22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kxG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.sw.commands.bspsettings" commandName="BSP Settings" category="_8q2LsG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kxW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kxm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kx22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.Revert" commandName="Revert Commit" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kyG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.StashDrop" commandName="Delete Stashed Commit..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kyW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kym2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ky22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kzG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kzW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kzm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableChannel" commandName="Disable Channel" description="Disable a Trace Channel" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1kz22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k0G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k0W2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.comment" commandName="Comment" description="Turn the selected lines into # style comments" category="_8q2Lwm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k0m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_8q2LtW2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1k022xEeqhENMVS6WjVA" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_8q1k1G2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k1W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k1m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Dynamic Help" description="Open the dynamic help" category="_8q2Lu22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k122xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k2G2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.CreateTag" commandName="Create Tag..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k2W2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k2m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k222xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k3G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k3W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k3m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k322xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k4G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k4W2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_8q2Lym2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k4m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k422xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_8q2Lum2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k5G2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k5W2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Directory" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k5m2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k522xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k6G2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k6W2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k6m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k622xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k7G2xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k7W2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k7m2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.commands.progflash" commandName="Program Flash Memory" description="Program Flash Memory" category="_8q2L0m2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k722xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k8G2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k8W2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_8q2LsW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k8m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_8q2Lsm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k822xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k9G2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k9W2xEeqhENMVS6WjVA" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k9m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k922xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k-G2xEeqhENMVS6WjVA" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k-W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k-m2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k-22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k_G2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k_W2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1k_m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_8q2Lu22xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1k_22xEeqhENMVS6WjVA" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_8q1lAG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lAW2xEeqhENMVS6WjVA" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lAm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lA22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lBG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lBW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lBm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_8q2LsW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lB22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lCG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableEvent" commandName="Disable Event" description="Disable Event" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lCW2xEeqhENMVS6WjVA" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lCm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lC22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lDG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lDW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lDm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lD22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.StashApply" commandName="Apply Stashed Changes" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lEG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lEW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lEm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open" category="_8q2Lz22xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1lE22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_8q1lFG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lFW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.quickAccess" commandName="Quick Access" description="Quickly access UI elements" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lFm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lF22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lGG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lGW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lGm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lG22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lHG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lHW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lHm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lH22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lIG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_8q2LuG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1lIW2xEeqhENMVS6WjVA" elementId="url" name="URL"/>
    <parameters xmi:id="_8q1lIm2xEeqhENMVS6WjVA" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_8q1lI22xEeqhENMVS6WjVA" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_8q1lJG2xEeqhENMVS6WjVA" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_8q1lJW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lJm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.xlcm.commands.acquire" commandName="Acquire a License Key..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lJ22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.libdoc.sdklibhelpint" commandName="Xilinx OS and Libraries Help" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lKG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lKW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lKm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lK22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lLG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lLW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_8q2LuG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1lLm2xEeqhENMVS6WjVA" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_8q1lL22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lMG2xEeqhENMVS6WjVA" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_8q2Ltm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lMW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lMm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.lockToolBar" commandName="Lock the Toolbars" description="Lock the Toolbars" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lM22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.hw.commands.changehwspec" commandName="Change Hardware Platform Specification" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lNG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.exportToText" commandName="Export to Text..." description="Export trace to text..." category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lNW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lNm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lN22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lOG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lOW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lOm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lO22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnSession" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lPG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="%RebaseInteractiveCurrentHandler.name" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lPW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeEnd" commandName="Show node end" description="Show the node end" category="_8q2LyG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lPm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lP22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.importtracepkg" commandName="Import Trace Package..." category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lQG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lQW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.importxml" commandName="Import XML analysis" description="Import an XML file containing analysis information" category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lQm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lQ22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lRG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lRW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lRm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lR22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lSG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lSW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disconnect" commandName="Disconnect" description="Disconnect to Target Node" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lSm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.managecustomparsers" commandName="Manage Custom Parsers..." description="Manage Custom Parsers" category="_8q2L0G2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lS22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lTG2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lTW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lTm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lT22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lUG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lUW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lUm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnEvent" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lU22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lVG2xEeqhENMVS6WjVA" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lVW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lVm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lV22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lWG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lWW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lWm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lW22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_8q2LtW2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1lXG2xEeqhENMVS6WjVA" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_8q1lXW2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.cdt.ui.add_watchpoint" commandName="Add Watchpoint (C/C++)" description="Allows to add a new watchpoint on an arbitrary symbol" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lXm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lX22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lYG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Make Target Build" description="Invoke a make target build for the selected container." category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lYW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_8q2Lym2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lYm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lY22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lZG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lZW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lZm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDUp" commandName="Scroll up" description="Scroll up the sequence diagram" category="_8q2LyG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lZ22xEeqhENMVS6WjVA" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1laG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1laW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lam2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1la22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lbG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lbW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lbm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lb22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lcG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lcW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lcm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lc22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ldG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnDomain" commandName="Enable Channel..." description="Enable a Trace Channel" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ldW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ldm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_8q2Lv22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ld22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1leG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1leW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lem2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1le22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lfG2xEeqhENMVS6WjVA" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_8q2LwG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lfW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lfm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lf22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lgG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lgW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lgm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lg22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lhG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lhW2xEeqhENMVS6WjVA" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_8q2Lqm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lhm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lh22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1liG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1liW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lim2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_8q2Lzm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1li22xEeqhENMVS6WjVA" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_8q2Ltm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ljG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_8q2LtW2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1ljW2xEeqhENMVS6WjVA" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_8q1ljm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lj22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lkG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lkW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.event" commandName="Enable Event..." description="Assign Event to Session and Channel and Enable Event " category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lkm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lk22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1llG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1llW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1llm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ll22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lmG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.delete_suppl_files" commandName="Delete Supplementary Files..." category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lmW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lmm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lm22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lnG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_8q2Lum2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lnW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_8q2Lu22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lnm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ln22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1loG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1loW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lom2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lo22xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lpG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewRenameBranch" commandName="Rename Branch..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lpW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lpm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_8q2LuG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1lp22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_8q1lqG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.commands.programfpga2" commandName="Configure FPGA with download.bit" category="_8q2LvW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lqW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_8q2Lu22xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1lqm2xEeqhENMVS6WjVA" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_8q1lq22xEeqhENMVS6WjVA" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_8q1lrG2xEeqhENMVS6WjVA" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_8q1lrW2xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.packs.commands.showPerspectiveCommand" commandName="Switch to C/C++ Packs Perspective" category="_8q2Ls22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lrm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lr22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lsG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lsW2xEeqhENMVS6WjVA" elementId="org.eclipse.rse.shells.ui.actions.LaunchShellCommand" commandName="Launch Shell" category="_8q2Lt22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lsm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ls22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ltG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ltW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_8q2Lum2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ltm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lt22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_8q2Lvm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1luG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1luW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lum2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.CherryPick" commandName="Cherry Pick" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lu22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lvG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lvW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lvm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lv22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_8q2Lz22xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1lwG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_8q1lwW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lwm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.newConnection" commandName="New Connection..." description="New Connection to Target Node" category="_8q2Lw22xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1lw22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.lttng2.control.ui.remoteServicesIdParameter" name="Remote Services ID"/>
    <parameters xmi:id="_8q1lxG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.lttng2.control.ui.connectionNameParameter" name="Connection Name"/>
  </commands>
  <commands xmi:id="_8q1lxW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lxm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_8q2LtG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1lx22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_8q1lyG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.new_folder" commandName="New Folder..." description="Create a new trace folder" category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lyW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1lym2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_8q2Lxm2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1ly22xEeqhENMVS6WjVA" elementId="title" name="Title"/>
    <parameters xmi:id="_8q1lzG2xEeqhENMVS6WjVA" elementId="message" name="Message"/>
    <parameters xmi:id="_8q1lzW2xEeqhENMVS6WjVA" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_8q1lzm2xEeqhENMVS6WjVA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_8q1lz22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l0G2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_8q2L022xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l0W2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l0m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l022xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l1G2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l1W2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.Revert" commandName="Revert Commit" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l1m2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDLeft" commandName="Scroll left" description="Scroll left the sequence diagram" category="_8q2LyG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l122xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l2G2xEeqhENMVS6WjVA" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_8q2Ltm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l2W2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l2m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Make Target" description="Create a new make build target for the selected container." category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l222xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l3G2xEeqhENMVS6WjVA" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l3W2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l3m2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l322xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l4G2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l4W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id To Clipboard" description="Copies the build id to the clipboard." category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l4m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l422xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l5G2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_8q2Lv22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l5W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_8q2LuG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1l5m2xEeqhENMVS6WjVA" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_8q1l522xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l6G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l6W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l6m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_8q2LrG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1l622xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_8q1l7G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l7W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l7m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l722xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l8G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l8W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l8m2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l822xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l9G2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l9W2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l9m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l922xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l-G2xEeqhENMVS6WjVA" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l-W2xEeqhENMVS6WjVA" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_8q2LwG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l-m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l-22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l_G2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.xlcm.commands.manage" commandName="Manage License..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l_W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l_m2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1l_22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mAG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mAW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mAm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mA22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_8q2L022xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mBG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.clear_offset" commandName="Clear Time Offset" description="Clear time offset" category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mBW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mBm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mB22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mCG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor " description="Toggles linking of a view's selection with the active editor's selection" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mCW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mCm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mC22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mDG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mDW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mDm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mD22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mEG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mEW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.CreateBranch" commandName="Create Branch" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mEm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_8q2Lum2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mE22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mFG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mFW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mFm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.sw.commands.scanrepo" commandName="Scan Repositories" category="_8q2LsG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mF22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mGG2xEeqhENMVS6WjVA" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mGW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mGm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mG22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_8q2LsW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mHG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mHW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mHm2xEeqhENMVS6WjVA" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_8q2Lu22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mH22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mIG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_8q2L1G2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mIW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mIm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mI22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mJG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mJW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mJm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mJ22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mKG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mKW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDRight" commandName="Scroll right" description="Scroll right the sequence diagram" category="_8q2LyG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mKm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mK22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mLG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mLW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mLm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mL22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mMG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawareModeExec" commandName="Auto refresh on exec" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mMW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.analysis_help" commandName="Help" description="Help" category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mMm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mM22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mNG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mNW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mNm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mN22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mOG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.tools.command.linkgen" commandName="Generate Linker Script" description="Generates a Linker Script" category="_8q2L0m2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mOW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mOm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_8q2Lzm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mO22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_8q2Lwm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mPG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mPW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.refresh" commandName="Refresh" description="Refresh Node Configuration" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mPm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_8q2Lym2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mP22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.RenameBranch" commandName="Rename Branch..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mQG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnSession" commandName="Enable Channel..." description="Enable a Trace Channel" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mQW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_8q2LrG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1mQm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_8q1mQ22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mRG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mRW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mRm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mR22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mSG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mSW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mSm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mS22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mTG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mTW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mTm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mT22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.import" commandName="Import..." description="Import traces into project" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mUG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mUW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mUm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mU22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mVG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Field" description="Create getting and setting methods for the field and use only those to access the field" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mVW2xEeqhENMVS6WjVA" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mVm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.commands.programfpga" commandName="Program FPGA" category="_8q2L0m2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mV22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.exporttracepkg" commandName="Export Trace Package..." category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mWG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mWW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mWm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_8q2Lzm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mW22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mXG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.ui.commands.buildsettings" commandName="Change C/C++ Build Settings" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mXW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mXm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_8q2Lq22xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1mX22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_8q1mYG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_8q1mYW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_8q1mYm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_8q2Lum2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mY22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mZG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mZW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_8q2LtG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1mZm2xEeqhENMVS6WjVA" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_8q1mZ22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1maG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1maW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.createSession" commandName="Create Session..." description="Create a Trace Session" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mam2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ma22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mbG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mbW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mbm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mb22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mcG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_8q2Lu22xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1mcW2xEeqhENMVS6WjVA" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_8q1mcm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mc22xEeqhENMVS6WjVA" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mdG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mdW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mdm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1md22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1meG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1meW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.synchronize_traces" commandName="Synchronize Traces" description="Synchronize 2 or more traces" category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mem2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1me22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mfG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_8q2Lv22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mfW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.CherryPick" commandName="Cherry Pick" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mfm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mf22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.profile.ui.commands.configureApm" commandName="Configure Performance Analysis" category="_8q2L0m2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mgG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mgW2xEeqhENMVS6WjVA" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mgm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mg22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mhG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.select_traces" commandName="Select Traces..." description="Select Traces" category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mhW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mhm2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.cdt.debug.ui.command.breakpointCategoryProperties" commandName="Breakpoint Scope Properties" description="Edits the breakpoint scope settings of the given group of breakpoints. " category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mh22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_8q2Lu22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1miG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1miW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mim2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mi22xEeqhENMVS6WjVA" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_8q2Lu22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mjG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.commands.viewQEMUConsole" commandName="QEMU Console" description="View QEMU Console" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mjW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mjm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mj22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.destroySession" commandName="Destroy Session..." description="Destroy a Trace Session" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mkG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mkW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mkm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mk22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mlG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mlW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mlm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ml22xEeqhENMVS6WjVA" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_8q2LwG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mmG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mmW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mmm2xEeqhENMVS6WjVA" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_8q2Ltm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mm22xEeqhENMVS6WjVA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_8q2Ly22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mnG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mnW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mnm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.ShowInHistory" commandName="Show in History" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mn22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1moG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnDomain" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1moW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mom2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mo22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_8q2Lum2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mpG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mpW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.tools.command.dumpmem" commandName="Dump/Restore Data File" description="Dump/Restore Data File" category="_8q2L0m2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mpm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mp22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mqG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mqW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_8q2Lzm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mqm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mq22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mrG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.calibrate" commandName="Calibrate" description="Quantify LTTng overhead" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mrW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mrm2xEeqhENMVS6WjVA" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mr22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1msG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawarestate" commandName="Enable Linux OS Awareness" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1msW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1msm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1ms22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.newEditor" commandName="New Editor" description="Open another editor on the active editor's input" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mtG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.GoToMessage" commandName="Go to associated message" description="Go to the associated message" category="_8q2LyG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mtW2xEeqhENMVS6WjVA" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mtm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mt22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1muG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1muW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mum2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mu22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mvG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.CreateBranch" commandName="Create Branch..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mvW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.profile.ui.commands.exportascsv" commandName="Export As CSV" category="_8q2L0m2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mvm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mv22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mwG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mwW2xEeqhENMVS6WjVA" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_8q2Lu22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mwm2xEeqhENMVS6WjVA" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_8q2Ltm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mw22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_8q2Lum2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mxG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_8q2L1G2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mxW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mxm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mx22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1myG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1myW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_8q2Lym2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1mym2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_8q1my22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_8q1mzG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mzW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mzm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1mz22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m0G2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m0W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m0m2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m022xEeqhENMVS6WjVA" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_8q2Ltm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m1G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m1W2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Annotations" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m1m2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m122xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m2G2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.xbash.command" commandName="Launch Bash Shell" description="Launch Bash Shell" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m2W2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m2m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m222xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m3G2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m3W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m3m2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnDomain" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m322xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.debug.ui.commands.toggleQualifiedTypeNames" commandName="Show Qualified Type Names" category="_8q2L0W2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m4G2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m4W2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m4m2xEeqhENMVS6WjVA" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_8q2Ly22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m422xEeqhENMVS6WjVA" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_8q2LtW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m5G2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m5W2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m5m2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m522xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m6G2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m6W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m6m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m622xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m7G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_8q2Lu22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m7W2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m7m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m722xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.Checkout" commandName="Checkout" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m8G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Last Edit Location" description="Last edit location" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m8W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m8m2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m822xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression " category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m9G2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.configureFilters" commandName="Configure Contents..." description="Configure the filters to apply to the markers view" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m9W2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1m9m2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_8q2Lxm2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1m922xEeqhENMVS6WjVA" elementId="title" name="Title"/>
    <parameters xmi:id="_8q1m-G2xEeqhENMVS6WjVA" elementId="message" name="Message"/>
    <parameters xmi:id="_8q1m-W2xEeqhENMVS6WjVA" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_8q1m-m2xEeqhENMVS6WjVA" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_8q1m-22xEeqhENMVS6WjVA" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_8q1m_G2xEeqhENMVS6WjVA" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_8q1m_W2xEeqhENMVS6WjVA" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_8q1m_m2xEeqhENMVS6WjVA" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_8q1m_22xEeqhENMVS6WjVA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_8q1nAG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nAW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commit.CreateTag" commandName="Create Tag..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nAm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nA22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nBG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.sw.ui.commands.RegenBspSources" commandName="Re-generate BSP Sources" category="_8q2LsG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nBW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.sw.commands.repositories" commandName="Repositories" category="_8q2LsG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nBm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nB22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nCG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_8q2L1G2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nCW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawareModeSuspend" commandName="Auto refresh on suspend" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nCm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.connect" commandName="Connect" description="Connect to Target Node" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nC22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nDG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nDW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.new_experiment" commandName="New..." description="Create Tracing Experiment" category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nDm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nD22xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.debug.ui.commands.refresh" commandName="Refresh" category="_8q2L0W2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nEG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.sw.ui.commands.ChangeReferencedBSP" commandName="Change Referenced BSP" category="_8q2LsG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nEW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nEm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_8q2Lym2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nE22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nFG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Tag" commandName="Tag" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nFW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nFm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nF22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nGG2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nGW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_8q2Lz22xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1nGm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_8q1nG22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_8q1nHG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nHW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nHm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_8q2L1G2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nH22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_8q2LtG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1nIG2xEeqhENMVS6WjVA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_8q1nIW2xEeqhENMVS6WjVA" elementId="org.eclipse.rse.terminals.ui.actions.LaunchTerminalCommand" commandName="Launch Terminal " category="_8q2Lt22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nIm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawarecontext" commandName="Select Linux OS Aware File" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nI22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nJG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_8q2L022xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nJW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_8q2Lu22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nJm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.stop" commandName="Stop" description="Stop Trace Session" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nJ22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nKG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nKW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.select_trace_type" commandName="Select Trace Type..." description="Select a trace type" category="_8q2LyW2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1nKm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.bundle" name="Bundle" optional="false"/>
    <parameters xmi:id="_8q1nK22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
    <parameters xmi:id="_8q1nLG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.icon" name="Icon" optional="false"/>
  </commands>
  <commands xmi:id="_8q1nLW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nLm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nL22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nMG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nMW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nMm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nM22xEeqhENMVS6WjVA" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nNG2xEeqhENMVS6WjVA" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_8q2Lqm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nNW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_8q2LvG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nNm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nN22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nOG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nOW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nOm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nO22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nPG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.import" commandName="Import..." description="Import Traces to LTTng Project" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nPW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nPm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nP22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nQG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_8q2LsW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nQW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nQm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nQ22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nRG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nRW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nRm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Annotations" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nR22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nSG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nSW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nSm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_8q2LzW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nS22xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nTG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nTW2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nTm2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.command.offset_traces" commandName="Apply Time Offset..." description="Shift traces by a constant time offset" category="_8q2LyW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nT22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nUG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nUW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nUm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_8q2L1G2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nU22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeStart" commandName="Show node start " description="Show the node start" category="_8q2LyG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nVG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nVW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nVm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nV22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nWG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nWW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.snapshot" commandName="Record Snapshot" description="Record a snapshot" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nWm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nW22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_8q2LuG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q1nXG2xEeqhENMVS6WjVA" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_8q1nXW2xEeqhENMVS6WjVA" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_8q1nXm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nX22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEvent" commandName="Enable Event" description="Enable Event" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nYG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_8q2Lu22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nYW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nYm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nY22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_8q2LzW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q1nZG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.CheckoutCommand" commandName="Checkout" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LAG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LAW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LAm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LA22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LBG2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LBW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_8q2LtW2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q2LBm2xEeqhENMVS6WjVA" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_8q2LB22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LCG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LCW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LCm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LC22xEeqhENMVS6WjVA" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LDG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LDW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LDm2xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Checkout" category="_8q2Lx22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LD22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_8q2LxG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LEG2xEeqhENMVS6WjVA" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LEW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LEm2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_8q2LtG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LE22xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_8q2LuW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LFG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_8q2LzG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LFW2xEeqhENMVS6WjVA" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_8q2Ltm2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LFm2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LF22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LGG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LGW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LGm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LG22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_8q2LrG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LHG2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_8q2LxW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LHW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_8q2Lr22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LHm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_8q2LuG2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LH22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_8q2LtG2xEeqhENMVS6WjVA">
    <parameters xmi:id="_8q2LIG2xEeqhENMVS6WjVA" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_8q2LIW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannel" commandName="Enable Channel" description="Enable a Trace Channel" category="_8q2Lw22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LIm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_8q2LrW2xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LI22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LJG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LJW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LJm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LJ22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LKG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LKW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LKm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LK22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LLG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LLW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.manageConfigsAction2" commandName="Manage..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LLm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigMenuAction" commandName="Set Active" description="Change active build configuration for project(s)" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LL22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.wsselection" commandName="Manage Working Sets..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LMG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LMW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LMm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LM22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LNG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LNW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LNm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LN22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LOG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LOW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LOm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LO22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LPG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LPW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LPm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LP22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LQG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LQW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LQm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.rse.core.search.searchActionSet/org.eclipse.rse.core.search.searchAction" commandName="Remote..." description="Opens Remote Search dialog page for text and file searching on remote systems" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LQ22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LRG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LRW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LRm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LR22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LSG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LSW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LSm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LS22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LTG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LTW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LTm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LT22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LUG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LUW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LUm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LU22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LVG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LVW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LVm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LV22xEeqhENMVS6WjVA" elementId="AUTOGEN:::com.xilinx.sdk.targetmanager.ui.viewContribution.restartXsdb/com.xilinx.sdk.targetmanager.ui.xsdb.ClearXSDBConsoleActionDelegate" commandName="Clear XSCT Console" description="Clear XSCT Console" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LWG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::com.xilinx.sdk.targetmanager.ui.viewContribution.clearQEMUConsole/com.xilinx.sdk.targetmanager.ui.qemu.ClearQEMUConsoleActionDelegate" commandName="Clear QEMU Console" description="Clear QEMU Console" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LWW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::com.xilinx.sdk.ui.filter/com.xilinx.sdk.ui.filter.action" commandName="Enable Filtering" description="Enable filtering functionality for project explorer view." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LWm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LW22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LXG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LXW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LXm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LX22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LYG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LYW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.RemoveAllGlobalsActionDelegate" commandName="Remove All Global Variables" description="Remove All Global Variables" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LYm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.RemoveGlobalsActionDelegate" commandName="Remove Global Variables" description="Remove Selected Global Variables" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LY22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.AddGlobalsActionDelegate" commandName="Add Global Variables..." description="Add Global Variables" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LZG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LZW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LZm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LZ22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LaG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LaW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lam2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2La22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LbG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LbW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lbm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lb22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LcG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LcW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lcm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lc22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LdG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LdW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Ldm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Ld22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LeG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LeW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lem2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Le22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LfG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LfW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lfm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lf22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LgG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LgW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lgm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lg22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LhG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LhW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lhm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lh22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LiG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LiW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lim2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Li22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LjG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LjW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Ljm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lj22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LkG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LkW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lkm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Lk22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LlG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LlW2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Llm2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2Ll22xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <commands xmi:id="_8q2LmG2xEeqhENMVS6WjVA" elementId="AUTOGEN:::org.eclipse.rse.ui.view.systemView.toolbar/org.eclipse.rse.ui.view.systemView.toolbar.linkWithSystemView" commandName="Link with Editor" category="_8q2Lz22xEeqhENMVS6WjVA"/>
  <addons xmi:id="_8q2LmW2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_8q2Lmm2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_8q2Lm22xEeqhENMVS6WjVA" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_8q2LnG2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_8q2LnW2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_8q2Lnm2xEeqhENMVS6WjVA" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_8q2Ln22xEeqhENMVS6WjVA" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_8q2LoG2xEeqhENMVS6WjVA" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_8q2LoW2xEeqhENMVS6WjVA" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon">
    <persistedState key="org.eclipse.cdt.ui.CPerspective" value=""/>
  </addons>
  <addons xmi:id="_8q2Lom2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_8q2Lo22xEeqhENMVS6WjVA" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_8q2LpG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <addons xmi:id="_8q2LpW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_8q2Lpm2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_8q2Lp22xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_8q2LqG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_8q2LqW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_i8zE8W5gEeqvVKbTJuC_gQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_mItGsXAVEeq57eTh4XJtOg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_eXegoYROEeqiufKhdLKJ0Q" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_y-dN8YYKEeq8loghZnKnYg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_8Fu50ZNjEequWrDyeI1hBw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="__uxI4ZQaEeqSJ5celE-NEg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <categories xmi:id="_8q2Lqm2xEeqhENMVS6WjVA" elementId="org.eclipse.team.ui.category.team" name="Team" description="Actions that apply when working with a Team"/>
  <categories xmi:id="_8q2Lq22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_8q2LrG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_8q2LrW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_8q2Lrm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_8q2Lr22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_8q2LsG2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.sw.commands.category" name="Sw Commands Category"/>
  <categories xmi:id="_8q2LsW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_8q2Lsm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_8q2Ls22xEeqhENMVS6WjVA" elementId="ilg.gnuarmeclipse.packs.commands.category" name="C/C++ Packages Category"/>
  <categories xmi:id="_8q2LtG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_8q2LtW2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_8q2Ltm2xEeqhENMVS6WjVA" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_8q2Lt22xEeqhENMVS6WjVA" elementId="org.eclipse.rse.ui.commands.category" name="Remote Systems"/>
  <categories xmi:id="_8q2LuG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_8q2LuW2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_8q2Lum2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_8q2Lu22xEeqhENMVS6WjVA" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_8q2LvG2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_8q2LvW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.hw.commands.hwcategory" name="Hardware Design"/>
  <categories xmi:id="_8q2Lvm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_8q2Lv22xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_8q2LwG2xEeqhENMVS6WjVA" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_8q2LwW2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.bootimage.category" name="Bootgen Category"/>
  <categories xmi:id="_8q2Lwm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_8q2Lw22xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.category" name="LTTng Trace Control Commands" description="LTTng Trace Control Commands"/>
  <categories xmi:id="_8q2LxG2xEeqhENMVS6WjVA" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_8q2LxW2xEeqhENMVS6WjVA" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_8q2Lxm2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_8q2Lx22xEeqhENMVS6WjVA" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_8q2LyG2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.category" name="UML2 Sequence Diagram Viewer Commands" description="UML2 Sequence Diagram Viewer Commands"/>
  <categories xmi:id="_8q2LyW2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.commands.category" name="Tracing" description="Tracing Commands"/>
  <categories xmi:id="_8q2Lym2xEeqhENMVS6WjVA" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_8q2Ly22xEeqhENMVS6WjVA" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_8q2LzG2xEeqhENMVS6WjVA" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_8q2LzW2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_8q2Lzm2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_8q2Lz22xEeqhENMVS6WjVA" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_8q2L0G2xEeqhENMVS6WjVA" elementId="org.eclipse.linuxtools.tmf.ui.commands.parser.category" name="Parser Commands" description="Parser Commands"/>
  <categories xmi:id="_8q2L0W2xEeqhENMVS6WjVA" elementId="org.eclipse.tcf.debug.ui.commands" name="TCF Debugger" description="TCF Debugger Commands"/>
  <categories xmi:id="_8q2L0m2xEeqhENMVS6WjVA" elementId="com.xilinx.sdk.tools.command.category" name="SDK Tools"/>
  <categories xmi:id="_8q2L022xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_8q2L1G2xEeqhENMVS6WjVA" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
