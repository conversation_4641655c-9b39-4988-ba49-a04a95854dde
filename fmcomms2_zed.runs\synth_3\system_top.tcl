# 
# Synthesis run script generated by Vivado
# 

set_msg_config -id {HDL 9-1061} -limit 100000
set_msg_config -id {HDL 9-1654} -limit 100000
set_msg_config  -ruleid {271}  -id {BD 41-1348}  -new_severity {INFO} 
set_msg_config  -ruleid {272}  -id {BD 41-1343}  -new_severity {INFO} 
set_msg_config  -ruleid {273}  -id {BD 41-1306}  -new_severity {INFO} 
set_msg_config  -ruleid {274}  -id {IP_Flow 19-1687}  -new_severity {INFO} 
set_msg_config  -ruleid {275}  -id {filemgmt 20-1763}  -new_severity {INFO} 
set_msg_config  -ruleid {276}  -id {BD 41-1276}  -severity {CRITICAL WARNING}  -new_severity {ERROR} 
create_project -in_memory -part xc7z035ffg676-2

set_param project.singleFileAddWarning.threshold 0
set_param project.compositeFile.enableAutoGeneration 0
set_param synth.vivado.isSynthRun true
set_msg_config -source 4 -id {IP_Flow 19-2162} -severity warning -new_severity info
set_property webtalk.parent_dir D:/FPGA/xiaoE/fmcomms2_zed.cache/wt [current_project]
set_property parent.project_path D:/FPGA/xiaoE/fmcomms2_zed.xpr [current_project]
set_property XPM_LIBRARIES XPM_CDC [current_project]
set_property default_lib xil_defaultlib [current_project]
set_property target_language Verilog [current_project]
set_property ip_repo_paths d:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/library [current_project]
set_property ip_output_repo d:/FPGA/xiaoE/fmcomms2_zed.cache/ip [current_project]
set_property ip_cache_permissions {read write} [current_project]
add_files -quiet d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0.dcp
set_property used_in_implementation false [get_files d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/ip/vio_0/vio_0.dcp]
read_verilog -library xil_defaultlib {
  D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/library/xilinx/common/ad_iobuf.v
  D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl/system_wrapper.v
  D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_top.v
}
add_files D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/system.bd
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_ps7_0/system_sys_ps7_0.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0_board.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_sys_rstgen_0/system_sys_rstgen_0_ooc.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_cntrl_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/ad_rst_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_xfer_status_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/xilinx/common/up_clock_mon_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/axi_ad9361_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_0/system_axi_ad9361_0_pps_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_tdd_sync_0/util_tdd_sync_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_0/util_clkdiv_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0_board.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_divclk_reset_0/system_util_ad9361_divclk_reset_0_ooc.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_fifo_0/util_wfifo_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_adc_pack_0/util_cpack_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_adc_dma_0/system_axi_ad9361_adc_dma_0_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_fifo_0/util_rfifo_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_util_ad9361_dac_upack_0/util_upack_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_axi_ad9361_dac_dma_0/system_axi_ad9361_dac_dma_0_constr.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_xbar_0/system_xbar_0_ooc.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_auto_pc_0/system_auto_pc_0_ooc.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_auto_pc_1/system_auto_pc_1_ooc.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_auto_pc_2/system_auto_pc_2_ooc.xdc]
set_property used_in_implementation false [get_files -all d:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/ip/system_auto_pc_3/system_auto_pc_3_ooc.xdc]
set_property used_in_implementation false [get_files -all D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/system_ooc.xdc]
set_property is_locked true [get_files D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/bd/system/system.bd]

foreach dcp [get_files -quiet -all *.dcp] {
  set_property used_in_implementation false $dcp
}
read_xdc D:/FPGA/xiaoE/fmcomms2_zed.srcs/constrs_1/new/cdc.xdc
set_property used_in_implementation false [get_files D:/FPGA/xiaoE/fmcomms2_zed.srcs/constrs_1/new/cdc.xdc]

read_xdc D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc
set_property used_in_implementation false [get_files D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/fmcomms2/zed/system_constr.xdc]

read_xdc D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc
set_property used_in_implementation false [get_files D:/FPGA/xiaoE/fmcomms2_zed.srcs/sources_1/imports/hdl-hdl_2017_r1/projects/common/zed/zed_system_constr.xdc]

read_xdc dont_touch.xdc
set_property used_in_implementation false [get_files dont_touch.xdc]

synth_design -top system_top -part xc7z035ffg676-2 -fanout_limit 400 -fsm_extraction one_hot -keep_equivalent_registers -resource_sharing off -no_lc -shreg_min_size 5


write_checkpoint -force -noxdef system_top.dcp

catch { report_utilization -file system_top_utilization_synth.rpt -pb system_top_utilization_synth.pb }
