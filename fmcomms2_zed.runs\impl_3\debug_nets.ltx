<?xml version="1.0" encoding="UTF-8"?>
<probeData version="2" minor="0">
  <probeset name="EDA_PROBESET" active="true">
    <probe type="vio_input" busType="net" source="netlist" spec="VIO_INPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="ACTIVITY_PERSISTENCE" value="SHORT"/>
        <Option Id="ACTIVITY_VALUE" value=""/>
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="INPUT_VALUE" value=""/>
        <Option Id="INPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="0"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="PL_UART_RX_IBUF"/>
      </nets>
    </probe>
    <probe type="vio_input" busType="net" source="netlist" spec="VIO_INPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="ACTIVITY_PERSISTENCE" value="SHORT"/>
        <Option Id="ACTIVITY_VALUE" value=""/>
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="INPUT_VALUE" value=""/>
        <Option Id="INPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="1"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="PL_UART1_RX_IBUF"/>
      </nets>
    </probe>
    <probe type="vio_input" busType="net" source="netlist" spec="VIO_INPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="ACTIVITY_PERSISTENCE" value="SHORT"/>
        <Option Id="ACTIVITY_VALUE" value=""/>
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="INPUT_VALUE" value=""/>
        <Option Id="INPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="2"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="PL_UART2_RX_IBUF"/>
      </nets>
    </probe>
    <probe type="vio_input" busType="net" source="netlist" spec="VIO_INPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="ACTIVITY_PERSISTENCE" value="SHORT"/>
        <Option Id="ACTIVITY_VALUE" value=""/>
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="INPUT_VALUE" value=""/>
        <Option Id="INPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="3"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="PL_UART4_RX_IBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="bus" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="HEX"/>
        <Option Id="PROBE_PORT" value="0"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="6"/>
      </probeOptions>
      <nets>
        <net name="test_OBUF[6]"/>
        <net name="test_OBUF[5]"/>
        <net name="test_OBUF[4]"/>
        <net name="test_OBUF[3]"/>
        <net name="test_OBUF[2]"/>
        <net name="test_OBUF[1]"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="1"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="PL_UART_TX_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="2"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="PL_UART1_TX_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="3"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="PL_UART2_TX_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="4"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="PL_UART4_TX_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="5"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="BK_CS1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="6"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="BK_MISO1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="7"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="BK_MOSI1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="8"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="BK_SCLK1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="9"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="BK_LOAD_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="10"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="BK_IRQ_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="11"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="BK_RST_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="12"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="BK_TR_SW_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="13"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="XD_R_B_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="14"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="XD_T_P_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="15"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="XD_XS_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="16"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="XD_HX_R_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="17"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="XD_T_R_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="18"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="XD_T_P1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="19"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="PL_CTRL_A_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="20"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="PL_CTRL_B_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="21"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="IO1_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="22"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="IO2_OBUF"/>
      </nets>
    </probe>
    <probe type="vio_output" busType="net" source="netlist" spec="VIO_OUTPUT_V2_RT">
      <probeOptions Id="DebugProbeParams">
        <Option Id="BSCAN_SWITCH_INDEX" value="0"/>
        <Option Id="CORE_LOCATION" value="1:0"/>
        <Option Id="HW_VIO" value="test_vio_0"/>
        <Option Id="OUTPUT_VALUE" value=""/>
        <Option Id="OUTPUT_VALUE_RADIX" value="BINARY"/>
        <Option Id="PROBE_PORT" value="23"/>
        <Option Id="PROBE_PORT_BITS" value="0"/>
        <Option Id="PROBE_PORT_BIT_COUNT" value="1"/>
      </probeOptions>
      <nets>
        <net name="TSELF_OBUF"/>
      </nets>
    </probe>
  </probeset>
</probeData>
