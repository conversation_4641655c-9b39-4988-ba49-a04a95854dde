<?xml version="1.0" encoding="UTF-8"?>
<Root MajorVersion="0" MinorVersion="36">
  <CompositeFile CompositeFileTopName="system" CanBeSetAsTop="true" CanDisplayChildGraph="true">
    <Description>Composite Fileset</Description>
    <Generation Name="SYNTHESIS" State="RESET" Timestamp="1753504633"/>
    <Generation Name="IMPLEMENTATION" State="RESET" Timestamp="1753504633"/>
    <Generation Name="SIMULATION" State="RESET" Timestamp="1753504633"/>
    <Generation Name="HW_HANDOFF" State="RESET" Timestamp="1753504633"/>
    <FileCollection Name="SOURCES" Type="SOURCES">
      <File Name="ip\system_sys_ps7_0\system_sys_ps7_0.xci" Type="IP">
        <Instance HierarchyPath="sys_ps7"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_sys_concat_intc_0\system_sys_concat_intc_0.xci" Type="IP">
        <Instance HierarchyPath="sys_concat_intc"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_sys_rstgen_0\system_sys_rstgen_0.xci" Type="IP">
        <Instance HierarchyPath="sys_rstgen"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_axi_cpu_interconnect_0\system_axi_cpu_interconnect_0.xci" Type="IP">
        <Instance HierarchyPath="axi_cpu_interconnect"/>
        <Properties IsEditable="false" IsVisible="false" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_axi_hp0_interconnect_0\system_axi_hp0_interconnect_0.xci" Type="IP">
        <Instance HierarchyPath="axi_hp0_interconnect"/>
        <Properties IsEditable="false" IsVisible="false" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_axi_ad9361_0\system_axi_ad9361_0.xci" Type="IP">
        <Instance HierarchyPath="axi_ad9361"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_util_ad9361_tdd_sync_0\system_util_ad9361_tdd_sync_0.xci" Type="IP">
        <Instance HierarchyPath="util_ad9361_tdd_sync"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_util_ad9361_divclk_sel_concat_0\system_util_ad9361_divclk_sel_concat_0.xci" Type="IP">
        <Instance HierarchyPath="util_ad9361_divclk_sel_concat"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_util_ad9361_divclk_sel_0\system_util_ad9361_divclk_sel_0.xci" Type="IP">
        <Instance HierarchyPath="util_ad9361_divclk_sel"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_util_ad9361_divclk_0\system_util_ad9361_divclk_0.xci" Type="IP">
        <Instance HierarchyPath="util_ad9361_divclk"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_util_ad9361_divclk_reset_0\system_util_ad9361_divclk_reset_0.xci" Type="IP">
        <Instance HierarchyPath="util_ad9361_divclk_reset"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_util_ad9361_adc_fifo_0\system_util_ad9361_adc_fifo_0.xci" Type="IP">
        <Instance HierarchyPath="util_ad9361_adc_fifo"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_util_ad9361_adc_pack_0\system_util_ad9361_adc_pack_0.xci" Type="IP">
        <Instance HierarchyPath="util_ad9361_adc_pack"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_axi_ad9361_adc_dma_0\system_axi_ad9361_adc_dma_0.xci" Type="IP">
        <Instance HierarchyPath="axi_ad9361_adc_dma"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_axi_ad9361_dac_fifo_0\system_axi_ad9361_dac_fifo_0.xci" Type="IP">
        <Instance HierarchyPath="axi_ad9361_dac_fifo"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_util_ad9361_dac_upack_0\system_util_ad9361_dac_upack_0.xci" Type="IP">
        <Instance HierarchyPath="util_ad9361_dac_upack"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_axi_ad9361_dac_dma_0\system_axi_ad9361_dac_dma_0.xci" Type="IP">
        <Instance HierarchyPath="axi_ad9361_dac_dma"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_axi_hp1_interconnect_0\system_axi_hp1_interconnect_0.xci" Type="IP">
        <Instance HierarchyPath="axi_hp1_interconnect"/>
        <Properties IsEditable="false" IsVisible="false" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_axi_hp2_interconnect_0\system_axi_hp2_interconnect_0.xci" Type="IP">
        <Instance HierarchyPath="axi_hp2_interconnect"/>
        <Properties IsEditable="false" IsVisible="false" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
      <File Name="ip\system_xbar_0\system_xbar_0.xci" Type="IP">
        <Instance HierarchyPath="axi_cpu_interconnect/xbar"/>
        <Properties IsEditable="false" IsVisible="true" Timestamp="0" IsTrackable="true" IsStatusTracked="true"/>
        <Library Name="xil_defaultlib"/>
        <UsedIn Val="SYNTHESIS"/>
        <UsedIn Val="IMPLEMENTATION"/>
        <UsedIn Val="SIMULATION"/>
      </File>
    </FileCollection>
  </CompositeFile>
</Root>
