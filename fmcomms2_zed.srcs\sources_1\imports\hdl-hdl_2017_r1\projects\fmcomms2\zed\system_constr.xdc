set_property BITSTREAM.GENERAL.COMPRESS TRUE [current_design]
# constraints
# ad9361  ad1
set_property -dict {PACKAGE_PIN H13 IOSTANDARD LVDS DIFF_TERM 1} [get_ports rx_frame_in_p]
set_property -dict {PACKAGE_PIN H12 IOSTANDARD LVDS DIFF_TERM 1} [get_ports rx_frame_in_n]
set_property -dict {PACKAGE_PIN G14 IOSTANDARD LVDS DIFF_TERM 1} [get_ports rx_clk_in_p]
set_property -dict {PACKAGE_PIN F14 IOSTANDARD LVDS DIFF_TERM 1} [get_ports rx_clk_in_n]
set_property -dict {PACKAGE_PIN F12 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[0]}]
set_property -dict {PACKAGE_PIN E12 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[0]}]
set_property -dict {PACKAGE_PIN G10 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[1]}]
set_property -dict {PACKAGE_PIN F10 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[1]}]
set_property -dict {PACKAGE_PIN G12 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[2]}]
set_property -dict {PACKAGE_PIN G11 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[2]}]
set_property -dict {PACKAGE_PIN D15 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[3]}]
set_property -dict {PACKAGE_PIN D14 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[3]}]
set_property -dict {PACKAGE_PIN E10 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[4]}]
set_property -dict {PACKAGE_PIN D10 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[4]}]
set_property -dict {PACKAGE_PIN G16 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_p[5]}]
set_property -dict {PACKAGE_PIN G15 IOSTANDARD LVDS DIFF_TERM 1} [get_ports {rx_data_in_n[5]}]

set_property -dict {PACKAGE_PIN K15 IOSTANDARD LVDS} [get_ports tx_frame_out_p]
set_property -dict {PACKAGE_PIN J15 IOSTANDARD LVDS} [get_ports tx_frame_out_n]
set_property -dict {PACKAGE_PIN J14 IOSTANDARD LVDS} [get_ports tx_clk_out_p]
set_property -dict {PACKAGE_PIN H14 IOSTANDARD LVDS} [get_ports tx_clk_out_n]
set_property -dict {PACKAGE_PIN F15 IOSTANDARD LVDS} [get_ports {tx_data_out_p[0]}]
set_property -dict {PACKAGE_PIN E15 IOSTANDARD LVDS} [get_ports {tx_data_out_n[0]}]
set_property -dict {PACKAGE_PIN C17 IOSTANDARD LVDS} [get_ports {tx_data_out_p[1]}]
set_property -dict {PACKAGE_PIN C16 IOSTANDARD LVDS} [get_ports {tx_data_out_n[1]}]
set_property -dict {PACKAGE_PIN B16 IOSTANDARD LVDS} [get_ports {tx_data_out_p[2]}]
set_property -dict {PACKAGE_PIN B15 IOSTANDARD LVDS} [get_ports {tx_data_out_n[2]}]
set_property -dict {PACKAGE_PIN K13 IOSTANDARD LVDS} [get_ports {tx_data_out_p[3]}]
set_property -dict {PACKAGE_PIN J13 IOSTANDARD LVDS} [get_ports {tx_data_out_n[3]}]
set_property -dict {PACKAGE_PIN B17 IOSTANDARD LVDS} [get_ports {tx_data_out_p[4]}]
set_property -dict {PACKAGE_PIN A17 IOSTANDARD LVDS} [get_ports {tx_data_out_n[4]}]
set_property -dict {PACKAGE_PIN C11 IOSTANDARD LVDS} [get_ports {tx_data_out_p[5]}]
set_property -dict {PACKAGE_PIN B11 IOSTANDARD LVDS} [get_ports {tx_data_out_n[5]}]

set_property -dict {PACKAGE_PIN G7 IOSTANDARD LVCMOS18} [get_ports {gpio_status[0]}]
set_property -dict {PACKAGE_PIN B7 IOSTANDARD LVCMOS18} [get_ports {gpio_status[1]}]
set_property -dict {PACKAGE_PIN D6 IOSTANDARD LVCMOS18} [get_ports {gpio_status[2]}]
set_property -dict {PACKAGE_PIN A8 IOSTANDARD LVCMOS18} [get_ports {gpio_status[3]}]
set_property -dict {PACKAGE_PIN A7 IOSTANDARD LVCMOS18} [get_ports {gpio_status[4]}]
set_property -dict {PACKAGE_PIN B6 IOSTANDARD LVCMOS18} [get_ports {gpio_status[5]}]
set_property -dict {PACKAGE_PIN C6 IOSTANDARD LVCMOS18} [get_ports {gpio_status[6]}]
set_property -dict {PACKAGE_PIN A5 IOSTANDARD LVCMOS18} [get_ports {gpio_status[7]}]

set_property -dict {PACKAGE_PIN C9 IOSTANDARD LVCMOS18} [get_ports {gpio_ctl[0]}]
set_property -dict {PACKAGE_PIN A10 IOSTANDARD LVCMOS18} [get_ports {gpio_ctl[1]}]
set_property -dict {PACKAGE_PIN A9 IOSTANDARD LVCMOS18} [get_ports {gpio_ctl[2]}]
set_property -dict {PACKAGE_PIN B9 IOSTANDARD LVCMOS18} [get_ports {gpio_ctl[3]}]

set_property -dict {PACKAGE_PIN B4 IOSTANDARD LVCMOS18} [get_ports gpio_en_agc]
set_property -dict {PACKAGE_PIN B5 IOSTANDARD LVCMOS18} [get_ports txnrx]
set_property -dict {PACKAGE_PIN A4 IOSTANDARD LVCMOS18} [get_ports enable]
set_property -dict {PACKAGE_PIN B2 IOSTANDARD LVCMOS18} [get_ports gpio_resetb]

set_property PACKAGE_PIN A2 [get_ports spi_csn]
set_property IOSTANDARD LVCMOS18 [get_ports spi_csn]
set_property PULLUP true [get_ports spi_csn]
set_property -dict {PACKAGE_PIN C3 IOSTANDARD LVCMOS18} [get_ports spi_clk]
set_property -dict {PACKAGE_PIN B1 IOSTANDARD LVCMOS18} [get_ports spi_mosi]
set_property -dict {PACKAGE_PIN C2 IOSTANDARD LVCMOS18} [get_ports spi_miso]
set_property -dict {PACKAGE_PIN C4 IOSTANDARD LVCMOS18} [get_ports gpio_sync]

set_property -dict {PACKAGE_PIN AB25 IOSTANDARD LVCMOS33} [get_ports {test[1]}]
#
