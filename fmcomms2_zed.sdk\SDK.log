13:06:32 INFO  : Registering command handlers for SDK TCF services
13:06:32 INFO  : Launching XSCT server: xsct.bat -interactive D:\hdl-hdl_2017_r1\hdl-hdl_2017_r1\projects\fmcomms2\zed\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
13:06:34 INFO  : XSCT server has started successfully.
13:06:35 INFO  : Successfully done setting XSCT server connection channel  
13:06:35 INFO  : Successfully done setting SDK workspace  
13:06:35 INFO  : Processing command line option -hwspec D:/hdl-hdl_2017_r1/hdl-hdl_2017_r1/projects/fmcomms2/zed/fmcomms2_zed.sdk/system_top.hdf.
17:36:53 INFO  : Launching XSCT server: xsct.bat -interactive D:\FPGA\z7ad9361_2017_one\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
17:36:55 INFO  : XSCT server has started successfully.
17:36:55 INFO  : Successfully done setting XSCT server connection channel  
17:36:59 INFO  : Successfully done setting SDK workspace  
17:37:05 INFO  : Registering command handlers for SDK TCF services
17:37:07 INFO  : Processing command line option -hwspec D:/FPGA/z7ad9361_2017_one/fmcomms2_zed.sdk/system_top.hdf.
16:55:10 INFO  : Launching XSCT server: xsct.bat -interactive E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
16:55:15 INFO  : XSCT server has started successfully.
16:55:18 INFO  : Successfully done setting XSCT server connection channel  
16:55:18 INFO  : Successfully done setting SDK workspace  
16:55:22 INFO  : Registering command handlers for SDK TCF services
16:55:34 INFO  : Processing command line option -hwspec E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top.hdf.
16:59:01 INFO  : Registering command handlers for SDK TCF services
16:59:02 INFO  : Launching XSCT server: xsct.bat -interactive E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
16:59:04 INFO  : XSCT server has started successfully.
16:59:04 INFO  : Successfully done setting XSCT server connection channel  
16:59:04 INFO  : Processing command line option -hwspec E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top.hdf.
16:59:04 INFO  : Successfully done setting SDK workspace  
17:02:47 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:02:47 INFO  : 'fpga -state' command is executed.
17:03:18 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:03:19 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
17:03:19 INFO  : 'jtag frequency' command is executed.
17:03:19 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
17:03:19 INFO  : Context for 'APU' is selected.
17:03:19 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
17:03:19 INFO  : Context for 'APU' is selected.
17:03:19 INFO  : 'stop' command is executed.
17:03:20 INFO  : 'ps7_init' command is executed.
17:03:20 INFO  : 'ps7_post_config' command is executed.
17:03:20 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:03:20 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
17:03:20 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:03:21 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf' is downloaded to processor 'ps7_cortexa9_0'.
17:03:21 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf
----------------End of Script----------------

17:03:21 INFO  : Memory regions updated for context APU
17:03:21 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:03:21 INFO  : 'con' command is executed.
17:03:21 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

17:03:21 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_fsbl.elf_on_local.tcl'
17:03:48 INFO  : Disconnected from the channel tcfchan#1.
17:04:05 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:04:05 INFO  : 'fpga -state' command is executed.
17:04:37 INFO  : Memory regions updated for context APU
17:05:33 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:42 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:12:04 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:12:04 INFO  : 'fpga -state' command is executed.
17:12:04 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:12:06 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
17:12:06 INFO  : 'jtag frequency' command is executed.
17:12:06 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
17:12:06 INFO  : Context for 'APU' is selected.
17:12:09 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
17:12:09 INFO  : Context for 'APU' is selected.
17:12:09 INFO  : 'stop' command is executed.
17:12:10 INFO  : 'ps7_init' command is executed.
17:12:10 INFO  : 'ps7_post_config' command is executed.
17:12:10 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:12:10 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
17:12:10 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:12:11 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf' is downloaded to processor 'ps7_cortexa9_0'.
17:12:11 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf
----------------End of Script----------------

17:12:11 INFO  : Memory regions updated for context APU
17:12:11 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:12:11 INFO  : 'con' command is executed.
17:12:11 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

17:12:11 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_fsbl.elf_on_local.tcl'
17:12:29 INFO  : Disconnected from the channel tcfchan#2.
17:13:00 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:13:00 INFO  : 'fpga -state' command is executed.
17:13:05 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:13:05 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
17:13:05 INFO  : 'jtag frequency' command is executed.
17:13:05 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
17:13:05 INFO  : Context for 'APU' is selected.
17:13:09 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
17:13:09 INFO  : Context for 'APU' is selected.
17:13:09 INFO  : 'stop' command is executed.
17:13:10 INFO  : 'ps7_init' command is executed.
17:13:10 INFO  : 'ps7_post_config' command is executed.
17:13:10 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:13:10 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
17:13:10 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:13:11 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf' is downloaded to processor 'ps7_cortexa9_0'.
17:13:11 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf
----------------End of Script----------------

17:13:11 INFO  : Memory regions updated for context APU
17:13:11 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:13:11 INFO  : 'con' command is executed.
17:13:11 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

17:13:11 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_fsbl.elf_on_local.tcl'
17:13:51 INFO  : Disconnected from the channel tcfchan#3.
17:15:47 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:15:47 INFO  : 'fpga -state' command is executed.
17:15:49 INFO  : Memory regions updated for context APU
17:15:57 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:15:57 INFO  : 'fpga -state' command is executed.
17:16:04 INFO  : Memory regions updated for context APU
17:16:08 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:16:08 INFO  : 'fpga -state' command is executed.
17:16:11 INFO  : Memory regions updated for context APU
17:16:16 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:16:16 INFO  : 'fpga -state' command is executed.
17:20:25 INFO  : Memory regions updated for context APU
17:22:51 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:22:51 INFO  : 'fpga -state' command is executed.
17:22:51 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:22:52 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
17:22:52 INFO  : 'jtag frequency' command is executed.
17:22:52 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
17:22:52 INFO  : Context for 'APU' is selected.
17:22:52 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
17:22:52 INFO  : Context for 'APU' is selected.
17:22:52 INFO  : 'stop' command is executed.
17:22:53 INFO  : 'ps7_init' command is executed.
17:22:53 INFO  : 'ps7_post_config' command is executed.
17:22:53 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:22:54 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
17:22:54 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:22:55 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf' is downloaded to processor 'ps7_cortexa9_0'.
17:22:55 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf
----------------End of Script----------------

17:22:55 INFO  : Memory regions updated for context APU
17:22:55 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:22:55 INFO  : 'con' command is executed.
17:22:55 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

17:22:55 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_fsbl.elf_on_local.tcl'
17:24:54 INFO  : Disconnected from the channel tcfchan#4.
17:28:44 INFO  : Invoking Bootgen: bootgen -image output.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\BOOT.bin
17:28:44 INFO  : Creating new bif file E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\output.bif
17:28:46 INFO  : Bootgen command execution is done.
17:40:50 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:41:08 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:41:29 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:41:36 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:41:36 INFO  : 'fpga -state' command is executed.
17:41:36 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:41:38 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
17:41:38 INFO  : 'jtag frequency' command is executed.
17:41:38 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
17:41:38 INFO  : Context for 'APU' is selected.
17:41:42 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
17:41:42 INFO  : Context for 'APU' is selected.
17:41:42 INFO  : 'stop' command is executed.
17:41:43 INFO  : 'ps7_init' command is executed.
17:41:43 INFO  : 'ps7_post_config' command is executed.
17:41:43 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:41:43 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
17:41:43 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:41:44 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf' is downloaded to processor 'ps7_cortexa9_0'.
17:41:44 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf
----------------End of Script----------------

17:41:45 INFO  : Memory regions updated for context APU
17:41:45 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:41:45 INFO  : 'con' command is executed.
17:41:45 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

17:41:45 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_fsbl.elf_on_local.tcl'
17:42:33 INFO  : Disconnected from the channel tcfchan#5.
17:42:46 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:42:47 INFO  : 'fpga -state' command is executed.
17:42:47 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:42:47 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
17:42:47 INFO  : 'jtag frequency' command is executed.
17:42:47 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
17:42:47 INFO  : Context for 'APU' is selected.
17:42:52 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
17:42:52 INFO  : Context for 'APU' is selected.
17:42:53 INFO  : 'stop' command is executed.
17:43:00 ERROR : Memory read error at 0xE0000034. AP transaction timeout
17:43:00 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
----------------End of Script----------------

17:43:26 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:43:26 INFO  : 'fpga -state' command is executed.
17:43:26 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:43:26 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
17:43:26 INFO  : 'jtag frequency' command is executed.
17:43:26 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
17:43:30 ERROR : no targets found with "name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"". available targets:
  1  DAP (AHB AP transaction error, DAP status 30000021)
  4* xc7z035
17:43:30 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
----------------End of Script----------------

17:43:41 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:43:47 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:43:47 INFO  : 'fpga -state' command is executed.
17:43:47 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:43:47 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
17:43:47 INFO  : 'jtag frequency' command is executed.
17:43:47 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
17:43:50 ERROR : no targets found with "name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"". available targets:
  1  DAP (AHB AP transaction error, DAP status 30000021)
  4* xc7z035
17:43:50 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
----------------End of Script----------------

17:44:42 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
17:44:42 INFO  : 'fpga -state' command is executed.
17:44:42 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:44:43 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
17:44:43 INFO  : 'jtag frequency' command is executed.
17:44:43 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
17:44:43 INFO  : Context for 'APU' is selected.
17:44:43 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
17:44:43 INFO  : Context for 'APU' is selected.
17:44:43 INFO  : 'stop' command is executed.
17:44:44 INFO  : 'ps7_init' command is executed.
17:44:44 INFO  : 'ps7_post_config' command is executed.
17:44:44 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:44:44 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
17:44:44 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:44:45 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf' is downloaded to processor 'ps7_cortexa9_0'.
17:44:45 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf
----------------End of Script----------------

17:44:45 INFO  : Memory regions updated for context APU
17:44:46 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
17:44:46 INFO  : 'con' command is executed.
17:44:46 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

17:44:46 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_fsbl.elf_on_local.tcl'
17:46:32 INFO  : Disconnected from the channel tcfchan#6.
17:47:43 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin
17:47:43 INFO  : Creating new bif file E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\fsbl.bif
17:47:45 INFO  : Bootgen command execution is done.
17:50:56 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
17:50:56 INFO  : Overwriting existing bif file E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\fsbl.bif
17:50:58 INFO  : Bootgen command execution is done.
18:05:52 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
18:05:52 INFO  : Overwriting existing bif file E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\fsbl.bif
18:05:54 INFO  : Bootgen command execution is done.
18:15:10 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
18:15:11 INFO  : Bootgen command execution is done.
18:22:55 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
18:22:56 INFO  : Bootgen command execution is done.
18:35:04 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
18:35:05 INFO  : Bootgen command execution is done.
18:36:41 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
18:36:43 INFO  : Bootgen command execution is done.
18:37:21 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
18:37:30 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
18:49:54 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
18:49:55 INFO  : Bootgen command execution is done.
19:21:46 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
19:21:47 INFO  : Bootgen command execution is done.
19:21:59 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
19:22:28 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
19:25:57 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
19:25:57 INFO  : Overwriting existing bif file E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\fsbl.bif
19:25:59 INFO  : Bootgen command execution is done.
19:26:21 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
19:27:14 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
19:30:07 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
19:30:08 INFO  : Bootgen command execution is done.
09:38:50 INFO  : SDK has detected change in the last modified timestamps for source hardware specification file Source:1584841073863,  Project:1584436209596
09:38:50 INFO  : Project system_top_hw_platform_0's source hardware specification located at E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top.hdf is now different from the local copy.
		 The local copy will be replaced with the source specification and your workspace will be updated.
09:39:48 INFO  : Project system_top_hw_platform_0's source hardware specification location will not be monitored anymore.
09:40:05 INFO  : SDK has detected change in the last modified timestamps for source hardware specification file Source:1584841073863,  Project:1584436209596
09:40:05 INFO  : Copied contents of E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\system_top.hdf into \system_top_hw_platform_0\system.hdf.
09:40:17 INFO  : Synchronizing projects in the workspace with the hardware platform specification changes.
09:41:16 INFO  : 
09:41:17 INFO  : Updating hardware inferred compiler options for fsbl.
09:41:17 INFO  : Clearing existing target manager status.
09:41:24 INFO  : Closing and re-opening the MSS file of ther project fsbl_bsp
09:41:27 INFO  : Workspace synchronized with the new hardware specification file. Cleaning dependent projects...
09:41:28 WARN  : Linker script will not be updated automatically. Users need to update it manually.
09:47:01 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
09:47:02 INFO  : Bootgen command execution is done.
14:31:49 INFO  : SDK has detected change in the last modified timestamps for source hardware specification file Source:1584858649804,  Project:1584841073863
14:31:49 INFO  : Project system_top_hw_platform_0's source hardware specification located at E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\system_top.hdf is now different from the local copy.
		 The local copy will be replaced with the source specification and your workspace will be updated.
14:32:04 INFO  : Copied contents of E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\system_top.hdf into \system_top_hw_platform_0\system.hdf.
14:32:18 INFO  : Synchronizing projects in the workspace with the hardware platform specification changes.
14:32:39 INFO  : 
14:32:40 INFO  : Updating hardware inferred compiler options for fsbl.
14:32:40 INFO  : Clearing existing target manager status.
14:32:40 INFO  : Closing and re-opening the MSS file of ther project fsbl_bsp
14:32:42 INFO  : Workspace synchronized with the new hardware specification file. Cleaning dependent projects...
14:32:43 WARN  : Linker script will not be updated automatically. Users need to update it manually.
14:35:03 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT.bin -w on
14:35:04 INFO  : Bootgen command execution is done.
14:36:02 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
16:50:04 INFO  : Launching XSCT server: xsct.bat -interactive E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
16:50:12 INFO  : XSCT server has started successfully.
16:50:12 INFO  : Successfully done setting XSCT server connection channel  
16:50:29 INFO  : Successfully done setting SDK workspace  
16:50:51 INFO  : Registering command handlers for SDK TCF services
16:51:35 INFO  : SDK has detected change in the last modified timestamps for source hardware specification file Source:1585039513082,  Project:1584858649804
16:51:35 INFO  : Copied contents of E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_40M.hdf into \system_top_hw_platform_0\system.hdf.
16:53:17 INFO  : Synchronizing projects in the workspace with the hardware platform specification changes.
16:54:05 INFO  : 
16:54:06 INFO  : Updating hardware inferred compiler options for fsbl.
16:54:06 INFO  : Clearing existing target manager status.
16:54:14 INFO  : Closing and re-opening the MSS file of ther project fsbl_bsp
16:54:20 INFO  : Workspace synchronized with the new hardware specification file. Cleaning dependent projects...
16:54:21 WARN  : Linker script will not be updated automatically. Users need to update it manually.
16:56:56 INFO  : Invoking Bootgen: bootgen -image output.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\BOOT.bin -w on
16:56:56 INFO  : Overwriting existing bif file E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\output.bif
16:56:59 INFO  : Bootgen command execution is done.
17:04:15 INFO  : Invoking Bootgen: bootgen -image output.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\BOOT.bin -w on
17:04:15 INFO  : Overwriting existing bif file E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\output.bif
17:04:16 INFO  : Bootgen command execution is done.
17:04:35 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:04:49 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:04:58 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:01 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:01 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:10 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:25 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:29 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:35 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:38 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:45 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:48 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:06:11 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:06:16 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:06:25 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:06:38 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:06:48 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:07:19 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:34:06 ERROR : Unexpected error while fetching XMD's response: 
java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:209)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284)
	at sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326)
	at sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178)
	at java.io.InputStreamReader.read(InputStreamReader.java:184)
	at java.io.BufferedReader.fill(BufferedReader.java:161)
	at java.io.BufferedReader.read(BufferedReader.java:182)
	at com.xilinx.sdk.targetmanager.internal.json.XMDJSONProcess$CommandResultsReader.run(XMDJSONProcess.java:332)
	at java.lang.Thread.run(Thread.java:745)
17:34:06 ERROR : Zero sized response read from XMD. This indicates a communication error or a programming error.
14:19:27 INFO  : Launching XSCT server: xsct.bat -interactive E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
14:19:32 INFO  : XSCT server has started successfully.
14:19:41 INFO  : Successfully done setting XSCT server connection channel  
14:19:41 INFO  : Successfully done setting SDK workspace  
14:19:53 INFO  : Registering command handlers for SDK TCF services
15:55:14 INFO  : SDK has detected change in the last modified timestamps for source hardware specification file Source:1585113281851,  Project:1585039513082
15:55:14 INFO  : Copied contents of E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_clk.hdf into \system_top_hw_platform_0\system.hdf.
15:56:14 INFO  : Synchronizing projects in the workspace with the hardware platform specification changes.
15:56:35 INFO  : 
15:56:36 INFO  : Updating hardware inferred compiler options for fsbl.
15:56:36 INFO  : Clearing existing target manager status.
15:56:44 INFO  : Closing and re-opening the MSS file of ther project fsbl_bsp
15:56:46 INFO  : Workspace synchronized with the new hardware specification file. Cleaning dependent projects...
15:56:47 WARN  : Linker script will not be updated automatically. Users need to update it manually.
16:00:24 INFO  : Invoking Bootgen: bootgen -image output.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\BOOT.bin -w on
16:00:24 INFO  : Overwriting existing bif file E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\output.bif
16:00:26 INFO  : Bootgen command execution is done.
16:01:03 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
16:13:31 ERROR : Unexpected error while parsing XMD response ?: com.google.gson.JsonSyntaxException: com.google.gson.stream.MalformedJsonException: invalid number or unquoted string near 
18:27:58 INFO  : Launching XSCT server: xsct.bat -interactive E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
18:28:04 INFO  : XSCT server has started successfully.
18:28:04 INFO  : Successfully done setting XSCT server connection channel  
18:28:17 INFO  : Successfully done setting SDK workspace  
18:28:28 INFO  : Registering command handlers for SDK TCF services
10:49:53 INFO  : SDK has detected change in the last modified timestamps for source hardware specification file Source:1585450111560,  Project:1585113281851
10:49:54 INFO  : Copied contents of E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\system_top_flash_50M.hdf into \system_top_hw_platform_0\system.hdf.
10:50:00 INFO  : Synchronizing projects in the workspace with the hardware platform specification changes.
10:50:25 INFO  : 
10:50:26 INFO  : Updating hardware inferred compiler options for fsbl.
10:50:26 INFO  : Clearing existing target manager status.
10:50:31 INFO  : Closing and re-opening the MSS file of ther project fsbl_bsp
10:50:33 INFO  : Workspace synchronized with the new hardware specification file. Cleaning dependent projects...
10:50:35 WARN  : Linker script will not be updated automatically. Users need to update it manually.
10:56:43 INFO  : Example project fsbl_bsp_xqspips_flash_lqspi_example_1 has been created successfully.
10:57:52 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
10:57:52 INFO  : 'fpga -state' command is executed.
10:57:52 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
10:57:52 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
10:57:52 INFO  : 'jtag frequency' command is executed.
10:57:52 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
10:57:53 INFO  : Context for 'APU' is selected.
10:57:53 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
10:57:54 INFO  : Context for 'APU' is selected.
10:57:54 INFO  : 'stop' command is executed.
10:57:55 INFO  : 'ps7_init' command is executed.
10:57:55 INFO  : 'ps7_post_config' command is executed.
10:57:55 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
10:57:55 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
10:57:55 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
10:57:56 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf' is downloaded to processor 'ps7_cortexa9_0'.
10:57:56 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf
----------------End of Script----------------

10:57:57 INFO  : Memory regions updated for context APU
10:57:57 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
10:57:57 INFO  : 'con' command is executed.
10:57:57 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

10:57:57 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_fsbl.elf_on_local.tcl'
11:01:09 INFO  : Disconnected from the channel tcfchan#1.
11:03:36 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
11:03:36 INFO  : 'fpga -state' command is executed.
11:03:36 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
11:03:37 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
11:03:37 INFO  : 'jtag frequency' command is executed.
11:03:37 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
11:03:37 INFO  : Context for 'APU' is selected.
11:03:37 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
11:03:37 INFO  : Context for 'APU' is selected.
11:03:37 INFO  : 'stop' command is executed.
11:03:39 INFO  : 'ps7_init' command is executed.
11:03:39 INFO  : 'ps7_post_config' command is executed.
11:03:39 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:03:39 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
11:03:39 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:03:40 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf' is downloaded to processor 'ps7_cortexa9_0'.
11:03:40 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf
----------------End of Script----------------

11:03:40 INFO  : Memory regions updated for context APU
11:03:41 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:03:41 INFO  : 'con' command is executed.
11:03:41 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

11:03:41 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_fsbl.elf_on_local.tcl'
11:04:21 INFO  : Disconnected from the channel tcfchan#2.
11:06:38 INFO  : Invoking Bootgen: bootgen -image fsbl_bsp_xqspips_flash_lqspi_example_1.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl_bsp_xqspips_flash_lqspi_example_1\bootimage\BOOT.bin
11:06:38 INFO  : Creating new bif file E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl_bsp_xqspips_flash_lqspi_example_1\bootimage\fsbl_bsp_xqspips_flash_lqspi_example_1.bif
11:06:39 INFO  : Bootgen command execution is done.
11:30:10 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
11:30:10 INFO  : 'fpga -state' command is executed.
11:30:10 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
11:30:10 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
11:30:10 INFO  : 'jtag frequency' command is executed.
11:30:10 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
11:30:10 INFO  : Context for 'APU' is selected.
11:30:10 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
11:30:11 INFO  : Context for 'APU' is selected.
11:30:11 INFO  : 'stop' command is executed.
11:30:11 INFO  : 'ps7_init' command is executed.
11:30:11 INFO  : 'ps7_post_config' command is executed.
11:30:12 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:30:12 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
11:30:12 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:30:13 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf' is downloaded to processor 'ps7_cortexa9_0'.
11:30:13 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl/Debug/fsbl.elf
----------------End of Script----------------

11:30:13 INFO  : Memory regions updated for context APU
11:30:13 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:30:13 INFO  : 'con' command is executed.
11:30:13 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

11:30:14 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_fsbl.elf_on_local.tcl'
11:30:42 INFO  : Disconnected from the channel tcfchan#3.
11:30:43 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
11:30:43 INFO  : 'fpga -state' command is executed.
11:30:47 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
11:30:47 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
11:30:47 INFO  : 'jtag frequency' command is executed.
11:30:47 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
11:30:47 INFO  : Context for 'APU' is selected.
11:30:47 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
11:30:47 INFO  : Context for 'APU' is selected.
11:30:47 INFO  : 'stop' command is executed.
11:30:48 INFO  : 'ps7_init' command is executed.
11:30:48 INFO  : 'ps7_post_config' command is executed.
11:30:48 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:30:48 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
11:30:48 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:30:52 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
11:30:52 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

11:30:52 INFO  : Memory regions updated for context APU
11:30:53 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:30:53 INFO  : 'con' command is executed.
11:30:53 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

11:30:53 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
11:31:27 INFO  : Disconnected from the channel tcfchan#4.
11:42:03 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
11:42:03 INFO  : 'fpga -state' command is executed.
11:42:08 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
11:42:09 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
11:42:09 INFO  : 'jtag frequency' command is executed.
11:42:09 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
11:42:09 INFO  : Context for 'APU' is selected.
11:42:09 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
11:42:09 INFO  : Context for 'APU' is selected.
11:42:09 INFO  : 'stop' command is executed.
11:42:10 INFO  : 'ps7_init' command is executed.
11:42:10 INFO  : 'ps7_post_config' command is executed.
11:42:10 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:42:10 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
11:42:10 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:42:14 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
11:42:14 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

11:42:14 INFO  : Memory regions updated for context APU
11:42:15 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:42:15 INFO  : 'con' command is executed.
11:42:15 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

11:42:15 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
11:42:18 INFO  : Disconnected from the channel tcfchan#5.
11:44:03 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
11:44:03 INFO  : 'fpga -state' command is executed.
11:44:05 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
11:44:05 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
11:44:05 INFO  : 'jtag frequency' command is executed.
11:44:05 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
11:44:06 INFO  : Context for 'APU' is selected.
11:44:06 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
11:44:06 INFO  : Context for 'APU' is selected.
11:44:06 INFO  : 'stop' command is executed.
11:44:07 INFO  : 'ps7_init' command is executed.
11:44:07 INFO  : 'ps7_post_config' command is executed.
11:44:07 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:44:07 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
11:44:07 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:44:11 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
11:44:11 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

11:44:11 INFO  : Memory regions updated for context APU
11:44:12 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:44:12 INFO  : 'con' command is executed.
11:44:12 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

11:44:12 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
11:44:16 INFO  : Disconnected from the channel tcfchan#6.
11:46:18 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
11:46:19 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
11:46:19 INFO  : 'jtag frequency' command is executed.
11:46:19 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
11:46:19 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
11:46:29 INFO  : FPGA configured successfully with bitstream "E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit"
11:46:29 INFO  : Context for 'APU' is selected.
11:46:29 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
11:46:29 INFO  : Context for 'APU' is selected.
11:46:29 INFO  : 'stop' command is executed.
11:46:30 INFO  : 'ps7_init' command is executed.
11:46:30 INFO  : 'ps7_post_config' command is executed.
11:46:30 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:46:30 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
11:46:30 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:46:34 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
11:46:34 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

11:46:34 INFO  : Memory regions updated for context APU
11:46:34 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
11:46:34 INFO  : 'con' command is executed.
11:46:34 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

11:46:34 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
11:55:27 INFO  : Disconnected from the channel tcfchan#7.
11:56:46 INFO  : Invoking Bootgen: bootgen -image fsbl.bif -arch zynq -o E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\BOOT-50M.bin
11:56:46 INFO  : Overwriting existing bif file E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\fsbl\bootimage\fsbl.bif
11:56:47 INFO  : Bootgen command execution is done.
12:04:36 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
12:04:37 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
12:04:37 INFO  : 'jtag frequency' command is executed.
12:04:37 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
12:04:37 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
12:04:47 INFO  : FPGA configured successfully with bitstream "E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit"
12:04:47 INFO  : Context for 'APU' is selected.
12:04:47 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
12:04:47 INFO  : Context for 'APU' is selected.
12:04:47 INFO  : 'stop' command is executed.
12:04:48 INFO  : 'ps7_init' command is executed.
12:04:48 INFO  : 'ps7_post_config' command is executed.
12:04:48 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
12:04:48 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
12:04:48 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
12:04:54 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
12:04:54 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

12:04:54 INFO  : Memory regions updated for context APU
12:04:54 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
12:04:54 INFO  : 'con' command is executed.
12:04:54 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

12:04:54 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
12:14:05 INFO  : Disconnected from the channel tcfchan#8.
12:14:06 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
12:14:06 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
12:14:07 INFO  : 'jtag frequency' command is executed.
12:14:07 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
12:14:07 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
12:14:16 INFO  : FPGA configured successfully with bitstream "E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit"
12:14:16 INFO  : Context for 'APU' is selected.
12:14:16 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
12:14:17 INFO  : Context for 'APU' is selected.
12:14:17 INFO  : 'stop' command is executed.
12:14:19 ERROR : Memory read error at 0xE0000034. AP transaction timeout
12:14:19 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
----------------End of Script----------------

12:14:33 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
12:14:33 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
12:14:33 INFO  : 'jtag frequency' command is executed.
12:14:33 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
12:14:34 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
12:14:43 INFO  : FPGA configured successfully with bitstream "E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit"
12:14:44 INFO  : Context for 'APU' is selected.
12:14:44 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
12:14:44 INFO  : Context for 'APU' is selected.
12:14:44 INFO  : 'stop' command is executed.
12:14:44 ERROR : Memory read error at 0xF8007080. AHB AP transaction error, DAP status f0000021
12:14:44 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
----------------End of Script----------------

12:14:58 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
12:14:59 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
12:14:59 INFO  : 'jtag frequency' command is executed.
12:14:59 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
12:14:59 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
12:15:08 INFO  : FPGA configured successfully with bitstream "E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit"
12:15:08 INFO  : Context for 'APU' is selected.
12:15:08 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
12:15:08 INFO  : Context for 'APU' is selected.
12:15:08 INFO  : 'stop' command is executed.
12:15:09 INFO  : 'ps7_init' command is executed.
12:15:09 INFO  : 'ps7_post_config' command is executed.
12:15:09 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
12:15:09 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
12:15:09 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
12:15:13 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
12:15:13 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

12:15:13 INFO  : Memory regions updated for context APU
12:15:13 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
12:15:13 INFO  : 'con' command is executed.
12:15:13 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

12:15:13 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
12:26:56 INFO  : Disconnected from the channel tcfchan#9.
12:26:57 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
12:26:57 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
12:26:58 INFO  : 'jtag frequency' command is executed.
12:26:58 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
12:26:58 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
12:27:07 INFO  : FPGA configured successfully with bitstream "E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit"
12:27:07 INFO  : Context for 'APU' is selected.
12:27:07 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
12:27:07 INFO  : Context for 'APU' is selected.
12:27:07 INFO  : 'stop' command is executed.
12:27:08 INFO  : 'ps7_init' command is executed.
12:27:08 INFO  : 'ps7_post_config' command is executed.
12:27:08 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
12:27:08 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
12:27:08 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
12:27:12 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
12:27:12 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

12:27:12 INFO  : Memory regions updated for context APU
12:27:12 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
12:27:12 INFO  : 'con' command is executed.
12:27:12 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

12:27:12 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
13:01:23 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
13:03:34 INFO  : Disconnected from the channel tcfchan#10.
13:06:51 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
13:06:52 INFO  : Jtag cable 'Platform Cable USB 00001a2b2a6701' is selected.
13:06:52 INFO  : 'jtag frequency' command is executed.
13:06:52 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
13:06:52 INFO  : 'targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1' command is executed.
13:07:01 INFO  : FPGA configured successfully with bitstream "E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit"
13:07:02 INFO  : Context for 'APU' is selected.
13:07:02 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
13:07:02 INFO  : Context for 'APU' is selected.
13:07:02 INFO  : 'stop' command is executed.
13:07:03 INFO  : 'ps7_init' command is executed.
13:07:03 INFO  : 'ps7_post_config' command is executed.
13:07:03 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
13:07:03 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
13:07:03 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
13:07:07 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
13:07:07 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

13:07:07 INFO  : Memory regions updated for context APU
13:07:07 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
13:07:08 INFO  : 'con' command is executed.
13:07:08 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB 00001a2b2a6701"} -index 0
con
----------------End of Script----------------

13:07:08 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
13:12:41 INFO  : Disconnected from the channel tcfchan#11.
13:21:13 ERROR : (XSDB Server)unexpected arguments: arm hw
13:21:13 ERROR : (XSDB Server)

13:21:25 ERROR : (XSDB Server)unexpected arguments: arm hw
13:21:25 ERROR : (XSDB Server)

15:10:25 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
15:10:27 INFO  : Jtag cable 'Digilent JTAG-SMT2 210251838408' is selected.
15:10:27 INFO  : 'jtag frequency' command is executed.
15:10:27 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
15:10:27 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408" && level==0} -index 1' command is executed.
15:10:45 INFO  : FPGA configured successfully with bitstream "E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit"
15:10:46 INFO  : Context for 'APU' is selected.
15:10:46 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
15:10:46 INFO  : Context for 'APU' is selected.
15:10:46 INFO  : 'stop' command is executed.
15:10:58 INFO  : 'ps7_init' command is executed.
15:10:58 INFO  : 'ps7_post_config' command is executed.
15:10:58 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
15:10:59 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
15:10:59 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
15:11:16 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
15:11:16 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

15:11:16 INFO  : Memory regions updated for context APU
15:11:16 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
15:11:16 INFO  : 'con' command is executed.
15:11:16 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
con
----------------End of Script----------------

15:11:16 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
15:16:18 INFO  : Disconnected from the channel tcfchan#12.
15:16:24 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
15:16:27 INFO  : Jtag cable 'Digilent JTAG-SMT2 210251838408' is selected.
15:16:27 INFO  : 'jtag frequency' command is executed.
15:16:27 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
15:16:27 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408" && level==0} -index 1' command is executed.
15:16:45 INFO  : FPGA configured successfully with bitstream "E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit"
15:16:45 INFO  : Context for 'APU' is selected.
15:16:45 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
15:16:45 INFO  : Context for 'APU' is selected.
15:16:46 INFO  : 'stop' command is executed.
15:16:53 INFO  : 'ps7_init' command is executed.
15:16:53 INFO  : 'ps7_post_config' command is executed.
15:16:53 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
15:16:53 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
15:16:53 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
15:17:04 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
15:17:04 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

15:17:04 INFO  : Memory regions updated for context APU
15:17:05 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
15:17:05 INFO  : 'con' command is executed.
15:17:05 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
con
----------------End of Script----------------

15:17:05 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
15:20:19 INFO  : Disconnected from the channel tcfchan#13.
16:06:33 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
16:06:38 INFO  : Jtag cable 'Digilent JTAG-SMT2 210251838408' is selected.
16:06:38 INFO  : 'jtag frequency' command is executed.
16:06:38 INFO  : Sourcing of 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl' is done.
16:06:39 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408" && level==0} -index 1' command is executed.
16:06:57 INFO  : FPGA configured successfully with bitstream "E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit"
16:06:57 INFO  : Context for 'APU' is selected.
16:06:57 INFO  : Hardware design information is loaded from 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf'.
16:06:57 INFO  : Context for 'APU' is selected.
16:06:57 INFO  : 'stop' command is executed.
16:07:05 INFO  : 'ps7_init' command is executed.
16:07:05 INFO  : 'ps7_post_config' command is executed.
16:07:05 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
16:07:05 INFO  : Processor reset is completed for 'ps7_cortexa9_0'.
16:07:05 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
16:07:16 INFO  : The application 'E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf' is downloaded to processor 'ps7_cortexa9_0'.
16:07:16 INFO  : ----------------XSDB Script----------------
connect -url tcp:127.0.0.1:3121
source E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/ps7_init.tcl
targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408" && level==0} -index 1
fpga -file E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_flash_50M.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
loadhw E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
stop
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
rst -processor
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
dow E:/workspace/Xilinx/R75_Z7035_0320/fmcomms2_zed.sdk/ad9361/Debug/ad9361.elf
----------------End of Script----------------

16:07:16 INFO  : Memory regions updated for context APU
16:07:17 INFO  : Context for processor 'ps7_cortexa9_0' is selected.
16:07:17 INFO  : 'con' command is executed.
16:07:17 INFO  : ----------------XSDB Script (After Launch)----------------
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Digilent JTAG-SMT2 210251838408"} -index 0
con
----------------End of Script----------------

16:07:17 INFO  : Launch script is exported to file 'E:\workspace\Xilinx\R75_Z7035_0320\fmcomms2_zed.sdk\.sdk\launch_scripts\xilinx_c-c++_application_(system_debugger)\system_debugger_using_debug_ad9361.elf_on_local.tcl'
16:08:08 INFO  : Disconnected from the channel tcfchan#14.
10:09:59 ERROR : Unexpected error while parsing XMD response ?: com.google.gson.JsonSyntaxException: com.google.gson.stream.MalformedJsonException: invalid number or unquoted string near 
12:05:24 INFO  : Registering command handlers for SDK TCF services
12:05:25 INFO  : Launching XSCT server: xsct.bat -interactive E:\FPGA\R75_Z7035_0320\R75_Z7035_0320\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
12:05:29 INFO  : XSCT server has started successfully.
12:05:39 INFO  : Successfully done setting XSCT server connection channel  
12:05:39 INFO  : Successfully done setting SDK workspace  
12:05:39 INFO  : Processing command line option -hwspec E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top.hdf.
12:06:20 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
12:06:25 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
12:06:34 INFO  : Connected to target on host '127.0.0.1' and port '3121'.
17:05:58 INFO  : Launching XSCT server: xsct.bat -interactive E:\FPGA\R75_Z7035_0320\R75_Z7035_0320\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
17:06:02 INFO  : XSCT server has started successfully.
17:06:06 INFO  : Successfully done setting XSCT server connection channel  
17:06:06 INFO  : Successfully done setting SDK workspace  
17:06:10 INFO  : Registering command handlers for SDK TCF services
17:06:20 INFO  : Processing command line option -hwspec E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top.hdf.
17:06:23 INFO  : Checking for hwspec changes in the project system_top_hw_platform_1.
16:46:49 INFO  : Launching XSCT server: xsct.bat -interactive D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
16:46:53 INFO  : XSCT server has started successfully.
16:46:55 INFO  : Successfully done setting XSCT server connection channel  
16:46:55 INFO  : Successfully done setting SDK workspace  
16:46:58 INFO  : Registering command handlers for SDK TCF services
16:47:02 INFO  : Processing command line option -hwspec D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top.hdf.
14:37:10 INFO  : Registering command handlers for SDK TCF services
14:37:11 INFO  : Launching XSCT server: xsct.bat -interactive D:\xiaoE\R75_Z7035_0320\fmcomms2_zed.sdk\temp_xsdb_launch_script.tcl
14:37:13 INFO  : XSCT server has started successfully.
14:37:13 INFO  : Successfully done setting XSCT server connection channel  
14:37:13 INFO  : Successfully done setting SDK workspace  
14:37:13 INFO  : Processing command line option -hwspec D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top.hdf.
14:37:13 INFO  : Checking for hwspec changes in the project system_top_hw_platform_2.
