{"design": {"design_info": {"boundary_crc": "0xE641E5597227BBBA", "device": "xc7z035ffg676-2", "name": "system", "synth_flow_mode": "None", "tool_version": "2018.3"}, "design_tree": {"axi_ad9361": "", "axi_ad9361_adc_dma": "", "axi_ad9361_dac_dma": "", "axi_ad9361_dac_fifo": "", "util_ad9361_adc_fifo": "", "util_ad9361_adc_pack": "", "util_ad9361_dac_upack": "", "util_ad9361_divclk": "", "util_ad9361_tdd_sync": "", "axi_cpu_interconnect": {"xbar": "", "s00_couplers": {}, "m00_couplers": {}, "m01_couplers": {}, "m02_couplers": {}, "m03_couplers": {}, "m04_couplers": {}, "m05_couplers": {}, "m06_couplers": {}, "m07_couplers": {}, "m08_couplers": {}, "m09_couplers": {}}, "axi_hp0_interconnect": {"s00_couplers": {}}, "axi_hp1_interconnect": {"s00_couplers": {}}, "axi_hp2_interconnect": {"s00_couplers": {}}, "sys_concat_intc": "", "sys_ps7": "", "sys_rstgen": "", "util_ad9361_divclk_reset": "", "util_ad9361_divclk_sel": "", "util_ad9361_divclk_sel_concat": ""}, "interface_ports": {"ddr": {"mode": "Master", "vlnv": "xilinx.com:interface:ddrx_rtl:1.0"}, "fixed_io": {"mode": "Master", "vlnv": "xilinx.com:display_processing_system7:fixedio_rtl:1.0"}}, "ports": {"FCLK_CLK0": {"direction": "O"}, "gpio_i": {"direction": "I", "left": "63", "right": "0"}, "gpio_o": {"direction": "O", "left": "63", "right": "0"}, "gpio_t": {"direction": "O", "left": "63", "right": "0"}, "spi0_csn_2_o": {"direction": "O"}, "spi0_csn_1_o": {"direction": "O"}, "spi0_csn_0_o": {"direction": "O"}, "spi0_csn_i": {"direction": "I"}, "spi0_clk_i": {"direction": "I"}, "spi0_clk_o": {"direction": "O"}, "spi0_sdo_i": {"direction": "I"}, "spi0_sdo_o": {"direction": "O"}, "spi0_sdi_i": {"direction": "I"}, "ps_intr_10": {"type": "intr", "direction": "I"}, "ps_intr_09": {"type": "intr", "direction": "I"}, "ps_intr_08": {"type": "intr", "direction": "I"}, "ps_intr_07": {"type": "intr", "direction": "I"}, "ps_intr_06": {"type": "intr", "direction": "I"}, "ps_intr_05": {"type": "intr", "direction": "I"}, "ps_intr_04": {"type": "intr", "direction": "I"}, "ps_intr_03": {"type": "intr", "direction": "I"}, "ps_intr_02": {"type": "intr", "direction": "I"}, "ps_intr_01": {"type": "intr", "direction": "I"}, "ps_intr_00": {"type": "intr", "direction": "I"}, "rx_clk_in_p": {"direction": "I"}, "rx_clk_in_n": {"direction": "I"}, "rx_frame_in_p": {"direction": "I"}, "rx_frame_in_n": {"direction": "I"}, "rx_data_in_p": {"direction": "I", "left": "5", "right": "0"}, "rx_data_in_n": {"direction": "I", "left": "5", "right": "0"}, "tx_clk_out_p": {"direction": "O"}, "tx_clk_out_n": {"direction": "O"}, "tx_frame_out_p": {"direction": "O"}, "tx_frame_out_n": {"direction": "O"}, "tx_data_out_p": {"direction": "O", "left": "5", "right": "0"}, "tx_data_out_n": {"direction": "O", "left": "5", "right": "0"}, "enable": {"direction": "O"}, "txnrx": {"direction": "O"}, "up_enable": {"direction": "I"}, "up_txnrx": {"direction": "I"}, "tdd_sync_o": {"direction": "O"}, "tdd_sync_t": {"direction": "O"}, "tdd_sync_i": {"direction": "I"}}, "components": {"axi_ad9361": {"vlnv": "analog.com:user:axi_ad9361:1.0", "xci_name": "system_axi_ad9361_0", "parameters": {"ADC_INIT_DELAY": {"value": "23"}, "ID": {"value": "0"}}}, "axi_ad9361_adc_dma": {"vlnv": "analog.com:user:axi_dmac:1.0", "xci_name": "system_axi_ad9361_adc_dma_0", "parameters": {"AXI_SLICE_DEST": {"value": "false"}, "AXI_SLICE_SRC": {"value": "false"}, "CYCLIC": {"value": "false"}, "DMA_2D_TRANSFER": {"value": "false"}, "DMA_DATA_WIDTH_SRC": {"value": "64"}, "DMA_TYPE_DEST": {"value": "0"}, "DMA_TYPE_SRC": {"value": "2"}, "SYNC_TRANSFER_START": {"value": "true"}}}, "axi_ad9361_dac_dma": {"vlnv": "analog.com:user:axi_dmac:1.0", "xci_name": "system_axi_ad9361_dac_dma_0", "parameters": {"AXI_SLICE_DEST": {"value": "true"}, "AXI_SLICE_SRC": {"value": "false"}, "CYCLIC": {"value": "true"}, "DMA_2D_TRANSFER": {"value": "false"}, "DMA_DATA_WIDTH_DEST": {"value": "64"}, "DMA_TYPE_DEST": {"value": "2"}, "DMA_TYPE_SRC": {"value": "0"}}}, "axi_ad9361_dac_fifo": {"vlnv": "analog.com:user:util_rfifo:1.0", "xci_name": "system_axi_ad9361_dac_fifo_0", "parameters": {"DIN_ADDRESS_WIDTH": {"value": "4"}, "DIN_DATA_WIDTH": {"value": "16"}, "DOUT_DATA_WIDTH": {"value": "16"}}}, "util_ad9361_adc_fifo": {"vlnv": "analog.com:user:util_wfifo:1.0", "xci_name": "system_util_ad9361_adc_fifo_0", "parameters": {"DIN_ADDRESS_WIDTH": {"value": "4"}, "DIN_DATA_WIDTH": {"value": "16"}, "DOUT_DATA_WIDTH": {"value": "16"}, "NUM_OF_CHANNELS": {"value": "4"}}}, "util_ad9361_adc_pack": {"vlnv": "analog.com:user:util_cpack:1.0", "xci_name": "system_util_ad9361_adc_pack_0", "parameters": {"CHANNEL_DATA_WIDTH": {"value": "16"}, "NUM_OF_CHANNELS": {"value": "4"}}}, "util_ad9361_dac_upack": {"vlnv": "analog.com:user:util_upack:1.0", "xci_name": "system_util_ad9361_dac_upack_0", "parameters": {"CHANNEL_DATA_WIDTH": {"value": "16"}, "NUM_OF_CHANNELS": {"value": "4"}}}, "util_ad9361_divclk": {"vlnv": "analog.com:user:util_clkdiv:1.0", "xci_name": "system_util_ad9361_divclk_0"}, "util_ad9361_tdd_sync": {"vlnv": "analog.com:user:util_tdd_sync:1.0", "xci_name": "system_util_ad9361_tdd_sync_0", "parameters": {"TDD_SYNC_PERIOD": {"value": "10000000"}}}, "axi_cpu_interconnect": {"vlnv": "xilinx.com:ip:axi_interconnect:2.1", "xci_name": "system_axi_cpu_interconnect_0", "parameters": {"NUM_MI": {"value": "10"}, "SYNCHRONIZATION_STAGES": {"value": "2"}}, "interface_ports": {"S00_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M00_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M01_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M02_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M03_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M04_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M05_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M06_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M07_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M08_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M09_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_RESET": {"value": "ARESETN"}}}, "ARESETN": {"type": "rst", "direction": "I"}, "S00_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S00_AXI"}, "ASSOCIATED_RESET": {"value": "S00_ARESETN"}}}, "S00_ARESETN": {"type": "rst", "direction": "I"}, "M00_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M00_AXI"}, "ASSOCIATED_RESET": {"value": "M00_ARESETN"}}}, "M00_ARESETN": {"type": "rst", "direction": "I"}, "M01_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M01_AXI"}, "ASSOCIATED_RESET": {"value": "M01_ARESETN"}}}, "M01_ARESETN": {"type": "rst", "direction": "I"}, "M02_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M02_AXI"}, "ASSOCIATED_RESET": {"value": "M02_ARESETN"}}}, "M02_ARESETN": {"type": "rst", "direction": "I"}, "M03_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M03_AXI"}, "ASSOCIATED_RESET": {"value": "M03_ARESETN"}}}, "M03_ARESETN": {"type": "rst", "direction": "I"}, "M04_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M04_AXI"}, "ASSOCIATED_RESET": {"value": "M04_ARESETN"}}}, "M04_ARESETN": {"type": "rst", "direction": "I"}, "M05_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M05_AXI"}, "ASSOCIATED_RESET": {"value": "M05_ARESETN"}}}, "M05_ARESETN": {"type": "rst", "direction": "I"}, "M06_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M06_AXI"}, "ASSOCIATED_RESET": {"value": "M06_ARESETN"}}}, "M06_ARESETN": {"type": "rst", "direction": "I"}, "M07_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M07_AXI"}, "ASSOCIATED_RESET": {"value": "M07_ARESETN"}}}, "M07_ARESETN": {"type": "rst", "direction": "I"}, "M08_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M08_AXI"}, "ASSOCIATED_RESET": {"value": "M08_ARESETN"}}}, "M08_ARESETN": {"type": "rst", "direction": "I"}, "M09_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M09_AXI"}, "ASSOCIATED_RESET": {"value": "M09_ARESETN"}}}, "M09_ARESETN": {"type": "rst", "direction": "I"}}, "components": {"xbar": {"vlnv": "xilinx.com:ip:axi_crossbar:2.1", "xci_name": "system_xbar_0", "parameters": {"NUM_MI": {"value": "10"}, "NUM_SI": {"value": "1"}, "STRATEGY": {"value": "0"}}}, "s00_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"s00_couplers_to_s00_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}, "m00_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"m00_couplers_to_m00_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}, "m01_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"m01_couplers_to_m01_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}, "m02_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"m02_couplers_to_m02_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}, "m03_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"m03_couplers_to_m03_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}, "m04_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"m04_couplers_to_m04_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}, "m05_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"m05_couplers_to_m05_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}, "m06_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"m06_couplers_to_m06_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}, "m07_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"m07_couplers_to_m07_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}, "m08_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"m08_couplers_to_m08_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}, "m09_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"m09_couplers_to_m09_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}}, "interface_nets": {"xbar_to_m09_couplers": {"interface_ports": ["xbar/M09_AXI", "m09_couplers/S_AXI"]}, "xbar_to_m08_couplers": {"interface_ports": ["xbar/M08_AXI", "m08_couplers/S_AXI"]}, "m09_couplers_to_axi_cpu_interconnect_upgraded_ipi": {"interface_ports": ["M09_AXI", "m09_couplers/M_AXI"]}, "axi_cpu_interconnect_upgraded_ipi_to_s00_couplers": {"interface_ports": ["S00_AXI", "s00_couplers/S_AXI"]}, "s00_couplers_to_xbar": {"interface_ports": ["s00_couplers/M_AXI", "xbar/S00_AXI"]}, "m00_couplers_to_axi_cpu_interconnect_upgraded_ipi": {"interface_ports": ["M00_AXI", "m00_couplers/M_AXI"]}, "xbar_to_m00_couplers": {"interface_ports": ["xbar/M00_AXI", "m00_couplers/S_AXI"]}, "m01_couplers_to_axi_cpu_interconnect_upgraded_ipi": {"interface_ports": ["M01_AXI", "m01_couplers/M_AXI"]}, "xbar_to_m01_couplers": {"interface_ports": ["xbar/M01_AXI", "m01_couplers/S_AXI"]}, "m02_couplers_to_axi_cpu_interconnect_upgraded_ipi": {"interface_ports": ["M02_AXI", "m02_couplers/M_AXI"]}, "xbar_to_m02_couplers": {"interface_ports": ["xbar/M02_AXI", "m02_couplers/S_AXI"]}, "m03_couplers_to_axi_cpu_interconnect_upgraded_ipi": {"interface_ports": ["M03_AXI", "m03_couplers/M_AXI"]}, "xbar_to_m03_couplers": {"interface_ports": ["xbar/M03_AXI", "m03_couplers/S_AXI"]}, "m04_couplers_to_axi_cpu_interconnect_upgraded_ipi": {"interface_ports": ["M04_AXI", "m04_couplers/M_AXI"]}, "xbar_to_m04_couplers": {"interface_ports": ["xbar/M04_AXI", "m04_couplers/S_AXI"]}, "m05_couplers_to_axi_cpu_interconnect_upgraded_ipi": {"interface_ports": ["M05_AXI", "m05_couplers/M_AXI"]}, "xbar_to_m05_couplers": {"interface_ports": ["xbar/M05_AXI", "m05_couplers/S_AXI"]}, "m06_couplers_to_axi_cpu_interconnect_upgraded_ipi": {"interface_ports": ["M06_AXI", "m06_couplers/M_AXI"]}, "xbar_to_m06_couplers": {"interface_ports": ["xbar/M06_AXI", "m06_couplers/S_AXI"]}, "m07_couplers_to_axi_cpu_interconnect_upgraded_ipi": {"interface_ports": ["M07_AXI", "m07_couplers/M_AXI"]}, "xbar_to_m07_couplers": {"interface_ports": ["xbar/M07_AXI", "m07_couplers/S_AXI"]}, "m08_couplers_to_axi_cpu_interconnect_upgraded_ipi": {"interface_ports": ["M08_AXI", "m08_couplers/M_AXI"]}}, "nets": {"axi_cpu_interconnect_upgraded_ipi_ACLK_net": {"ports": ["ACLK", "xbar/aclk", "s00_couplers/S_ACLK", "s00_couplers/M_ACLK", "m00_couplers/M_ACLK", "m01_couplers/M_ACLK", "m02_couplers/M_ACLK", "m03_couplers/M_ACLK", "m04_couplers/M_ACLK", "m05_couplers/M_ACLK", "m06_couplers/M_ACLK", "m07_couplers/M_ACLK", "m08_couplers/M_ACLK", "m09_couplers/M_ACLK", "m00_couplers/S_ACLK", "m01_couplers/S_ACLK", "m02_couplers/S_ACLK", "m03_couplers/S_ACLK", "m04_couplers/S_ACLK", "m05_couplers/S_ACLK", "m06_couplers/S_ACLK", "m07_couplers/S_ACLK", "m08_couplers/S_ACLK", "m09_couplers/S_ACLK"]}, "axi_cpu_interconnect_upgraded_ipi_ARESETN_net": {"ports": ["ARESETN", "xbar/aresetn", "s00_couplers/S_ARESETN", "s00_couplers/M_ARESETN", "m00_couplers/M_ARESETN", "m01_couplers/M_ARESETN", "m02_couplers/M_ARESETN", "m03_couplers/M_ARESETN", "m04_couplers/M_ARESETN", "m05_couplers/M_ARESETN", "m06_couplers/M_ARESETN", "m07_couplers/M_ARESETN", "m08_couplers/M_ARESETN", "m09_couplers/M_ARESETN", "m00_couplers/S_ARESETN", "m01_couplers/S_ARESETN", "m02_couplers/S_ARESETN", "m03_couplers/S_ARESETN", "m04_couplers/S_ARESETN", "m05_couplers/S_ARESETN", "m06_couplers/S_ARESETN", "m07_couplers/S_ARESETN", "m08_couplers/S_ARESETN", "m09_couplers/S_ARESETN"]}}}, "axi_hp0_interconnect": {"vlnv": "xilinx.com:ip:axi_interconnect:2.1", "xci_name": "system_axi_hp0_interconnect_0", "parameters": {"NUM_MI": {"value": "1"}, "NUM_SI": {"value": "1"}, "SYNCHRONIZATION_STAGES": {"value": "2"}}, "interface_ports": {"S00_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M00_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_RESET": {"value": "ARESETN"}}}, "ARESETN": {"type": "rst", "direction": "I"}, "S00_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S00_AXI"}, "ASSOCIATED_RESET": {"value": "S00_ARESETN"}}}, "S00_ARESETN": {"type": "rst", "direction": "I"}, "M00_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M00_AXI"}, "ASSOCIATED_RESET": {"value": "M00_ARESETN"}}}, "M00_ARESETN": {"type": "rst", "direction": "I"}}, "components": {"s00_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"s00_couplers_to_s00_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}}, "interface_nets": {"axi_hp0_interconnect_upgraded_ipi_to_s00_couplers": {"interface_ports": ["S00_AXI", "s00_couplers/S_AXI"]}, "s00_couplers_to_axi_hp0_interconnect_upgraded_ipi": {"interface_ports": ["M00_AXI", "s00_couplers/M_AXI"]}}, "nets": {"axi_hp0_interconnect_upgraded_ipi_ACLK_net": {"ports": ["M00_ACLK", "s00_couplers/M_ACLK"]}, "axi_hp0_interconnect_upgraded_ipi_ARESETN_net": {"ports": ["M00_ARESETN", "s00_couplers/M_ARESETN"]}, "S00_ACLK_1": {"ports": ["S00_ACLK", "s00_couplers/S_ACLK"]}, "S00_ARESETN_1": {"ports": ["S00_ARESETN", "s00_couplers/S_ARESETN"]}}}, "axi_hp1_interconnect": {"vlnv": "xilinx.com:ip:axi_interconnect:2.1", "xci_name": "system_axi_hp1_interconnect_0", "parameters": {"NUM_MI": {"value": "1"}, "NUM_SI": {"value": "1"}, "SYNCHRONIZATION_STAGES": {"value": "2"}}, "interface_ports": {"S00_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M00_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_RESET": {"value": "ARESETN"}}}, "ARESETN": {"type": "rst", "direction": "I"}, "S00_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S00_AXI"}, "ASSOCIATED_RESET": {"value": "S00_ARESETN"}}}, "S00_ARESETN": {"type": "rst", "direction": "I"}, "M00_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M00_AXI"}, "ASSOCIATED_RESET": {"value": "M00_ARESETN"}}}, "M00_ARESETN": {"type": "rst", "direction": "I"}}, "components": {"s00_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"s00_couplers_to_s00_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}}, "interface_nets": {"s00_couplers_to_axi_hp1_interconnect_upgraded_ipi": {"interface_ports": ["M00_AXI", "s00_couplers/M_AXI"]}, "axi_hp1_interconnect_upgraded_ipi_to_s00_couplers": {"interface_ports": ["S00_AXI", "s00_couplers/S_AXI"]}}, "nets": {"axi_hp1_interconnect_upgraded_ipi_ACLK_net": {"ports": ["M00_ACLK", "s00_couplers/M_ACLK"]}, "axi_hp1_interconnect_upgraded_ipi_ARESETN_net": {"ports": ["M00_ARESETN", "s00_couplers/M_ARESETN"]}, "S00_ACLK_1": {"ports": ["S00_ACLK", "s00_couplers/S_ACLK"]}, "S00_ARESETN_1": {"ports": ["S00_ARESETN", "s00_couplers/S_ARESETN"]}}}, "axi_hp2_interconnect": {"vlnv": "xilinx.com:ip:axi_interconnect:2.1", "xci_name": "system_axi_hp2_interconnect_0", "parameters": {"NUM_MI": {"value": "1"}, "NUM_SI": {"value": "1"}, "SYNCHRONIZATION_STAGES": {"value": "2"}}, "interface_ports": {"S00_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "M00_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_RESET": {"value": "ARESETN"}}}, "ARESETN": {"type": "rst", "direction": "I"}, "S00_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S00_AXI"}, "ASSOCIATED_RESET": {"value": "S00_ARESETN"}}}, "S00_ARESETN": {"type": "rst", "direction": "I"}, "M00_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M00_AXI"}, "ASSOCIATED_RESET": {"value": "M00_ARESETN"}}}, "M00_ARESETN": {"type": "rst", "direction": "I"}}, "components": {"s00_couplers": {"interface_ports": {"M_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "S_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"M_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M_AXI"}, "ASSOCIATED_RESET": {"value": "M_ARESETN"}}}, "M_ARESETN": {"type": "rst", "direction": "I"}, "S_ACLK": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "S_AXI"}, "ASSOCIATED_RESET": {"value": "S_ARESETN"}}}, "S_ARESETN": {"type": "rst", "direction": "I"}}, "interface_nets": {"s00_couplers_to_s00_couplers": {"interface_ports": ["S_AXI", "M_AXI"]}}}}, "interface_nets": {"s00_couplers_to_axi_hp2_interconnect_upgraded_ipi": {"interface_ports": ["M00_AXI", "s00_couplers/M_AXI"]}, "axi_hp2_interconnect_upgraded_ipi_to_s00_couplers": {"interface_ports": ["S00_AXI", "s00_couplers/S_AXI"]}}, "nets": {"axi_hp2_interconnect_upgraded_ipi_ACLK_net": {"ports": ["M00_ACLK", "s00_couplers/M_ACLK"]}, "axi_hp2_interconnect_upgraded_ipi_ARESETN_net": {"ports": ["M00_ARESETN", "s00_couplers/M_ARESETN"]}, "S00_ACLK_1": {"ports": ["S00_ACLK", "s00_couplers/S_ACLK"]}, "S00_ARESETN_1": {"ports": ["S00_ARESETN", "s00_couplers/S_ARESETN"]}}}, "sys_concat_intc": {"vlnv": "xilinx.com:ip:xlconcat:2.1", "xci_name": "system_sys_concat_intc_0", "parameters": {"NUM_PORTS": {"value": "16"}, "dout_width": {"value": "16"}}}, "sys_ps7": {"vlnv": "xilinx.com:ip:processing_system7:5.5", "xci_name": "system_sys_ps7_0", "parameters": {"PCW_ACT_APU_PERIPHERAL_FREQMHZ": {"value": "675.000000"}, "PCW_ACT_CAN0_PERIPHERAL_FREQMHZ": {"value": "23.8095"}, "PCW_ACT_CAN1_PERIPHERAL_FREQMHZ": {"value": "23.8095"}, "PCW_ACT_CAN_PERIPHERAL_FREQMHZ": {"value": "10.000000"}, "PCW_ACT_DCI_PERIPHERAL_FREQMHZ": {"value": "10.096154"}, "PCW_ACT_ENET0_PERIPHERAL_FREQMHZ": {"value": "125.000000"}, "PCW_ACT_ENET1_PERIPHERAL_FREQMHZ": {"value": "10.000000"}, "PCW_ACT_FPGA0_PERIPHERAL_FREQMHZ": {"value": "100.000000"}, "PCW_ACT_FPGA1_PERIPHERAL_FREQMHZ": {"value": "200.000000"}, "PCW_ACT_FPGA2_PERIPHERAL_FREQMHZ": {"value": "10.000000"}, "PCW_ACT_FPGA3_PERIPHERAL_FREQMHZ": {"value": "10.000000"}, "PCW_ACT_I2C_PERIPHERAL_FREQMHZ": {"value": "50"}, "PCW_ACT_PCAP_PERIPHERAL_FREQMHZ": {"value": "200.000000"}, "PCW_ACT_QSPI_PERIPHERAL_FREQMHZ": {"value": "200.000000"}, "PCW_ACT_SDIO_PERIPHERAL_FREQMHZ": {"value": "10.000000"}, "PCW_ACT_SMC_PERIPHERAL_FREQMHZ": {"value": "10.000000"}, "PCW_ACT_SPI_PERIPHERAL_FREQMHZ": {"value": "166.666672"}, "PCW_ACT_TPIU_PERIPHERAL_FREQMHZ": {"value": "200.000000"}, "PCW_ACT_TTC0_CLK0_PERIPHERAL_FREQMHZ": {"value": "112.500000"}, "PCW_ACT_TTC0_CLK1_PERIPHERAL_FREQMHZ": {"value": "112.500000"}, "PCW_ACT_TTC0_CLK2_PERIPHERAL_FREQMHZ": {"value": "112.500000"}, "PCW_ACT_TTC1_CLK0_PERIPHERAL_FREQMHZ": {"value": "112.500000"}, "PCW_ACT_TTC1_CLK1_PERIPHERAL_FREQMHZ": {"value": "112.500000"}, "PCW_ACT_TTC1_CLK2_PERIPHERAL_FREQMHZ": {"value": "112.500000"}, "PCW_ACT_TTC_PERIPHERAL_FREQMHZ": {"value": "50"}, "PCW_ACT_UART_PERIPHERAL_FREQMHZ": {"value": "50.000000"}, "PCW_ACT_USB0_PERIPHERAL_FREQMHZ": {"value": "60"}, "PCW_ACT_USB1_PERIPHERAL_FREQMHZ": {"value": "60"}, "PCW_ACT_WDT_PERIPHERAL_FREQMHZ": {"value": "112.500000"}, "PCW_APU_CLK_RATIO_ENABLE": {"value": "6:2:1"}, "PCW_APU_PERIPHERAL_FREQMHZ": {"value": "666.666667"}, "PCW_CAN0_PERIPHERAL_CLKSRC": {"value": "External"}, "PCW_CAN0_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_CAN1_PERIPHERAL_CLKSRC": {"value": "External"}, "PCW_CAN1_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_CAN_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_CAN_PERIPHERAL_VALID": {"value": "0"}, "PCW_CLK0_FREQ": {"value": "100000000"}, "PCW_CLK1_FREQ": {"value": "200000000"}, "PCW_CLK2_FREQ": {"value": "10000000"}, "PCW_CLK3_FREQ": {"value": "10000000"}, "PCW_CORE0_FIQ_INTR": {"value": "0"}, "PCW_CORE0_IRQ_INTR": {"value": "0"}, "PCW_CORE1_FIQ_INTR": {"value": "0"}, "PCW_CORE1_IRQ_INTR": {"value": "0"}, "PCW_CPU_CPU_6X4X_MAX_RANGE": {"value": "800"}, "PCW_CPU_PERIPHERAL_CLKSRC": {"value": "ARM PLL"}, "PCW_CRYSTAL_PERIPHERAL_FREQMHZ": {"value": "50"}, "PCW_DCI_PERIPHERAL_CLKSRC": {"value": "DDR PLL"}, "PCW_DCI_PERIPHERAL_FREQMHZ": {"value": "10.159"}, "PCW_DDR_PERIPHERAL_CLKSRC": {"value": "DDR PLL"}, "PCW_DDR_RAM_BASEADDR": {"value": "0x00100000"}, "PCW_DDR_RAM_HIGHADDR": {"value": "0x3FFFFFFF"}, "PCW_DM_WIDTH": {"value": "4"}, "PCW_DQS_WIDTH": {"value": "4"}, "PCW_DQ_WIDTH": {"value": "32"}, "PCW_ENET0_BASEADDR": {"value": "0xE000B000"}, "PCW_ENET0_ENET0_IO": {"value": "MIO 16 .. 27"}, "PCW_ENET0_GRP_MDIO_ENABLE": {"value": "0"}, "PCW_ENET0_HIGHADDR": {"value": "0xE000BFFF"}, "PCW_ENET0_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_ENET0_PERIPHERAL_ENABLE": {"value": "1"}, "PCW_ENET0_PERIPHERAL_FREQMHZ": {"value": "1000 Mbps"}, "PCW_ENET0_RESET_ENABLE": {"value": "1"}, "PCW_ENET0_RESET_IO": {"value": "MIO 40"}, "PCW_ENET1_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_ENET1_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_ENET_RESET_ENABLE": {"value": "1"}, "PCW_ENET_RESET_POLARITY": {"value": "Active Low"}, "PCW_ENET_RESET_SELECT": {"value": "Share reset pin"}, "PCW_EN_4K_TIMER": {"value": "0"}, "PCW_EN_CAN0": {"value": "0"}, "PCW_EN_CAN1": {"value": "0"}, "PCW_EN_CLK0_PORT": {"value": "1"}, "PCW_EN_CLK1_PORT": {"value": "1"}, "PCW_EN_CLK2_PORT": {"value": "0"}, "PCW_EN_CLK3_PORT": {"value": "0"}, "PCW_EN_CLKTRIG0_PORT": {"value": "0"}, "PCW_EN_CLKTRIG1_PORT": {"value": "0"}, "PCW_EN_CLKTRIG2_PORT": {"value": "0"}, "PCW_EN_CLKTRIG3_PORT": {"value": "0"}, "PCW_EN_DDR": {"value": "1"}, "PCW_EN_EMIO_CAN0": {"value": "0"}, "PCW_EN_EMIO_CAN1": {"value": "0"}, "PCW_EN_EMIO_CD_SDIO0": {"value": "0"}, "PCW_EN_EMIO_CD_SDIO1": {"value": "0"}, "PCW_EN_EMIO_ENET0": {"value": "0"}, "PCW_EN_EMIO_ENET1": {"value": "0"}, "PCW_EN_EMIO_GPIO": {"value": "1"}, "PCW_EN_EMIO_I2C0": {"value": "0"}, "PCW_EN_EMIO_I2C1": {"value": "0"}, "PCW_EN_EMIO_MODEM_UART0": {"value": "0"}, "PCW_EN_EMIO_MODEM_UART1": {"value": "0"}, "PCW_EN_EMIO_PJTAG": {"value": "0"}, "PCW_EN_EMIO_SDIO0": {"value": "0"}, "PCW_EN_EMIO_SDIO1": {"value": "0"}, "PCW_EN_EMIO_SPI0": {"value": "1"}, "PCW_EN_EMIO_SPI1": {"value": "1"}, "PCW_EN_EMIO_SRAM_INT": {"value": "0"}, "PCW_EN_EMIO_TRACE": {"value": "0"}, "PCW_EN_EMIO_TTC0": {"value": "0"}, "PCW_EN_EMIO_TTC1": {"value": "0"}, "PCW_EN_EMIO_UART0": {"value": "0"}, "PCW_EN_EMIO_UART1": {"value": "0"}, "PCW_EN_EMIO_WDT": {"value": "0"}, "PCW_EN_EMIO_WP_SDIO0": {"value": "0"}, "PCW_EN_EMIO_WP_SDIO1": {"value": "0"}, "PCW_EN_ENET0": {"value": "1"}, "PCW_EN_ENET1": {"value": "0"}, "PCW_EN_GPIO": {"value": "1"}, "PCW_EN_I2C0": {"value": "0"}, "PCW_EN_I2C1": {"value": "0"}, "PCW_EN_MODEM_UART0": {"value": "0"}, "PCW_EN_MODEM_UART1": {"value": "0"}, "PCW_EN_PJTAG": {"value": "0"}, "PCW_EN_PTP_ENET0": {"value": "0"}, "PCW_EN_PTP_ENET1": {"value": "0"}, "PCW_EN_QSPI": {"value": "1"}, "PCW_EN_RST0_PORT": {"value": "1"}, "PCW_EN_RST1_PORT": {"value": "1"}, "PCW_EN_RST2_PORT": {"value": "0"}, "PCW_EN_RST3_PORT": {"value": "0"}, "PCW_EN_SDIO0": {"value": "0"}, "PCW_EN_SDIO1": {"value": "0"}, "PCW_EN_SMC": {"value": "0"}, "PCW_EN_SPI0": {"value": "1"}, "PCW_EN_SPI1": {"value": "1"}, "PCW_EN_TRACE": {"value": "0"}, "PCW_EN_TTC0": {"value": "0"}, "PCW_EN_TTC1": {"value": "0"}, "PCW_EN_UART0": {"value": "1"}, "PCW_EN_UART1": {"value": "0"}, "PCW_EN_USB0": {"value": "0"}, "PCW_EN_USB1": {"value": "0"}, "PCW_EN_WDT": {"value": "0"}, "PCW_FCLK0_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_FCLK1_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_FCLK2_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_FCLK3_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_FCLK_CLK0_BUF": {"value": "TRUE"}, "PCW_FCLK_CLK1_BUF": {"value": "TRUE"}, "PCW_FPGA0_PERIPHERAL_FREQMHZ": {"value": "100.0"}, "PCW_FPGA1_PERIPHERAL_FREQMHZ": {"value": "200.0"}, "PCW_FPGA2_PERIPHERAL_FREQMHZ": {"value": "50"}, "PCW_FPGA3_PERIPHERAL_FREQMHZ": {"value": "50"}, "PCW_FPGA_FCLK0_ENABLE": {"value": "1"}, "PCW_FPGA_FCLK1_ENABLE": {"value": "1"}, "PCW_GP0_EN_MODIFIABLE_TXN": {"value": "0"}, "PCW_GP0_NUM_READ_THREADS": {"value": "4"}, "PCW_GP0_NUM_WRITE_THREADS": {"value": "4"}, "PCW_GP1_EN_MODIFIABLE_TXN": {"value": "0"}, "PCW_GP1_NUM_READ_THREADS": {"value": "4"}, "PCW_GP1_NUM_WRITE_THREADS": {"value": "4"}, "PCW_GPIO_BASEADDR": {"value": "0xE000A000"}, "PCW_GPIO_EMIO_GPIO_ENABLE": {"value": "1"}, "PCW_GPIO_EMIO_GPIO_IO": {"value": "64"}, "PCW_GPIO_EMIO_GPIO_WIDTH": {"value": "64"}, "PCW_GPIO_HIGHADDR": {"value": "0xE000AFFF"}, "PCW_GPIO_MIO_GPIO_ENABLE": {"value": "1"}, "PCW_GPIO_MIO_GPIO_IO": {"value": "MIO"}, "PCW_GPIO_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_I2C0_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_I2C1_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_I2C_RESET_ENABLE": {"value": "1"}, "PCW_I2C_RESET_POLARITY": {"value": "Active Low"}, "PCW_IMPORT_BOARD_PRESET": {"value": "ZedBoard"}, "PCW_INCLUDE_ACP_TRANS_CHECK": {"value": "0"}, "PCW_IRQ_F2P_INTR": {"value": "1"}, "PCW_IRQ_F2P_MODE": {"value": "REVERSE"}, "PCW_MIO_0_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_0_PULLUP": {"value": "disabled"}, "PCW_MIO_0_SLEW": {"value": "slow"}, "PCW_MIO_10_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_10_PULLUP": {"value": "disabled"}, "PCW_MIO_10_SLEW": {"value": "slow"}, "PCW_MIO_11_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_11_PULLUP": {"value": "disabled"}, "PCW_MIO_11_SLEW": {"value": "slow"}, "PCW_MIO_12_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_12_PULLUP": {"value": "disabled"}, "PCW_MIO_12_SLEW": {"value": "slow"}, "PCW_MIO_13_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_13_PULLUP": {"value": "disabled"}, "PCW_MIO_13_SLEW": {"value": "slow"}, "PCW_MIO_14_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_14_PULLUP": {"value": "disabled"}, "PCW_MIO_14_SLEW": {"value": "slow"}, "PCW_MIO_15_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_15_PULLUP": {"value": "disabled"}, "PCW_MIO_15_SLEW": {"value": "slow"}, "PCW_MIO_16_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_16_PULLUP": {"value": "disabled"}, "PCW_MIO_16_SLEW": {"value": "fast"}, "PCW_MIO_17_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_17_PULLUP": {"value": "disabled"}, "PCW_MIO_17_SLEW": {"value": "fast"}, "PCW_MIO_18_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_18_PULLUP": {"value": "disabled"}, "PCW_MIO_18_SLEW": {"value": "fast"}, "PCW_MIO_19_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_19_PULLUP": {"value": "disabled"}, "PCW_MIO_19_SLEW": {"value": "fast"}, "PCW_MIO_1_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_1_PULLUP": {"value": "disabled"}, "PCW_MIO_1_SLEW": {"value": "fast"}, "PCW_MIO_20_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_20_PULLUP": {"value": "disabled"}, "PCW_MIO_20_SLEW": {"value": "fast"}, "PCW_MIO_21_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_21_PULLUP": {"value": "disabled"}, "PCW_MIO_21_SLEW": {"value": "fast"}, "PCW_MIO_22_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_22_PULLUP": {"value": "disabled"}, "PCW_MIO_22_SLEW": {"value": "fast"}, "PCW_MIO_23_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_23_PULLUP": {"value": "disabled"}, "PCW_MIO_23_SLEW": {"value": "fast"}, "PCW_MIO_24_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_24_PULLUP": {"value": "disabled"}, "PCW_MIO_24_SLEW": {"value": "fast"}, "PCW_MIO_25_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_25_PULLUP": {"value": "disabled"}, "PCW_MIO_25_SLEW": {"value": "fast"}, "PCW_MIO_26_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_26_PULLUP": {"value": "disabled"}, "PCW_MIO_26_SLEW": {"value": "fast"}, "PCW_MIO_27_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_27_PULLUP": {"value": "disabled"}, "PCW_MIO_27_SLEW": {"value": "fast"}, "PCW_MIO_28_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_28_PULLUP": {"value": "disabled"}, "PCW_MIO_28_SLEW": {"value": "fast"}, "PCW_MIO_29_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_29_PULLUP": {"value": "disabled"}, "PCW_MIO_29_SLEW": {"value": "fast"}, "PCW_MIO_2_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_2_SLEW": {"value": "fast"}, "PCW_MIO_30_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_30_PULLUP": {"value": "disabled"}, "PCW_MIO_30_SLEW": {"value": "fast"}, "PCW_MIO_31_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_31_PULLUP": {"value": "disabled"}, "PCW_MIO_31_SLEW": {"value": "fast"}, "PCW_MIO_32_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_32_PULLUP": {"value": "disabled"}, "PCW_MIO_32_SLEW": {"value": "fast"}, "PCW_MIO_33_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_33_PULLUP": {"value": "disabled"}, "PCW_MIO_33_SLEW": {"value": "fast"}, "PCW_MIO_34_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_34_PULLUP": {"value": "disabled"}, "PCW_MIO_34_SLEW": {"value": "fast"}, "PCW_MIO_35_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_35_PULLUP": {"value": "disabled"}, "PCW_MIO_35_SLEW": {"value": "fast"}, "PCW_MIO_36_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_36_PULLUP": {"value": "disabled"}, "PCW_MIO_36_SLEW": {"value": "fast"}, "PCW_MIO_37_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_37_PULLUP": {"value": "disabled"}, "PCW_MIO_37_SLEW": {"value": "fast"}, "PCW_MIO_38_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_38_PULLUP": {"value": "disabled"}, "PCW_MIO_38_SLEW": {"value": "fast"}, "PCW_MIO_39_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_39_PULLUP": {"value": "disabled"}, "PCW_MIO_39_SLEW": {"value": "fast"}, "PCW_MIO_3_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_3_SLEW": {"value": "fast"}, "PCW_MIO_40_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_40_PULLUP": {"value": "disabled"}, "PCW_MIO_40_SLEW": {"value": "fast"}, "PCW_MIO_41_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_41_PULLUP": {"value": "disabled"}, "PCW_MIO_41_SLEW": {"value": "fast"}, "PCW_MIO_42_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_42_PULLUP": {"value": "disabled"}, "PCW_MIO_42_SLEW": {"value": "fast"}, "PCW_MIO_43_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_43_PULLUP": {"value": "disabled"}, "PCW_MIO_43_SLEW": {"value": "fast"}, "PCW_MIO_44_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_44_PULLUP": {"value": "disabled"}, "PCW_MIO_44_SLEW": {"value": "fast"}, "PCW_MIO_45_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_45_PULLUP": {"value": "disabled"}, "PCW_MIO_45_SLEW": {"value": "fast"}, "PCW_MIO_46_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_46_PULLUP": {"value": "disabled"}, "PCW_MIO_46_SLEW": {"value": "slow"}, "PCW_MIO_47_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_47_PULLUP": {"value": "disabled"}, "PCW_MIO_47_SLEW": {"value": "slow"}, "PCW_MIO_48_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_48_PULLUP": {"value": "disabled"}, "PCW_MIO_48_SLEW": {"value": "slow"}, "PCW_MIO_49_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_49_PULLUP": {"value": "disabled"}, "PCW_MIO_49_SLEW": {"value": "slow"}, "PCW_MIO_4_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_4_SLEW": {"value": "fast"}, "PCW_MIO_50_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_50_PULLUP": {"value": "disabled"}, "PCW_MIO_50_SLEW": {"value": "slow"}, "PCW_MIO_51_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_51_PULLUP": {"value": "disabled"}, "PCW_MIO_51_SLEW": {"value": "slow"}, "PCW_MIO_52_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_52_PULLUP": {"value": "disabled"}, "PCW_MIO_52_SLEW": {"value": "slow"}, "PCW_MIO_53_IOTYPE": {"value": "LVCMOS 2.5V"}, "PCW_MIO_53_PULLUP": {"value": "disabled"}, "PCW_MIO_53_SLEW": {"value": "slow"}, "PCW_MIO_5_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_5_SLEW": {"value": "fast"}, "PCW_MIO_6_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_6_SLEW": {"value": "fast"}, "PCW_MIO_7_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_7_SLEW": {"value": "slow"}, "PCW_MIO_8_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_8_SLEW": {"value": "fast"}, "PCW_MIO_9_IOTYPE": {"value": "LVCMOS 3.3V"}, "PCW_MIO_9_PULLUP": {"value": "disabled"}, "PCW_MIO_9_SLEW": {"value": "slow"}, "PCW_MIO_PRIMITIVE": {"value": "54"}, "PCW_MIO_TREE_PERIPHERALS": {"value": "GPIO#Quad SPI Flash#Quad SPI Flash#Quad SPI Flash#Quad SPI Flash#Quad SPI Flash#Quad SPI Flash#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#UART 0#UART 0#Enet 0#Enet 0#Enet 0#Enet 0#Enet 0#Enet 0#Enet 0#Enet 0#Enet 0#Enet 0#Enet 0#Enet 0#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#ENET Reset#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO#GPIO"}, "PCW_MIO_TREE_SIGNALS": {"value": "gpio[0]#qspi0_ss_b#qspi0_io[0]#qspi0_io[1]#qspi0_io[2]#qspi0_io[3]/HOLD_B#qspi0_sclk#gpio[7]#gpio[8]#gpio[9]#gpio[10]#gpio[11]#gpio[12]#gpio[13]#rx#tx#tx_clk#txd[0]#txd[1]#txd[2]#txd[3]#tx_ctl#rx_clk#rxd[0]#rxd[1]#rxd[2]#rxd[3]#rx_ctl#gpio[28]#gpio[29]#gpio[30]#gpio[31]#gpio[32]#gpio[33]#gpio[34]#gpio[35]#gpio[36]#gpio[37]#gpio[38]#gpio[39]#reset#gpio[41]#gpio[42]#gpio[43]#gpio[44]#gpio[45]#gpio[46]#gpio[47]#gpio[48]#gpio[49]#gpio[50]#gpio[51]#gpio[52]#gpio[53]"}, "PCW_M_AXI_GP0_ENABLE_STATIC_REMAP": {"value": "0"}, "PCW_M_AXI_GP0_ID_WIDTH": {"value": "12"}, "PCW_M_AXI_GP0_SUPPORT_NARROW_BURST": {"value": "0"}, "PCW_M_AXI_GP0_THREAD_ID_WIDTH": {"value": "12"}, "PCW_NAND_CYCLES_T_AR": {"value": "1"}, "PCW_NAND_CYCLES_T_CLR": {"value": "1"}, "PCW_NAND_CYCLES_T_RC": {"value": "11"}, "PCW_NAND_CYCLES_T_REA": {"value": "1"}, "PCW_NAND_CYCLES_T_RR": {"value": "1"}, "PCW_NAND_CYCLES_T_WC": {"value": "11"}, "PCW_NAND_CYCLES_T_WP": {"value": "1"}, "PCW_NOR_CS0_T_CEOE": {"value": "1"}, "PCW_NOR_CS0_T_PC": {"value": "1"}, "PCW_NOR_CS0_T_RC": {"value": "11"}, "PCW_NOR_CS0_T_TR": {"value": "1"}, "PCW_NOR_CS0_T_WC": {"value": "11"}, "PCW_NOR_CS0_T_WP": {"value": "1"}, "PCW_NOR_CS0_WE_TIME": {"value": "0"}, "PCW_NOR_CS1_T_CEOE": {"value": "1"}, "PCW_NOR_CS1_T_PC": {"value": "1"}, "PCW_NOR_CS1_T_RC": {"value": "11"}, "PCW_NOR_CS1_T_TR": {"value": "1"}, "PCW_NOR_CS1_T_WC": {"value": "11"}, "PCW_NOR_CS1_T_WP": {"value": "1"}, "PCW_NOR_CS1_WE_TIME": {"value": "0"}, "PCW_NOR_SRAM_CS0_T_CEOE": {"value": "1"}, "PCW_NOR_SRAM_CS0_T_PC": {"value": "1"}, "PCW_NOR_SRAM_CS0_T_RC": {"value": "11"}, "PCW_NOR_SRAM_CS0_T_TR": {"value": "1"}, "PCW_NOR_SRAM_CS0_T_WC": {"value": "11"}, "PCW_NOR_SRAM_CS0_T_WP": {"value": "1"}, "PCW_NOR_SRAM_CS0_WE_TIME": {"value": "0"}, "PCW_NOR_SRAM_CS1_T_CEOE": {"value": "1"}, "PCW_NOR_SRAM_CS1_T_PC": {"value": "1"}, "PCW_NOR_SRAM_CS1_T_RC": {"value": "11"}, "PCW_NOR_SRAM_CS1_T_TR": {"value": "1"}, "PCW_NOR_SRAM_CS1_T_WC": {"value": "11"}, "PCW_NOR_SRAM_CS1_T_WP": {"value": "1"}, "PCW_NOR_SRAM_CS1_WE_TIME": {"value": "0"}, "PCW_OVERRIDE_BASIC_CLOCK": {"value": "0"}, "PCW_P2F_DMAC0_INTR": {"value": "0"}, "PCW_P2F_DMAC1_INTR": {"value": "0"}, "PCW_P2F_DMAC2_INTR": {"value": "0"}, "PCW_P2F_DMAC3_INTR": {"value": "0"}, "PCW_P2F_DMAC4_INTR": {"value": "0"}, "PCW_P2F_DMAC5_INTR": {"value": "0"}, "PCW_P2F_DMAC6_INTR": {"value": "0"}, "PCW_P2F_DMAC7_INTR": {"value": "0"}, "PCW_P2F_DMAC_ABORT_INTR": {"value": "0"}, "PCW_P2F_ENET0_INTR": {"value": "0"}, "PCW_P2F_GPIO_INTR": {"value": "0"}, "PCW_P2F_QSPI_INTR": {"value": "0"}, "PCW_P2F_SPI0_INTR": {"value": "0"}, "PCW_P2F_SPI1_INTR": {"value": "0"}, "PCW_P2F_UART0_INTR": {"value": "0"}, "PCW_PACKAGE_DDR_BOARD_DELAY0": {"value": "0.063"}, "PCW_PACKAGE_DDR_BOARD_DELAY1": {"value": "0.062"}, "PCW_PACKAGE_DDR_BOARD_DELAY2": {"value": "0.065"}, "PCW_PACKAGE_DDR_BOARD_DELAY3": {"value": "0.083"}, "PCW_PACKAGE_DDR_DQS_TO_CLK_DELAY_0": {"value": "-0.007"}, "PCW_PACKAGE_DDR_DQS_TO_CLK_DELAY_1": {"value": "-0.010"}, "PCW_PACKAGE_DDR_DQS_TO_CLK_DELAY_2": {"value": "-0.006"}, "PCW_PACKAGE_DDR_DQS_TO_CLK_DELAY_3": {"value": "-0.048"}, "PCW_PACKAGE_NAME": {"value": "clg484"}, "PCW_PCAP_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_PCAP_PERIPHERAL_FREQMHZ": {"value": "200"}, "PCW_PERIPHERAL_BOARD_PRESET": {"value": "part0"}, "PCW_PJTAG_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_PLL_BYPASSMODE_ENABLE": {"value": "0"}, "PCW_PRESET_BANK0_VOLTAGE": {"value": "LVCMOS 3.3V"}, "PCW_PRESET_BANK1_VOLTAGE": {"value": "LVCMOS 2.5V"}, "PCW_PS7_SI_REV": {"value": "PRODUCTION"}, "PCW_QSPI_GRP_FBCLK_ENABLE": {"value": "0"}, "PCW_QSPI_GRP_IO1_ENABLE": {"value": "0"}, "PCW_QSPI_GRP_SINGLE_SS_ENABLE": {"value": "1"}, "PCW_QSPI_GRP_SINGLE_SS_IO": {"value": "MIO 1 .. 6"}, "PCW_QSPI_GRP_SS1_ENABLE": {"value": "0"}, "PCW_QSPI_INTERNAL_HIGHADDRESS": {"value": "0xFCFFFFFF"}, "PCW_QSPI_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_QSPI_PERIPHERAL_ENABLE": {"value": "1"}, "PCW_QSPI_PERIPHERAL_FREQMHZ": {"value": "200"}, "PCW_QSPI_QSPI_IO": {"value": "MIO 1 .. 6"}, "PCW_SD0_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_SD1_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_SDIO_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_SDIO_PERIPHERAL_VALID": {"value": "0"}, "PCW_SINGLE_QSPI_DATA_MODE": {"value": "x4"}, "PCW_SMC_CYCLE_T0": {"value": "NA"}, "PCW_SMC_CYCLE_T1": {"value": "NA"}, "PCW_SMC_CYCLE_T2": {"value": "NA"}, "PCW_SMC_CYCLE_T3": {"value": "NA"}, "PCW_SMC_CYCLE_T4": {"value": "NA"}, "PCW_SMC_CYCLE_T5": {"value": "NA"}, "PCW_SMC_CYCLE_T6": {"value": "NA"}, "PCW_SMC_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_SMC_PERIPHERAL_VALID": {"value": "0"}, "PCW_SPI0_BASEADDR": {"value": "0xE0006000"}, "PCW_SPI0_HIGHADDR": {"value": "0xE0006FFF"}, "PCW_SPI0_PERIPHERAL_ENABLE": {"value": "1"}, "PCW_SPI0_SPI0_IO": {"value": "EMIO"}, "PCW_SPI1_BASEADDR": {"value": "0xE0007000"}, "PCW_SPI1_HIGHADDR": {"value": "0xE0007FFF"}, "PCW_SPI1_PERIPHERAL_ENABLE": {"value": "1"}, "PCW_SPI1_SPI1_IO": {"value": "EMIO"}, "PCW_SPI_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_SPI_PERIPHERAL_FREQMHZ": {"value": "166.666666"}, "PCW_SPI_PERIPHERAL_VALID": {"value": "1"}, "PCW_S_AXI_HP0_DATA_WIDTH": {"value": "64"}, "PCW_S_AXI_HP0_ID_WIDTH": {"value": "6"}, "PCW_S_AXI_HP1_DATA_WIDTH": {"value": "64"}, "PCW_S_AXI_HP1_ID_WIDTH": {"value": "6"}, "PCW_S_AXI_HP2_DATA_WIDTH": {"value": "64"}, "PCW_S_AXI_HP2_ID_WIDTH": {"value": "6"}, "PCW_TPIU_PERIPHERAL_CLKSRC": {"value": "External"}, "PCW_TRACE_INTERNAL_WIDTH": {"value": "2"}, "PCW_TRACE_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_TTC0_CLK0_PERIPHERAL_CLKSRC": {"value": "CPU_1X"}, "PCW_TTC0_CLK0_PERIPHERAL_DIVISOR0": {"value": "1"}, "PCW_TTC0_CLK1_PERIPHERAL_CLKSRC": {"value": "CPU_1X"}, "PCW_TTC0_CLK1_PERIPHERAL_DIVISOR0": {"value": "1"}, "PCW_TTC0_CLK2_PERIPHERAL_CLKSRC": {"value": "CPU_1X"}, "PCW_TTC0_CLK2_PERIPHERAL_DIVISOR0": {"value": "1"}, "PCW_TTC0_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_TTC1_CLK0_PERIPHERAL_CLKSRC": {"value": "CPU_1X"}, "PCW_TTC1_CLK0_PERIPHERAL_DIVISOR0": {"value": "1"}, "PCW_TTC1_CLK1_PERIPHERAL_CLKSRC": {"value": "CPU_1X"}, "PCW_TTC1_CLK1_PERIPHERAL_DIVISOR0": {"value": "1"}, "PCW_TTC1_CLK2_PERIPHERAL_CLKSRC": {"value": "CPU_1X"}, "PCW_TTC1_CLK2_PERIPHERAL_DIVISOR0": {"value": "1"}, "PCW_TTC1_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_UART0_BASEADDR": {"value": "0xE0000000"}, "PCW_UART0_BAUD_RATE": {"value": "115200"}, "PCW_UART0_GRP_FULL_ENABLE": {"value": "0"}, "PCW_UART0_HIGHADDR": {"value": "0xE0000FFF"}, "PCW_UART0_PERIPHERAL_ENABLE": {"value": "1"}, "PCW_UART0_UART0_IO": {"value": "MIO 14 .. 15"}, "PCW_UART1_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_UART_PERIPHERAL_CLKSRC": {"value": "IO PLL"}, "PCW_UART_PERIPHERAL_FREQMHZ": {"value": "50"}, "PCW_UART_PERIPHERAL_VALID": {"value": "1"}, "PCW_UIPARAM_ACT_DDR_FREQ_MHZ": {"value": "525.000000"}, "PCW_UIPARAM_DDR_ADV_ENABLE": {"value": "0"}, "PCW_UIPARAM_DDR_AL": {"value": "0"}, "PCW_UIPARAM_DDR_BL": {"value": "8"}, "PCW_UIPARAM_DDR_BOARD_DELAY0": {"value": "0.41"}, "PCW_UIPARAM_DDR_BOARD_DELAY1": {"value": "0.411"}, "PCW_UIPARAM_DDR_BOARD_DELAY2": {"value": "0.341"}, "PCW_UIPARAM_DDR_BOARD_DELAY3": {"value": "0.358"}, "PCW_UIPARAM_DDR_BUS_WIDTH": {"value": "32 Bit"}, "PCW_UIPARAM_DDR_CLOCK_0_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_CLOCK_0_PACKAGE_LENGTH": {"value": "61.0905"}, "PCW_UIPARAM_DDR_CLOCK_0_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_CLOCK_1_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_CLOCK_1_PACKAGE_LENGTH": {"value": "61.0905"}, "PCW_UIPARAM_DDR_CLOCK_1_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_CLOCK_2_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_CLOCK_2_PACKAGE_LENGTH": {"value": "61.0905"}, "PCW_UIPARAM_DDR_CLOCK_2_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_CLOCK_3_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_CLOCK_3_PACKAGE_LENGTH": {"value": "61.0905"}, "PCW_UIPARAM_DDR_CLOCK_3_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_CLOCK_STOP_EN": {"value": "0"}, "PCW_UIPARAM_DDR_DQS_0_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_DQS_0_PACKAGE_LENGTH": {"value": "68.4725"}, "PCW_UIPARAM_DDR_DQS_0_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_DQS_1_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_DQS_1_PACKAGE_LENGTH": {"value": "71.086"}, "PCW_UIPARAM_DDR_DQS_1_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_DQS_2_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_DQS_2_PACKAGE_LENGTH": {"value": "66.794"}, "PCW_UIPARAM_DDR_DQS_2_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_DQS_3_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_DQS_3_PACKAGE_LENGTH": {"value": "108.7385"}, "PCW_UIPARAM_DDR_DQS_3_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_0": {"value": "0.025"}, "PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_1": {"value": "0.028"}, "PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_2": {"value": "-0.009"}, "PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_3": {"value": "-0.061"}, "PCW_UIPARAM_DDR_DQ_0_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_DQ_0_PACKAGE_LENGTH": {"value": "64.1705"}, "PCW_UIPARAM_DDR_DQ_0_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_DQ_1_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_DQ_1_PACKAGE_LENGTH": {"value": "63.686"}, "PCW_UIPARAM_DDR_DQ_1_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_DQ_2_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_DQ_2_PACKAGE_LENGTH": {"value": "68.46"}, "PCW_UIPARAM_DDR_DQ_2_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_DQ_3_LENGTH_MM": {"value": "0"}, "PCW_UIPARAM_DDR_DQ_3_PACKAGE_LENGTH": {"value": "105.4895"}, "PCW_UIPARAM_DDR_DQ_3_PROPOGATION_DELAY": {"value": "160"}, "PCW_UIPARAM_DDR_ENABLE": {"value": "1"}, "PCW_UIPARAM_DDR_FREQ_MHZ": {"value": "533.333313"}, "PCW_UIPARAM_DDR_HIGH_TEMP": {"value": "Normal (0-85)"}, "PCW_UIPARAM_DDR_MEMORY_TYPE": {"value": "DDR 3"}, "PCW_UIPARAM_DDR_PARTNO": {"value": "MT41K256M16 RE-125"}, "PCW_UIPARAM_DDR_TRAIN_DATA_EYE": {"value": "1"}, "PCW_UIPARAM_DDR_TRAIN_READ_GATE": {"value": "1"}, "PCW_UIPARAM_DDR_TRAIN_WRITE_LEVEL": {"value": "1"}, "PCW_UIPARAM_DDR_USE_INTERNAL_VREF": {"value": "1"}, "PCW_UIPARAM_GENERATE_SUMMARY": {"value": "NA"}, "PCW_USB0_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_USB1_PERIPHERAL_ENABLE": {"value": "0"}, "PCW_USB_RESET_ENABLE": {"value": "1"}, "PCW_USB_RESET_POLARITY": {"value": "Active Low"}, "PCW_USE_AXI_FABRIC_IDLE": {"value": "0"}, "PCW_USE_AXI_NONSECURE": {"value": "0"}, "PCW_USE_CORESIGHT": {"value": "0"}, "PCW_USE_CROSS_TRIGGER": {"value": "0"}, "PCW_USE_CR_FABRIC": {"value": "1"}, "PCW_USE_DDR_BYPASS": {"value": "0"}, "PCW_USE_DEBUG": {"value": "0"}, "PCW_USE_DMA0": {"value": "1"}, "PCW_USE_DMA1": {"value": "1"}, "PCW_USE_DMA2": {"value": "1"}, "PCW_USE_DMA3": {"value": "0"}, "PCW_USE_EXPANDED_IOP": {"value": "0"}, "PCW_USE_FABRIC_INTERRUPT": {"value": "1"}, "PCW_USE_HIGH_OCM": {"value": "0"}, "PCW_USE_M_AXI_GP0": {"value": "1"}, "PCW_USE_M_AXI_GP1": {"value": "0"}, "PCW_USE_PROC_EVENT_BUS": {"value": "0"}, "PCW_USE_PS_SLCR_REGISTERS": {"value": "0"}, "PCW_USE_S_AXI_ACP": {"value": "0"}, "PCW_USE_S_AXI_GP0": {"value": "0"}, "PCW_USE_S_AXI_GP1": {"value": "0"}, "PCW_USE_S_AXI_HP0": {"value": "1"}, "PCW_USE_S_AXI_HP1": {"value": "1"}, "PCW_USE_S_AXI_HP2": {"value": "1"}, "PCW_USE_S_AXI_HP3": {"value": "0"}, "PCW_USE_TRACE": {"value": "0"}, "PCW_VALUE_SILVERSION": {"value": "3"}, "PCW_WDT_PERIPHERAL_CLKSRC": {"value": "CPU_1X"}, "PCW_WDT_PERIPHERAL_DIVISOR0": {"value": "1"}, "PCW_WDT_PERIPHERAL_ENABLE": {"value": "0"}}}, "sys_rstgen": {"vlnv": "xilinx.com:ip:proc_sys_reset:5.0", "xci_name": "system_sys_rstgen_0", "parameters": {"C_EXT_RST_WIDTH": {"value": "1"}}}, "util_ad9361_divclk_reset": {"vlnv": "xilinx.com:ip:proc_sys_reset:5.0", "xci_name": "system_util_ad9361_divclk_reset_0"}, "util_ad9361_divclk_sel": {"vlnv": "xilinx.com:ip:util_reduced_logic:2.0", "xci_name": "system_util_ad9361_divclk_sel_0", "parameters": {"C_SIZE": {"value": "2"}}}, "util_ad9361_divclk_sel_concat": {"vlnv": "xilinx.com:ip:xlconcat:2.1", "xci_name": "system_util_ad9361_divclk_sel_concat_0", "parameters": {"NUM_PORTS": {"value": "2"}}}}, "interface_nets": {"S00_AXI_4": {"interface_ports": ["axi_hp2_interconnect/S00_AXI", "axi_ad9361_dac_dma/m_src_axi"]}, "sys_ps7_FIXED_IO": {"interface_ports": ["fixed_io", "sys_ps7/FIXED_IO"]}, "sys_ps7_DDR": {"interface_ports": ["ddr", "sys_ps7/DDR"]}, "axi_hp0_interconnect_M00_AXI": {"interface_ports": ["axi_hp0_interconnect/M00_AXI", "sys_ps7/S_AXI_HP0"]}, "axi_cpu_interconnect_M07_AXI": {"interface_ports": ["axi_cpu_interconnect/M07_AXI", "axi_ad9361/s_axi"]}, "S00_AXI_1": {"interface_ports": ["axi_cpu_interconnect/S00_AXI", "sys_ps7/M_AXI_GP0"]}, "axi_hp2_interconnect_M00_AXI": {"interface_ports": ["axi_hp2_interconnect/M00_AXI", "sys_ps7/S_AXI_HP2"]}, "axi_cpu_interconnect_M08_AXI": {"interface_ports": ["axi_cpu_interconnect/M08_AXI", "axi_ad9361_adc_dma/s_axi"]}, "axi_hp1_interconnect_M00_AXI": {"interface_ports": ["axi_hp1_interconnect/M00_AXI", "sys_ps7/S_AXI_HP1"]}, "axi_cpu_interconnect_M09_AXI": {"interface_ports": ["axi_cpu_interconnect/M09_AXI", "axi_ad9361_dac_dma/s_axi"]}, "S00_AXI_3": {"interface_ports": ["axi_hp1_interconnect/S00_AXI", "axi_ad9361_adc_dma/m_dest_axi"]}}, "nets": {"sys_cpu_clk": {"ports": ["sys_ps7/FCLK_CLK0", "FCLK_CLK0", "axi_ad9361/s_axi_aclk", "axi_ad9361_adc_dma/s_axi_aclk", "axi_ad9361_adc_dma/m_dest_axi_aclk", "axi_ad9361_dac_dma/s_axi_aclk", "axi_ad9361_dac_dma/m_src_axi_aclk", "util_ad9361_tdd_sync/clk", "axi_cpu_interconnect/ACLK", "axi_cpu_interconnect/S00_ACLK", "axi_cpu_interconnect/M00_ACLK", "axi_cpu_interconnect/M01_ACLK", "axi_cpu_interconnect/M02_ACLK", "axi_cpu_interconnect/M03_ACLK", "axi_cpu_interconnect/M04_ACLK", "axi_cpu_interconnect/M05_ACLK", "axi_cpu_interconnect/M06_ACLK", "axi_cpu_interconnect/M07_ACLK", "axi_cpu_interconnect/M08_ACLK", "axi_cpu_interconnect/M09_ACLK", "axi_hp0_interconnect/ACLK", "axi_hp0_interconnect/S00_ACLK", "axi_hp0_interconnect/M00_ACLK", "axi_hp1_interconnect/ACLK", "axi_hp1_interconnect/S00_ACLK", "axi_hp1_interconnect/M00_ACLK", "axi_hp2_interconnect/ACLK", "axi_hp2_interconnect/S00_ACLK", "axi_hp2_interconnect/M00_ACLK", "sys_ps7/M_AXI_GP0_ACLK", "sys_ps7/S_AXI_HP0_ACLK", "sys_ps7/S_AXI_HP1_ACLK", "sys_ps7/S_AXI_HP2_ACLK", "sys_ps7/DMA0_ACLK", "sys_ps7/DMA1_ACLK", "sys_ps7/DMA2_ACLK", "sys_rstgen/slowest_sync_clk"]}, "sys_200m_clk": {"ports": ["sys_ps7/FCLK_CLK1", "axi_ad9361/delay_clk"]}, "sys_cpu_reset": {"ports": ["sys_rstgen/peripheral_reset"]}, "sys_cpu_resetn": {"ports": ["sys_rstgen/peripheral_aresetn", "axi_ad9361/s_axi_aresetn", "axi_ad9361_adc_dma/s_axi_aresetn", "axi_ad9361_adc_dma/m_dest_axi_aresetn", "axi_ad9361_dac_dma/s_axi_aresetn", "axi_ad9361_dac_dma/m_src_axi_aresetn", "util_ad9361_tdd_sync/rstn", "axi_cpu_interconnect/ARESETN", "axi_cpu_interconnect/S00_ARESETN", "axi_cpu_interconnect/M00_ARESETN", "axi_cpu_interconnect/M01_ARESETN", "axi_cpu_interconnect/M02_ARESETN", "axi_cpu_interconnect/M03_ARESETN", "axi_cpu_interconnect/M04_ARESETN", "axi_cpu_interconnect/M05_ARESETN", "axi_cpu_interconnect/M06_ARESETN", "axi_cpu_interconnect/M07_ARESETN", "axi_cpu_interconnect/M08_ARESETN", "axi_cpu_interconnect/M09_ARESETN", "axi_hp0_interconnect/ARESETN", "axi_hp0_interconnect/S00_ARESETN", "axi_hp0_interconnect/M00_ARESETN", "axi_hp1_interconnect/ARESETN", "axi_hp1_interconnect/S00_ARESETN", "axi_hp1_interconnect/M00_ARESETN", "axi_hp2_interconnect/ARESETN", "axi_hp2_interconnect/S00_ARESETN", "axi_hp2_interconnect/M00_ARESETN", "util_ad9361_divclk_reset/ext_reset_in"]}, "sys_ps7_FCLK_RESET0_N": {"ports": ["sys_ps7/FCLK_RESET0_N", "sys_rstgen/ext_reset_in"]}, "gpio_i_1": {"ports": ["gpio_i", "sys_ps7/GPIO_I"]}, "sys_ps7_GPIO_O": {"ports": ["sys_ps7/GPIO_O", "gpio_o"]}, "sys_ps7_GPIO_T": {"ports": ["sys_ps7/GPIO_T", "gpio_t"]}, "sys_ps7_SPI0_SS2_O": {"ports": ["sys_ps7/SPI0_SS2_O", "spi0_csn_2_o"]}, "sys_ps7_SPI0_SS1_O": {"ports": ["sys_ps7/SPI0_SS1_O", "spi0_csn_1_o"]}, "sys_ps7_SPI0_SS_O": {"ports": ["sys_ps7/SPI0_SS_O", "spi0_csn_0_o"]}, "spi0_csn_i_1": {"ports": ["spi0_csn_i", "sys_ps7/SPI0_SS_I"]}, "spi0_clk_i_1": {"ports": ["spi0_clk_i", "sys_ps7/SPI0_SCLK_I"]}, "sys_ps7_SPI0_SCLK_O": {"ports": ["sys_ps7/SPI0_SCLK_O", "spi0_clk_o"]}, "spi0_sdo_i_1": {"ports": ["spi0_sdo_i", "sys_ps7/SPI0_MOSI_I"]}, "sys_ps7_SPI0_MOSI_O": {"ports": ["sys_ps7/SPI0_MOSI_O", "spi0_sdo_o"]}, "spi0_sdi_i_1": {"ports": ["spi0_sdi_i", "sys_ps7/SPI0_MISO_I"]}, "sys_concat_intc_dout": {"ports": ["sys_concat_intc/dout", "sys_ps7/IRQ_F2P"]}, "ps_intr_10_1": {"ports": ["ps_intr_10", "sys_concat_intc/In10", "sys_concat_intc/In11", "sys_concat_intc/In14", "sys_concat_intc/In15"]}, "ps_intr_09_1": {"ports": ["ps_intr_09", "sys_concat_intc/In9"]}, "ps_intr_08_1": {"ports": ["ps_intr_08", "sys_concat_intc/In8"]}, "ps_intr_07_1": {"ports": ["ps_intr_07", "sys_concat_intc/In7"]}, "ps_intr_06_1": {"ports": ["ps_intr_06", "sys_concat_intc/In6"]}, "ps_intr_05_1": {"ports": ["ps_intr_05", "sys_concat_intc/In5"]}, "ps_intr_04_1": {"ports": ["ps_intr_04", "sys_concat_intc/In4"]}, "ps_intr_03_1": {"ports": ["ps_intr_03", "sys_concat_intc/In3"]}, "ps_intr_02_1": {"ports": ["ps_intr_02", "sys_concat_intc/In2"]}, "ps_intr_01_1": {"ports": ["ps_intr_01", "sys_concat_intc/In1"]}, "ps_intr_00_1": {"ports": ["ps_intr_00", "sys_concat_intc/In0"]}, "axi_ad9361_l_clk": {"ports": ["axi_ad9361/l_clk", "axi_ad9361/clk", "axi_ad9361_dac_fifo/dout_clk", "util_ad9361_adc_fifo/din_clk", "util_ad9361_divclk/clk"]}, "rx_clk_in_p_1": {"ports": ["rx_clk_in_p", "axi_ad9361/rx_clk_in_p"]}, "rx_clk_in_n_1": {"ports": ["rx_clk_in_n", "axi_ad9361/rx_clk_in_n"]}, "rx_frame_in_p_1": {"ports": ["rx_frame_in_p", "axi_ad9361/rx_frame_in_p"]}, "rx_frame_in_n_1": {"ports": ["rx_frame_in_n", "axi_ad9361/rx_frame_in_n"]}, "rx_data_in_p_1": {"ports": ["rx_data_in_p", "axi_ad9361/rx_data_in_p"]}, "rx_data_in_n_1": {"ports": ["rx_data_in_n", "axi_ad9361/rx_data_in_n"]}, "axi_ad9361_tx_clk_out_p": {"ports": ["axi_ad9361/tx_clk_out_p", "tx_clk_out_p"]}, "axi_ad9361_tx_clk_out_n": {"ports": ["axi_ad9361/tx_clk_out_n", "tx_clk_out_n"]}, "axi_ad9361_tx_frame_out_p": {"ports": ["axi_ad9361/tx_frame_out_p", "tx_frame_out_p"]}, "axi_ad9361_tx_frame_out_n": {"ports": ["axi_ad9361/tx_frame_out_n", "tx_frame_out_n"]}, "axi_ad9361_tx_data_out_p": {"ports": ["axi_ad9361/tx_data_out_p", "tx_data_out_p"]}, "axi_ad9361_tx_data_out_n": {"ports": ["axi_ad9361/tx_data_out_n", "tx_data_out_n"]}, "axi_ad9361_enable": {"ports": ["axi_ad9361/enable", "enable"]}, "axi_ad9361_txnrx": {"ports": ["axi_ad9361/txnrx", "txnrx"]}, "up_enable_1": {"ports": ["up_enable", "axi_ad9361/up_enable"]}, "up_txnrx_1": {"ports": ["up_txnrx", "axi_ad9361/up_txnrx"]}, "util_ad9361_tdd_sync_sync_out": {"ports": ["util_ad9361_tdd_sync/sync_out", "tdd_sync_o", "axi_ad9361/tdd_sync"]}, "axi_ad9361_tdd_sync_cntr": {"ports": ["axi_ad9361/tdd_sync_cntr", "tdd_sync_t", "util_ad9361_tdd_sync/sync_mode"]}, "tdd_sync_i_1": {"ports": ["tdd_sync_i", "util_ad9361_tdd_sync/sync_in"]}, "axi_ad9361_adc_r1_mode": {"ports": ["axi_ad9361/adc_r1_mode", "util_ad9361_divclk_sel_concat/In0"]}, "axi_ad9361_dac_r1_mode": {"ports": ["axi_ad9361/dac_r1_mode", "util_ad9361_divclk_sel_concat/In1"]}, "util_ad9361_divclk_sel_concat_dout": {"ports": ["util_ad9361_divclk_sel_concat/dout", "util_ad9361_divclk_sel/Op1"]}, "util_ad9361_divclk_sel_Res": {"ports": ["util_ad9361_divclk_sel/Res", "util_ad9361_divclk/clk_sel"]}, "util_ad9361_divclk_clk_out": {"ports": ["util_ad9361_divclk/clk_out", "axi_ad9361_adc_dma/fifo_wr_clk", "axi_ad9361_dac_dma/fifo_rd_clk", "axi_ad9361_dac_fifo/din_clk", "util_ad9361_adc_fifo/dout_clk", "util_ad9361_adc_pack/adc_clk", "util_ad9361_dac_upack/dac_clk", "util_ad9361_divclk_reset/slowest_sync_clk"]}, "axi_ad9361_rst": {"ports": ["axi_ad9361/rst", "axi_ad9361_dac_fifo/dout_rst", "util_ad9361_adc_fifo/din_rst"]}, "util_ad9361_divclk_reset_peripheral_aresetn": {"ports": ["util_ad9361_divclk_reset/peripheral_aresetn", "axi_ad9361_dac_fifo/din_rstn", "util_ad9361_adc_fifo/dout_rstn"]}, "axi_ad9361_adc_enable_i0": {"ports": ["axi_ad9361/adc_enable_i0", "util_ad9361_adc_fifo/din_enable_0"]}, "axi_ad9361_adc_valid_i0": {"ports": ["axi_ad9361/adc_valid_i0", "util_ad9361_adc_fifo/din_valid_0"]}, "axi_ad9361_adc_data_i0": {"ports": ["axi_ad9361/adc_data_i0", "util_ad9361_adc_fifo/din_data_0"]}, "axi_ad9361_adc_enable_q0": {"ports": ["axi_ad9361/adc_enable_q0", "util_ad9361_adc_fifo/din_enable_1"]}, "axi_ad9361_adc_valid_q0": {"ports": ["axi_ad9361/adc_valid_q0", "util_ad9361_adc_fifo/din_valid_1"]}, "axi_ad9361_adc_data_q0": {"ports": ["axi_ad9361/adc_data_q0", "util_ad9361_adc_fifo/din_data_1"]}, "axi_ad9361_adc_enable_i1": {"ports": ["axi_ad9361/adc_enable_i1", "util_ad9361_adc_fifo/din_enable_2"]}, "axi_ad9361_adc_valid_i1": {"ports": ["axi_ad9361/adc_valid_i1", "util_ad9361_adc_fifo/din_valid_2"]}, "axi_ad9361_adc_data_i1": {"ports": ["axi_ad9361/adc_data_i1", "util_ad9361_adc_fifo/din_data_2"]}, "axi_ad9361_adc_enable_q1": {"ports": ["axi_ad9361/adc_enable_q1", "util_ad9361_adc_fifo/din_enable_3"]}, "axi_ad9361_adc_valid_q1": {"ports": ["axi_ad9361/adc_valid_q1", "util_ad9361_adc_fifo/din_valid_3"]}, "axi_ad9361_adc_data_q1": {"ports": ["axi_ad9361/adc_data_q1", "util_ad9361_adc_fifo/din_data_3"]}, "util_ad9361_adc_fifo_din_ovf": {"ports": ["util_ad9361_adc_fifo/din_ovf", "axi_ad9361/adc_dovf"]}, "util_ad9361_divclk_reset_peripheral_reset": {"ports": ["util_ad9361_divclk_reset/peripheral_reset", "util_ad9361_adc_pack/adc_rst"]}, "util_ad9361_adc_fifo_dout_enable_0": {"ports": ["util_ad9361_adc_fifo/dout_enable_0", "util_ad9361_adc_pack/adc_enable_0"]}, "util_ad9361_adc_fifo_dout_valid_0": {"ports": ["util_ad9361_adc_fifo/dout_valid_0", "util_ad9361_adc_pack/adc_valid_0"]}, "util_ad9361_adc_fifo_dout_data_0": {"ports": ["util_ad9361_adc_fifo/dout_data_0", "util_ad9361_adc_pack/adc_data_0"]}, "util_ad9361_adc_fifo_dout_enable_1": {"ports": ["util_ad9361_adc_fifo/dout_enable_1", "util_ad9361_adc_pack/adc_enable_1"]}, "util_ad9361_adc_fifo_dout_valid_1": {"ports": ["util_ad9361_adc_fifo/dout_valid_1", "util_ad9361_adc_pack/adc_valid_1"]}, "util_ad9361_adc_fifo_dout_data_1": {"ports": ["util_ad9361_adc_fifo/dout_data_1", "util_ad9361_adc_pack/adc_data_1"]}, "util_ad9361_adc_fifo_dout_enable_2": {"ports": ["util_ad9361_adc_fifo/dout_enable_2", "util_ad9361_adc_pack/adc_enable_2"]}, "util_ad9361_adc_fifo_dout_valid_2": {"ports": ["util_ad9361_adc_fifo/dout_valid_2", "util_ad9361_adc_pack/adc_valid_2"]}, "util_ad9361_adc_fifo_dout_data_2": {"ports": ["util_ad9361_adc_fifo/dout_data_2", "util_ad9361_adc_pack/adc_data_2"]}, "util_ad9361_adc_fifo_dout_enable_3": {"ports": ["util_ad9361_adc_fifo/dout_enable_3", "util_ad9361_adc_pack/adc_enable_3"]}, "util_ad9361_adc_fifo_dout_valid_3": {"ports": ["util_ad9361_adc_fifo/dout_valid_3", "util_ad9361_adc_pack/adc_valid_3"]}, "util_ad9361_adc_fifo_dout_data_3": {"ports": ["util_ad9361_adc_fifo/dout_data_3", "util_ad9361_adc_pack/adc_data_3"]}, "util_ad9361_adc_pack_adc_valid": {"ports": ["util_ad9361_adc_pack/adc_valid", "axi_ad9361_adc_dma/fifo_wr_en"]}, "util_ad9361_adc_pack_adc_sync": {"ports": ["util_ad9361_adc_pack/adc_sync", "axi_ad9361_adc_dma/fifo_wr_sync"]}, "util_ad9361_adc_pack_adc_data": {"ports": ["util_ad9361_adc_pack/adc_data", "axi_ad9361_adc_dma/fifo_wr_din"]}, "axi_ad9361_adc_dma_fifo_wr_overflow": {"ports": ["axi_ad9361_adc_dma/fifo_wr_overflow", "util_ad9361_adc_fifo/dout_ovf"]}, "axi_ad9361_dac_enable_i0": {"ports": ["axi_ad9361/dac_enable_i0", "axi_ad9361_dac_fifo/dout_enable_0"]}, "axi_ad9361_dac_valid_i0": {"ports": ["axi_ad9361/dac_valid_i0", "axi_ad9361_dac_fifo/dout_valid_0"]}, "axi_ad9361_dac_fifo_dout_data_0": {"ports": ["axi_ad9361_dac_fifo/dout_data_0", "axi_ad9361/dac_data_i0"]}, "axi_ad9361_dac_enable_q0": {"ports": ["axi_ad9361/dac_enable_q0", "axi_ad9361_dac_fifo/dout_enable_1"]}, "axi_ad9361_dac_valid_q0": {"ports": ["axi_ad9361/dac_valid_q0", "axi_ad9361_dac_fifo/dout_valid_1"]}, "axi_ad9361_dac_fifo_dout_data_1": {"ports": ["axi_ad9361_dac_fifo/dout_data_1", "axi_ad9361/dac_data_q0"]}, "axi_ad9361_dac_enable_i1": {"ports": ["axi_ad9361/dac_enable_i1", "axi_ad9361_dac_fifo/dout_enable_2"]}, "axi_ad9361_dac_valid_i1": {"ports": ["axi_ad9361/dac_valid_i1", "axi_ad9361_dac_fifo/dout_valid_2"]}, "axi_ad9361_dac_fifo_dout_data_2": {"ports": ["axi_ad9361_dac_fifo/dout_data_2", "axi_ad9361/dac_data_i1"]}, "axi_ad9361_dac_enable_q1": {"ports": ["axi_ad9361/dac_enable_q1", "axi_ad9361_dac_fifo/dout_enable_3"]}, "axi_ad9361_dac_valid_q1": {"ports": ["axi_ad9361/dac_valid_q1", "axi_ad9361_dac_fifo/dout_valid_3"]}, "axi_ad9361_dac_fifo_dout_data_3": {"ports": ["axi_ad9361_dac_fifo/dout_data_3", "axi_ad9361/dac_data_q1"]}, "axi_ad9361_dac_fifo_dout_unf": {"ports": ["axi_ad9361_dac_fifo/dout_unf", "axi_ad9361/dac_dunf"]}, "axi_ad9361_dac_fifo_din_enable_0": {"ports": ["axi_ad9361_dac_fifo/din_enable_0", "util_ad9361_dac_upack/dac_enable_0"]}, "axi_ad9361_dac_fifo_din_valid_0": {"ports": ["axi_ad9361_dac_fifo/din_valid_0", "util_ad9361_dac_upack/dac_valid_0"]}, "util_ad9361_dac_upack_dac_valid_out_0": {"ports": ["util_ad9361_dac_upack/dac_valid_out_0", "axi_ad9361_dac_fifo/din_valid_in_0"]}, "util_ad9361_dac_upack_dac_data_0": {"ports": ["util_ad9361_dac_upack/dac_data_0", "axi_ad9361_dac_fifo/din_data_0"]}, "axi_ad9361_dac_fifo_din_enable_1": {"ports": ["axi_ad9361_dac_fifo/din_enable_1", "util_ad9361_dac_upack/dac_enable_1"]}, "axi_ad9361_dac_fifo_din_valid_1": {"ports": ["axi_ad9361_dac_fifo/din_valid_1", "util_ad9361_dac_upack/dac_valid_1"]}, "util_ad9361_dac_upack_dac_valid_out_1": {"ports": ["util_ad9361_dac_upack/dac_valid_out_1", "axi_ad9361_dac_fifo/din_valid_in_1"]}, "util_ad9361_dac_upack_dac_data_1": {"ports": ["util_ad9361_dac_upack/dac_data_1", "axi_ad9361_dac_fifo/din_data_1"]}, "axi_ad9361_dac_fifo_din_enable_2": {"ports": ["axi_ad9361_dac_fifo/din_enable_2", "util_ad9361_dac_upack/dac_enable_2"]}, "axi_ad9361_dac_fifo_din_valid_2": {"ports": ["axi_ad9361_dac_fifo/din_valid_2", "util_ad9361_dac_upack/dac_valid_2"]}, "util_ad9361_dac_upack_dac_valid_out_2": {"ports": ["util_ad9361_dac_upack/dac_valid_out_2", "axi_ad9361_dac_fifo/din_valid_in_2"]}, "util_ad9361_dac_upack_dac_data_2": {"ports": ["util_ad9361_dac_upack/dac_data_2", "axi_ad9361_dac_fifo/din_data_2"]}, "axi_ad9361_dac_fifo_din_enable_3": {"ports": ["axi_ad9361_dac_fifo/din_enable_3", "util_ad9361_dac_upack/dac_enable_3"]}, "axi_ad9361_dac_fifo_din_valid_3": {"ports": ["axi_ad9361_dac_fifo/din_valid_3", "util_ad9361_dac_upack/dac_valid_3"]}, "util_ad9361_dac_upack_dac_valid_out_3": {"ports": ["util_ad9361_dac_upack/dac_valid_out_3", "axi_ad9361_dac_fifo/din_valid_in_3"]}, "util_ad9361_dac_upack_dac_data_3": {"ports": ["util_ad9361_dac_upack/dac_data_3", "axi_ad9361_dac_fifo/din_data_3"]}, "util_ad9361_dac_upack_dac_valid": {"ports": ["util_ad9361_dac_upack/dac_valid", "axi_ad9361_dac_dma/fifo_rd_en"]}, "axi_ad9361_dac_dma_fifo_rd_dout": {"ports": ["axi_ad9361_dac_dma/fifo_rd_dout", "util_ad9361_dac_upack/dac_data"]}, "axi_ad9361_dac_dma_fifo_rd_underflow": {"ports": ["axi_ad9361_dac_dma/fifo_rd_underflow", "axi_ad9361_dac_fifo/din_unf"]}, "axi_ad9361_adc_dma_irq": {"ports": ["axi_ad9361_adc_dma/irq", "sys_concat_intc/In13"]}, "axi_ad9361_dac_dma_irq": {"ports": ["axi_ad9361_dac_dma/irq", "sys_concat_intc/In12"]}}, "addressing": {"/axi_ad9361_adc_dma": {"address_spaces": {"m_dest_axi": {"range": "512M", "width": "32", "segments": {"SEG_sys_ps7_HP1_DDR_LOWOCM": {"address_block": "/sys_ps7/S_AXI_HP1/HP1_DDR_LOWOCM", "offset": "0x00000000", "range": "512M"}}}}}, "/axi_ad9361_dac_dma": {"address_spaces": {"m_src_axi": {"range": "512M", "width": "32", "segments": {"SEG_sys_ps7_HP2_DDR_LOWOCM": {"address_block": "/sys_ps7/S_AXI_HP2/HP2_DDR_LOWOCM", "offset": "0x00000000", "range": "512M"}}}}}, "/sys_ps7": {"address_spaces": {"Data": {"range": "4G", "width": "32", "segments": {"SEG_data_axi_ad9361": {"address_block": "/axi_ad9361/s_axi/axi_lite", "offset": "0x79020000", "range": "64K"}, "SEG_data_axi_ad9361_adc_dma": {"address_block": "/axi_ad9361_adc_dma/s_axi/axi_lite", "offset": "0x7C400000", "range": "4K"}, "SEG_data_axi_ad9361_dac_dma": {"address_block": "/axi_ad9361_dac_dma/s_axi/axi_lite", "offset": "0x7C420000", "range": "4K"}}}}}}}}