<?xml version="1.0" encoding="UTF-8"?>
<spirit:design xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>xci</spirit:library>
  <spirit:name>unknown</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:componentInstances>
    <spirit:componentInstance>
      <spirit:instanceName>system_util_ad9361_adc_fifo_0</spirit:instanceName>
      <spirit:componentRef spirit:vendor="analog.com" spirit:library="user" spirit:name="util_wfifo" spirit:version="1.0"/>
      <spirit:configurableElementValues>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.DIN_ADDRESS_WIDTH">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.DIN_DATA_WIDTH">16</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.DOUT_DATA_WIDTH">16</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.NUM_OF_CHANNELS">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Component_Name">system_util_ad9361_adc_fifo_0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.DIN_ADDRESS_WIDTH">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.DIN_DATA_WIDTH">16</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.DOUT_DATA_WIDTH">16</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.NUM_OF_CHANNELS">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.ARCHITECTURE">zynq</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.BASE_BOARD_PART"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.BOARD_CONNECTIONS"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.DEVICE">xc7z035</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.PACKAGE">ffg676</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.PREFHDL">VERILOG</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SILICON_REVISION"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SIMULATOR_LANGUAGE">MIXED</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SPEEDGRADE">-2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.TEMPERATURE_GRADE"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.USE_RDI_CUSTOMIZATION">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.USE_RDI_GENERATION">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.IPCONTEXT">IP_Integrator</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.IPREVISION">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.LOCKEDBYUSER">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.MANAGED">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.OUTPUTDIR">.</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SELECTEDSIMMODEL"/>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SHAREDDIR">../../ipshared</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SWVERSION">2016.4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SYNTHESISFLOW">OUT_OF_CONTEXT</spirit:configurableElementValue>
      </spirit:configurableElementValues>
      <spirit:vendorExtensions>
        <xilinx:componentInstanceExtensions>
          <xilinx:configElementInfos>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.DIN_ADDRESS_WIDTH" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.DIN_DATA_WIDTH" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.DOUT_DATA_WIDTH" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_OF_CHANNELS" xilinx:valueSource="user"/>
          </xilinx:configElementInfos>
        </xilinx:componentInstanceExtensions>
      </spirit:vendorExtensions>
    </spirit:componentInstance>
  </spirit:componentInstances>
</spirit:design>
