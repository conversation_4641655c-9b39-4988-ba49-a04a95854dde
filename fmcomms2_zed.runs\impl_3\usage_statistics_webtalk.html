<HTML><HEAD><TITLE>Device Usage Statistics Report</TITLE></HEAD>
<BODY TEXT='#000000' BGCOLOR='#FFFFFF' LINK='#0000EE' VLINK='#551A8B' ALINK='#FF0000'><H3>Device Usage Page (usage_statistics_webtalk.html)</H3>This HTML page displays the device usage statistics that will be sent to Xilinx.<BR>To see the actual file transmitted to Xilinx, please click <A HREF="./usage_statistics_webtalk.xml">here</A>.<BR><BR><HR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>software_version_and_target_device</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>beta</B></TD><TD>FALSE</TD>
  <TD BGCOLOR='#DBE5F1'><B>build_version</B></TD><TD>1756540</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>date_generated</B></TD><TD>Tue Mar 17 17:10:06 2020</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_platform</B></TD><TD>WIN64</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>product_version</B></TD><TD>Vivado v2016.4 (64-bit)</TD>
  <TD BGCOLOR='#DBE5F1'><B>project_id</B></TD><TD>d99595073c624624ae85d6bca6a02b61</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>project_iteration</B></TD><TD>4</TD>
  <TD BGCOLOR='#DBE5F1'><B>random_id</B></TD><TD>d5aa0efbea9552bb80be422e35c8ba78</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>registration_id</B></TD><TD>d5aa0efbea9552bb80be422e35c8ba78</TD>
  <TD BGCOLOR='#DBE5F1'><B>route_design</B></TD><TD>TRUE</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_device</B></TD><TD>xc7z035</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_family</B></TD><TD>zynq</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_package</B></TD><TD>ffg676</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_speed</B></TD><TD>-2</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>tool_flow</B></TD><TD>Vivado</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>user_environment</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>cpu_name</B></TD><TD>AMD Ryzen 5 2400G with Radeon Vega Graphics    </TD>
  <TD BGCOLOR='#DBE5F1'><B>cpu_speed</B></TD><TD>3593 MHz</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>os_name</B></TD><TD>Microsoft Windows 8 or later , 64-bit</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_release</B></TD><TD>major release  (build 9200)</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>system_ram</B></TD><TD>16.000 GB</TD>
  <TD BGCOLOR='#DBE5F1'><B>total_processors</B></TD><TD>1</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>vivado_usage</B></TD></TR>
<TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>java_command_handlers</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>autoconnecttarget=3</TD>
   <TD>closeproject=2</TD>
   <TD>coreview=2</TD>
   <TD>createtophdl=5</TD>
</TR><TR ALIGN='LEFT'>   <TD>customizecore=2</TD>
   <TD>customizersbblock=21</TD>
   <TD>editcopy=1</TD>
   <TD>editdelete=40</TD>
</TR><TR ALIGN='LEFT'>   <TD>editpaste=2</TD>
   <TD>editproperties=1</TD>
   <TD>launchprogramfpga=4</TD>
   <TD>managecompositetargets=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>newexporthardware=1</TD>
   <TD>newlaunchhardware=3</TD>
   <TD>openblockdesign=1</TD>
   <TD>openhardwaremanager=4</TD>
</TR><TR ALIGN='LEFT'>   <TD>openrecenttarget=3</TD>
   <TD>projectsettingscmdhandler=3</TD>
   <TD>reportipstatus=4</TD>
   <TD>runbitgen=12</TD>
</TR><TR ALIGN='LEFT'>   <TD>runimplementation=2</TD>
   <TD>runsynthesis=2</TD>
   <TD>savedesign=5</TD>
   <TD>saversbdesign=6</TD>
</TR><TR ALIGN='LEFT'>   <TD>showview=7</TD>
   <TD>timingconstraintswizard=1</TD>
   <TD>ui.views.c.aj=1</TD>
   <TD>upgradeip=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>validatersbdesign=8</TD>
   <TD>viewtaskimplementation=1</TD>
</TR>  </TABLE>
  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>other_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>guimode=10</TD>
   <TD>tclmode=1</TD>
</TR>  </TABLE>
</TR><TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>project_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>constraintsetcount=1</TD>
   <TD>core_container=false</TD>
   <TD>currentimplrun=impl_3</TD>
   <TD>currentsynthesisrun=synth_3</TD>
</TR><TR ALIGN='LEFT'>   <TD>default_library=xil_defaultlib</TD>
   <TD>designmode=RTL</TD>
   <TD>export_simulation_activehdl=3</TD>
   <TD>export_simulation_ies=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_modelsim=3</TD>
   <TD>export_simulation_questa=3</TD>
   <TD>export_simulation_riviera=3</TD>
   <TD>export_simulation_vcs=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_xsim=3</TD>
   <TD>implstrategy=Vivado Implementation Defaults</TD>
   <TD>launch_simulation_activehdl=0</TD>
   <TD>launch_simulation_ies=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_modelsim=0</TD>
   <TD>launch_simulation_questa=0</TD>
   <TD>launch_simulation_riviera=0</TD>
   <TD>launch_simulation_vcs=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_xsim=0</TD>
   <TD>simulator_language=Mixed</TD>
   <TD>srcsetcount=6</TD>
   <TD>synthesisstrategy=Flow_PerfOptimized_high</TD>
</TR><TR ALIGN='LEFT'>   <TD>target_language=Verilog</TD>
   <TD>target_simulator=XSim</TD>
   <TD>totalimplruns=4</TD>
   <TD>totalsynthesisruns=4</TD>
</TR>  </TABLE>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>unisim_transformation</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>post_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bibuf=130</TD>
    <TD>bufg=3</TD>
    <TD>bufgctrl=1</TD>
    <TD>bufr=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>carry4=777</TD>
    <TD>dsp48e1=60</TD>
    <TD>fdce=7232</TD>
    <TD>fdpe=16</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdre=19138</TD>
    <TD>fdse=335</TD>
    <TD>gnd=403</TD>
    <TD>ibuf=20</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibufds=8</TD>
    <TD>iddr=7</TD>
    <TD>idelayctrl=1</TD>
    <TD>idelaye2=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut1=1221</TD>
    <TD>lut2=2414</TD>
    <TD>lut3=2987</TD>
    <TD>lut4=1967</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut5=2449</TD>
    <TD>lut6=6292</TD>
    <TD>muxf7=429</TD>
    <TD>muxf8=102</TD>
</TR><TR ALIGN='LEFT'>    <TD>obuf=34</TD>
    <TD>obufds=8</TD>
    <TD>obuft=17</TD>
    <TD>oddr=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>ps7=1</TD>
    <TD>ramb36e1=4</TD>
    <TD>srl16e=135</TD>
    <TD>srlc32e=141</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcc=484</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>pre_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bibuf=130</TD>
    <TD>bufg=3</TD>
    <TD>bufgctrl=1</TD>
    <TD>bufr=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>carry4=777</TD>
    <TD>dsp48e1=60</TD>
    <TD>fdce=7232</TD>
    <TD>fdpe=16</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdre=19138</TD>
    <TD>fdse=335</TD>
    <TD>gnd=403</TD>
    <TD>ibuf=5</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibufds=8</TD>
    <TD>iddr=7</TD>
    <TD>idelayctrl=1</TD>
    <TD>idelaye2=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>iobuf=15</TD>
    <TD>lut1=1221</TD>
    <TD>lut2=2414</TD>
    <TD>lut3=2987</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut4=1967</TD>
    <TD>lut5=2449</TD>
    <TD>lut6=6292</TD>
    <TD>muxf7=429</TD>
</TR><TR ALIGN='LEFT'>    <TD>muxf8=102</TD>
    <TD>obuf=34</TD>
    <TD>obufds=8</TD>
    <TD>obuft=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>oddr=10</TD>
    <TD>ps7=1</TD>
    <TD>ramb36e1=4</TD>
    <TD>srl16e=135</TD>
</TR><TR ALIGN='LEFT'>    <TD>srlc32e=141</TD>
    <TD>vcc=484</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>power_opt_design</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options_spo</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-cell_types=default::all</TD>
    <TD>-clocks=default::[not_specified]</TD>
    <TD>-exclude_cells=default::[not_specified]</TD>
    <TD>-include_cells=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bram_ports_augmented=1</TD>
    <TD>bram_ports_newly_gated=6</TD>
    <TD>bram_ports_total=8</TD>
    <TD>flow_state=default</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_augmented=0</TD>
    <TD>slice_registers_newly_gated=0</TD>
    <TD>slice_registers_total=23001</TD>
    <TD>srls_augmented=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>srls_newly_gated=0</TD>
    <TD>srls_total=276</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>ip_statistics</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>IP_Integrator/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bdsource=USER</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>maxhierdepth=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>numblks=38</TD>
    <TD>numhdlrefblks=0</TD>
    <TD>numhierblks=18</TD>
    <TD>numhlsblks=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>numnonxlnxblks=9</TD>
    <TD>numpkgbdblks=0</TD>
    <TD>numreposblks=20</TD>
    <TD>numsysgenblks=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>synth_mode=Global</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=BlockDiagram</TD>
    <TD>x_ipname=system</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.00.a</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_crossbar_v2_1_12_axi_crossbar/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=32</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_protocol=0</TD>
    <TD>c_axi_ruser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_connectivity_mode=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_addr_width=0x0000000c0000000c0000001000000000000000000000000000000000000000000000000000000000</TD>
    <TD>c_m_axi_base_addr=0x000000007c420000000000007c4000000000000079020000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff</TD>
    <TD>c_m_axi_read_connectivity=0x00000001000000010000000100000001000000010000000100000001000000010000000100000001</TD>
    <TD>c_m_axi_read_issuing=0x00000008000000080000000200000008000000080000000800000008000000080000000800000008</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_secure=0x00000000000000000000000000000000000000000000000000000000000000000000000000000000</TD>
    <TD>c_m_axi_write_connectivity=0x00000001000000010000000100000001000000010000000100000001000000010000000100000001</TD>
    <TD>c_m_axi_write_issuing=0x00000008000000080000000200000008000000080000000800000008000000080000000800000008</TD>
    <TD>c_num_addr_ranges=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_master_slots=10</TD>
    <TD>c_num_slave_slots=1</TD>
    <TD>c_r_register=0</TD>
    <TD>c_s_axi_arb_priority=0x00000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_base_id=0x00000000</TD>
    <TD>c_s_axi_read_acceptance=0x00000008</TD>
    <TD>c_s_axi_single_thread=0x00000000</TD>
    <TD>c_s_axi_thread_id_width=0x0000000c</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_write_acceptance=0x00000008</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=12</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_crossbar</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_protocol_converter_v2_1_11_axi_protocol_converter/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=16</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_ruser_width=1</TD>
    <TD>c_axi_supports_read=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_supports_write=1</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_ignore_id=0</TD>
    <TD>c_m_axi_protocol=2</TD>
    <TD>c_s_axi_protocol=0</TD>
    <TD>c_translation_mode=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=11</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_protocol_converter</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_protocol_converter_v2_1_11_axi_protocol_converter/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=12</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_ruser_width=1</TD>
    <TD>c_axi_supports_read=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_supports_write=1</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_ignore_id=0</TD>
    <TD>c_m_axi_protocol=2</TD>
    <TD>c_s_axi_protocol=0</TD>
    <TD>c_translation_mode=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=11</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_protocol_converter</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_protocol_converter_v2_1_11_axi_protocol_converter/3</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=12</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_ruser_width=1</TD>
    <TD>c_axi_supports_read=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_supports_write=1</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_ignore_id=0</TD>
    <TD>c_m_axi_protocol=2</TD>
    <TD>c_s_axi_protocol=0</TD>
    <TD>c_translation_mode=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=11</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_protocol_converter</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_protocol_converter_v2_1_11_axi_protocol_converter/4</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=32</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_ruser_width=1</TD>
    <TD>c_axi_supports_read=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_supports_write=1</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_ignore_id=0</TD>
    <TD>c_m_axi_protocol=0</TD>
    <TD>c_s_axi_protocol=1</TD>
    <TD>c_translation_mode=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=11</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_protocol_converter</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>labtools_xsdbm_v2_00_a/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_bscan_mode=false</TD>
    <TD>c_bscan_mode_with_core=false</TD>
    <TD>c_clk_input_freq_hz=300000000</TD>
    <TD>c_enable_clk_divider=false</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_bscan_master_ports=0</TD>
    <TD>c_two_prim_mode=false</TD>
    <TD>c_use_ext_bscan=false</TD>
    <TD>c_user_scan_chain=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_xsdb_num_slaves=1</TD>
    <TD>component_name=dbg_hub_CV</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>proc_sys_reset/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aux_reset_high=0</TD>
    <TD>c_aux_rst_width=4</TD>
    <TD>c_ext_reset_high=0</TD>
    <TD>c_ext_rst_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_family=zynq</TD>
    <TD>c_num_bus_rst=1</TD>
    <TD>c_num_interconnect_aresetn=1</TD>
    <TD>c_num_perp_aresetn=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_perp_rst=1</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=proc_sys_reset</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=5.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>proc_sys_reset/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aux_reset_high=0</TD>
    <TD>c_aux_rst_width=4</TD>
    <TD>c_ext_reset_high=0</TD>
    <TD>c_ext_rst_width=4</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_family=zynq</TD>
    <TD>c_num_bus_rst=1</TD>
    <TD>c_num_interconnect_aresetn=1</TD>
    <TD>c_num_perp_aresetn=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_perp_rst=1</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=proc_sys_reset</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=5.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>processing_system7_v5.5_user_configuration/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>pcw_apu_clk_ratio_enable=6:2:1</TD>
    <TD>pcw_apu_peripheral_freqmhz=666.666667</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_armpll_ctrl_fbdiv=27</TD>
    <TD>pcw_can0_grp_clk_enable=0</TD>
    <TD>pcw_can0_peripheral_clksrc=External</TD>
    <TD>pcw_can0_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_can0_peripheral_freqmhz=-1</TD>
    <TD>pcw_can1_grp_clk_enable=0</TD>
    <TD>pcw_can1_peripheral_clksrc=External</TD>
    <TD>pcw_can1_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_can1_peripheral_freqmhz=-1</TD>
    <TD>pcw_can_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_can_peripheral_freqmhz=100</TD>
    <TD>pcw_cpu_cpu_pll_freqmhz=1350.000</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_cpu_peripheral_clksrc=ARM PLL</TD>
    <TD>pcw_crystal_peripheral_freqmhz=50</TD>
    <TD>pcw_dci_peripheral_clksrc=DDR PLL</TD>
    <TD>pcw_dci_peripheral_freqmhz=10.159</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ddr_ddr_pll_freqmhz=1050.000</TD>
    <TD>pcw_ddr_hpr_to_critical_priority_level=15</TD>
    <TD>pcw_ddr_hprlpr_queue_partition=HPR(0)/LPR(32)</TD>
    <TD>pcw_ddr_lpr_to_critical_priority_level=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ddr_peripheral_clksrc=DDR PLL</TD>
    <TD>pcw_ddr_port0_hpr_enable=0</TD>
    <TD>pcw_ddr_port1_hpr_enable=0</TD>
    <TD>pcw_ddr_port2_hpr_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ddr_port3_hpr_enable=0</TD>
    <TD>pcw_ddr_write_to_critical_priority_level=2</TD>
    <TD>pcw_ddrpll_ctrl_fbdiv=21</TD>
    <TD>pcw_enet0_enet0_io=MIO 16 .. 27</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_enet0_grp_mdio_enable=0</TD>
    <TD>pcw_enet0_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_enet0_peripheral_enable=1</TD>
    <TD>pcw_enet0_peripheral_freqmhz=1000 Mbps</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_enet0_reset_enable=1</TD>
    <TD>pcw_enet0_reset_io=MIO 40</TD>
    <TD>pcw_enet1_grp_mdio_enable=0</TD>
    <TD>pcw_enet1_peripheral_clksrc=IO PLL</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_enet1_peripheral_enable=0</TD>
    <TD>pcw_enet1_peripheral_freqmhz=1000 Mbps</TD>
    <TD>pcw_enet1_reset_enable=0</TD>
    <TD>pcw_enet_reset_polarity=Active Low</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_fclk0_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_fclk1_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_fclk2_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_fclk3_peripheral_clksrc=IO PLL</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_fpga0_peripheral_freqmhz=100.0</TD>
    <TD>pcw_fpga1_peripheral_freqmhz=200.0</TD>
    <TD>pcw_fpga2_peripheral_freqmhz=50</TD>
    <TD>pcw_fpga3_peripheral_freqmhz=50</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_fpga_fclk0_enable=1</TD>
    <TD>pcw_fpga_fclk1_enable=1</TD>
    <TD>pcw_fpga_fclk2_enable=0</TD>
    <TD>pcw_fpga_fclk3_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_gpio_emio_gpio_enable=1</TD>
    <TD>pcw_gpio_emio_gpio_io=64</TD>
    <TD>pcw_gpio_mio_gpio_enable=1</TD>
    <TD>pcw_gpio_mio_gpio_io=MIO</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_gpio_peripheral_enable=0</TD>
    <TD>pcw_i2c0_grp_int_enable=0</TD>
    <TD>pcw_i2c0_peripheral_enable=0</TD>
    <TD>pcw_i2c0_reset_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_i2c1_grp_int_enable=0</TD>
    <TD>pcw_i2c1_peripheral_enable=0</TD>
    <TD>pcw_i2c1_reset_enable=0</TD>
    <TD>pcw_i2c_reset_polarity=Active Low</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_io_io_pll_freqmhz=1000.000</TD>
    <TD>pcw_iopll_ctrl_fbdiv=20</TD>
    <TD>pcw_irq_f2p_mode=REVERSE</TD>
    <TD>pcw_m_axi_gp0_freqmhz=100</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_m_axi_gp1_freqmhz=10</TD>
    <TD>pcw_nand_cycles_t_ar=1</TD>
    <TD>pcw_nand_cycles_t_clr=1</TD>
    <TD>pcw_nand_cycles_t_rc=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nand_cycles_t_rea=1</TD>
    <TD>pcw_nand_cycles_t_rr=1</TD>
    <TD>pcw_nand_cycles_t_wc=11</TD>
    <TD>pcw_nand_cycles_t_wp=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nand_grp_d8_enable=0</TD>
    <TD>pcw_nand_peripheral_enable=0</TD>
    <TD>pcw_nor_cs0_t_ceoe=1</TD>
    <TD>pcw_nor_cs0_t_pc=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_cs0_t_rc=11</TD>
    <TD>pcw_nor_cs0_t_tr=1</TD>
    <TD>pcw_nor_cs0_t_wc=11</TD>
    <TD>pcw_nor_cs0_t_wp=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_cs0_we_time=0</TD>
    <TD>pcw_nor_cs1_t_ceoe=1</TD>
    <TD>pcw_nor_cs1_t_pc=1</TD>
    <TD>pcw_nor_cs1_t_rc=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_cs1_t_tr=1</TD>
    <TD>pcw_nor_cs1_t_wc=11</TD>
    <TD>pcw_nor_cs1_t_wp=1</TD>
    <TD>pcw_nor_cs1_we_time=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_grp_a25_enable=0</TD>
    <TD>pcw_nor_grp_cs0_enable=0</TD>
    <TD>pcw_nor_grp_cs1_enable=0</TD>
    <TD>pcw_nor_grp_sram_cs0_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_grp_sram_cs1_enable=0</TD>
    <TD>pcw_nor_grp_sram_int_enable=0</TD>
    <TD>pcw_nor_peripheral_enable=0</TD>
    <TD>pcw_nor_sram_cs0_t_ceoe=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs0_t_pc=1</TD>
    <TD>pcw_nor_sram_cs0_t_rc=11</TD>
    <TD>pcw_nor_sram_cs0_t_tr=1</TD>
    <TD>pcw_nor_sram_cs0_t_wc=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs0_t_wp=1</TD>
    <TD>pcw_nor_sram_cs0_we_time=0</TD>
    <TD>pcw_nor_sram_cs1_t_ceoe=1</TD>
    <TD>pcw_nor_sram_cs1_t_pc=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs1_t_rc=11</TD>
    <TD>pcw_nor_sram_cs1_t_tr=1</TD>
    <TD>pcw_nor_sram_cs1_t_wc=11</TD>
    <TD>pcw_nor_sram_cs1_t_wp=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs1_we_time=0</TD>
    <TD>pcw_override_basic_clock=0</TD>
    <TD>pcw_pcap_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_pcap_peripheral_freqmhz=200</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_pjtag_peripheral_enable=0</TD>
    <TD>pcw_preset_bank0_voltage=LVCMOS 3.3V</TD>
    <TD>pcw_preset_bank1_voltage=LVCMOS 2.5V</TD>
    <TD>pcw_qspi_grp_fbclk_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_qspi_grp_io1_enable=0</TD>
    <TD>pcw_qspi_grp_single_ss_enable=1</TD>
    <TD>pcw_qspi_grp_single_ss_io=MIO 1 .. 6</TD>
    <TD>pcw_qspi_grp_ss1_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_qspi_internal_highaddress=0xFCFFFFFF</TD>
    <TD>pcw_qspi_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_qspi_peripheral_enable=1</TD>
    <TD>pcw_qspi_peripheral_freqmhz=200</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_qspi_qspi_io=MIO 1 .. 6</TD>
    <TD>pcw_s_axi_acp_freqmhz=10</TD>
    <TD>pcw_s_axi_gp0_freqmhz=10</TD>
    <TD>pcw_s_axi_gp1_freqmhz=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_s_axi_hp0_data_width=64</TD>
    <TD>pcw_s_axi_hp0_freqmhz=100</TD>
    <TD>pcw_s_axi_hp1_data_width=64</TD>
    <TD>pcw_s_axi_hp1_freqmhz=100</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_s_axi_hp2_data_width=64</TD>
    <TD>pcw_s_axi_hp2_freqmhz=100</TD>
    <TD>pcw_s_axi_hp3_data_width=64</TD>
    <TD>pcw_s_axi_hp3_freqmhz=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_sd0_grp_cd_enable=0</TD>
    <TD>pcw_sd0_grp_pow_enable=0</TD>
    <TD>pcw_sd0_grp_wp_enable=0</TD>
    <TD>pcw_sd0_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_sd1_grp_cd_enable=0</TD>
    <TD>pcw_sd1_grp_pow_enable=0</TD>
    <TD>pcw_sd1_grp_wp_enable=0</TD>
    <TD>pcw_sd1_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_sdio_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_sdio_peripheral_freqmhz=100</TD>
    <TD>pcw_smc_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_smc_peripheral_freqmhz=100</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi0_grp_ss0_enable=1</TD>
    <TD>pcw_spi0_grp_ss0_io=EMIO</TD>
    <TD>pcw_spi0_grp_ss1_enable=1</TD>
    <TD>pcw_spi0_grp_ss1_io=EMIO</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi0_grp_ss2_enable=1</TD>
    <TD>pcw_spi0_grp_ss2_io=EMIO</TD>
    <TD>pcw_spi0_peripheral_enable=1</TD>
    <TD>pcw_spi0_spi0_io=EMIO</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi1_grp_ss0_enable=1</TD>
    <TD>pcw_spi1_grp_ss0_io=EMIO</TD>
    <TD>pcw_spi1_grp_ss1_enable=1</TD>
    <TD>pcw_spi1_grp_ss1_io=EMIO</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi1_grp_ss2_enable=1</TD>
    <TD>pcw_spi1_grp_ss2_io=EMIO</TD>
    <TD>pcw_spi1_peripheral_enable=1</TD>
    <TD>pcw_spi1_spi1_io=EMIO</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_spi_peripheral_freqmhz=166.666666</TD>
    <TD>pcw_tpiu_peripheral_clksrc=External</TD>
    <TD>pcw_tpiu_peripheral_freqmhz=200</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_trace_grp_16bit_enable=0</TD>
    <TD>pcw_trace_grp_2bit_enable=0</TD>
    <TD>pcw_trace_grp_32bit_enable=0</TD>
    <TD>pcw_trace_grp_4bit_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_trace_grp_8bit_enable=0</TD>
    <TD>pcw_trace_peripheral_enable=0</TD>
    <TD>pcw_ttc0_clk0_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc0_clk0_peripheral_freqmhz=133.333333</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc0_clk1_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc0_clk1_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc0_clk2_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc0_clk2_peripheral_freqmhz=133.333333</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc0_peripheral_enable=0</TD>
    <TD>pcw_ttc1_clk0_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc1_clk0_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc1_clk1_peripheral_clksrc=CPU_1X</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc1_clk1_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc1_clk2_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc1_clk2_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc1_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc_peripheral_freqmhz=50</TD>
    <TD>pcw_uart0_baud_rate=115200</TD>
    <TD>pcw_uart0_grp_full_enable=0</TD>
    <TD>pcw_uart0_peripheral_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uart0_uart0_io=MIO 14 .. 15</TD>
    <TD>pcw_uart1_baud_rate=115200</TD>
    <TD>pcw_uart1_grp_full_enable=0</TD>
    <TD>pcw_uart1_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uart_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_uart_peripheral_freqmhz=50</TD>
    <TD>pcw_uiparam_ddr_adv_enable=0</TD>
    <TD>pcw_uiparam_ddr_al=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_bank_addr_count=3</TD>
    <TD>pcw_uiparam_ddr_bl=8</TD>
    <TD>pcw_uiparam_ddr_board_delay0=0.41</TD>
    <TD>pcw_uiparam_ddr_board_delay1=0.411</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_board_delay2=0.341</TD>
    <TD>pcw_uiparam_ddr_board_delay3=0.358</TD>
    <TD>pcw_uiparam_ddr_bus_width=32 Bit</TD>
    <TD>pcw_uiparam_ddr_cl=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_clock_0_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_clock_0_package_length=61.0905</TD>
    <TD>pcw_uiparam_ddr_clock_0_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_clock_1_length_mm=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_clock_1_package_length=61.0905</TD>
    <TD>pcw_uiparam_ddr_clock_1_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_clock_2_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_clock_2_package_length=61.0905</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_clock_2_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_clock_3_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_clock_3_package_length=61.0905</TD>
    <TD>pcw_uiparam_ddr_clock_3_propogation_delay=160</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_clock_stop_en=0</TD>
    <TD>pcw_uiparam_ddr_col_addr_count=10</TD>
    <TD>pcw_uiparam_ddr_cwl=6</TD>
    <TD>pcw_uiparam_ddr_device_capacity=4096 MBits</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dq_0_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dq_0_package_length=64.1705</TD>
    <TD>pcw_uiparam_ddr_dq_0_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dq_1_length_mm=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dq_1_package_length=63.686</TD>
    <TD>pcw_uiparam_ddr_dq_1_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dq_2_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dq_2_package_length=68.46</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dq_2_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dq_3_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dq_3_package_length=105.4895</TD>
    <TD>pcw_uiparam_ddr_dq_3_propogation_delay=160</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_0_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dqs_0_package_length=68.4725</TD>
    <TD>pcw_uiparam_ddr_dqs_0_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_1_length_mm=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_1_package_length=71.086</TD>
    <TD>pcw_uiparam_ddr_dqs_1_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_2_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dqs_2_package_length=66.794</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_2_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_3_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dqs_3_package_length=108.7385</TD>
    <TD>pcw_uiparam_ddr_dqs_3_propogation_delay=160</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_0=0.025</TD>
    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_1=0.028</TD>
    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_2=-0.009</TD>
    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_3=-0.061</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dram_width=16 Bits</TD>
    <TD>pcw_uiparam_ddr_ecc=Disabled</TD>
    <TD>pcw_uiparam_ddr_enable=1</TD>
    <TD>pcw_uiparam_ddr_freq_mhz=533.333313</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_high_temp=Normal (0-85)</TD>
    <TD>pcw_uiparam_ddr_memory_type=DDR 3</TD>
    <TD>pcw_uiparam_ddr_partno=MT41K256M16 RE-125</TD>
    <TD>pcw_uiparam_ddr_row_addr_count=15</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_speed_bin=DDR3_1066F</TD>
    <TD>pcw_uiparam_ddr_t_faw=40.0</TD>
    <TD>pcw_uiparam_ddr_t_ras_min=35.0</TD>
    <TD>pcw_uiparam_ddr_t_rc=48.75</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_t_rcd=7</TD>
    <TD>pcw_uiparam_ddr_t_rp=7</TD>
    <TD>pcw_uiparam_ddr_train_data_eye=1</TD>
    <TD>pcw_uiparam_ddr_train_read_gate=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_train_write_level=1</TD>
    <TD>pcw_uiparam_ddr_use_internal_vref=1</TD>
    <TD>pcw_usb0_peripheral_enable=0</TD>
    <TD>pcw_usb0_peripheral_freqmhz=60</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_usb0_reset_enable=0</TD>
    <TD>pcw_usb1_peripheral_enable=0</TD>
    <TD>pcw_usb1_peripheral_freqmhz=60</TD>
    <TD>pcw_usb1_reset_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_usb_reset_polarity=Active Low</TD>
    <TD>pcw_use_cross_trigger=0</TD>
    <TD>pcw_use_m_axi_gp0=1</TD>
    <TD>pcw_use_m_axi_gp1=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_use_s_axi_acp=0</TD>
    <TD>pcw_use_s_axi_gp0=0</TD>
    <TD>pcw_use_s_axi_gp1=0</TD>
    <TD>pcw_use_s_axi_hp0=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_use_s_axi_hp1=1</TD>
    <TD>pcw_use_s_axi_hp2=1</TD>
    <TD>pcw_use_s_axi_hp3=0</TD>
    <TD>pcw_wdt_peripheral_clksrc=CPU_1X</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_wdt_peripheral_enable=0</TD>
    <TD>pcw_wdt_peripheral_freqmhz=133.333333</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>processing_system7_v5_5_processing_system7/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_dm_width=4</TD>
    <TD>c_dq_width=32</TD>
    <TD>c_dqs_width=4</TD>
    <TD>c_emio_gpio_width=64</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_en_emio_enet0=0</TD>
    <TD>c_en_emio_enet1=0</TD>
    <TD>c_en_emio_pjtag=0</TD>
    <TD>c_en_emio_trace=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fclk_clk0_buf=TRUE</TD>
    <TD>c_fclk_clk1_buf=TRUE</TD>
    <TD>c_fclk_clk2_buf=FALSE</TD>
    <TD>c_fclk_clk3_buf=FALSE</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gp0_en_modifiable_txn=0</TD>
    <TD>c_gp1_en_modifiable_txn=0</TD>
    <TD>c_include_acp_trans_check=0</TD>
    <TD>c_include_trace_buffer=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_irq_f2p_mode=REVERSE</TD>
    <TD>c_m_axi_gp0_enable_static_remap=0</TD>
    <TD>c_m_axi_gp0_id_width=12</TD>
    <TD>c_m_axi_gp0_thread_id_width=12</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_gp1_enable_static_remap=0</TD>
    <TD>c_m_axi_gp1_id_width=12</TD>
    <TD>c_m_axi_gp1_thread_id_width=12</TD>
    <TD>c_mio_primitive=54</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_f2p_intr_inputs=16</TD>
    <TD>c_package_name=clg484</TD>
    <TD>c_ps7_si_rev=PRODUCTION</TD>
    <TD>c_s_axi_acp_aruser_val=31</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_acp_awuser_val=31</TD>
    <TD>c_s_axi_acp_id_width=3</TD>
    <TD>c_s_axi_gp0_id_width=6</TD>
    <TD>c_s_axi_gp1_id_width=6</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_hp0_data_width=64</TD>
    <TD>c_s_axi_hp0_id_width=6</TD>
    <TD>c_s_axi_hp1_data_width=64</TD>
    <TD>c_s_axi_hp1_id_width=6</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_hp2_data_width=64</TD>
    <TD>c_s_axi_hp2_id_width=6</TD>
    <TD>c_s_axi_hp3_data_width=64</TD>
    <TD>c_s_axi_hp3_id_width=6</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_trace_buffer_clock_delay=12</TD>
    <TD>c_trace_buffer_fifo_size=128</TD>
    <TD>c_trace_internal_width=2</TD>
    <TD>c_trace_pipeline_width=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_use_axi_nonsecure=0</TD>
    <TD>c_use_default_acp_user_val=0</TD>
    <TD>c_use_m_axi_gp0=1</TD>
    <TD>c_use_m_axi_gp1=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_use_s_axi_acp=0</TD>
    <TD>c_use_s_axi_gp0=0</TD>
    <TD>c_use_s_axi_hp0=1</TD>
    <TD>c_use_s_axi_hp1=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_use_s_axi_hp2=1</TD>
    <TD>c_use_s_axi_hp3=0</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>use_trace_data_edge_detector=0</TD>
    <TD>x_ipcorerevision=3</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipname=processing_system7</TD>
    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipversion=5.5</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>util_reduced_logic/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_operation=and</TD>
    <TD>c_size=2</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=2</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=util_reduced_logic</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>vio/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_build_revision=0</TD>
    <TD>c_core_info1=0</TD>
    <TD>c_core_info2=0</TD>
    <TD>c_core_major_ver=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_core_minor_alpha_ver=97</TD>
    <TD>c_core_minor_ver=0</TD>
    <TD>c_core_type=2</TD>
    <TD>c_cse_drv_ver=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_en_probe_in_activity=1</TD>
    <TD>c_major_version=2013</TD>
    <TD>c_minor_version=1</TD>
    <TD>c_next_slave=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_probe_in=4</TD>
    <TD>c_num_probe_out=24</TD>
    <TD>c_pipe_iface=0</TD>
    <TD>c_probe_in0_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in100_width=1</TD>
    <TD>c_probe_in101_width=1</TD>
    <TD>c_probe_in102_width=1</TD>
    <TD>c_probe_in103_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in104_width=1</TD>
    <TD>c_probe_in105_width=1</TD>
    <TD>c_probe_in106_width=1</TD>
    <TD>c_probe_in107_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in108_width=1</TD>
    <TD>c_probe_in109_width=1</TD>
    <TD>c_probe_in10_width=1</TD>
    <TD>c_probe_in110_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in111_width=1</TD>
    <TD>c_probe_in112_width=1</TD>
    <TD>c_probe_in113_width=1</TD>
    <TD>c_probe_in114_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in115_width=1</TD>
    <TD>c_probe_in116_width=1</TD>
    <TD>c_probe_in117_width=1</TD>
    <TD>c_probe_in118_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in119_width=1</TD>
    <TD>c_probe_in11_width=1</TD>
    <TD>c_probe_in120_width=1</TD>
    <TD>c_probe_in121_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in122_width=1</TD>
    <TD>c_probe_in123_width=1</TD>
    <TD>c_probe_in124_width=1</TD>
    <TD>c_probe_in125_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in126_width=1</TD>
    <TD>c_probe_in127_width=1</TD>
    <TD>c_probe_in128_width=1</TD>
    <TD>c_probe_in129_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in12_width=1</TD>
    <TD>c_probe_in130_width=1</TD>
    <TD>c_probe_in131_width=1</TD>
    <TD>c_probe_in132_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in133_width=1</TD>
    <TD>c_probe_in134_width=1</TD>
    <TD>c_probe_in135_width=1</TD>
    <TD>c_probe_in136_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in137_width=1</TD>
    <TD>c_probe_in138_width=1</TD>
    <TD>c_probe_in139_width=1</TD>
    <TD>c_probe_in13_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in140_width=1</TD>
    <TD>c_probe_in141_width=1</TD>
    <TD>c_probe_in142_width=1</TD>
    <TD>c_probe_in143_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in144_width=1</TD>
    <TD>c_probe_in145_width=1</TD>
    <TD>c_probe_in146_width=1</TD>
    <TD>c_probe_in147_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in148_width=1</TD>
    <TD>c_probe_in149_width=1</TD>
    <TD>c_probe_in14_width=1</TD>
    <TD>c_probe_in150_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in151_width=1</TD>
    <TD>c_probe_in152_width=1</TD>
    <TD>c_probe_in153_width=1</TD>
    <TD>c_probe_in154_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in155_width=1</TD>
    <TD>c_probe_in156_width=1</TD>
    <TD>c_probe_in157_width=1</TD>
    <TD>c_probe_in158_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in159_width=1</TD>
    <TD>c_probe_in15_width=1</TD>
    <TD>c_probe_in160_width=1</TD>
    <TD>c_probe_in161_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in162_width=1</TD>
    <TD>c_probe_in163_width=1</TD>
    <TD>c_probe_in164_width=1</TD>
    <TD>c_probe_in165_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in166_width=1</TD>
    <TD>c_probe_in167_width=1</TD>
    <TD>c_probe_in168_width=1</TD>
    <TD>c_probe_in169_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in16_width=1</TD>
    <TD>c_probe_in170_width=1</TD>
    <TD>c_probe_in171_width=1</TD>
    <TD>c_probe_in172_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in173_width=1</TD>
    <TD>c_probe_in174_width=1</TD>
    <TD>c_probe_in175_width=1</TD>
    <TD>c_probe_in176_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in177_width=1</TD>
    <TD>c_probe_in178_width=1</TD>
    <TD>c_probe_in179_width=1</TD>
    <TD>c_probe_in17_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in180_width=1</TD>
    <TD>c_probe_in181_width=1</TD>
    <TD>c_probe_in182_width=1</TD>
    <TD>c_probe_in183_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in184_width=1</TD>
    <TD>c_probe_in185_width=1</TD>
    <TD>c_probe_in186_width=1</TD>
    <TD>c_probe_in187_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in188_width=1</TD>
    <TD>c_probe_in189_width=1</TD>
    <TD>c_probe_in18_width=1</TD>
    <TD>c_probe_in190_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in191_width=1</TD>
    <TD>c_probe_in192_width=1</TD>
    <TD>c_probe_in193_width=1</TD>
    <TD>c_probe_in194_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in195_width=1</TD>
    <TD>c_probe_in196_width=1</TD>
    <TD>c_probe_in197_width=1</TD>
    <TD>c_probe_in198_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in199_width=1</TD>
    <TD>c_probe_in19_width=1</TD>
    <TD>c_probe_in1_width=1</TD>
    <TD>c_probe_in200_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in201_width=1</TD>
    <TD>c_probe_in202_width=1</TD>
    <TD>c_probe_in203_width=1</TD>
    <TD>c_probe_in204_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in205_width=1</TD>
    <TD>c_probe_in206_width=1</TD>
    <TD>c_probe_in207_width=1</TD>
    <TD>c_probe_in208_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in209_width=1</TD>
    <TD>c_probe_in20_width=1</TD>
    <TD>c_probe_in210_width=1</TD>
    <TD>c_probe_in211_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in212_width=1</TD>
    <TD>c_probe_in213_width=1</TD>
    <TD>c_probe_in214_width=1</TD>
    <TD>c_probe_in215_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in216_width=1</TD>
    <TD>c_probe_in217_width=1</TD>
    <TD>c_probe_in218_width=1</TD>
    <TD>c_probe_in219_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in21_width=1</TD>
    <TD>c_probe_in220_width=1</TD>
    <TD>c_probe_in221_width=1</TD>
    <TD>c_probe_in222_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in223_width=1</TD>
    <TD>c_probe_in224_width=1</TD>
    <TD>c_probe_in225_width=1</TD>
    <TD>c_probe_in226_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in227_width=1</TD>
    <TD>c_probe_in228_width=1</TD>
    <TD>c_probe_in229_width=1</TD>
    <TD>c_probe_in22_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in230_width=1</TD>
    <TD>c_probe_in231_width=1</TD>
    <TD>c_probe_in232_width=1</TD>
    <TD>c_probe_in233_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in234_width=1</TD>
    <TD>c_probe_in235_width=1</TD>
    <TD>c_probe_in236_width=1</TD>
    <TD>c_probe_in237_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in238_width=1</TD>
    <TD>c_probe_in239_width=1</TD>
    <TD>c_probe_in23_width=1</TD>
    <TD>c_probe_in240_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in241_width=1</TD>
    <TD>c_probe_in242_width=1</TD>
    <TD>c_probe_in243_width=1</TD>
    <TD>c_probe_in244_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in245_width=1</TD>
    <TD>c_probe_in246_width=1</TD>
    <TD>c_probe_in247_width=1</TD>
    <TD>c_probe_in248_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in249_width=1</TD>
    <TD>c_probe_in24_width=1</TD>
    <TD>c_probe_in250_width=1</TD>
    <TD>c_probe_in251_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in252_width=1</TD>
    <TD>c_probe_in253_width=1</TD>
    <TD>c_probe_in254_width=1</TD>
    <TD>c_probe_in255_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in25_width=1</TD>
    <TD>c_probe_in26_width=1</TD>
    <TD>c_probe_in27_width=1</TD>
    <TD>c_probe_in28_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in29_width=1</TD>
    <TD>c_probe_in2_width=1</TD>
    <TD>c_probe_in30_width=1</TD>
    <TD>c_probe_in31_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in32_width=1</TD>
    <TD>c_probe_in33_width=1</TD>
    <TD>c_probe_in34_width=1</TD>
    <TD>c_probe_in35_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in36_width=1</TD>
    <TD>c_probe_in37_width=1</TD>
    <TD>c_probe_in38_width=1</TD>
    <TD>c_probe_in39_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in3_width=1</TD>
    <TD>c_probe_in40_width=1</TD>
    <TD>c_probe_in41_width=1</TD>
    <TD>c_probe_in42_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in43_width=1</TD>
    <TD>c_probe_in44_width=1</TD>
    <TD>c_probe_in45_width=1</TD>
    <TD>c_probe_in46_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in47_width=1</TD>
    <TD>c_probe_in48_width=1</TD>
    <TD>c_probe_in49_width=1</TD>
    <TD>c_probe_in4_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in50_width=1</TD>
    <TD>c_probe_in51_width=1</TD>
    <TD>c_probe_in52_width=1</TD>
    <TD>c_probe_in53_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in54_width=1</TD>
    <TD>c_probe_in55_width=1</TD>
    <TD>c_probe_in56_width=1</TD>
    <TD>c_probe_in57_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in58_width=1</TD>
    <TD>c_probe_in59_width=1</TD>
    <TD>c_probe_in5_width=1</TD>
    <TD>c_probe_in60_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in61_width=1</TD>
    <TD>c_probe_in62_width=1</TD>
    <TD>c_probe_in63_width=1</TD>
    <TD>c_probe_in64_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in65_width=1</TD>
    <TD>c_probe_in66_width=1</TD>
    <TD>c_probe_in67_width=1</TD>
    <TD>c_probe_in68_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in69_width=1</TD>
    <TD>c_probe_in6_width=1</TD>
    <TD>c_probe_in70_width=1</TD>
    <TD>c_probe_in71_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in72_width=1</TD>
    <TD>c_probe_in73_width=1</TD>
    <TD>c_probe_in74_width=1</TD>
    <TD>c_probe_in75_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in76_width=1</TD>
    <TD>c_probe_in77_width=1</TD>
    <TD>c_probe_in78_width=1</TD>
    <TD>c_probe_in79_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in7_width=1</TD>
    <TD>c_probe_in80_width=1</TD>
    <TD>c_probe_in81_width=1</TD>
    <TD>c_probe_in82_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in83_width=1</TD>
    <TD>c_probe_in84_width=1</TD>
    <TD>c_probe_in85_width=1</TD>
    <TD>c_probe_in86_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in87_width=1</TD>
    <TD>c_probe_in88_width=1</TD>
    <TD>c_probe_in89_width=1</TD>
    <TD>c_probe_in8_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in90_width=1</TD>
    <TD>c_probe_in91_width=1</TD>
    <TD>c_probe_in92_width=1</TD>
    <TD>c_probe_in93_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in94_width=1</TD>
    <TD>c_probe_in95_width=1</TD>
    <TD>c_probe_in96_width=1</TD>
    <TD>c_probe_in97_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_in98_width=1</TD>
    <TD>c_probe_in99_width=1</TD>
    <TD>c_probe_in9_width=1</TD>
    <TD>c_probe_out0_init_val=0x00</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out0_width=6</TD>
    <TD>c_probe_out100_init_val=0</TD>
    <TD>c_probe_out100_width=1</TD>
    <TD>c_probe_out101_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out101_width=1</TD>
    <TD>c_probe_out102_init_val=0</TD>
    <TD>c_probe_out102_width=1</TD>
    <TD>c_probe_out103_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out103_width=1</TD>
    <TD>c_probe_out104_init_val=0</TD>
    <TD>c_probe_out104_width=1</TD>
    <TD>c_probe_out105_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out105_width=1</TD>
    <TD>c_probe_out106_init_val=0</TD>
    <TD>c_probe_out106_width=1</TD>
    <TD>c_probe_out107_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out107_width=1</TD>
    <TD>c_probe_out108_init_val=0</TD>
    <TD>c_probe_out108_width=1</TD>
    <TD>c_probe_out109_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out109_width=1</TD>
    <TD>c_probe_out10_init_val=0x0</TD>
    <TD>c_probe_out10_width=1</TD>
    <TD>c_probe_out110_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out110_width=1</TD>
    <TD>c_probe_out111_init_val=0</TD>
    <TD>c_probe_out111_width=1</TD>
    <TD>c_probe_out112_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out112_width=1</TD>
    <TD>c_probe_out113_init_val=0</TD>
    <TD>c_probe_out113_width=1</TD>
    <TD>c_probe_out114_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out114_width=1</TD>
    <TD>c_probe_out115_init_val=0</TD>
    <TD>c_probe_out115_width=1</TD>
    <TD>c_probe_out116_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out116_width=1</TD>
    <TD>c_probe_out117_init_val=0</TD>
    <TD>c_probe_out117_width=1</TD>
    <TD>c_probe_out118_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out118_width=1</TD>
    <TD>c_probe_out119_init_val=0</TD>
    <TD>c_probe_out119_width=1</TD>
    <TD>c_probe_out11_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out11_width=1</TD>
    <TD>c_probe_out120_init_val=0</TD>
    <TD>c_probe_out120_width=1</TD>
    <TD>c_probe_out121_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out121_width=1</TD>
    <TD>c_probe_out122_init_val=0</TD>
    <TD>c_probe_out122_width=1</TD>
    <TD>c_probe_out123_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out123_width=1</TD>
    <TD>c_probe_out124_init_val=0</TD>
    <TD>c_probe_out124_width=1</TD>
    <TD>c_probe_out125_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out125_width=1</TD>
    <TD>c_probe_out126_init_val=0</TD>
    <TD>c_probe_out126_width=1</TD>
    <TD>c_probe_out127_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out127_width=1</TD>
    <TD>c_probe_out128_init_val=0</TD>
    <TD>c_probe_out128_width=1</TD>
    <TD>c_probe_out129_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out129_width=1</TD>
    <TD>c_probe_out12_init_val=0x0</TD>
    <TD>c_probe_out12_width=1</TD>
    <TD>c_probe_out130_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out130_width=1</TD>
    <TD>c_probe_out131_init_val=0</TD>
    <TD>c_probe_out131_width=1</TD>
    <TD>c_probe_out132_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out132_width=1</TD>
    <TD>c_probe_out133_init_val=0</TD>
    <TD>c_probe_out133_width=1</TD>
    <TD>c_probe_out134_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out134_width=1</TD>
    <TD>c_probe_out135_init_val=0</TD>
    <TD>c_probe_out135_width=1</TD>
    <TD>c_probe_out136_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out136_width=1</TD>
    <TD>c_probe_out137_init_val=0</TD>
    <TD>c_probe_out137_width=1</TD>
    <TD>c_probe_out138_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out138_width=1</TD>
    <TD>c_probe_out139_init_val=0</TD>
    <TD>c_probe_out139_width=1</TD>
    <TD>c_probe_out13_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out13_width=1</TD>
    <TD>c_probe_out140_init_val=0</TD>
    <TD>c_probe_out140_width=1</TD>
    <TD>c_probe_out141_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out141_width=1</TD>
    <TD>c_probe_out142_init_val=0</TD>
    <TD>c_probe_out142_width=1</TD>
    <TD>c_probe_out143_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out143_width=1</TD>
    <TD>c_probe_out144_init_val=0</TD>
    <TD>c_probe_out144_width=1</TD>
    <TD>c_probe_out145_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out145_width=1</TD>
    <TD>c_probe_out146_init_val=0</TD>
    <TD>c_probe_out146_width=1</TD>
    <TD>c_probe_out147_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out147_width=1</TD>
    <TD>c_probe_out148_init_val=0</TD>
    <TD>c_probe_out148_width=1</TD>
    <TD>c_probe_out149_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out149_width=1</TD>
    <TD>c_probe_out14_init_val=0x0</TD>
    <TD>c_probe_out14_width=1</TD>
    <TD>c_probe_out150_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out150_width=1</TD>
    <TD>c_probe_out151_init_val=0</TD>
    <TD>c_probe_out151_width=1</TD>
    <TD>c_probe_out152_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out152_width=1</TD>
    <TD>c_probe_out153_init_val=0</TD>
    <TD>c_probe_out153_width=1</TD>
    <TD>c_probe_out154_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out154_width=1</TD>
    <TD>c_probe_out155_init_val=0</TD>
    <TD>c_probe_out155_width=1</TD>
    <TD>c_probe_out156_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out156_width=1</TD>
    <TD>c_probe_out157_init_val=0</TD>
    <TD>c_probe_out157_width=1</TD>
    <TD>c_probe_out158_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out158_width=1</TD>
    <TD>c_probe_out159_init_val=0</TD>
    <TD>c_probe_out159_width=1</TD>
    <TD>c_probe_out15_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out15_width=1</TD>
    <TD>c_probe_out160_init_val=0</TD>
    <TD>c_probe_out160_width=1</TD>
    <TD>c_probe_out161_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out161_width=1</TD>
    <TD>c_probe_out162_init_val=0</TD>
    <TD>c_probe_out162_width=1</TD>
    <TD>c_probe_out163_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out163_width=1</TD>
    <TD>c_probe_out164_init_val=0</TD>
    <TD>c_probe_out164_width=1</TD>
    <TD>c_probe_out165_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out165_width=1</TD>
    <TD>c_probe_out166_init_val=0</TD>
    <TD>c_probe_out166_width=1</TD>
    <TD>c_probe_out167_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out167_width=1</TD>
    <TD>c_probe_out168_init_val=0</TD>
    <TD>c_probe_out168_width=1</TD>
    <TD>c_probe_out169_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out169_width=1</TD>
    <TD>c_probe_out16_init_val=0x0</TD>
    <TD>c_probe_out16_width=1</TD>
    <TD>c_probe_out170_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out170_width=1</TD>
    <TD>c_probe_out171_init_val=0</TD>
    <TD>c_probe_out171_width=1</TD>
    <TD>c_probe_out172_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out172_width=1</TD>
    <TD>c_probe_out173_init_val=0</TD>
    <TD>c_probe_out173_width=1</TD>
    <TD>c_probe_out174_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out174_width=1</TD>
    <TD>c_probe_out175_init_val=0</TD>
    <TD>c_probe_out175_width=1</TD>
    <TD>c_probe_out176_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out176_width=1</TD>
    <TD>c_probe_out177_init_val=0</TD>
    <TD>c_probe_out177_width=1</TD>
    <TD>c_probe_out178_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out178_width=1</TD>
    <TD>c_probe_out179_init_val=0</TD>
    <TD>c_probe_out179_width=1</TD>
    <TD>c_probe_out17_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out17_width=1</TD>
    <TD>c_probe_out180_init_val=0</TD>
    <TD>c_probe_out180_width=1</TD>
    <TD>c_probe_out181_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out181_width=1</TD>
    <TD>c_probe_out182_init_val=0</TD>
    <TD>c_probe_out182_width=1</TD>
    <TD>c_probe_out183_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out183_width=1</TD>
    <TD>c_probe_out184_init_val=0</TD>
    <TD>c_probe_out184_width=1</TD>
    <TD>c_probe_out185_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out185_width=1</TD>
    <TD>c_probe_out186_init_val=0</TD>
    <TD>c_probe_out186_width=1</TD>
    <TD>c_probe_out187_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out187_width=1</TD>
    <TD>c_probe_out188_init_val=0</TD>
    <TD>c_probe_out188_width=1</TD>
    <TD>c_probe_out189_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out189_width=1</TD>
    <TD>c_probe_out18_init_val=0x0</TD>
    <TD>c_probe_out18_width=1</TD>
    <TD>c_probe_out190_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out190_width=1</TD>
    <TD>c_probe_out191_init_val=0</TD>
    <TD>c_probe_out191_width=1</TD>
    <TD>c_probe_out192_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out192_width=1</TD>
    <TD>c_probe_out193_init_val=0</TD>
    <TD>c_probe_out193_width=1</TD>
    <TD>c_probe_out194_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out194_width=1</TD>
    <TD>c_probe_out195_init_val=0</TD>
    <TD>c_probe_out195_width=1</TD>
    <TD>c_probe_out196_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out196_width=1</TD>
    <TD>c_probe_out197_init_val=0</TD>
    <TD>c_probe_out197_width=1</TD>
    <TD>c_probe_out198_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out198_width=1</TD>
    <TD>c_probe_out199_init_val=0</TD>
    <TD>c_probe_out199_width=1</TD>
    <TD>c_probe_out19_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out19_width=1</TD>
    <TD>c_probe_out1_init_val=0x0</TD>
    <TD>c_probe_out1_width=1</TD>
    <TD>c_probe_out200_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out200_width=1</TD>
    <TD>c_probe_out201_init_val=0</TD>
    <TD>c_probe_out201_width=1</TD>
    <TD>c_probe_out202_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out202_width=1</TD>
    <TD>c_probe_out203_init_val=0</TD>
    <TD>c_probe_out203_width=1</TD>
    <TD>c_probe_out204_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out204_width=1</TD>
    <TD>c_probe_out205_init_val=0</TD>
    <TD>c_probe_out205_width=1</TD>
    <TD>c_probe_out206_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out206_width=1</TD>
    <TD>c_probe_out207_init_val=0</TD>
    <TD>c_probe_out207_width=1</TD>
    <TD>c_probe_out208_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out208_width=1</TD>
    <TD>c_probe_out209_init_val=0</TD>
    <TD>c_probe_out209_width=1</TD>
    <TD>c_probe_out20_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out20_width=1</TD>
    <TD>c_probe_out210_init_val=0</TD>
    <TD>c_probe_out210_width=1</TD>
    <TD>c_probe_out211_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out211_width=1</TD>
    <TD>c_probe_out212_init_val=0</TD>
    <TD>c_probe_out212_width=1</TD>
    <TD>c_probe_out213_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out213_width=1</TD>
    <TD>c_probe_out214_init_val=0</TD>
    <TD>c_probe_out214_width=1</TD>
    <TD>c_probe_out215_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out215_width=1</TD>
    <TD>c_probe_out216_init_val=0</TD>
    <TD>c_probe_out216_width=1</TD>
    <TD>c_probe_out217_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out217_width=1</TD>
    <TD>c_probe_out218_init_val=0</TD>
    <TD>c_probe_out218_width=1</TD>
    <TD>c_probe_out219_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out219_width=1</TD>
    <TD>c_probe_out21_init_val=0x0</TD>
    <TD>c_probe_out21_width=1</TD>
    <TD>c_probe_out220_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out220_width=1</TD>
    <TD>c_probe_out221_init_val=0</TD>
    <TD>c_probe_out221_width=1</TD>
    <TD>c_probe_out222_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out222_width=1</TD>
    <TD>c_probe_out223_init_val=0</TD>
    <TD>c_probe_out223_width=1</TD>
    <TD>c_probe_out224_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out224_width=1</TD>
    <TD>c_probe_out225_init_val=0</TD>
    <TD>c_probe_out225_width=1</TD>
    <TD>c_probe_out226_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out226_width=1</TD>
    <TD>c_probe_out227_init_val=0</TD>
    <TD>c_probe_out227_width=1</TD>
    <TD>c_probe_out228_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out228_width=1</TD>
    <TD>c_probe_out229_init_val=0</TD>
    <TD>c_probe_out229_width=1</TD>
    <TD>c_probe_out22_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out22_width=1</TD>
    <TD>c_probe_out230_init_val=0</TD>
    <TD>c_probe_out230_width=1</TD>
    <TD>c_probe_out231_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out231_width=1</TD>
    <TD>c_probe_out232_init_val=0</TD>
    <TD>c_probe_out232_width=1</TD>
    <TD>c_probe_out233_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out233_width=1</TD>
    <TD>c_probe_out234_init_val=0</TD>
    <TD>c_probe_out234_width=1</TD>
    <TD>c_probe_out235_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out235_width=1</TD>
    <TD>c_probe_out236_init_val=0</TD>
    <TD>c_probe_out236_width=1</TD>
    <TD>c_probe_out237_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out237_width=1</TD>
    <TD>c_probe_out238_init_val=0</TD>
    <TD>c_probe_out238_width=1</TD>
    <TD>c_probe_out239_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out239_width=1</TD>
    <TD>c_probe_out23_init_val=0x0</TD>
    <TD>c_probe_out23_width=1</TD>
    <TD>c_probe_out240_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out240_width=1</TD>
    <TD>c_probe_out241_init_val=0</TD>
    <TD>c_probe_out241_width=1</TD>
    <TD>c_probe_out242_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out242_width=1</TD>
    <TD>c_probe_out243_init_val=0</TD>
    <TD>c_probe_out243_width=1</TD>
    <TD>c_probe_out244_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out244_width=1</TD>
    <TD>c_probe_out245_init_val=0</TD>
    <TD>c_probe_out245_width=1</TD>
    <TD>c_probe_out246_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out246_width=1</TD>
    <TD>c_probe_out247_init_val=0</TD>
    <TD>c_probe_out247_width=1</TD>
    <TD>c_probe_out248_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out248_width=1</TD>
    <TD>c_probe_out249_init_val=0</TD>
    <TD>c_probe_out249_width=1</TD>
    <TD>c_probe_out24_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out24_width=1</TD>
    <TD>c_probe_out250_init_val=0</TD>
    <TD>c_probe_out250_width=1</TD>
    <TD>c_probe_out251_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out251_width=1</TD>
    <TD>c_probe_out252_init_val=0</TD>
    <TD>c_probe_out252_width=1</TD>
    <TD>c_probe_out253_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out253_width=1</TD>
    <TD>c_probe_out254_init_val=0</TD>
    <TD>c_probe_out254_width=1</TD>
    <TD>c_probe_out255_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out255_width=1</TD>
    <TD>c_probe_out25_init_val=0</TD>
    <TD>c_probe_out25_width=1</TD>
    <TD>c_probe_out26_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out26_width=1</TD>
    <TD>c_probe_out27_init_val=0</TD>
    <TD>c_probe_out27_width=1</TD>
    <TD>c_probe_out28_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out28_width=1</TD>
    <TD>c_probe_out29_init_val=0</TD>
    <TD>c_probe_out29_width=1</TD>
    <TD>c_probe_out2_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out2_width=1</TD>
    <TD>c_probe_out30_init_val=0</TD>
    <TD>c_probe_out30_width=1</TD>
    <TD>c_probe_out31_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out31_width=1</TD>
    <TD>c_probe_out32_init_val=0</TD>
    <TD>c_probe_out32_width=1</TD>
    <TD>c_probe_out33_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out33_width=1</TD>
    <TD>c_probe_out34_init_val=0</TD>
    <TD>c_probe_out34_width=1</TD>
    <TD>c_probe_out35_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out35_width=1</TD>
    <TD>c_probe_out36_init_val=0</TD>
    <TD>c_probe_out36_width=1</TD>
    <TD>c_probe_out37_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out37_width=1</TD>
    <TD>c_probe_out38_init_val=0</TD>
    <TD>c_probe_out38_width=1</TD>
    <TD>c_probe_out39_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out39_width=1</TD>
    <TD>c_probe_out3_init_val=0x0</TD>
    <TD>c_probe_out3_width=1</TD>
    <TD>c_probe_out40_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out40_width=1</TD>
    <TD>c_probe_out41_init_val=0</TD>
    <TD>c_probe_out41_width=1</TD>
    <TD>c_probe_out42_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out42_width=1</TD>
    <TD>c_probe_out43_init_val=0</TD>
    <TD>c_probe_out43_width=1</TD>
    <TD>c_probe_out44_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out44_width=1</TD>
    <TD>c_probe_out45_init_val=0</TD>
    <TD>c_probe_out45_width=1</TD>
    <TD>c_probe_out46_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out46_width=1</TD>
    <TD>c_probe_out47_init_val=0</TD>
    <TD>c_probe_out47_width=1</TD>
    <TD>c_probe_out48_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out48_width=1</TD>
    <TD>c_probe_out49_init_val=0</TD>
    <TD>c_probe_out49_width=1</TD>
    <TD>c_probe_out4_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out4_width=1</TD>
    <TD>c_probe_out50_init_val=0</TD>
    <TD>c_probe_out50_width=1</TD>
    <TD>c_probe_out51_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out51_width=1</TD>
    <TD>c_probe_out52_init_val=0</TD>
    <TD>c_probe_out52_width=1</TD>
    <TD>c_probe_out53_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out53_width=1</TD>
    <TD>c_probe_out54_init_val=0</TD>
    <TD>c_probe_out54_width=1</TD>
    <TD>c_probe_out55_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out55_width=1</TD>
    <TD>c_probe_out56_init_val=0</TD>
    <TD>c_probe_out56_width=1</TD>
    <TD>c_probe_out57_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out57_width=1</TD>
    <TD>c_probe_out58_init_val=0</TD>
    <TD>c_probe_out58_width=1</TD>
    <TD>c_probe_out59_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out59_width=1</TD>
    <TD>c_probe_out5_init_val=0x0</TD>
    <TD>c_probe_out5_width=1</TD>
    <TD>c_probe_out60_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out60_width=1</TD>
    <TD>c_probe_out61_init_val=0</TD>
    <TD>c_probe_out61_width=1</TD>
    <TD>c_probe_out62_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out62_width=1</TD>
    <TD>c_probe_out63_init_val=0</TD>
    <TD>c_probe_out63_width=1</TD>
    <TD>c_probe_out64_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out64_width=1</TD>
    <TD>c_probe_out65_init_val=0</TD>
    <TD>c_probe_out65_width=1</TD>
    <TD>c_probe_out66_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out66_width=1</TD>
    <TD>c_probe_out67_init_val=0</TD>
    <TD>c_probe_out67_width=1</TD>
    <TD>c_probe_out68_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out68_width=1</TD>
    <TD>c_probe_out69_init_val=0</TD>
    <TD>c_probe_out69_width=1</TD>
    <TD>c_probe_out6_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out6_width=1</TD>
    <TD>c_probe_out70_init_val=0</TD>
    <TD>c_probe_out70_width=1</TD>
    <TD>c_probe_out71_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out71_width=1</TD>
    <TD>c_probe_out72_init_val=0</TD>
    <TD>c_probe_out72_width=1</TD>
    <TD>c_probe_out73_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out73_width=1</TD>
    <TD>c_probe_out74_init_val=0</TD>
    <TD>c_probe_out74_width=1</TD>
    <TD>c_probe_out75_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out75_width=1</TD>
    <TD>c_probe_out76_init_val=0</TD>
    <TD>c_probe_out76_width=1</TD>
    <TD>c_probe_out77_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out77_width=1</TD>
    <TD>c_probe_out78_init_val=0</TD>
    <TD>c_probe_out78_width=1</TD>
    <TD>c_probe_out79_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out79_width=1</TD>
    <TD>c_probe_out7_init_val=0x0</TD>
    <TD>c_probe_out7_width=1</TD>
    <TD>c_probe_out80_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out80_width=1</TD>
    <TD>c_probe_out81_init_val=0</TD>
    <TD>c_probe_out81_width=1</TD>
    <TD>c_probe_out82_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out82_width=1</TD>
    <TD>c_probe_out83_init_val=0</TD>
    <TD>c_probe_out83_width=1</TD>
    <TD>c_probe_out84_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out84_width=1</TD>
    <TD>c_probe_out85_init_val=0</TD>
    <TD>c_probe_out85_width=1</TD>
    <TD>c_probe_out86_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out86_width=1</TD>
    <TD>c_probe_out87_init_val=0</TD>
    <TD>c_probe_out87_width=1</TD>
    <TD>c_probe_out88_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out88_width=1</TD>
    <TD>c_probe_out89_init_val=0</TD>
    <TD>c_probe_out89_width=1</TD>
    <TD>c_probe_out8_init_val=0x0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out8_width=1</TD>
    <TD>c_probe_out90_init_val=0</TD>
    <TD>c_probe_out90_width=1</TD>
    <TD>c_probe_out91_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out91_width=1</TD>
    <TD>c_probe_out92_init_val=0</TD>
    <TD>c_probe_out92_width=1</TD>
    <TD>c_probe_out93_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out93_width=1</TD>
    <TD>c_probe_out94_init_val=0</TD>
    <TD>c_probe_out94_width=1</TD>
    <TD>c_probe_out95_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out95_width=1</TD>
    <TD>c_probe_out96_init_val=0</TD>
    <TD>c_probe_out96_width=1</TD>
    <TD>c_probe_out97_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out97_width=1</TD>
    <TD>c_probe_out98_init_val=0</TD>
    <TD>c_probe_out98_width=1</TD>
    <TD>c_probe_out99_init_val=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_probe_out99_width=1</TD>
    <TD>c_probe_out9_init_val=0x0</TD>
    <TD>c_probe_out9_width=1</TD>
    <TD>c_use_test_reg=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_xdevicefamily=zynq</TD>
    <TD>c_xlnx_hw_probe_info=DEFAULT</TD>
    <TD>c_xsdb_slave_type=33</TD>
    <TD>core_container=NA</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=1</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=vio</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=3.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xlconcat/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>dout_width=16</TD>
    <TD>in0_width=1</TD>
    <TD>in10_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in11_width=1</TD>
    <TD>in12_width=1</TD>
    <TD>in13_width=1</TD>
    <TD>in14_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in15_width=1</TD>
    <TD>in16_width=1</TD>
    <TD>in17_width=1</TD>
    <TD>in18_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in19_width=1</TD>
    <TD>in1_width=1</TD>
    <TD>in20_width=1</TD>
    <TD>in21_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in22_width=1</TD>
    <TD>in23_width=1</TD>
    <TD>in24_width=1</TD>
    <TD>in25_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in26_width=1</TD>
    <TD>in27_width=1</TD>
    <TD>in28_width=1</TD>
    <TD>in29_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in2_width=1</TD>
    <TD>in30_width=1</TD>
    <TD>in31_width=1</TD>
    <TD>in3_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in4_width=1</TD>
    <TD>in5_width=1</TD>
    <TD>in6_width=1</TD>
    <TD>in7_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in8_width=1</TD>
    <TD>in9_width=1</TD>
    <TD>iptotal=1</TD>
    <TD>num_ports=16</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=2</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=xlconcat</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xlconcat/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>dout_width=2</TD>
    <TD>in0_width=1</TD>
    <TD>in10_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in11_width=1</TD>
    <TD>in12_width=1</TD>
    <TD>in13_width=1</TD>
    <TD>in14_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in15_width=1</TD>
    <TD>in16_width=1</TD>
    <TD>in17_width=1</TD>
    <TD>in18_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in19_width=1</TD>
    <TD>in1_width=1</TD>
    <TD>in20_width=1</TD>
    <TD>in21_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in22_width=1</TD>
    <TD>in23_width=1</TD>
    <TD>in24_width=1</TD>
    <TD>in25_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in26_width=1</TD>
    <TD>in27_width=1</TD>
    <TD>in28_width=1</TD>
    <TD>in29_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in2_width=1</TD>
    <TD>in30_width=1</TD>
    <TD>in31_width=1</TD>
    <TD>in3_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in4_width=1</TD>
    <TD>in5_width=1</TD>
    <TD>in6_width=1</TD>
    <TD>in7_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in8_width=1</TD>
    <TD>in9_width=1</TD>
    <TD>iptotal=1</TD>
    <TD>num_ports=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=2</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=xlconcat</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2016.4</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_drc</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-append=default::[not_specified]</TD>
    <TD>-checks=default::[not_specified]</TD>
    <TD>-fail_on=default::[not_specified]</TD>
    <TD>-force=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-format=default::[not_specified]</TD>
    <TD>-messages=default::[not_specified]</TD>
    <TD>-name=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-ruledecks=default::[not_specified]</TD>
    <TD>-upgrade_cw=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>results</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>aval-4=56</TD>
    <TD>check-3=1</TD>
    <TD>dpop-1=4</TD>
    <TD>reqp-1839=20</TD>
</TR><TR ALIGN='LEFT'>    <TD>rtstat-10=1</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_methodology</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-append=default::[not_specified]</TD>
    <TD>-checks=default::[not_specified]</TD>
    <TD>-fail_on=default::[not_specified]</TD>
    <TD>-force=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-format=default::[not_specified]</TD>
    <TD>-messages=default::[not_specified]</TD>
    <TD>-name=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>results</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>pdrc-190=18</TD>
    <TD>timing-17=1000</TD>
    <TD>timing-18=33</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_power</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-advisory=default::[not_specified]</TD>
    <TD>-append=default::[not_specified]</TD>
    <TD>-file=[specified]</TD>
    <TD>-format=default::text</TD>
</TR><TR ALIGN='LEFT'>    <TD>-hier=default::power</TD>
    <TD>-l=default::[not_specified]</TD>
    <TD>-name=default::[not_specified]</TD>
    <TD>-no_propagation=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-return_string=default::[not_specified]</TD>
    <TD>-rpx=[specified]</TD>
    <TD>-verbose=default::[not_specified]</TD>
    <TD>-vid=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-xpe=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>airflow=250 (LFM)</TD>
    <TD>ambient_temp=25.0 (C)</TD>
    <TD>bi-dir_toggle=12.500000</TD>
    <TD>bidir_output_enable=1.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>board_layers=12to15 (12 to 15 Layers)</TD>
    <TD>board_selection=medium (10&quot;x10&quot;)</TD>
    <TD>bram=0.002252</TD>
    <TD>clocks=0.036319</TD>
</TR><TR ALIGN='LEFT'>    <TD>confidence_level_clock_activity=Low</TD>
    <TD>confidence_level_design_state=High</TD>
    <TD>confidence_level_device_models=High</TD>
    <TD>confidence_level_internal_activity=Medium</TD>
</TR><TR ALIGN='LEFT'>    <TD>confidence_level_io_activity=Low</TD>
    <TD>confidence_level_overall=Low</TD>
    <TD>customer=TBD</TD>
    <TD>customer_class=TBD</TD>
</TR><TR ALIGN='LEFT'>    <TD>devstatic=0.256339</TD>
    <TD>die=xc7z035ffg676-2</TD>
    <TD>dsp=0.106291</TD>
    <TD>dsp_output_toggle=12.500000</TD>
</TR><TR ALIGN='LEFT'>    <TD>dynamic=2.089737</TD>
    <TD>effective_thetaja=1.9</TD>
    <TD>enable_probability=0.990000</TD>
    <TD>family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>ff_toggle=12.500000</TD>
    <TD>flow_state=routed</TD>
    <TD>heatsink=medium (Medium Profile)</TD>
    <TD>i/o=0.203002</TD>
</TR><TR ALIGN='LEFT'>    <TD>input_toggle=12.500000</TD>
    <TD>junction_temp=29.4 (C)</TD>
    <TD>logic=0.073102</TD>
    <TD>mgtavcc_dynamic_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>mgtavcc_static_current=0.000000</TD>
    <TD>mgtavcc_total_current=0.000000</TD>
    <TD>mgtavcc_voltage=1.000000</TD>
    <TD>mgtavtt_dynamic_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>mgtavtt_static_current=0.000000</TD>
    <TD>mgtavtt_total_current=0.000000</TD>
    <TD>mgtavtt_voltage=1.200000</TD>
    <TD>mgtvccaux_dynamic_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>mgtvccaux_static_current=0.000000</TD>
    <TD>mgtvccaux_total_current=0.000000</TD>
    <TD>mgtvccaux_voltage=1.800000</TD>
    <TD>netlist_net_matched=NA</TD>
</TR><TR ALIGN='LEFT'>    <TD>off-chip_power=0.009800</TD>
    <TD>on-chip_power=2.346076</TD>
    <TD>output_enable=1.000000</TD>
    <TD>output_load=5.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>output_toggle=12.500000</TD>
    <TD>package=ffg676</TD>
    <TD>pct_clock_constrained=17.000000</TD>
    <TD>pct_inputs_defined=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>platform=nt64</TD>
    <TD>process=typical</TD>
    <TD>ps7=1.542734</TD>
    <TD>ram_enable=50.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>ram_write=50.000000</TD>
    <TD>read_saif=False</TD>
    <TD>set/reset_probability=0.000000</TD>
    <TD>signal_rate=False</TD>
</TR><TR ALIGN='LEFT'>    <TD>signals=0.126038</TD>
    <TD>simulation_file=None</TD>
    <TD>speedgrade=-2</TD>
    <TD>static_prob=False</TD>
</TR><TR ALIGN='LEFT'>    <TD>temp_grade=commercial</TD>
    <TD>thetajb=3.4 (C/W)</TD>
    <TD>thetasa=3.4 (C/W)</TD>
    <TD>toggle_rate=False</TD>
</TR><TR ALIGN='LEFT'>    <TD>user_board_temp=25.0 (C)</TD>
    <TD>user_effective_thetaja=1.9</TD>
    <TD>user_junc_temp=29.4 (C)</TD>
    <TD>user_thetajb=3.4 (C/W)</TD>
</TR><TR ALIGN='LEFT'>    <TD>user_thetasa=3.4 (C/W)</TD>
    <TD>vccadc_dynamic_current=0.000000</TD>
    <TD>vccadc_static_current=0.020000</TD>
    <TD>vccadc_total_current=0.020000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccadc_voltage=1.800000</TD>
    <TD>vccaux_dynamic_current=0.021773</TD>
    <TD>vccaux_io_dynamic_current=0.000000</TD>
    <TD>vccaux_io_static_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccaux_io_total_current=0.000000</TD>
    <TD>vccaux_io_voltage=1.800000</TD>
    <TD>vccaux_static_current=0.054005</TD>
    <TD>vccaux_total_current=0.075778</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccaux_voltage=1.800000</TD>
    <TD>vccbram_dynamic_current=0.000072</TD>
    <TD>vccbram_static_current=0.002007</TD>
    <TD>vccbram_total_current=0.002079</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccbram_voltage=1.000000</TD>
    <TD>vccint_dynamic_current=0.352914</TD>
    <TD>vccint_static_current=0.060722</TD>
    <TD>vccint_total_current=0.413636</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccint_voltage=1.000000</TD>
    <TD>vcco12_dynamic_current=0.000000</TD>
    <TD>vcco12_static_current=0.000000</TD>
    <TD>vcco12_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco12_voltage=1.200000</TD>
    <TD>vcco135_dynamic_current=0.000000</TD>
    <TD>vcco135_static_current=0.000000</TD>
    <TD>vcco135_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco135_voltage=1.350000</TD>
    <TD>vcco15_dynamic_current=0.000000</TD>
    <TD>vcco15_static_current=0.001000</TD>
    <TD>vcco15_total_current=0.001000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco15_voltage=1.500000</TD>
    <TD>vcco18_dynamic_current=0.088906</TD>
    <TD>vcco18_static_current=0.001000</TD>
    <TD>vcco18_total_current=0.089906</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco18_voltage=1.800000</TD>
    <TD>vcco25_dynamic_current=0.000000</TD>
    <TD>vcco25_static_current=0.001000</TD>
    <TD>vcco25_total_current=0.001000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco25_voltage=2.500000</TD>
    <TD>vcco33_dynamic_current=0.001392</TD>
    <TD>vcco33_static_current=0.001000</TD>
    <TD>vcco33_total_current=0.002392</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco33_voltage=3.300000</TD>
    <TD>vcco_ddr_dynamic_current=0.456904</TD>
    <TD>vcco_ddr_static_current=0.002000</TD>
    <TD>vcco_ddr_total_current=0.458904</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco_ddr_voltage=1.500000</TD>
    <TD>vcco_mio0_dynamic_current=0.001500</TD>
    <TD>vcco_mio0_static_current=0.001000</TD>
    <TD>vcco_mio0_total_current=0.002500</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco_mio0_voltage=3.300000</TD>
    <TD>vcco_mio1_dynamic_current=0.001875</TD>
    <TD>vcco_mio1_static_current=0.001000</TD>
    <TD>vcco_mio1_total_current=0.002875</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco_mio1_voltage=2.500000</TD>
    <TD>vccpaux_dynamic_current=0.050935</TD>
    <TD>vccpaux_static_current=0.010330</TD>
    <TD>vccpaux_total_current=0.061265</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccpaux_voltage=1.800000</TD>
    <TD>vccpint_dynamic_current=0.731079</TD>
    <TD>vccpint_static_current=0.018507</TD>
    <TD>vccpint_total_current=0.749586</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccpint_voltage=1.000000</TD>
    <TD>vccpll_dynamic_current=0.013878</TD>
    <TD>vccpll_static_current=0.003000</TD>
    <TD>vccpll_total_current=0.016878</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccpll_voltage=1.800000</TD>
    <TD>version=2016.4</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_utilization</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>clocking</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufgctrl_available=32</TD>
    <TD>bufgctrl_fixed=0</TD>
    <TD>bufgctrl_used=5</TD>
    <TD>bufgctrl_util_percentage=15.63</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufhce_available=168</TD>
    <TD>bufhce_fixed=0</TD>
    <TD>bufhce_used=0</TD>
    <TD>bufhce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufio_available=32</TD>
    <TD>bufio_fixed=0</TD>
    <TD>bufio_used=0</TD>
    <TD>bufio_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufmrce_available=16</TD>
    <TD>bufmrce_fixed=0</TD>
    <TD>bufmrce_used=0</TD>
    <TD>bufmrce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufr_available=32</TD>
    <TD>bufr_fixed=0</TD>
    <TD>bufr_used=2</TD>
    <TD>bufr_util_percentage=6.25</TD>
</TR><TR ALIGN='LEFT'>    <TD>mmcme2_adv_available=8</TD>
    <TD>mmcme2_adv_fixed=0</TD>
    <TD>mmcme2_adv_used=0</TD>
    <TD>mmcme2_adv_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>plle2_adv_available=8</TD>
    <TD>plle2_adv_fixed=0</TD>
    <TD>plle2_adv_used=0</TD>
    <TD>plle2_adv_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>dsp</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>dsp48e1_only_used=60</TD>
    <TD>dsps_available=900</TD>
    <TD>dsps_fixed=0</TD>
    <TD>dsps_used=60</TD>
</TR><TR ALIGN='LEFT'>    <TD>dsps_util_percentage=6.67</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>io_standard</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>blvds_25=0</TD>
    <TD>diff_hstl_i=0</TD>
    <TD>diff_hstl_i_18=0</TD>
    <TD>diff_hstl_i_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_hstl_i_dci_18=0</TD>
    <TD>diff_hstl_ii=0</TD>
    <TD>diff_hstl_ii_18=0</TD>
    <TD>diff_hstl_ii_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_hstl_ii_dci_18=0</TD>
    <TD>diff_hstl_ii_t_dci=0</TD>
    <TD>diff_hstl_ii_t_dci_18=0</TD>
    <TD>diff_hsul_12=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_hsul_12_dci=0</TD>
    <TD>diff_mobile_ddr=0</TD>
    <TD>diff_sstl12=0</TD>
    <TD>diff_sstl12_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl12_t_dci=0</TD>
    <TD>diff_sstl135=0</TD>
    <TD>diff_sstl135_dci=0</TD>
    <TD>diff_sstl135_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl135_t_dci=0</TD>
    <TD>diff_sstl15=1</TD>
    <TD>diff_sstl15_dci=0</TD>
    <TD>diff_sstl15_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl15_t_dci=1</TD>
    <TD>diff_sstl18_i=0</TD>
    <TD>diff_sstl18_i_dci=0</TD>
    <TD>diff_sstl18_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl18_ii_dci=0</TD>
    <TD>diff_sstl18_ii_t_dci=0</TD>
    <TD>hslvdci_15=0</TD>
    <TD>hslvdci_18=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hstl_i=0</TD>
    <TD>hstl_i_12=0</TD>
    <TD>hstl_i_18=0</TD>
    <TD>hstl_i_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hstl_i_dci_18=0</TD>
    <TD>hstl_ii=0</TD>
    <TD>hstl_ii_18=0</TD>
    <TD>hstl_ii_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hstl_ii_dci_18=0</TD>
    <TD>hstl_ii_t_dci=0</TD>
    <TD>hstl_ii_t_dci_18=0</TD>
    <TD>hsul_12=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hsul_12_dci=0</TD>
    <TD>lvcmos12=0</TD>
    <TD>lvcmos15=0</TD>
    <TD>lvcmos18=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvcmos25=1</TD>
    <TD>lvcmos33=1</TD>
    <TD>lvdci_15=0</TD>
    <TD>lvdci_18=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvdci_dv2_15=0</TD>
    <TD>lvdci_dv2_18=0</TD>
    <TD>lvds=1</TD>
    <TD>lvds_25=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvttl=0</TD>
    <TD>mini_lvds_25=0</TD>
    <TD>mobile_ddr=0</TD>
    <TD>pci33_3=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>ppds_25=0</TD>
    <TD>rsds_25=0</TD>
    <TD>sstl12=0</TD>
    <TD>sstl12_dci=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl12_t_dci=0</TD>
    <TD>sstl135=0</TD>
    <TD>sstl135_dci=0</TD>
    <TD>sstl135_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl135_t_dci=0</TD>
    <TD>sstl15=1</TD>
    <TD>sstl15_dci=0</TD>
    <TD>sstl15_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl15_t_dci=1</TD>
    <TD>sstl18_i=0</TD>
    <TD>sstl18_i_dci=0</TD>
    <TD>sstl18_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl18_ii_dci=0</TD>
    <TD>sstl18_ii_t_dci=0</TD>
    <TD>tmds_33=0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>memory</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>block_ram_tile_available=500</TD>
    <TD>block_ram_tile_fixed=0</TD>
    <TD>block_ram_tile_used=4</TD>
    <TD>block_ram_tile_util_percentage=0.80</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb18_available=1000</TD>
    <TD>ramb18_fixed=0</TD>
    <TD>ramb18_used=0</TD>
    <TD>ramb18_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36_fifo_available=500</TD>
    <TD>ramb36_fifo_fixed=0</TD>
    <TD>ramb36_fifo_used=4</TD>
    <TD>ramb36_fifo_util_percentage=0.80</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36e1_only_used=4</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>primitives</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bibuf_functional_category=IO</TD>
    <TD>bibuf_used=130</TD>
    <TD>bscane2_functional_category=Others</TD>
    <TD>bscane2_used=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufg_functional_category=Clock</TD>
    <TD>bufg_used=4</TD>
    <TD>bufgctrl_functional_category=Clock</TD>
    <TD>bufgctrl_used=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufr_functional_category=Clock</TD>
    <TD>bufr_used=2</TD>
    <TD>carry4_functional_category=CarryLogic</TD>
    <TD>carry4_used=789</TD>
</TR><TR ALIGN='LEFT'>    <TD>dsp48e1_functional_category=Block Arithmetic</TD>
    <TD>dsp48e1_used=60</TD>
    <TD>fdce_functional_category=Flop &amp; Latch</TD>
    <TD>fdce_used=7213</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdpe_functional_category=Flop &amp; Latch</TD>
    <TD>fdpe_used=53</TD>
    <TD>fdre_functional_category=Flop &amp; Latch</TD>
    <TD>fdre_used=15402</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdse_functional_category=Flop &amp; Latch</TD>
    <TD>fdse_used=333</TD>
    <TD>ibuf_functional_category=IO</TD>
    <TD>ibuf_used=20</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibufds_functional_category=IO</TD>
    <TD>ibufds_used=8</TD>
    <TD>iddr_functional_category=IO</TD>
    <TD>iddr_used=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>idelayctrl_functional_category=IO</TD>
    <TD>idelayctrl_used=1</TD>
    <TD>idelaye2_functional_category=IO</TD>
    <TD>idelaye2_used=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut1_functional_category=LUT</TD>
    <TD>lut1_used=860</TD>
    <TD>lut2_functional_category=LUT</TD>
    <TD>lut2_used=2250</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut3_functional_category=LUT</TD>
    <TD>lut3_used=2626</TD>
    <TD>lut4_functional_category=LUT</TD>
    <TD>lut4_used=1287</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut5_functional_category=LUT</TD>
    <TD>lut5_used=1410</TD>
    <TD>lut6_functional_category=LUT</TD>
    <TD>lut6_used=3460</TD>
</TR><TR ALIGN='LEFT'>    <TD>muxf7_functional_category=MuxFx</TD>
    <TD>muxf7_used=301</TD>
    <TD>muxf8_functional_category=MuxFx</TD>
    <TD>muxf8_used=85</TD>
</TR><TR ALIGN='LEFT'>    <TD>obuf_functional_category=IO</TD>
    <TD>obuf_used=34</TD>
    <TD>obufds_functional_category=IO</TD>
    <TD>obufds_used=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>obuft_functional_category=IO</TD>
    <TD>obuft_used=17</TD>
    <TD>oddr_functional_category=IO</TD>
    <TD>oddr_used=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>ps7_functional_category=Specialized Resource</TD>
    <TD>ps7_used=1</TD>
    <TD>ramb36e1_functional_category=Block Memory</TD>
    <TD>ramb36e1_used=4</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramd32_functional_category=Distributed Memory</TD>
    <TD>ramd32_used=36</TD>
    <TD>rams32_functional_category=Distributed Memory</TD>
    <TD>rams32_used=12</TD>
</TR><TR ALIGN='LEFT'>    <TD>srl16e_functional_category=Distributed Memory</TD>
    <TD>srl16e_used=135</TD>
    <TD>srlc32e_functional_category=Distributed Memory</TD>
    <TD>srlc32e_used=141</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>slice_logic</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>f7_muxes_available=109300</TD>
    <TD>f7_muxes_fixed=0</TD>
    <TD>f7_muxes_used=301</TD>
    <TD>f7_muxes_util_percentage=0.28</TD>
</TR><TR ALIGN='LEFT'>    <TD>f8_muxes_available=54650</TD>
    <TD>f8_muxes_fixed=0</TD>
    <TD>f8_muxes_used=85</TD>
    <TD>f8_muxes_util_percentage=0.16</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_distributed_ram_fixed=0</TD>
    <TD>lut_as_distributed_ram_used=24</TD>
    <TD>lut_as_logic_available=171900</TD>
    <TD>lut_as_logic_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_used=11520</TD>
    <TD>lut_as_logic_util_percentage=6.70</TD>
    <TD>lut_as_memory_available=70400</TD>
    <TD>lut_as_memory_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_used=244</TD>
    <TD>lut_as_memory_util_percentage=0.35</TD>
    <TD>lut_as_shift_register_fixed=0</TD>
    <TD>lut_as_shift_register_used=220</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_flip_flop_available=343800</TD>
    <TD>register_as_flip_flop_fixed=0</TD>
    <TD>register_as_flip_flop_used=23001</TD>
    <TD>register_as_flip_flop_util_percentage=6.69</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_latch_available=343800</TD>
    <TD>register_as_latch_fixed=0</TD>
    <TD>register_as_latch_used=0</TD>
    <TD>register_as_latch_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_luts_available=171900</TD>
    <TD>slice_luts_fixed=0</TD>
    <TD>slice_luts_used=11764</TD>
    <TD>slice_luts_util_percentage=6.84</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_available=343800</TD>
    <TD>slice_registers_fixed=0</TD>
    <TD>slice_registers_used=23001</TD>
    <TD>slice_registers_util_percentage=6.69</TD>
</TR><TR ALIGN='LEFT'>    <TD>fully_used_lut_ff_pairs_fixed=6.69</TD>
    <TD>fully_used_lut_ff_pairs_used=97</TD>
    <TD>lut_as_distributed_ram_fixed=0</TD>
    <TD>lut_as_distributed_ram_used=24</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_available=171900</TD>
    <TD>lut_as_logic_fixed=0</TD>
    <TD>lut_as_logic_used=11520</TD>
    <TD>lut_as_logic_util_percentage=6.70</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_available=70400</TD>
    <TD>lut_as_memory_fixed=0</TD>
    <TD>lut_as_memory_used=244</TD>
    <TD>lut_as_memory_util_percentage=0.35</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_shift_register_fixed=0</TD>
    <TD>lut_as_shift_register_used=220</TD>
    <TD>lut_ff_pairs_with_one_unused_flip_flop_fixed=220</TD>
    <TD>lut_ff_pairs_with_one_unused_flip_flop_used=5561</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_ff_pairs_with_one_unused_lut_output_fixed=5561</TD>
    <TD>lut_ff_pairs_with_one_unused_lut_output_used=6882</TD>
    <TD>lut_flip_flop_pairs_available=171900</TD>
    <TD>lut_flip_flop_pairs_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_flip_flop_pairs_used=7265</TD>
    <TD>lut_flip_flop_pairs_util_percentage=4.23</TD>
    <TD>slice_available=54650</TD>
    <TD>slice_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_used=6450</TD>
    <TD>slice_util_percentage=11.80</TD>
    <TD>slicel_fixed=0</TD>
    <TD>slicel_used=4206</TD>
</TR><TR ALIGN='LEFT'>    <TD>slicem_fixed=0</TD>
    <TD>slicem_used=2244</TD>
    <TD>unique_control_sets_used=701</TD>
    <TD>using_o5_and_o6_fixed=701</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o5_and_o6_used=56</TD>
    <TD>using_o5_output_only_fixed=56</TD>
    <TD>using_o5_output_only_used=8</TD>
    <TD>using_o6_output_only_fixed=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o6_output_only_used=156</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>specific_feature</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bscane2_available=4</TD>
    <TD>bscane2_fixed=0</TD>
    <TD>bscane2_used=1</TD>
    <TD>bscane2_util_percentage=25.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>capturee2_available=1</TD>
    <TD>capturee2_fixed=0</TD>
    <TD>capturee2_used=0</TD>
    <TD>capturee2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>dna_port_available=1</TD>
    <TD>dna_port_fixed=0</TD>
    <TD>dna_port_used=0</TD>
    <TD>dna_port_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>efuse_usr_available=1</TD>
    <TD>efuse_usr_fixed=0</TD>
    <TD>efuse_usr_used=0</TD>
    <TD>efuse_usr_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>frame_ecce2_available=1</TD>
    <TD>frame_ecce2_fixed=0</TD>
    <TD>frame_ecce2_used=0</TD>
    <TD>frame_ecce2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>icape2_available=2</TD>
    <TD>icape2_fixed=0</TD>
    <TD>icape2_used=0</TD>
    <TD>icape2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcie_2_1_available=1</TD>
    <TD>pcie_2_1_fixed=0</TD>
    <TD>pcie_2_1_used=0</TD>
    <TD>pcie_2_1_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>startupe2_available=1</TD>
    <TD>startupe2_fixed=0</TD>
    <TD>startupe2_used=0</TD>
    <TD>startupe2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>xadc_available=1</TD>
    <TD>xadc_fixed=0</TD>
    <TD>xadc_used=0</TD>
    <TD>xadc_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>router</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>actual_expansions=19074953</TD>
    <TD>bogomips=0</TD>
    <TD>bram18=0</TD>
    <TD>bram36=4</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufg=0</TD>
    <TD>bufr=2</TD>
    <TD>congestion_level=0</TD>
    <TD>ctrls=701</TD>
</TR><TR ALIGN='LEFT'>    <TD>dsp=60</TD>
    <TD>effort=2</TD>
    <TD>estimated_expansions=25234596</TD>
    <TD>ff=23001</TD>
</TR><TR ALIGN='LEFT'>    <TD>global_clocks=5</TD>
    <TD>high_fanout_nets=13</TD>
    <TD>iob=88</TD>
    <TD>lut=11858</TD>
</TR><TR ALIGN='LEFT'>    <TD>movable_instances=37712</TD>
    <TD>nets=46747</TD>
    <TD>pins=222928</TD>
    <TD>pll=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>router_runtime=3.000000</TD>
    <TD>router_timing_driven=1</TD>
    <TD>threads=2</TD>
    <TD>timing_constraints_exist=1</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>synthesis</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-assert=default::[not_specified]</TD>
    <TD>-bufg=default::12</TD>
    <TD>-cascade_dsp=default::auto</TD>
    <TD>-constrset=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-control_set_opt_threshold=default::auto</TD>
    <TD>-directive=default::default</TD>
    <TD>-fanout_limit=400</TD>
    <TD>-flatten_hierarchy=default::rebuilt</TD>
</TR><TR ALIGN='LEFT'>    <TD>-fsm_extraction=one_hot</TD>
    <TD>-gated_clock_conversion=default::off</TD>
    <TD>-generic=default::[not_specified]</TD>
    <TD>-include_dirs=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-keep_equivalent_registers=[specified]</TD>
    <TD>-max_bram=default::-1</TD>
    <TD>-max_bram_cascade_height=default::-1</TD>
    <TD>-max_dsp=default::-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>-max_uram=default::-1</TD>
    <TD>-max_uram_cascade_height=default::-1</TD>
    <TD>-mode=default::default</TD>
    <TD>-name=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-no_lc=[specified]</TD>
    <TD>-no_srlextract=default::[not_specified]</TD>
    <TD>-no_timing_driven=default::[not_specified]</TD>
    <TD>-part=xc7z035ffg676-2</TD>
</TR><TR ALIGN='LEFT'>    <TD>-resource_sharing=off</TD>
    <TD>-retiming=default::[not_specified]</TD>
    <TD>-rtl=default::[not_specified]</TD>
    <TD>-rtl_skip_constraints=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-rtl_skip_ip=default::[not_specified]</TD>
    <TD>-seu_protect=default::none</TD>
    <TD>-shreg_min_size=5</TD>
    <TD>-top=system_top</TD>
</TR><TR ALIGN='LEFT'>    <TD>-verilog_define=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>elapsed=00:06:17s</TD>
    <TD>hls_ip=0</TD>
    <TD>memory_gain=1108.930MB</TD>
    <TD>memory_peak=1420.063MB</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
</BODY>
</HTML>
