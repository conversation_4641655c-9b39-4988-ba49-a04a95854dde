Copyright 1986-2016 Xilinx, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2016.4 (win64) Build 1756540 Mon Jan 23 19:11:23 MST 2017
| Date         : Tue Oct 29 13:20:18 2019
| Host         : cduser1 running 64-bit major release  (build 9200)
| Command      : report_utilization -file system_top_utilization_synth.rpt -pb system_top_utilization_synth.pb
| Design       : system_top
| Device       : 7z045ffg900-2
| Design State : Synthesized
---------------------------------------------------------------------------------------------------------------

Utilization Design Information

Table of Contents
-----------------
1. Slice Logic
1.1 Summary of Registers by Type
2. Memory
3. DSP
4. IO and GT Specific
5. Clocking
6. Specific Feature
7. Primitives
8. Black Boxes
9. Instantiated Netlists

1. Slice Logic
--------------

+----------------------------+-------+-------+-----------+-------+
|          Site Type         |  Used | Fixed | Available | Util% |
+----------------------------+-------+-------+-----------+-------+
| Slice LUTs*                | 17136 |     0 |    218600 |  7.84 |
|   LUT as Logic             | 16860 |     0 |    218600 |  7.71 |
|   LUT as Memory            |   276 |     0 |     70400 |  0.39 |
|     LUT as Distributed RAM |     0 |     0 |           |       |
|     LUT as Shift Register  |   276 |     0 |           |       |
| Slice Registers            | 26491 |     0 |    437200 |  6.06 |
|   Register as Flip Flop    | 26491 |     0 |    437200 |  6.06 |
|   Register as Latch        |     0 |     0 |    437200 |  0.00 |
| F7 Muxes                   |   426 |     0 |    109300 |  0.39 |
| F8 Muxes                   |   102 |     0 |     54650 |  0.19 |
+----------------------------+-------+-------+-----------+-------+
* Warning! The Final LUT count, after physical optimizations and full implementation, is typically lower. Run opt_design after synthesis, if not already completed, for a more realistic count.


1.1 Summary of Registers by Type
--------------------------------

+-------+--------------+-------------+--------------+
| Total | Clock Enable | Synchronous | Asynchronous |
+-------+--------------+-------------+--------------+
| 0     |            _ |           - |            - |
| 0     |            _ |           - |          Set |
| 0     |            _ |           - |        Reset |
| 0     |            _ |         Set |            - |
| 0     |            _ |       Reset |            - |
| 0     |          Yes |           - |            - |
| 16    |          Yes |           - |          Set |
| 7232  |          Yes |           - |        Reset |
| 328   |          Yes |         Set |            - |
| 18915 |          Yes |       Reset |            - |
+-------+--------------+-------------+--------------+


2. Memory
---------

+-------------------+------+-------+-----------+-------+
|     Site Type     | Used | Fixed | Available | Util% |
+-------------------+------+-------+-----------+-------+
| Block RAM Tile    |    4 |     0 |       545 |  0.73 |
|   RAMB36/FIFO*    |    4 |     0 |       545 |  0.73 |
|     RAMB36E1 only |    4 |       |           |       |
|   RAMB18          |    0 |     0 |      1090 |  0.00 |
+-------------------+------+-------+-----------+-------+
* Note: Each Block RAM Tile only has one FIFO logic available and therefore can accommodate only one FIFO36E1 or one FIFO18E1. However, if a FIFO18E1 occupies a Block RAM Tile, that tile can still accommodate a RAMB18E1


3. DSP
------

+----------------+------+-------+-----------+-------+
|    Site Type   | Used | Fixed | Available | Util% |
+----------------+------+-------+-----------+-------+
| DSPs           |   60 |     0 |       900 |  6.67 |
|   DSP48E1 only |   60 |       |           |       |
+----------------+------+-------+-----------+-------+


4. IO and GT Specific
---------------------

+-----------------------------+------+-------+-----------+--------+
|          Site Type          | Used | Fixed | Available |  Util% |
+-----------------------------+------+-------+-----------+--------+
| Bonded IOB                  |   55 |     0 |       362 |  15.19 |
| Bonded IPADs                |    0 |     0 |        50 |   0.00 |
| Bonded OPADs                |    0 |     0 |        32 |   0.00 |
| Bonded IOPADs               |  130 |     0 |       130 | 100.00 |
| PHY_CONTROL                 |    0 |     0 |         8 |   0.00 |
| PHASER_REF                  |    0 |     0 |         8 |   0.00 |
| OUT_FIFO                    |    0 |     0 |        32 |   0.00 |
| IN_FIFO                     |    0 |     0 |        32 |   0.00 |
| IDELAYCTRL                  |    1 |     0 |         8 |  12.50 |
| IBUFDS                      |    8 |     0 |       348 |   2.30 |
| GTXE2_COMMON                |    0 |     0 |         4 |   0.00 |
| GTXE2_CHANNEL               |    0 |     0 |        16 |   0.00 |
| PHASER_OUT/PHASER_OUT_PHY   |    0 |     0 |        32 |   0.00 |
| PHASER_IN/PHASER_IN_PHY     |    0 |     0 |        32 |   0.00 |
| IDELAYE2/IDELAYE2_FINEDELAY |    7 |     0 |       400 |   1.75 |
|   IDELAYE2 only             |    7 |     0 |           |        |
| ODELAYE2/ODELAYE2_FINEDELAY |    0 |     0 |       150 |   0.00 |
| IBUFDS_GTE2                 |    0 |     0 |         8 |   0.00 |
| ILOGIC                      |    7 |     0 |       362 |   1.93 |
|   IDDR                      |    7 |       |           |        |
| OLOGIC                      |   10 |     0 |       362 |   2.76 |
|   ODDR                      |   10 |       |           |        |
+-----------------------------+------+-------+-----------+--------+


5. Clocking
-----------

+------------+------+-------+-----------+-------+
|  Site Type | Used | Fixed | Available | Util% |
+------------+------+-------+-----------+-------+
| BUFGCTRL   |    4 |     0 |        32 | 12.50 |
| BUFIO      |    0 |     0 |        32 |  0.00 |
| MMCME2_ADV |    0 |     0 |         8 |  0.00 |
| PLLE2_ADV  |    0 |     0 |         8 |  0.00 |
| BUFMRCE    |    0 |     0 |        16 |  0.00 |
| BUFHCE     |    0 |     0 |       168 |  0.00 |
| BUFR       |    2 |     0 |        32 |  6.25 |
+------------+------+-------+-----------+-------+


6. Specific Feature
-------------------

+-------------+------+-------+-----------+-------+
|  Site Type  | Used | Fixed | Available | Util% |
+-------------+------+-------+-----------+-------+
| BSCANE2     |    0 |     0 |         4 |  0.00 |
| CAPTUREE2   |    0 |     0 |         1 |  0.00 |
| DNA_PORT    |    0 |     0 |         1 |  0.00 |
| EFUSE_USR   |    0 |     0 |         1 |  0.00 |
| FRAME_ECCE2 |    0 |     0 |         1 |  0.00 |
| ICAPE2      |    0 |     0 |         2 |  0.00 |
| PCIE_2_1    |    0 |     0 |         1 |  0.00 |
| STARTUPE2   |    0 |     0 |         1 |  0.00 |
| XADC        |    0 |     0 |         1 |  0.00 |
+-------------+------+-------+-----------+-------+


7. Primitives
-------------

+------------+-------+----------------------+
|  Ref Name  |  Used |  Functional Category |
+------------+-------+----------------------+
| FDRE       | 18915 |         Flop & Latch |
| FDCE       |  7232 |         Flop & Latch |
| LUT6       |  6167 |                  LUT |
| LUT3       |  2946 |                  LUT |
| LUT5       |  2450 |                  LUT |
| LUT2       |  2402 |                  LUT |
| LUT4       |  1953 |                  LUT |
| LUT1       |  1219 |                  LUT |
| CARRY4     |   777 |           CarryLogic |
| MUXF7      |   426 |                MuxFx |
| FDSE       |   328 |         Flop & Latch |
| SRLC32E    |   141 |   Distributed Memory |
| SRL16E     |   135 |   Distributed Memory |
| BIBUF      |   130 |                   IO |
| MUXF8      |   102 |                MuxFx |
| DSP48E1    |    60 |     Block Arithmetic |
| OBUFDS     |    16 |                   IO |
| IBUF       |    16 |                   IO |
| FDPE       |    16 |         Flop & Latch |
| OBUFT      |    15 |                   IO |
| ODDR       |    10 |                   IO |
| INV        |     8 |                  LUT |
| IBUFDS     |     8 |                   IO |
| OBUF       |     7 |                   IO |
| IDELAYE2   |     7 |                   IO |
| IDDR       |     7 |                   IO |
| RAMB36E1   |     4 |         Block Memory |
| BUFG       |     3 |                Clock |
| BUFR       |     2 |                Clock |
| PS7        |     1 | Specialized Resource |
| IDELAYCTRL |     1 |                   IO |
| BUFGCTRL   |     1 |                Clock |
+------------+-------+----------------------+


8. Black Boxes
--------------

+----------+------+
| Ref Name | Used |
+----------+------+


9. Instantiated Netlists
------------------------

+----------+------+
| Ref Name | Used |
+----------+------+


