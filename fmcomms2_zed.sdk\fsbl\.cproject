<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="xilinx.gnu.armv7.exe.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="xilinx.gnu.armv7.exe.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="" id="xilinx.gnu.armv7.exe.debug.**********" name="Debug" parent="xilinx.gnu.armv7.exe.debug" prebuildStep="a9-linaro-pre-build-step">
					<folderInfo id="xilinx.gnu.armv7.exe.debug.**********." name="/" resourcePath="">
						<toolChain id="xilinx.gnu.armv7.exe.debug.toolchain.676539344" name="Xilinx ARM v7 GNU Toolchain" superClass="xilinx.gnu.armv7.exe.debug.toolchain">
							<targetPlatform binaryParser="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" id="xilinx.armv7.target.gnu.base.debug.428532050" isAbstract="false" name="Debug Platform" superClass="xilinx.armv7.target.gnu.base.debug"/>
							<builder buildPath="${workspace_loc:/fsbl}/Debug" enableAutoBuild="true" id="xilinx.gnu.armv7.toolchain.builder.debug.419016792" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="GNU make" superClass="xilinx.gnu.armv7.toolchain.builder.debug"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.assembler.debug.901720571" name="ARM v7 gcc assembler" superClass="xilinx.gnu.armv7.c.toolchain.assembler.debug">
								<inputType id="xilinx.gnu.assembler.input.2062516319" superClass="xilinx.gnu.assembler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.c.toolchain.compiler.debug.114554249" name="ARM v7 gcc compiler" superClass="xilinx.gnu.armv7.c.toolchain.compiler.debug">
								<option defaultValue="gnu.c.optimization.level.none" id="xilinx.gnu.compiler.option.optimization.level.1342671567" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.112061704" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.746830753" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.344181784" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
								<option id="xilinx.gnu.compiler.misc.other.1088386400" name="Other flags" superClass="xilinx.gnu.compiler.misc.other" value="-c -fmessage-length=0 -MT&quot;$@&quot; -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard" valueType="string"/>
								<option id="xilinx.gnu.compiler.dircategory.includes.212499493" name="Include Paths" superClass="xilinx.gnu.compiler.dircategory.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/system_top_hw_platform_0}&quot;"/>
								</option>
								<option id="xilinx.gnu.compiler.symbols.defined.1703481842" name="Defined symbols (-D)" superClass="xilinx.gnu.compiler.symbols.defined" valueType="definedSymbols"/>
								<inputType id="xilinx.gnu.armv7.c.compiler.input.654464465" name="C source files" superClass="xilinx.gnu.armv7.c.compiler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.compiler.debug.39257542" name="ARM v7 g++ compiler" superClass="xilinx.gnu.armv7.cxx.toolchain.compiler.debug">
								<option defaultValue="gnu.c.optimization.level.none" id="xilinx.gnu.compiler.option.optimization.level.1233365712" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.814042689" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.1945260539" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.339012956" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.toolchain.archiver.264681101" name="ARM v7 archiver" superClass="xilinx.gnu.armv7.toolchain.archiver"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.linker.debug.1398079248" name="ARM v7 gcc linker" superClass="xilinx.gnu.armv7.c.toolchain.linker.debug">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.1042884764" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.990759324" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.44951661" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
								<option id="xilinx.gnu.c.link.option.ldflags.1856977788" name="Linker Flags" superClass="xilinx.gnu.c.link.option.ldflags" value=" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -Wl,-build-id=none -specs=Xilinx.spec" valueType="string"/>
								<inputType id="xilinx.gnu.linker.input.1096733160" superClass="xilinx.gnu.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
								<inputType id="xilinx.gnu.linker.input.lscript.619941508" name="Linker Script" superClass="xilinx.gnu.linker.input.lscript"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.linker.debug.549777631" name="ARM v7 g++ linker" superClass="xilinx.gnu.armv7.cxx.toolchain.linker.debug">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.1150983126" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.816344465" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.26791723" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.size.debug.**********" name="ARM v7 Print Size" superClass="xilinx.gnu.armv7.size.debug"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="xilinx.gnu.armv7.exe.release.628687245">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="xilinx.gnu.armv7.exe.release.628687245" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="rm -rf" description="" id="xilinx.gnu.armv7.exe.release.628687245" name="Release" parent="xilinx.gnu.armv7.exe.release" prebuildStep="a9-linaro-pre-build-step">
					<folderInfo id="xilinx.gnu.armv7.exe.release.628687245." name="/" resourcePath="">
						<toolChain id="xilinx.gnu.armv7.exe.release.toolchain.985461401" name="Xilinx ARM v7 GNU Toolchain" superClass="xilinx.gnu.armv7.exe.release.toolchain">
							<targetPlatform binaryParser="com.xilinx.sdk.managedbuilder.XELF.arm.a53.x32" id="xilinx.armv7.target.gnu.base.release.552131962" isAbstract="false" name="Release Platform" superClass="xilinx.armv7.target.gnu.base.release"/>
							<builder buildPath="${workspace_loc:/fsbl}/Release" enableAutoBuild="true" id="xilinx.gnu.armv7.toolchain.builder.release.1162687391" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="GNU make" superClass="xilinx.gnu.armv7.toolchain.builder.release"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.assembler.release.2106940188" name="ARM v7 gcc assembler" superClass="xilinx.gnu.armv7.c.toolchain.assembler.release">
								<inputType id="xilinx.gnu.assembler.input.1834332200" superClass="xilinx.gnu.assembler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.c.toolchain.compiler.release.1413714811" name="ARM v7 gcc compiler" superClass="xilinx.gnu.armv7.c.toolchain.compiler.release">
								<option defaultValue="gnu.c.optimization.level.more" id="xilinx.gnu.compiler.option.optimization.level.1854250345" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.733524173" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.480619068" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.475047824" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
								<option id="xilinx.gnu.compiler.misc.other.420616785" name="Other flags" superClass="xilinx.gnu.compiler.misc.other" value="-c -fmessage-length=0 -MT&quot;$@&quot; -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard" valueType="string"/>
								<option id="xilinx.gnu.compiler.dircategory.includes.1611907933" name="Include Paths" superClass="xilinx.gnu.compiler.dircategory.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/system_top_hw_platform_0}&quot;"/>
								</option>
								<inputType id="xilinx.gnu.armv7.c.compiler.input.1879026245" name="C source files" superClass="xilinx.gnu.armv7.c.compiler.input"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.compiler.release.333703408" name="ARM v7 g++ compiler" superClass="xilinx.gnu.armv7.cxx.toolchain.compiler.release">
								<option defaultValue="gnu.c.optimization.level.more" id="xilinx.gnu.compiler.option.optimization.level.1100023395" name="Optimization Level" superClass="xilinx.gnu.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.option.debugging.level.464844069" name="Debug Level" superClass="xilinx.gnu.compiler.option.debugging.level" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="xilinx.gnu.compiler.inferred.swplatform.includes.33671371" name="Software Platform Include Path" superClass="xilinx.gnu.compiler.inferred.swplatform.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/include"/>
								</option>
								<option id="xilinx.gnu.compiler.inferred.swplatform.flags.797076327" name="Software Platform Inferred Flags" superClass="xilinx.gnu.compiler.inferred.swplatform.flags" value="  " valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.toolchain.archiver.1755420642" name="ARM v7 archiver" superClass="xilinx.gnu.armv7.toolchain.archiver"/>
							<tool id="xilinx.gnu.armv7.c.toolchain.linker.release.2109365012" name="ARM v7 gcc linker" superClass="xilinx.gnu.armv7.c.toolchain.linker.release">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.153835023" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.262236612" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.1585813101" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
								<option id="xilinx.gnu.c.link.option.ldflags.228693107" name="Linker Flags" superClass="xilinx.gnu.c.link.option.ldflags" value=" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -Wl,-build-id=none -specs=Xilinx.spec" valueType="string"/>
								<inputType id="xilinx.gnu.linker.input.2113164666" superClass="xilinx.gnu.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
								<inputType id="xilinx.gnu.linker.input.lscript.1935388592" name="Linker Script" superClass="xilinx.gnu.linker.input.lscript"/>
							</tool>
							<tool id="xilinx.gnu.armv7.cxx.toolchain.linker.release.485431113" name="ARM v7 g++ linker" superClass="xilinx.gnu.armv7.cxx.toolchain.linker.release">
								<option id="xilinx.gnu.linker.inferred.swplatform.lpath.1128460807" name="Software Platform Library Path" superClass="xilinx.gnu.linker.inferred.swplatform.lpath" valueType="libPaths">
									<listOptionValue builtIn="false" value="../../fsbl_bsp/ps7_cortexa9_0/lib"/>
								</option>
								<option id="xilinx.gnu.linker.inferred.swplatform.flags.1878477960" name="Software Platform Inferred Flags" superClass="xilinx.gnu.linker.inferred.swplatform.flags" valueType="libs">
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lxilffs,-lxil,-lgcc,-lc,--end-group"/>
									<listOptionValue builtIn="false" value="-Wl,--start-group,-lrsa,-lxil,-lgcc,-lc,--end-group"/>
								</option>
								<option id="xilinx.gnu.c.linker.option.lscript.1834231023" name="Linker Script" superClass="xilinx.gnu.c.linker.option.lscript" value="../src/lscript.ld" valueType="string"/>
							</tool>
							<tool id="xilinx.gnu.armv7.size.release.**********" name="ARM v7 Print Size" superClass="xilinx.gnu.armv7.size.release"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="fsbl.xilinx.gnu.armv7.exe.**********" name="Xilinx ARM v7 Executable" projectType="xilinx.gnu.armv7.exe"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.release.628687245;xilinx.gnu.armv7.exe.release.628687245.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.debug.**********;xilinx.gnu.armv7.exe.debug.**********.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.debug.**********;xilinx.gnu.armv7.exe.debug.**********.;xilinx.gnu.armv7.c.toolchain.compiler.debug.114554249;xilinx.gnu.armv7.c.compiler.input.654464465">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="xilinx.gnu.armv7.exe.release.628687245;xilinx.gnu.armv7.exe.release.628687245.;xilinx.gnu.armv7.c.toolchain.compiler.release.1413714811;xilinx.gnu.armv7.c.compiler.input.1879026245">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.xilinx.managedbuilder.ui.ARMA53X32GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="/fsbl"/>
		</configuration>
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/fsbl"/>
		</configuration>
	</storageModule>
</cproject>
