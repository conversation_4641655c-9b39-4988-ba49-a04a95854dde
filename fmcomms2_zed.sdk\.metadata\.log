!SESSION 2020-04-22 12:05:01.340 -----------------------------------------------
eclipse.buildId=2016.4
java.version=1.8.0_66
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -data E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk

This is a continuation of log file E:\FPGA\R75_Z7035_0320\R75_Z7035_0320\fmcomms2_zed.sdk\.metadata\.bak_0.log
Created Time: 2020-04-22 12:05:45.234

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:05:45.237
!MESSAGE XSCT Command: [::hsi::utils::get_all_register_data -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_0], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:05:45.269
!MESSAGE XSCT command with result: [::hsi::utils::get_all_register_data -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_0], Result: [null, {"axi_ad9361": {},
"axi_ad9361_adc_dma": {},
"axi_ad9361_dac_dma": {},
"ps7_afi_0": {},
"ps7_afi_1": {},
"ps7_afi_2": {},
"ps7_afi_3": {},
"ps7_coresight_comp_0": {},
"ps7_ddr_0": {},
"ps7_ddrc_0": {},
"ps7_dev_cfg_0": {},
"ps7_dma_ns": {},
"ps7_dma_s": {},
"ps7_ethernet_0": {},
"ps7_globaltimer_0": {},
"ps7_gpio_0": {},
"ps7_gpv_0": {},
"ps7_intc_dist_0": {},
"ps7_iop_bus_config_0": {},
"ps7_l2cachec_0": {},
"ps7_ocmc_0": {},
"ps7_pl310_0": {},
"ps7_pmu_0": {},
"ps7_qspi_0": {},
"ps7_qspi_linear_0": {},
"ps7_ram_0": {},
"ps7_ram_1": {},
"ps7_scuc_0": {},
"ps7_scugic_0": {},
"ps7_scutimer_0": {},
"ps7_scuwdt_0": {},
"ps7_slcr_0": {},
"ps7_spi_0": {},
"ps7_spi_1": {},
"ps7_uart_0": {},
"ps7_xadc_0": {},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:05:45.272
!MESSAGE XSCT Command: [::hsi::utils::get_all_register_data -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_1], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:05:45.279
!MESSAGE XSCT command with result: [::hsi::utils::get_all_register_data -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_1], Result: [null, {"axi_ad9361": {},
"axi_ad9361_adc_dma": {},
"axi_ad9361_dac_dma": {},
"ps7_afi_0": {},
"ps7_afi_1": {},
"ps7_afi_2": {},
"ps7_afi_3": {},
"ps7_coresight_comp_0": {},
"ps7_ddr_0": {},
"ps7_ddrc_0": {},
"ps7_dev_cfg_0": {},
"ps7_dma_ns": {},
"ps7_dma_s": {},
"ps7_ethernet_0": {},
"ps7_globaltimer_0": {},
"ps7_gpio_0": {},
"ps7_gpv_0": {},
"ps7_intc_dist_0": {},
"ps7_iop_bus_config_0": {},
"ps7_l2cachec_0": {},
"ps7_ocmc_0": {},
"ps7_pl310_0": {},
"ps7_pmu_0": {},
"ps7_qspi_0": {},
"ps7_qspi_linear_0": {},
"ps7_ram_0": {},
"ps7_ram_1": {},
"ps7_scuc_0": {},
"ps7_scugic_0": {},
"ps7_scutimer_0": {},
"ps7_scuwdt_0": {},
"ps7_slcr_0": {},
"ps7_spi_0": {},
"ps7_spi_1": {},
"ps7_uart_0": {},
"ps7_xadc_0": {},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:05:50.125
!MESSAGE XSCT Command: [::hsi::utils::get_design_properties -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:05:50.130
!MESSAGE XSCT command with result: [::hsi::utils::get_design_properties -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, {"device": "7z035",
"family": "zynq",
"timestamp": "Fri Mar 27 14:56:01 2020",
"vivado_version": "2016.4",
"part": "xc7z035ffg676-2",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:05:50.143
!MESSAGE XSCT Command: [::hsi::utils::get_all_periphs -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:05:50.161
!MESSAGE XSCT command with result: [::hsi::utils::get_all_periphs -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, {"axi_ad9361": {"hier_name": "axi_ad9361",
"type": "axi_ad9361",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_adc_dma": {"hier_name": "axi_ad9361_adc_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_dma": {"hier_name": "axi_ad9361_dac_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_fifo": {"hier_name": "axi_ad9361_dac_fifo",
"type": "util_rfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_cpu_interconnect": {"hier_name": "axi_cpu_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp0_interconnect": {"hier_name": "axi_hp0_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp1_interconnect": {"hier_name": "axi_hp1_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp2_interconnect": {"hier_name": "axi_hp2_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_quad_spi_0": {"hier_name": "axi_quad_spi_0",
"type": "axi_quad_spi",
"version": "3.2",
"ip_type": "MEMORY_CNTLR",
},
"sys_concat_intc": {"hier_name": "sys_concat_intc",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"sys_ps7": {"hier_name": "sys_ps7",
"type": "processing_system7",
"version": "5.5",
"ip_type": "",
},
"sys_rstgen": {"hier_name": "sys_rstgen",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_fifo": {"hier_name": "util_ad9361_adc_fifo",
"type": "util_wfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_pack": {"hier_name": "util_ad9361_adc_pack",
"type": "util_cpack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_dac_upack": {"hier_name": "util_ad9361_dac_upack",
"type": "util_upack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk": {"hier_name": "util_ad9361_divclk",
"type": "util_clkdiv",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_reset": {"hier_name": "util_ad9361_divclk_reset",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel": {"hier_name": "util_ad9361_divclk_sel",
"type": "util_reduced_logic",
"version": "2.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel_concat": {"hier_name": "util_ad9361_divclk_sel_concat",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"util_ad9361_tdd_sync": {"hier_name": "util_ad9361_tdd_sync",
"type": "util_tdd_sync",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"ps7_clockc_0": {"hier_name": "ps7_clockc_0",
"type": "ps7_clockc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_uart_0": {"hier_name": "ps7_uart_0",
"type": "ps7_uart",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pl310_0": {"hier_name": "ps7_pl310_0",
"type": "ps7_pl310",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pmu_0": {"hier_name": "ps7_pmu_0",
"type": "ps7_pmu",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_0": {"hier_name": "ps7_qspi_0",
"type": "ps7_qspi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_linear_0": {"hier_name": "ps7_qspi_linear_0",
"type": "ps7_qspi_linear",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_axi_interconnect_0": {"hier_name": "ps7_axi_interconnect_0",
"type": "ps7_axi_interconnect",
"version": "1.00.a",
"ip_type": "BUS",
},
"ps7_cortexa9_0": {"hier_name": "ps7_cortexa9_0",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_cortexa9_1": {"hier_name": "ps7_cortexa9_1",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_ddr_0": {"hier_name": "ps7_ddr_0",
"type": "ps7_ddr",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ethernet_0": {"hier_name": "ps7_ethernet_0",
"type": "ps7_ethernet",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_0": {"hier_name": "ps7_spi_0",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_1": {"hier_name": "ps7_spi_1",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpio_0": {"hier_name": "ps7_gpio_0",
"type": "ps7_gpio",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ddrc_0": {"hier_name": "ps7_ddrc_0",
"type": "ps7_ddrc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dev_cfg_0": {"hier_name": "ps7_dev_cfg_0",
"type": "ps7_dev_cfg",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_xadc_0": {"hier_name": "ps7_xadc_0",
"type": "ps7_xadc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ocmc_0": {"hier_name": "ps7_ocmc_0",
"type": "ps7_ocmc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_coresight_comp_0": {"hier_name": "ps7_coresight_comp_0",
"type": "ps7_coresight_comp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpv_0": {"hier_name": "ps7_gpv_0",
"type": "ps7_gpv",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuc_0": {"hier_name": "ps7_scuc_0",
"type": "ps7_scuc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_globaltimer_0": {"hier_name": "ps7_globaltimer_0",
"type": "ps7_globaltimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_intc_dist_0": {"hier_name": "ps7_intc_dist_0",
"type": "ps7_intc_dist",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_l2cachec_0": {"hier_name": "ps7_l2cachec_0",
"type": "ps7_l2cachec",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_s": {"hier_name": "ps7_dma_s",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_iop_bus_config_0": {"hier_name": "ps7_iop_bus_config_0",
"type": "ps7_iop_bus_config",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ram_0": {"hier_name": "ps7_ram_0",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ram_1": {"hier_name": "ps7_ram_1",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_scugic_0": {"hier_name": "ps7_scugic_0",
"type": "ps7_scugic",
"version": "1.00.a",
"ip_type": "INTERRUPT_CNTLR",
},
"ps7_scutimer_0": {"hier_name": "ps7_scutimer_0",
"type": "ps7_scutimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuwdt_0": {"hier_name": "ps7_scuwdt_0",
"type": "ps7_scuwdt",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_slcr_0": {"hier_name": "ps7_slcr_0",
"type": "ps7_slcr",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_ns": {"hier_name": "ps7_dma_ns",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_0": {"hier_name": "ps7_afi_0",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_1": {"hier_name": "ps7_afi_1",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_2": {"hier_name": "ps7_afi_2",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_3": {"hier_name": "ps7_afi_3",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_m_axi_gp0": {"hier_name": "ps7_m_axi_gp0",
"type": "ps7_m_axi_gp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:17.279
!MESSAGE XSCT Command: [connect -url tcp:127.0.0.1:3121], Thread: Worker-5

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:20.779
!MESSAGE XSCT command with result: [connect -url tcp:127.0.0.1:3121], Result: [null, tcfchan#1]. Thread: Worker-5

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:20.856
!MESSAGE XSCT Command: [jtag targets -filter {level == 0}], Thread: Worker-5

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:21.167
!MESSAGE XSCT command with result: [jtag targets -filter {level == 0}], Result: [null,   1  Platform Cable USB 00001abd0b9c01]. Thread: Worker-5

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:21.171
!MESSAGE XSCT Command: [jtag targets -filter {jtag_cable_name =~ "Platform Cable USB 00001abd0b9c01" && level==1}], Thread: Worker-5

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:21.188
!MESSAGE XSCT command with result: [jtag targets -filter {jtag_cable_name =~ "Platform Cable USB 00001abd0b9c01" && level==1}], Result: [null,      2  arm_dap (idcode 4ba00477 irlen 4)
     3  xc7z035 (idcode 23732093 irlen 6 fpga)]. Thread: Worker-5

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:25.346
!MESSAGE XSCT Command: [connect -url tcp:127.0.0.1:3121], Thread: Worker-4

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:25.411
!MESSAGE XSCT command with result: [connect -url tcp:127.0.0.1:3121], Result: [null, tcfchan#1]. Thread: Worker-4

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:25.415
!MESSAGE XSCT Command: [jtag targets -filter {level == 0}], Thread: Worker-4

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:25.423
!MESSAGE XSCT command with result: [jtag targets -filter {level == 0}], Result: [null,   1  Platform Cable USB 00001abd0b9c01]. Thread: Worker-4

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:25.426
!MESSAGE XSCT Command: [jtag targets -filter {jtag_cable_name =~ "Platform Cable USB 00001abd0b9c01" && level==1}], Thread: Worker-4

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:25.443
!MESSAGE XSCT command with result: [jtag targets -filter {jtag_cable_name =~ "Platform Cable USB 00001abd0b9c01" && level==1}], Result: [null,      2  arm_dap (idcode 4ba00477 irlen 4)
     3  xc7z035 (idcode 23732093 irlen 6 fpga)]. Thread: Worker-4

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:34.169
!MESSAGE XSCT Command: [connect -url tcp:127.0.0.1:3121], Thread: Worker-0

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:34.174
!MESSAGE XSCT command with result: [connect -url tcp:127.0.0.1:3121], Result: [null, tcfchan#1]. Thread: Worker-0

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:34.179
!MESSAGE XSCT Command: [jtag targets -filter {level == 0}], Thread: Worker-0

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:34.188
!MESSAGE XSCT command with result: [jtag targets -filter {level == 0}], Result: [null,   1  Platform Cable USB 00001abd0b9c01]. Thread: Worker-0

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:34.193
!MESSAGE XSCT Command: [jtag targets -filter {jtag_cable_name =~ "Platform Cable USB 00001abd0b9c01" && level==1}], Thread: Worker-0

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-22 12:06:34.210
!MESSAGE XSCT command with result: [jtag targets -filter {jtag_cable_name =~ "Platform Cable USB 00001abd0b9c01" && level==1}], Result: [null,      2  arm_dap (idcode 4ba00477 irlen 4)
     3  xc7z035 (idcode 23732093 irlen 6 fpga)]. Thread: Worker-0

!ENTRY com.xilinx.sdk.utils 1 0 2020-04-22 12:08:03.133
!MESSAGE Updating toolusage

!ENTRY com.xilinx.sdk.utils 1 0 2020-04-22 12:14:19.617
!MESSAGE Updating toolusage

!ENTRY com.xilinx.sdk.utils 1 0 2020-04-22 13:52:01.808
!MESSAGE Updating toolusage

!ENTRY com.xilinx.sdk.utils 1 0 2020-04-22 16:29:58.835
!MESSAGE Executed Webtalk command
!SESSION 2020-04-24 17:05:35.051 -----------------------------------------------
eclipse.buildId=2016.4
java.version=1.8.0_66
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -data E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk

!ENTRY org.eclipse.ui 2 0 2020-04-24 17:05:51.492
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2020-04-24 17:05:51.492
!MESSAGE Commands should really have a category: plug-in='com.xilinx.sdk.appwiz', id='com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences', categoryId='com.xilinx.sdk.app.commands.category'

!ENTRY org.eclipse.ui 2 0 2020-04-24 17:05:53.789
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2020-04-24 17:05:53.789
!MESSAGE Commands should really have a category: plug-in='com.xilinx.sdk.appwiz', id='com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences', categoryId='com.xilinx.sdk.app.commands.category'

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:02.836
!MESSAGE XSCT Command: [::hsi::utils::openhw E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:02.883
!MESSAGE XSCT Command: [set sdk::sdk_chan tcfchan#0], Thread: Thread-13

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.780
!MESSAGE XSCT command with result: [::hsi::utils::openhw E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.812
!MESSAGE XSCT command with result: [set sdk::sdk_chan tcfchan#0], Result: [null, tcfchan#0]. Thread: Thread-13

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.812
!MESSAGE XSCT Command: [setws E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk], Thread: Thread-13

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.827
!MESSAGE XSCT command with result: [setws E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk], Result: [null, ]. Thread: Thread-13

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.858
!MESSAGE XSCT Command: [::hsi::utils::get_design_properties -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.858
!MESSAGE XSCT command with result: [::hsi::utils::get_design_properties -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Result: [null, {"device": "7z035",
"family": "zynq",
"timestamp": "Fri Mar 20 19:54:34 2020",
"vivado_version": "2016.4",
"part": "xc7z035ffg676-2",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.858
!MESSAGE XSCT Command: [::hsi::utils::get_all_periphs -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.890
!MESSAGE XSCT command with result: [::hsi::utils::get_all_periphs -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Result: [null, {"axi_ad9361": {"hier_name": "axi_ad9361",
"type": "axi_ad9361",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_adc_dma": {"hier_name": "axi_ad9361_adc_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_dma": {"hier_name": "axi_ad9361_dac_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_fifo": {"hier_name": "axi_ad9361_dac_fifo",
"type": "util_rfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_cpu_interconnect": {"hier_name": "axi_cpu_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp0_interconnect": {"hier_name": "axi_hp0_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp1_interconnect": {"hier_name": "axi_hp1_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp2_interconnect": {"hier_name": "axi_hp2_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"sys_concat_intc": {"hier_name": "sys_concat_intc",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"sys_ps7": {"hier_name": "sys_ps7",
"type": "processing_system7",
"version": "5.5",
"ip_type": "",
},
"sys_rstgen": {"hier_name": "sys_rstgen",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_fifo": {"hier_name": "util_ad9361_adc_fifo",
"type": "util_wfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_pack": {"hier_name": "util_ad9361_adc_pack",
"type": "util_cpack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_dac_upack": {"hier_name": "util_ad9361_dac_upack",
"type": "util_upack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk": {"hier_name": "util_ad9361_divclk",
"type": "util_clkdiv",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_reset": {"hier_name": "util_ad9361_divclk_reset",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel": {"hier_name": "util_ad9361_divclk_sel",
"type": "util_reduced_logic",
"version": "2.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel_concat": {"hier_name": "util_ad9361_divclk_sel_concat",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"util_ad9361_tdd_sync": {"hier_name": "util_ad9361_tdd_sync",
"type": "util_tdd_sync",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"ps7_clockc_0": {"hier_name": "ps7_clockc_0",
"type": "ps7_clockc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_uart_0": {"hier_name": "ps7_uart_0",
"type": "ps7_uart",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pl310_0": {"hier_name": "ps7_pl310_0",
"type": "ps7_pl310",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pmu_0": {"hier_name": "ps7_pmu_0",
"type": "ps7_pmu",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_0": {"hier_name": "ps7_qspi_0",
"type": "ps7_qspi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_linear_0": {"hier_name": "ps7_qspi_linear_0",
"type": "ps7_qspi_linear",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_axi_interconnect_0": {"hier_name": "ps7_axi_interconnect_0",
"type": "ps7_axi_interconnect",
"version": "1.00.a",
"ip_type": "BUS",
},
"ps7_cortexa9_0": {"hier_name": "ps7_cortexa9_0",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_cortexa9_1": {"hier_name": "ps7_cortexa9_1",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_ddr_0": {"hier_name": "ps7_ddr_0",
"type": "ps7_ddr",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ethernet_0": {"hier_name": "ps7_ethernet_0",
"type": "ps7_ethernet",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_0": {"hier_name": "ps7_spi_0",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_1": {"hier_name": "ps7_spi_1",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpio_0": {"hier_name": "ps7_gpio_0",
"type": "ps7_gpio",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ddrc_0": {"hier_name": "ps7_ddrc_0",
"type": "ps7_ddrc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dev_cfg_0": {"hier_name": "ps7_dev_cfg_0",
"type": "ps7_dev_cfg",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_xadc_0": {"hier_name": "ps7_xadc_0",
"type": "ps7_xadc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ocmc_0": {"hier_name": "ps7_ocmc_0",
"type": "ps7_ocmc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_coresight_comp_0": {"hier_name": "ps7_coresight_comp_0",
"type": "ps7_coresight_comp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpv_0": {"hier_name": "ps7_gpv_0",
"type": "ps7_gpv",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuc_0": {"hier_name": "ps7_scuc_0",
"type": "ps7_scuc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_globaltimer_0": {"hier_name": "ps7_globaltimer_0",
"type": "ps7_globaltimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_intc_dist_0": {"hier_name": "ps7_intc_dist_0",
"type": "ps7_intc_dist",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_l2cachec_0": {"hier_name": "ps7_l2cachec_0",
"type": "ps7_l2cachec",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_s": {"hier_name": "ps7_dma_s",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_iop_bus_config_0": {"hier_name": "ps7_iop_bus_config_0",
"type": "ps7_iop_bus_config",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ram_0": {"hier_name": "ps7_ram_0",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ram_1": {"hier_name": "ps7_ram_1",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_scugic_0": {"hier_name": "ps7_scugic_0",
"type": "ps7_scugic",
"version": "1.00.a",
"ip_type": "INTERRUPT_CNTLR",
},
"ps7_scutimer_0": {"hier_name": "ps7_scutimer_0",
"type": "ps7_scutimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuwdt_0": {"hier_name": "ps7_scuwdt_0",
"type": "ps7_scuwdt",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_slcr_0": {"hier_name": "ps7_slcr_0",
"type": "ps7_slcr",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_ns": {"hier_name": "ps7_dma_ns",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_0": {"hier_name": "ps7_afi_0",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_1": {"hier_name": "ps7_afi_1",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_2": {"hier_name": "ps7_afi_2",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_3": {"hier_name": "ps7_afi_3",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_m_axi_gp0": {"hier_name": "ps7_m_axi_gp0",
"type": "ps7_m_axi_gp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.890
!MESSAGE XSCT Command: [::hsi::utils::get_addr_ranges -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_0], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.905
!MESSAGE XSCT command with result: [::hsi::utils::get_addr_ranges -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_0], Result: [null, {"axi_ad9361_s_axi": {"name": "axi_ad9361",
"base": "0x79020000",
"high": "0x7902FFFF",
"size": "65536",
"slaveintf": "s_axi",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"axi_ad9361_adc_dma_s_axi": {"name": "axi_ad9361_adc_dma",
"base": "0x7C400000",
"high": "0x7C400FFF",
"size": "4096",
"slaveintf": "s_axi",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"axi_ad9361_dac_dma_s_axi": {"name": "axi_ad9361_dac_dma",
"base": "0x7C420000",
"high": "0x7C420FFF",
"size": "4096",
"slaveintf": "s_axi",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_0": {"name": "ps7_afi_0",
"base": "0xF8008000",
"high": "0xF8008FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_1": {"name": "ps7_afi_1",
"base": "0xF8009000",
"high": "0xF8009FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_2": {"name": "ps7_afi_2",
"base": "0xF800A000",
"high": "0xF800AFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_3": {"name": "ps7_afi_3",
"base": "0xF800B000",
"high": "0xF800BFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_coresight_comp_0": {"name": "ps7_coresight_comp_0",
"base": "0xF8800000",
"high": "0xF88FFFFF",
"size": "1048576",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ddrc_0": {"name": "ps7_ddrc_0",
"base": "0xF8006000",
"high": "0xF8006FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_dev_cfg_0": {"name": "ps7_dev_cfg_0",
"base": "0xF8007000",
"high": "0xF80070FF",
"size": "256",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_dma_ns": {"name": "ps7_dma_ns",
"base": "0xF8004000",
"high": "0xF8004FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_dma_s": {"name": "ps7_dma_s",
"base": "0xF8003000",
"high": "0xF8003FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ethernet_0": {"name": "ps7_ethernet_0",
"base": "0xE000B000",
"high": "0xE000BFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_globaltimer_0": {"name": "ps7_globaltimer_0",
"base": "0xF8F00200",
"high": "0xF8F002FF",
"size": "256",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_gpio_0": {"name": "ps7_gpio_0",
"base": "0xE000A000",
"high": "0xE000AFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_gpv_0": {"name": "ps7_gpv_0",
"base": "0xF8900000",
"high": "0xF89FFFFF",
"size": "1048576",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_intc_dist_0": {"name": "ps7_intc_dist_0",
"base": "0xF8F01000",
"high": "0xF8F01FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_iop_bus_config_0": {"name": "ps7_iop_bus_config_0",
"base": "0xE0200000",
"high": "0xE0200FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_l2cachec_0": {"name": "ps7_l2cachec_0",
"base": "0xF8F02000",
"high": "0xF8F02FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ocmc_0": {"name": "ps7_ocmc_0",
"base": "0xF800C000",
"high": "0xF800CFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_pl310_0": {"name": "ps7_pl310_0",
"base": "0xF8F02000",
"high": "0xF8F02FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_pmu_0": {"name": "ps7_pmu_0",
"base": "0xF8893000",
"high": "0xF8893FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_qspi_0": {"name": "ps7_qspi_0",
"base": "0xE000D000",
"high": "0xE000DFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scuc_0": {"name": "ps7_scuc_0",
"base": "0xF8F00000",
"high": "0xF8F000FC",
"size": "253",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scugic_0": {"name": "ps7_scugic_0",
"base": "0xF8F00100",
"high": "0xF8F001FF",
"size": "256",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scutimer_0": {"name": "ps7_scutimer_0",
"base": "0xF8F00600",
"high": "0xF8F0061F",
"size": "32",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scuwdt_0": {"name": "ps7_scuwdt_0",
"base": "0xF8F00620",
"high": "0xF8F006FF",
"size": "224",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_slcr_0": {"name": "ps7_slcr_0",
"base": "0xF8000000",
"high": "0xF8000FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_spi_0": {"name": "ps7_spi_0",
"base": "0xE0006000",
"high": "0xE0006FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_spi_1": {"name": "ps7_spi_1",
"base": "0xE0007000",
"high": "0xE0007FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_uart_0": {"name": "ps7_uart_0",
"base": "0xE0000000",
"high": "0xE0000FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_xadc_0": {"name": "ps7_xadc_0",
"base": "0xF8007100",
"high": "0xF8007120",
"size": "33",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_qspi_linear_0": {"name": "ps7_qspi_linear_0",
"base": "0xFC000000",
"high": "0xFCFFFFFF",
"size": "16777216",
"slaveintf": "",
"type": "MEMORY",
"flags": "5",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ddr_0": {"name": "ps7_ddr_0",
"base": "0x00100000",
"high": "0x3FFFFFFF",
"size": "1072693248",
"slaveintf": "",
"type": "MEMORY",
"flags": "7",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ram_0": {"name": "ps7_ram_0",
"base": "0x00000000",
"high": "0x0002FFFF",
"size": "196608",
"slaveintf": "",
"type": "MEMORY",
"flags": "7",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ram_1": {"name": "ps7_ram_1",
"base": "0xFFFF0000",
"high": "0xFFFFFDFF",
"size": "65024",
"slaveintf": "",
"type": "MEMORY",
"flags": "7",
"segment": "",
"acctype": "",
"tz": "",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.952
!MESSAGE XSCT Command: [::hsi::utils::get_all_register_data -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_0], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.968
!MESSAGE XSCT command with result: [::hsi::utils::get_all_register_data -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_0], Result: [null, {"axi_ad9361": {},
"axi_ad9361_adc_dma": {},
"axi_ad9361_dac_dma": {},
"ps7_afi_0": {},
"ps7_afi_1": {},
"ps7_afi_2": {},
"ps7_afi_3": {},
"ps7_coresight_comp_0": {},
"ps7_ddr_0": {},
"ps7_ddrc_0": {},
"ps7_dev_cfg_0": {},
"ps7_dma_ns": {},
"ps7_dma_s": {},
"ps7_ethernet_0": {},
"ps7_globaltimer_0": {},
"ps7_gpio_0": {},
"ps7_gpv_0": {},
"ps7_intc_dist_0": {},
"ps7_iop_bus_config_0": {},
"ps7_l2cachec_0": {},
"ps7_ocmc_0": {},
"ps7_pl310_0": {},
"ps7_pmu_0": {},
"ps7_qspi_0": {},
"ps7_qspi_linear_0": {},
"ps7_ram_0": {},
"ps7_ram_1": {},
"ps7_scuc_0": {},
"ps7_scugic_0": {},
"ps7_scutimer_0": {},
"ps7_scuwdt_0": {},
"ps7_slcr_0": {},
"ps7_spi_0": {},
"ps7_spi_1": {},
"ps7_uart_0": {},
"ps7_xadc_0": {},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.968
!MESSAGE XSCT Command: [::hsi::utils::get_all_register_data -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_1], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:06.968
!MESSAGE XSCT command with result: [::hsi::utils::get_all_register_data -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_1], Result: [null, {"axi_ad9361": {},
"axi_ad9361_adc_dma": {},
"axi_ad9361_dac_dma": {},
"ps7_afi_0": {},
"ps7_afi_1": {},
"ps7_afi_2": {},
"ps7_afi_3": {},
"ps7_coresight_comp_0": {},
"ps7_ddr_0": {},
"ps7_ddrc_0": {},
"ps7_dev_cfg_0": {},
"ps7_dma_ns": {},
"ps7_dma_s": {},
"ps7_ethernet_0": {},
"ps7_globaltimer_0": {},
"ps7_gpio_0": {},
"ps7_gpv_0": {},
"ps7_intc_dist_0": {},
"ps7_iop_bus_config_0": {},
"ps7_l2cachec_0": {},
"ps7_ocmc_0": {},
"ps7_pl310_0": {},
"ps7_pmu_0": {},
"ps7_qspi_0": {},
"ps7_qspi_linear_0": {},
"ps7_ram_0": {},
"ps7_ram_1": {},
"ps7_scuc_0": {},
"ps7_scugic_0": {},
"ps7_scutimer_0": {},
"ps7_scuwdt_0": {},
"ps7_slcr_0": {},
"ps7_spi_0": {},
"ps7_spi_1": {},
"ps7_uart_0": {},
"ps7_xadc_0": {},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:10.499
!MESSAGE XSCT Command: [::hsi::utils::init_repo], Thread: Worker-4

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:14.702
!MESSAGE XSCT Command: [::hsi::utils::openhw E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:20.349
!MESSAGE XSCT command with result: [::hsi::utils::init_repo], Result: [null, ]. Thread: Worker-4

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:22.622
!MESSAGE XSCT command with result: [::hsi::utils::openhw E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:22.622
!MESSAGE XSCT Command: [::hsi::utils::get_design_properties -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:22.622
!MESSAGE XSCT command with result: [::hsi::utils::get_design_properties -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, {"device": "7z035",
"family": "zynq",
"timestamp": "Fri Mar 27 14:56:01 2020",
"vivado_version": "2016.4",
"part": "xc7z035ffg676-2",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:22.652
!MESSAGE XSCT Command: [::hsi::utils::get_all_periphs -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-04-24 17:06:22.668
!MESSAGE XSCT command with result: [::hsi::utils::get_all_periphs -json E:/FPGA/R75_Z7035_0320/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, {"axi_ad9361": {"hier_name": "axi_ad9361",
"type": "axi_ad9361",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_adc_dma": {"hier_name": "axi_ad9361_adc_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_dma": {"hier_name": "axi_ad9361_dac_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_fifo": {"hier_name": "axi_ad9361_dac_fifo",
"type": "util_rfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_cpu_interconnect": {"hier_name": "axi_cpu_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp0_interconnect": {"hier_name": "axi_hp0_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp1_interconnect": {"hier_name": "axi_hp1_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp2_interconnect": {"hier_name": "axi_hp2_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_quad_spi_0": {"hier_name": "axi_quad_spi_0",
"type": "axi_quad_spi",
"version": "3.2",
"ip_type": "MEMORY_CNTLR",
},
"sys_concat_intc": {"hier_name": "sys_concat_intc",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"sys_ps7": {"hier_name": "sys_ps7",
"type": "processing_system7",
"version": "5.5",
"ip_type": "",
},
"sys_rstgen": {"hier_name": "sys_rstgen",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_fifo": {"hier_name": "util_ad9361_adc_fifo",
"type": "util_wfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_pack": {"hier_name": "util_ad9361_adc_pack",
"type": "util_cpack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_dac_upack": {"hier_name": "util_ad9361_dac_upack",
"type": "util_upack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk": {"hier_name": "util_ad9361_divclk",
"type": "util_clkdiv",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_reset": {"hier_name": "util_ad9361_divclk_reset",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel": {"hier_name": "util_ad9361_divclk_sel",
"type": "util_reduced_logic",
"version": "2.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel_concat": {"hier_name": "util_ad9361_divclk_sel_concat",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"util_ad9361_tdd_sync": {"hier_name": "util_ad9361_tdd_sync",
"type": "util_tdd_sync",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"ps7_clockc_0": {"hier_name": "ps7_clockc_0",
"type": "ps7_clockc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_uart_0": {"hier_name": "ps7_uart_0",
"type": "ps7_uart",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pl310_0": {"hier_name": "ps7_pl310_0",
"type": "ps7_pl310",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pmu_0": {"hier_name": "ps7_pmu_0",
"type": "ps7_pmu",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_0": {"hier_name": "ps7_qspi_0",
"type": "ps7_qspi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_linear_0": {"hier_name": "ps7_qspi_linear_0",
"type": "ps7_qspi_linear",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_axi_interconnect_0": {"hier_name": "ps7_axi_interconnect_0",
"type": "ps7_axi_interconnect",
"version": "1.00.a",
"ip_type": "BUS",
},
"ps7_cortexa9_0": {"hier_name": "ps7_cortexa9_0",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_cortexa9_1": {"hier_name": "ps7_cortexa9_1",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_ddr_0": {"hier_name": "ps7_ddr_0",
"type": "ps7_ddr",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ethernet_0": {"hier_name": "ps7_ethernet_0",
"type": "ps7_ethernet",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_0": {"hier_name": "ps7_spi_0",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_1": {"hier_name": "ps7_spi_1",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpio_0": {"hier_name": "ps7_gpio_0",
"type": "ps7_gpio",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ddrc_0": {"hier_name": "ps7_ddrc_0",
"type": "ps7_ddrc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dev_cfg_0": {"hier_name": "ps7_dev_cfg_0",
"type": "ps7_dev_cfg",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_xadc_0": {"hier_name": "ps7_xadc_0",
"type": "ps7_xadc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ocmc_0": {"hier_name": "ps7_ocmc_0",
"type": "ps7_ocmc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_coresight_comp_0": {"hier_name": "ps7_coresight_comp_0",
"type": "ps7_coresight_comp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpv_0": {"hier_name": "ps7_gpv_0",
"type": "ps7_gpv",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuc_0": {"hier_name": "ps7_scuc_0",
"type": "ps7_scuc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_globaltimer_0": {"hier_name": "ps7_globaltimer_0",
"type": "ps7_globaltimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_intc_dist_0": {"hier_name": "ps7_intc_dist_0",
"type": "ps7_intc_dist",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_l2cachec_0": {"hier_name": "ps7_l2cachec_0",
"type": "ps7_l2cachec",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_s": {"hier_name": "ps7_dma_s",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_iop_bus_config_0": {"hier_name": "ps7_iop_bus_config_0",
"type": "ps7_iop_bus_config",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ram_0": {"hier_name": "ps7_ram_0",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ram_1": {"hier_name": "ps7_ram_1",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_scugic_0": {"hier_name": "ps7_scugic_0",
"type": "ps7_scugic",
"version": "1.00.a",
"ip_type": "INTERRUPT_CNTLR",
},
"ps7_scutimer_0": {"hier_name": "ps7_scutimer_0",
"type": "ps7_scutimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuwdt_0": {"hier_name": "ps7_scuwdt_0",
"type": "ps7_scuwdt",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_slcr_0": {"hier_name": "ps7_slcr_0",
"type": "ps7_slcr",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_ns": {"hier_name": "ps7_dma_ns",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_0": {"hier_name": "ps7_afi_0",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_1": {"hier_name": "ps7_afi_1",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_2": {"hier_name": "ps7_afi_2",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_3": {"hier_name": "ps7_afi_3",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_m_axi_gp0": {"hier_name": "ps7_m_axi_gp0",
"type": "ps7_m_axi_gp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 1 0 2020-04-24 17:06:31.872
!MESSAGE Updating toolusage

!ENTRY com.xilinx.sdk.utils 1 0 2020-04-24 17:07:44.487
!MESSAGE Updating toolusage

!ENTRY com.xilinx.sdk.utils 1 0 2020-04-24 17:10:42.352
!MESSAGE Updating toolusage

!ENTRY com.xilinx.sdk.utils 1 0 2020-04-24 17:19:29.034
!MESSAGE Updating toolusage
!SESSION 2020-05-11 16:46:28.130 -----------------------------------------------
eclipse.buildId=2016.4
java.version=1.8.0_66
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -data D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk

!ENTRY org.eclipse.ui 2 0 2020-05-11 16:46:42.981
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2020-05-11 16:46:42.981
!MESSAGE Commands should really have a category: plug-in='com.xilinx.sdk.appwiz', id='com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences', categoryId='com.xilinx.sdk.app.commands.category'

!ENTRY org.eclipse.ui 2 0 2020-05-11 16:46:44.032
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2020-05-11 16:46:44.032
!MESSAGE Commands should really have a category: plug-in='com.xilinx.sdk.appwiz', id='com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences', categoryId='com.xilinx.sdk.app.commands.category'

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:53.178
!MESSAGE XSCT Command: [::hsi::utils::openhw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:53.188
!MESSAGE XSCT Command: [set sdk::sdk_chan tcfchan#0], Thread: Thread-13

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.778
!MESSAGE XSCT command with result: [::hsi::utils::openhw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.788
!MESSAGE XSCT command with result: [set sdk::sdk_chan tcfchan#0], Result: [null, tcfchan#0]. Thread: Thread-13

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.788
!MESSAGE XSCT Command: [setws D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk], Thread: Thread-13

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.798
!MESSAGE XSCT command with result: [setws D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk], Result: [null, ]. Thread: Thread-13

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.808
!MESSAGE XSCT Command: [::hsi::utils::get_design_properties -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.808
!MESSAGE XSCT command with result: [::hsi::utils::get_design_properties -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Result: [null, {"device": "7z035",
"family": "zynq",
"timestamp": "Fri Mar 20 19:54:34 2020",
"vivado_version": "2016.4",
"part": "xc7z035ffg676-2",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.828
!MESSAGE XSCT Command: [::hsi::utils::get_all_periphs -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.838
!MESSAGE XSCT command with result: [::hsi::utils::get_all_periphs -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf], Result: [null, {"axi_ad9361": {"hier_name": "axi_ad9361",
"type": "axi_ad9361",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_adc_dma": {"hier_name": "axi_ad9361_adc_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_dma": {"hier_name": "axi_ad9361_dac_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_fifo": {"hier_name": "axi_ad9361_dac_fifo",
"type": "util_rfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_cpu_interconnect": {"hier_name": "axi_cpu_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp0_interconnect": {"hier_name": "axi_hp0_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp1_interconnect": {"hier_name": "axi_hp1_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp2_interconnect": {"hier_name": "axi_hp2_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"sys_concat_intc": {"hier_name": "sys_concat_intc",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"sys_ps7": {"hier_name": "sys_ps7",
"type": "processing_system7",
"version": "5.5",
"ip_type": "",
},
"sys_rstgen": {"hier_name": "sys_rstgen",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_fifo": {"hier_name": "util_ad9361_adc_fifo",
"type": "util_wfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_pack": {"hier_name": "util_ad9361_adc_pack",
"type": "util_cpack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_dac_upack": {"hier_name": "util_ad9361_dac_upack",
"type": "util_upack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk": {"hier_name": "util_ad9361_divclk",
"type": "util_clkdiv",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_reset": {"hier_name": "util_ad9361_divclk_reset",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel": {"hier_name": "util_ad9361_divclk_sel",
"type": "util_reduced_logic",
"version": "2.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel_concat": {"hier_name": "util_ad9361_divclk_sel_concat",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"util_ad9361_tdd_sync": {"hier_name": "util_ad9361_tdd_sync",
"type": "util_tdd_sync",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"ps7_clockc_0": {"hier_name": "ps7_clockc_0",
"type": "ps7_clockc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_uart_0": {"hier_name": "ps7_uart_0",
"type": "ps7_uart",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pl310_0": {"hier_name": "ps7_pl310_0",
"type": "ps7_pl310",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pmu_0": {"hier_name": "ps7_pmu_0",
"type": "ps7_pmu",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_0": {"hier_name": "ps7_qspi_0",
"type": "ps7_qspi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_linear_0": {"hier_name": "ps7_qspi_linear_0",
"type": "ps7_qspi_linear",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_axi_interconnect_0": {"hier_name": "ps7_axi_interconnect_0",
"type": "ps7_axi_interconnect",
"version": "1.00.a",
"ip_type": "BUS",
},
"ps7_cortexa9_0": {"hier_name": "ps7_cortexa9_0",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_cortexa9_1": {"hier_name": "ps7_cortexa9_1",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_ddr_0": {"hier_name": "ps7_ddr_0",
"type": "ps7_ddr",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ethernet_0": {"hier_name": "ps7_ethernet_0",
"type": "ps7_ethernet",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_0": {"hier_name": "ps7_spi_0",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_1": {"hier_name": "ps7_spi_1",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpio_0": {"hier_name": "ps7_gpio_0",
"type": "ps7_gpio",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ddrc_0": {"hier_name": "ps7_ddrc_0",
"type": "ps7_ddrc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dev_cfg_0": {"hier_name": "ps7_dev_cfg_0",
"type": "ps7_dev_cfg",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_xadc_0": {"hier_name": "ps7_xadc_0",
"type": "ps7_xadc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ocmc_0": {"hier_name": "ps7_ocmc_0",
"type": "ps7_ocmc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_coresight_comp_0": {"hier_name": "ps7_coresight_comp_0",
"type": "ps7_coresight_comp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpv_0": {"hier_name": "ps7_gpv_0",
"type": "ps7_gpv",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuc_0": {"hier_name": "ps7_scuc_0",
"type": "ps7_scuc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_globaltimer_0": {"hier_name": "ps7_globaltimer_0",
"type": "ps7_globaltimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_intc_dist_0": {"hier_name": "ps7_intc_dist_0",
"type": "ps7_intc_dist",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_l2cachec_0": {"hier_name": "ps7_l2cachec_0",
"type": "ps7_l2cachec",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_s": {"hier_name": "ps7_dma_s",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_iop_bus_config_0": {"hier_name": "ps7_iop_bus_config_0",
"type": "ps7_iop_bus_config",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ram_0": {"hier_name": "ps7_ram_0",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ram_1": {"hier_name": "ps7_ram_1",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_scugic_0": {"hier_name": "ps7_scugic_0",
"type": "ps7_scugic",
"version": "1.00.a",
"ip_type": "INTERRUPT_CNTLR",
},
"ps7_scutimer_0": {"hier_name": "ps7_scutimer_0",
"type": "ps7_scutimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuwdt_0": {"hier_name": "ps7_scuwdt_0",
"type": "ps7_scuwdt",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_slcr_0": {"hier_name": "ps7_slcr_0",
"type": "ps7_slcr",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_ns": {"hier_name": "ps7_dma_ns",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_0": {"hier_name": "ps7_afi_0",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_1": {"hier_name": "ps7_afi_1",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_2": {"hier_name": "ps7_afi_2",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_3": {"hier_name": "ps7_afi_3",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_m_axi_gp0": {"hier_name": "ps7_m_axi_gp0",
"type": "ps7_m_axi_gp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.848
!MESSAGE XSCT Command: [::hsi::utils::get_addr_ranges -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_0], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.868
!MESSAGE XSCT command with result: [::hsi::utils::get_addr_ranges -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_0], Result: [null, {"axi_ad9361_s_axi": {"name": "axi_ad9361",
"base": "0x79020000",
"high": "0x7902FFFF",
"size": "65536",
"slaveintf": "s_axi",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"axi_ad9361_adc_dma_s_axi": {"name": "axi_ad9361_adc_dma",
"base": "0x7C400000",
"high": "0x7C400FFF",
"size": "4096",
"slaveintf": "s_axi",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"axi_ad9361_dac_dma_s_axi": {"name": "axi_ad9361_dac_dma",
"base": "0x7C420000",
"high": "0x7C420FFF",
"size": "4096",
"slaveintf": "s_axi",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_0": {"name": "ps7_afi_0",
"base": "0xF8008000",
"high": "0xF8008FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_1": {"name": "ps7_afi_1",
"base": "0xF8009000",
"high": "0xF8009FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_2": {"name": "ps7_afi_2",
"base": "0xF800A000",
"high": "0xF800AFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_3": {"name": "ps7_afi_3",
"base": "0xF800B000",
"high": "0xF800BFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_coresight_comp_0": {"name": "ps7_coresight_comp_0",
"base": "0xF8800000",
"high": "0xF88FFFFF",
"size": "1048576",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ddrc_0": {"name": "ps7_ddrc_0",
"base": "0xF8006000",
"high": "0xF8006FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_dev_cfg_0": {"name": "ps7_dev_cfg_0",
"base": "0xF8007000",
"high": "0xF80070FF",
"size": "256",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_dma_ns": {"name": "ps7_dma_ns",
"base": "0xF8004000",
"high": "0xF8004FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_dma_s": {"name": "ps7_dma_s",
"base": "0xF8003000",
"high": "0xF8003FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ethernet_0": {"name": "ps7_ethernet_0",
"base": "0xE000B000",
"high": "0xE000BFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_globaltimer_0": {"name": "ps7_globaltimer_0",
"base": "0xF8F00200",
"high": "0xF8F002FF",
"size": "256",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_gpio_0": {"name": "ps7_gpio_0",
"base": "0xE000A000",
"high": "0xE000AFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_gpv_0": {"name": "ps7_gpv_0",
"base": "0xF8900000",
"high": "0xF89FFFFF",
"size": "1048576",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_intc_dist_0": {"name": "ps7_intc_dist_0",
"base": "0xF8F01000",
"high": "0xF8F01FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_iop_bus_config_0": {"name": "ps7_iop_bus_config_0",
"base": "0xE0200000",
"high": "0xE0200FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_l2cachec_0": {"name": "ps7_l2cachec_0",
"base": "0xF8F02000",
"high": "0xF8F02FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ocmc_0": {"name": "ps7_ocmc_0",
"base": "0xF800C000",
"high": "0xF800CFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_pl310_0": {"name": "ps7_pl310_0",
"base": "0xF8F02000",
"high": "0xF8F02FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_pmu_0": {"name": "ps7_pmu_0",
"base": "0xF8893000",
"high": "0xF8893FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_qspi_0": {"name": "ps7_qspi_0",
"base": "0xE000D000",
"high": "0xE000DFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scuc_0": {"name": "ps7_scuc_0",
"base": "0xF8F00000",
"high": "0xF8F000FC",
"size": "253",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scugic_0": {"name": "ps7_scugic_0",
"base": "0xF8F00100",
"high": "0xF8F001FF",
"size": "256",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scutimer_0": {"name": "ps7_scutimer_0",
"base": "0xF8F00600",
"high": "0xF8F0061F",
"size": "32",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scuwdt_0": {"name": "ps7_scuwdt_0",
"base": "0xF8F00620",
"high": "0xF8F006FF",
"size": "224",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_slcr_0": {"name": "ps7_slcr_0",
"base": "0xF8000000",
"high": "0xF8000FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_spi_0": {"name": "ps7_spi_0",
"base": "0xE0006000",
"high": "0xE0006FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_spi_1": {"name": "ps7_spi_1",
"base": "0xE0007000",
"high": "0xE0007FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_uart_0": {"name": "ps7_uart_0",
"base": "0xE0000000",
"high": "0xE0000FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_xadc_0": {"name": "ps7_xadc_0",
"base": "0xF8007100",
"high": "0xF8007120",
"size": "33",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_qspi_linear_0": {"name": "ps7_qspi_linear_0",
"base": "0xFC000000",
"high": "0xFCFFFFFF",
"size": "16777216",
"slaveintf": "",
"type": "MEMORY",
"flags": "5",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ddr_0": {"name": "ps7_ddr_0",
"base": "0x00100000",
"high": "0x3FFFFFFF",
"size": "1072693248",
"slaveintf": "",
"type": "MEMORY",
"flags": "7",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ram_0": {"name": "ps7_ram_0",
"base": "0x00000000",
"high": "0x0002FFFF",
"size": "196608",
"slaveintf": "",
"type": "MEMORY",
"flags": "7",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ram_1": {"name": "ps7_ram_1",
"base": "0xFFFF0000",
"high": "0xFFFFFDFF",
"size": "65024",
"slaveintf": "",
"type": "MEMORY",
"flags": "7",
"segment": "",
"acctype": "",
"tz": "",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.898
!MESSAGE XSCT Command: [::hsi::utils::get_all_register_data -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_0], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.918
!MESSAGE XSCT command with result: [::hsi::utils::get_all_register_data -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_0], Result: [null, {"axi_ad9361": {},
"axi_ad9361_adc_dma": {},
"axi_ad9361_dac_dma": {},
"ps7_afi_0": {},
"ps7_afi_1": {},
"ps7_afi_2": {},
"ps7_afi_3": {},
"ps7_coresight_comp_0": {},
"ps7_ddr_0": {},
"ps7_ddrc_0": {},
"ps7_dev_cfg_0": {},
"ps7_dma_ns": {},
"ps7_dma_s": {},
"ps7_ethernet_0": {},
"ps7_globaltimer_0": {},
"ps7_gpio_0": {},
"ps7_gpv_0": {},
"ps7_intc_dist_0": {},
"ps7_iop_bus_config_0": {},
"ps7_l2cachec_0": {},
"ps7_ocmc_0": {},
"ps7_pl310_0": {},
"ps7_pmu_0": {},
"ps7_qspi_0": {},
"ps7_qspi_linear_0": {},
"ps7_ram_0": {},
"ps7_ram_1": {},
"ps7_scuc_0": {},
"ps7_scugic_0": {},
"ps7_scutimer_0": {},
"ps7_scuwdt_0": {},
"ps7_slcr_0": {},
"ps7_spi_0": {},
"ps7_spi_1": {},
"ps7_uart_0": {},
"ps7_xadc_0": {},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.918
!MESSAGE XSCT Command: [::hsi::utils::get_all_register_data -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_1], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:55.928
!MESSAGE XSCT command with result: [::hsi::utils::get_all_register_data -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_1/system.hdf ps7_cortexa9_1], Result: [null, {"axi_ad9361": {},
"axi_ad9361_adc_dma": {},
"axi_ad9361_dac_dma": {},
"ps7_afi_0": {},
"ps7_afi_1": {},
"ps7_afi_2": {},
"ps7_afi_3": {},
"ps7_coresight_comp_0": {},
"ps7_ddr_0": {},
"ps7_ddrc_0": {},
"ps7_dev_cfg_0": {},
"ps7_dma_ns": {},
"ps7_dma_s": {},
"ps7_ethernet_0": {},
"ps7_globaltimer_0": {},
"ps7_gpio_0": {},
"ps7_gpv_0": {},
"ps7_intc_dist_0": {},
"ps7_iop_bus_config_0": {},
"ps7_l2cachec_0": {},
"ps7_ocmc_0": {},
"ps7_pl310_0": {},
"ps7_pmu_0": {},
"ps7_qspi_0": {},
"ps7_qspi_linear_0": {},
"ps7_ram_0": {},
"ps7_ram_1": {},
"ps7_scuc_0": {},
"ps7_scugic_0": {},
"ps7_scutimer_0": {},
"ps7_scuwdt_0": {},
"ps7_slcr_0": {},
"ps7_spi_0": {},
"ps7_spi_1": {},
"ps7_uart_0": {},
"ps7_xadc_0": {},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:46:58.378
!MESSAGE XSCT Command: [::hsi::utils::init_repo], Thread: Worker-4

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:02.148
!MESSAGE XSCT command with result: [::hsi::utils::init_repo], Result: [null, ]. Thread: Worker-4

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:02.178
!MESSAGE XSCT Command: [::hsi::utils::openhw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: Worker-7

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:04.118
!MESSAGE XSCT command with result: [::hsi::utils::openhw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, ]. Thread: Worker-7

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:04.389
!MESSAGE XSCT Command: [::hsi::utils::openhw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf], Thread: Worker-7

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:04.989
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.core.CommandLauncher.printCommandLine(CommandLauncher.java:287)
	at org.eclipse.cdt.core.CommandLauncher.waitAndRead(CommandLauncher.java:250)
	at org.eclipse.cdt.internal.core.BuildRunnerHelper.build(BuildRunnerHelper.java:273)
	at org.eclipse.cdt.make.core.MakeBuilder.invokeMake(MakeBuilder.java:219)
	at org.eclipse.cdt.make.core.MakeBuilder.build(MakeBuilder.java:108)
	at org.eclipse.core.internal.events.BuildManager$2.run(BuildManager.java:734)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:205)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:245)
	at org.eclipse.core.internal.events.BuildManager$1.run(BuildManager.java:300)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:303)
	at org.eclipse.core.internal.events.BuildManager.basicBuildLoop(BuildManager.java:359)
	at org.eclipse.core.internal.events.BuildManager.build(BuildManager.java:382)
	at org.eclipse.core.internal.events.AutoBuildJob.doBuild(AutoBuildJob.java:144)
	at org.eclipse.core.internal.events.AutoBuildJob.run(AutoBuildJob.java:235)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:55)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.339
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.349
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.559
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.569
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.659
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.669
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.759
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.769
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.879
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.889
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:05.999
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.009
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.159
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.169
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.289
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.299
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.409
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.419
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.539
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.549
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.659
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.699
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:06.759
!MESSAGE XSCT command with result: [::hsi::utils::openhw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf], Result: [null, ]. Thread: Worker-7

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.799
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.809
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.909
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:06.919
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.019
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.029
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.539
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.549
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.659
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.659
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.769
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.779
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.899
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.909
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.989
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:07.999
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:08.059
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:10.029
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:10.039
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:10.099
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:10.129
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:10.139
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:10.199
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:10.259
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:10.269
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:10.330
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:11.729
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:11.729
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:11.789
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:12.680
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:12.690
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:12.740
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:13.770
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:13.780
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:13.840
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:14.720
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:14.730
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:14.810
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:15.700
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:15.700
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:15.760
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:16.600
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:16.600
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:16.660
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:17.160
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:17.170
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:17.230
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:17.750
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:17.750
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:17.810
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:18.590
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:18.590
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:18.650
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:19.440
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:19.450
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:19.540
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:23.531
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:23.541
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:23.601
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:24.521
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:24.531
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:24.591
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:25.481
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:25.481
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:25.541
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:25.651
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:25.661
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:25.771
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2020-05-11 16:47:25.831
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.closeConsoleOutputStream(ConsoleOutputSniffer.java:160)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.close(ConsoleOutputSniffer.java:68)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.close(ProcessClosure.java:98)
	at org.eclipse.cdt.internal.core.ProcessClosure.isAlive(ProcessClosure.java:193)
	at org.eclipse.cdt.core.CommandLauncher.waitAndRead(CommandLauncher.java:259)
	at org.eclipse.cdt.internal.core.BuildRunnerHelper.build(BuildRunnerHelper.java:273)
	at org.eclipse.cdt.make.core.MakeBuilder.invokeMake(MakeBuilder.java:219)
	at org.eclipse.cdt.make.core.MakeBuilder.build(MakeBuilder.java:108)
	at org.eclipse.core.internal.events.BuildManager$2.run(BuildManager.java:734)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:205)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:245)
	at org.eclipse.core.internal.events.BuildManager$1.run(BuildManager.java:300)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:303)
	at org.eclipse.core.internal.events.BuildManager.basicBuildLoop(BuildManager.java:359)
	at org.eclipse.core.internal.events.BuildManager.build(BuildManager.java:382)
	at org.eclipse.core.internal.events.AutoBuildJob.doBuild(AutoBuildJob.java:144)
	at org.eclipse.core.internal.events.AutoBuildJob.run(AutoBuildJob.java:235)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:55)

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:26.341
!MESSAGE XSCT Command: [::hsi::utils::get_design_properties -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf], Thread: Worker-7

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:26.351
!MESSAGE XSCT command with result: [::hsi::utils::get_design_properties -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf], Result: [null, {"device": "7z035",
"family": "zynq",
"timestamp": "Fri Mar 20 19:54:34 2020",
"vivado_version": "2016.4",
"part": "xc7z035ffg676-2",
}]. Thread: Worker-7

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:26.361
!MESSAGE XSCT Command: [::hsi::utils::get_all_periphs -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf], Thread: Worker-7

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:26.381
!MESSAGE XSCT command with result: [::hsi::utils::get_all_periphs -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf], Result: [null, {"axi_ad9361": {"hier_name": "axi_ad9361",
"type": "axi_ad9361",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_adc_dma": {"hier_name": "axi_ad9361_adc_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_dma": {"hier_name": "axi_ad9361_dac_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_fifo": {"hier_name": "axi_ad9361_dac_fifo",
"type": "util_rfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_cpu_interconnect": {"hier_name": "axi_cpu_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp0_interconnect": {"hier_name": "axi_hp0_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp1_interconnect": {"hier_name": "axi_hp1_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp2_interconnect": {"hier_name": "axi_hp2_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"sys_concat_intc": {"hier_name": "sys_concat_intc",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"sys_ps7": {"hier_name": "sys_ps7",
"type": "processing_system7",
"version": "5.5",
"ip_type": "",
},
"sys_rstgen": {"hier_name": "sys_rstgen",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_fifo": {"hier_name": "util_ad9361_adc_fifo",
"type": "util_wfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_pack": {"hier_name": "util_ad9361_adc_pack",
"type": "util_cpack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_dac_upack": {"hier_name": "util_ad9361_dac_upack",
"type": "util_upack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk": {"hier_name": "util_ad9361_divclk",
"type": "util_clkdiv",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_reset": {"hier_name": "util_ad9361_divclk_reset",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel": {"hier_name": "util_ad9361_divclk_sel",
"type": "util_reduced_logic",
"version": "2.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel_concat": {"hier_name": "util_ad9361_divclk_sel_concat",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"util_ad9361_tdd_sync": {"hier_name": "util_ad9361_tdd_sync",
"type": "util_tdd_sync",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"ps7_clockc_0": {"hier_name": "ps7_clockc_0",
"type": "ps7_clockc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_uart_0": {"hier_name": "ps7_uart_0",
"type": "ps7_uart",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pl310_0": {"hier_name": "ps7_pl310_0",
"type": "ps7_pl310",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pmu_0": {"hier_name": "ps7_pmu_0",
"type": "ps7_pmu",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_0": {"hier_name": "ps7_qspi_0",
"type": "ps7_qspi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_linear_0": {"hier_name": "ps7_qspi_linear_0",
"type": "ps7_qspi_linear",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_axi_interconnect_0": {"hier_name": "ps7_axi_interconnect_0",
"type": "ps7_axi_interconnect",
"version": "1.00.a",
"ip_type": "BUS",
},
"ps7_cortexa9_0": {"hier_name": "ps7_cortexa9_0",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_cortexa9_1": {"hier_name": "ps7_cortexa9_1",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_ddr_0": {"hier_name": "ps7_ddr_0",
"type": "ps7_ddr",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ethernet_0": {"hier_name": "ps7_ethernet_0",
"type": "ps7_ethernet",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_0": {"hier_name": "ps7_spi_0",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_1": {"hier_name": "ps7_spi_1",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpio_0": {"hier_name": "ps7_gpio_0",
"type": "ps7_gpio",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ddrc_0": {"hier_name": "ps7_ddrc_0",
"type": "ps7_ddrc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dev_cfg_0": {"hier_name": "ps7_dev_cfg_0",
"type": "ps7_dev_cfg",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_xadc_0": {"hier_name": "ps7_xadc_0",
"type": "ps7_xadc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ocmc_0": {"hier_name": "ps7_ocmc_0",
"type": "ps7_ocmc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_coresight_comp_0": {"hier_name": "ps7_coresight_comp_0",
"type": "ps7_coresight_comp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpv_0": {"hier_name": "ps7_gpv_0",
"type": "ps7_gpv",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuc_0": {"hier_name": "ps7_scuc_0",
"type": "ps7_scuc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_globaltimer_0": {"hier_name": "ps7_globaltimer_0",
"type": "ps7_globaltimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_intc_dist_0": {"hier_name": "ps7_intc_dist_0",
"type": "ps7_intc_dist",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_l2cachec_0": {"hier_name": "ps7_l2cachec_0",
"type": "ps7_l2cachec",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_s": {"hier_name": "ps7_dma_s",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_iop_bus_config_0": {"hier_name": "ps7_iop_bus_config_0",
"type": "ps7_iop_bus_config",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ram_0": {"hier_name": "ps7_ram_0",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ram_1": {"hier_name": "ps7_ram_1",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_scugic_0": {"hier_name": "ps7_scugic_0",
"type": "ps7_scugic",
"version": "1.00.a",
"ip_type": "INTERRUPT_CNTLR",
},
"ps7_scutimer_0": {"hier_name": "ps7_scutimer_0",
"type": "ps7_scutimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuwdt_0": {"hier_name": "ps7_scuwdt_0",
"type": "ps7_scuwdt",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_slcr_0": {"hier_name": "ps7_slcr_0",
"type": "ps7_slcr",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_ns": {"hier_name": "ps7_dma_ns",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_0": {"hier_name": "ps7_afi_0",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_1": {"hier_name": "ps7_afi_1",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_2": {"hier_name": "ps7_afi_2",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_3": {"hier_name": "ps7_afi_3",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_m_axi_gp0": {"hier_name": "ps7_m_axi_gp0",
"type": "ps7_m_axi_gp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
}]. Thread: Worker-7

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:26.541
!MESSAGE XSCT Command: [::hsi::utils::get_addr_ranges -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf ps7_cortexa9_0], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:26.551
!MESSAGE XSCT command with result: [::hsi::utils::get_addr_ranges -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf ps7_cortexa9_0], Result: [null, {"axi_ad9361_s_axi": {"name": "axi_ad9361",
"base": "0x79020000",
"high": "0x7902FFFF",
"size": "65536",
"slaveintf": "s_axi",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"axi_ad9361_adc_dma_s_axi": {"name": "axi_ad9361_adc_dma",
"base": "0x7C400000",
"high": "0x7C400FFF",
"size": "4096",
"slaveintf": "s_axi",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"axi_ad9361_dac_dma_s_axi": {"name": "axi_ad9361_dac_dma",
"base": "0x7C420000",
"high": "0x7C420FFF",
"size": "4096",
"slaveintf": "s_axi",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_0": {"name": "ps7_afi_0",
"base": "0xF8008000",
"high": "0xF8008FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_1": {"name": "ps7_afi_1",
"base": "0xF8009000",
"high": "0xF8009FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_2": {"name": "ps7_afi_2",
"base": "0xF800A000",
"high": "0xF800AFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_afi_3": {"name": "ps7_afi_3",
"base": "0xF800B000",
"high": "0xF800BFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_coresight_comp_0": {"name": "ps7_coresight_comp_0",
"base": "0xF8800000",
"high": "0xF88FFFFF",
"size": "1048576",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ddrc_0": {"name": "ps7_ddrc_0",
"base": "0xF8006000",
"high": "0xF8006FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_dev_cfg_0": {"name": "ps7_dev_cfg_0",
"base": "0xF8007000",
"high": "0xF80070FF",
"size": "256",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_dma_ns": {"name": "ps7_dma_ns",
"base": "0xF8004000",
"high": "0xF8004FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_dma_s": {"name": "ps7_dma_s",
"base": "0xF8003000",
"high": "0xF8003FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ethernet_0": {"name": "ps7_ethernet_0",
"base": "0xE000B000",
"high": "0xE000BFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_globaltimer_0": {"name": "ps7_globaltimer_0",
"base": "0xF8F00200",
"high": "0xF8F002FF",
"size": "256",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_gpio_0": {"name": "ps7_gpio_0",
"base": "0xE000A000",
"high": "0xE000AFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_gpv_0": {"name": "ps7_gpv_0",
"base": "0xF8900000",
"high": "0xF89FFFFF",
"size": "1048576",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_intc_dist_0": {"name": "ps7_intc_dist_0",
"base": "0xF8F01000",
"high": "0xF8F01FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_iop_bus_config_0": {"name": "ps7_iop_bus_config_0",
"base": "0xE0200000",
"high": "0xE0200FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_l2cachec_0": {"name": "ps7_l2cachec_0",
"base": "0xF8F02000",
"high": "0xF8F02FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ocmc_0": {"name": "ps7_ocmc_0",
"base": "0xF800C000",
"high": "0xF800CFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_pl310_0": {"name": "ps7_pl310_0",
"base": "0xF8F02000",
"high": "0xF8F02FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_pmu_0": {"name": "ps7_pmu_0",
"base": "0xF8893000",
"high": "0xF8893FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_qspi_0": {"name": "ps7_qspi_0",
"base": "0xE000D000",
"high": "0xE000DFFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scuc_0": {"name": "ps7_scuc_0",
"base": "0xF8F00000",
"high": "0xF8F000FC",
"size": "253",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scugic_0": {"name": "ps7_scugic_0",
"base": "0xF8F00100",
"high": "0xF8F001FF",
"size": "256",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scutimer_0": {"name": "ps7_scutimer_0",
"base": "0xF8F00600",
"high": "0xF8F0061F",
"size": "32",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_scuwdt_0": {"name": "ps7_scuwdt_0",
"base": "0xF8F00620",
"high": "0xF8F006FF",
"size": "224",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_slcr_0": {"name": "ps7_slcr_0",
"base": "0xF8000000",
"high": "0xF8000FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_spi_0": {"name": "ps7_spi_0",
"base": "0xE0006000",
"high": "0xE0006FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_spi_1": {"name": "ps7_spi_1",
"base": "0xE0007000",
"high": "0xE0007FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_uart_0": {"name": "ps7_uart_0",
"base": "0xE0000000",
"high": "0xE0000FFF",
"size": "4096",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_xadc_0": {"name": "ps7_xadc_0",
"base": "0xF8007100",
"high": "0xF8007120",
"size": "33",
"slaveintf": "",
"type": "REGISTER",
"flags": "3",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_qspi_linear_0": {"name": "ps7_qspi_linear_0",
"base": "0xFC000000",
"high": "0xFCFFFFFF",
"size": "16777216",
"slaveintf": "",
"type": "MEMORY",
"flags": "5",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ddr_0": {"name": "ps7_ddr_0",
"base": "0x00100000",
"high": "0x3FFFFFFF",
"size": "1072693248",
"slaveintf": "",
"type": "MEMORY",
"flags": "7",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ram_0": {"name": "ps7_ram_0",
"base": "0x00000000",
"high": "0x0002FFFF",
"size": "196608",
"slaveintf": "",
"type": "MEMORY",
"flags": "7",
"segment": "",
"acctype": "",
"tz": "",
},
"ps7_ram_1": {"name": "ps7_ram_1",
"base": "0xFFFF0000",
"high": "0xFFFFFDFF",
"size": "65024",
"slaveintf": "",
"type": "MEMORY",
"flags": "7",
"segment": "",
"acctype": "",
"tz": "",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:26.591
!MESSAGE XSCT Command: [::hsi::utils::get_all_register_data -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf ps7_cortexa9_0], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:26.611
!MESSAGE XSCT command with result: [::hsi::utils::get_all_register_data -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf ps7_cortexa9_0], Result: [null, {"axi_ad9361": {},
"axi_ad9361_adc_dma": {},
"axi_ad9361_dac_dma": {},
"ps7_afi_0": {},
"ps7_afi_1": {},
"ps7_afi_2": {},
"ps7_afi_3": {},
"ps7_coresight_comp_0": {},
"ps7_ddr_0": {},
"ps7_ddrc_0": {},
"ps7_dev_cfg_0": {},
"ps7_dma_ns": {},
"ps7_dma_s": {},
"ps7_ethernet_0": {},
"ps7_globaltimer_0": {},
"ps7_gpio_0": {},
"ps7_gpv_0": {},
"ps7_intc_dist_0": {},
"ps7_iop_bus_config_0": {},
"ps7_l2cachec_0": {},
"ps7_ocmc_0": {},
"ps7_pl310_0": {},
"ps7_pmu_0": {},
"ps7_qspi_0": {},
"ps7_qspi_linear_0": {},
"ps7_ram_0": {},
"ps7_ram_1": {},
"ps7_scuc_0": {},
"ps7_scugic_0": {},
"ps7_scutimer_0": {},
"ps7_scuwdt_0": {},
"ps7_slcr_0": {},
"ps7_spi_0": {},
"ps7_spi_1": {},
"ps7_uart_0": {},
"ps7_xadc_0": {},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:26.611
!MESSAGE XSCT Command: [::hsi::utils::get_all_register_data -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf ps7_cortexa9_1], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 16:47:26.621
!MESSAGE XSCT command with result: [::hsi::utils::get_all_register_data -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf ps7_cortexa9_1], Result: [null, {"axi_ad9361": {},
"axi_ad9361_adc_dma": {},
"axi_ad9361_dac_dma": {},
"ps7_afi_0": {},
"ps7_afi_1": {},
"ps7_afi_2": {},
"ps7_afi_3": {},
"ps7_coresight_comp_0": {},
"ps7_ddr_0": {},
"ps7_ddrc_0": {},
"ps7_dev_cfg_0": {},
"ps7_dma_ns": {},
"ps7_dma_s": {},
"ps7_ethernet_0": {},
"ps7_globaltimer_0": {},
"ps7_gpio_0": {},
"ps7_gpv_0": {},
"ps7_intc_dist_0": {},
"ps7_iop_bus_config_0": {},
"ps7_l2cachec_0": {},
"ps7_ocmc_0": {},
"ps7_pl310_0": {},
"ps7_pmu_0": {},
"ps7_qspi_0": {},
"ps7_qspi_linear_0": {},
"ps7_ram_0": {},
"ps7_ram_1": {},
"ps7_scuc_0": {},
"ps7_scugic_0": {},
"ps7_scutimer_0": {},
"ps7_scuwdt_0": {},
"ps7_slcr_0": {},
"ps7_spi_0": {},
"ps7_spi_1": {},
"ps7_uart_0": {},
"ps7_xadc_0": {},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.600
!MESSAGE XSCT Command: [ ::hsi::utils::set_current_hw_sw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss ], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.611
!MESSAGE XSCT command with result: [ ::hsi::utils::set_current_hw_sw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss ], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.615
!MESSAGE XSCT Command: [::hsi::utils::get_hw_sw_details -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.636
!MESSAGE XSCT command with result: [::hsi::utils::get_hw_sw_details -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Result: [null, {"procname": "ps7_cortexa9_0",
"osname": "standalone",
"osver": "6.1",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.641
!MESSAGE XSCT Command: [hsi::utils::get_supported_os -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.653
!MESSAGE XSCT command with result: [hsi::utils::get_supported_os -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, {"ps7_cortexa9_0": {"freertos823_xilinx_v1_3": {"name": "freertos823_xilinx",
"version": "1.3",
"desc": "FreeRTOS is a market leading open source RTOS",
"compilerflags": "",
"linkerflags": "-Wl,--start-group,-lxil,-lfreertos,-lgcc,-lc,--end-group",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/ThirdParty/bsp/freertos823_xilinx_v1_3",
},
"standalone_v6_1": {"name": "standalone",
"version": "6.1",
"desc": "Standalone is a simple, low-level software layer. It provides access to basic processor features such as caches, interrupts and exceptions as well as the basic features of a hosted environment, such as standard input and output, profiling, abort and exit.",
"compilerflags": "",
"linkerflags": "-Wl,--start-group,-lxil,-lgcc,-lc,--end-group",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/lib/bsp/standalone_v6_1",
},
},
"ps7_cortexa9_1": {"freertos823_xilinx_v1_3": {"name": "freertos823_xilinx",
"version": "1.3",
"desc": "FreeRTOS is a market leading open source RTOS",
"compilerflags": "",
"linkerflags": "-Wl,--start-group,-lxil,-lfreertos,-lgcc,-lc,--end-group",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/ThirdParty/bsp/freertos823_xilinx_v1_3",
},
"standalone_v6_1": {"name": "standalone",
"version": "6.1",
"desc": "Standalone is a simple, low-level software layer. It provides access to basic processor features such as caches, interrupts and exceptions as well as the basic features of a hosted environment, such as standard input and output, profiling, abort and exit.",
"compilerflags": "",
"linkerflags": "-Wl,--start-group,-lxil,-lgcc,-lc,--end-group",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/lib/bsp/standalone_v6_1",
},
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.679
!MESSAGE XSCT Command: [::hsi::utils::get_connected_periphs D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf ps7_cortexa9_0], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.686
!MESSAGE XSCT command with result: [::hsi::utils::get_connected_periphs D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf ps7_cortexa9_0], Result: [null, axi_ad9361 axi_ad9361_adc_dma axi_ad9361_dac_dma axi_quad_spi_0 ps7_afi_0 ps7_afi_1 ps7_afi_2 ps7_afi_3 ps7_coresight_comp_0 ps7_ddr_0 ps7_ddrc_0 ps7_dev_cfg_0 ps7_dma_ns ps7_dma_s ps7_ethernet_0 ps7_globaltimer_0 ps7_gpio_0 ps7_gpv_0 ps7_intc_dist_0 ps7_iop_bus_config_0 ps7_l2cachec_0 ps7_ocmc_0 ps7_pl310_0 ps7_pmu_0 ps7_qspi_0 ps7_qspi_linear_0 ps7_ram_0 ps7_ram_1 ps7_scuc_0 ps7_scugic_0 ps7_scutimer_0 ps7_scuwdt_0 ps7_slcr_0 ps7_spi_0 ps7_spi_1 ps7_uart_0 ps7_xadc_0]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.691
!MESSAGE XSCT Command: [::hsi::utils::get_drivers_for_sw -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.701
!MESSAGE XSCT command with result: [::hsi::utils::get_drivers_for_sw -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Result: [null, {"axi_ad9361": {"name": "generic",
"ver": "2.0",
},
"axi_ad9361_adc_dma": {"name": "generic",
"ver": "2.0",
},
"axi_ad9361_dac_dma": {"name": "generic",
"ver": "2.0",
},
"ps7_afi_0": {"name": "generic",
"ver": "2.0",
},
"ps7_afi_1": {"name": "generic",
"ver": "2.0",
},
"ps7_afi_2": {"name": "generic",
"ver": "2.0",
},
"ps7_afi_3": {"name": "generic",
"ver": "2.0",
},
"ps7_coresight_comp_0": {"name": "coresightps_dcc",
"ver": "1.3",
},
"ps7_ddr_0": {"name": "ddrps",
"ver": "1.0",
},
"ps7_ddrc_0": {"name": "generic",
"ver": "2.0",
},
"ps7_dev_cfg_0": {"name": "devcfg",
"ver": "3.4",
},
"ps7_dma_ns": {"name": "dmaps",
"ver": "2.3",
},
"ps7_dma_s": {"name": "dmaps",
"ver": "2.3",
},
"ps7_ethernet_0": {"name": "emacps",
"ver": "3.3",
},
"ps7_globaltimer_0": {"name": "generic",
"ver": "2.0",
},
"ps7_gpio_0": {"name": "gpiops",
"ver": "3.1",
},
"ps7_gpv_0": {"name": "generic",
"ver": "2.0",
},
"ps7_intc_dist_0": {"name": "generic",
"ver": "2.0",
},
"ps7_iop_bus_config_0": {"name": "generic",
"ver": "2.0",
},
"ps7_l2cachec_0": {"name": "generic",
"ver": "2.0",
},
"ps7_ocmc_0": {"name": "generic",
"ver": "2.0",
},
"ps7_pl310_0": {"name": "generic",
"ver": "2.0",
},
"ps7_pmu_0": {"name": "generic",
"ver": "2.0",
},
"ps7_qspi_0": {"name": "qspips",
"ver": "3.3",
},
"ps7_qspi_linear_0": {"name": "generic",
"ver": "2.0",
},
"ps7_ram_0": {"name": "generic",
"ver": "2.0",
},
"ps7_ram_1": {"name": "generic",
"ver": "2.0",
},
"ps7_scuc_0": {"name": "generic",
"ver": "2.0",
},
"ps7_scugic_0": {"name": "scugic",
"ver": "3.5",
},
"ps7_scutimer_0": {"name": "scutimer",
"ver": "2.1",
},
"ps7_scuwdt_0": {"name": "scuwdt",
"ver": "2.1",
},
"ps7_slcr_0": {"name": "generic",
"ver": "2.0",
},
"ps7_spi_0": {"name": "spips",
"ver": "3.0",
},
"ps7_spi_1": {"name": "spips",
"ver": "3.0",
},
"ps7_uart_0": {"name": "uartps",
"ver": "3.3",
},
"ps7_xadc_0": {"name": "xadcps",
"ver": "2.2",
},
"axi_quad_spi_0": {"name": "spi",
"ver": "4.2",
},
"ps7_cortexa9_0": {"name": "cpu_cortexa9",
"ver": "2.3",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.706
!MESSAGE XSCT Command: [hsi::utils::get_drivers_for_hw -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:11.826
!MESSAGE XSCT command with result: [hsi::utils::get_drivers_for_hw -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, {"axi_ad9361": {"version": "1.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"axi_ad9361_adc_dma": {"version": "1.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"axi_ad9361_dac_dma": {"version": "1.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"axi_ad9361_dac_fifo": {"version": "1.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"axi_cpu_interconnect": {"version": "2.1",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"axi_hp0_interconnect": {"version": "2.1",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"axi_hp1_interconnect": {"version": "2.1",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"axi_hp2_interconnect": {"version": "2.1",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"axi_quad_spi_0": {"version": "3.2",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"spi_v4_2": {"name": "spi",
"version": "4.2",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/spi_v4_2",
},
},
},
"sys_concat_intc": {"version": "2.1",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"sys_ps7": {"version": "5.5",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"sys_rstgen": {"version": "5.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"util_ad9361_adc_fifo": {"version": "1.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"util_ad9361_adc_pack": {"version": "1.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"util_ad9361_dac_upack": {"version": "1.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"util_ad9361_divclk": {"version": "1.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"util_ad9361_divclk_reset": {"version": "5.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"util_ad9361_divclk_sel": {"version": "2.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"util_ad9361_divclk_sel_concat": {"version": "2.1",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"util_ad9361_tdd_sync": {"version": "1.0",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_clockc_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_uart_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"uartps_v3_3": {"name": "uartps",
"version": "3.3",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/uartps_v3_3",
},
},
},
"ps7_pl310_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_pmu_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_qspi_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"qspips_v3_3": {"name": "qspips",
"version": "3.3",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/qspips_v3_3",
},
},
},
"ps7_qspi_linear_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_axi_interconnect_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_cortexa9_0": {"version": "5.2",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"cpu_cortexa9_v2_3": {"name": "cpu_cortexa9",
"version": "2.3",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/cpu_cortexa9_v2_3",
},
},
},
"ps7_cortexa9_1": {"version": "5.2",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"cpu_cortexa9_v2_3": {"name": "cpu_cortexa9",
"version": "2.3",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/cpu_cortexa9_v2_3",
},
},
},
"ps7_ddr_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"ddrps_v1_0": {"name": "ddrps",
"version": "1.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/ddrps_v1_0",
},
},
},
"ps7_ethernet_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"emacps_v3_3": {"name": "emacps",
"version": "3.3",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/emacps_v3_3",
},
},
},
"ps7_spi_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"spips_v3_0": {"name": "spips",
"version": "3.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/spips_v3_0",
},
},
},
"ps7_spi_1": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"spips_v3_0": {"name": "spips",
"version": "3.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/spips_v3_0",
},
},
},
"ps7_gpio_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"gpiops_v3_1": {"name": "gpiops",
"version": "3.1",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/gpiops_v3_1",
},
},
},
"ps7_ddrc_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_dev_cfg_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"devcfg_v3_4": {"name": "devcfg",
"version": "3.4",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/devcfg_v3_4",
},
},
},
"ps7_xadc_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"xadcps_v2_2": {"name": "xadcps",
"version": "2.2",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/xadcps_v2_2",
},
},
},
"ps7_ocmc_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_coresight_comp_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"coresightps_dcc_v1_3": {"name": "coresightps_dcc",
"version": "1.3",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/coresightps_dcc_v1_3",
},
},
},
"ps7_gpv_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_scuc_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_globaltimer_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_intc_dist_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_l2cachec_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_dma_s": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"dmaps_v2_3": {"name": "dmaps",
"version": "2.3",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/dmaps_v2_3",
},
},
},
"ps7_iop_bus_config_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_ram_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_ram_1": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_scugic_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"scugic_v3_5": {"name": "scugic",
"version": "3.5",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/scugic_v3_5",
},
},
},
"ps7_scutimer_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"scutimer_v2_1": {"name": "scutimer",
"version": "2.1",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/scutimer_v2_1",
},
},
},
"ps7_scuwdt_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"scuwdt_v2_1": {"name": "scuwdt",
"version": "2.1",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/scuwdt_v2_1",
},
},
},
"ps7_slcr_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_dma_ns": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
"dmaps_v2_3": {"name": "dmaps",
"version": "2.3",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/dmaps_v2_3",
},
},
},
"ps7_afi_0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_afi_1": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_afi_2": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_afi_3": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
"ps7_m_axi_gp0": {"version": "1.00.a",
"driver_info": {"generic": {"name": "generic",
"version": "2.0",
"repo": "D:/Xilinx/SDK/2016.4/data/embeddedsw/XilinxProcessorIPLib/drivers/generic_v2_0",
},
},
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.201
!MESSAGE XSCT Command: [ ::hsi::utils::set_current_hw_sw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss ], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.208
!MESSAGE XSCT command with result: [ ::hsi::utils::set_current_hw_sw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss ], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.213
!MESSAGE XSCT Command: [::hsi::utils::get_hw_sw_details -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.222
!MESSAGE XSCT command with result: [::hsi::utils::get_hw_sw_details -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Result: [null, {"procname": "ps7_cortexa9_0",
"osname": "standalone",
"osver": "6.1",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.232
!MESSAGE XSCT Command: [::hsi::utils::get_libs_from_sw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.238
!MESSAGE XSCT command with result: [::hsi::utils::get_libs_from_sw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Result: [null, xilffs xilrsa]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.244
!MESSAGE XSCT Command: [::hsi::utils::get_sw_libs -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.251
!MESSAGE XSCT command with result: [::hsi::utils::get_sw_libs -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Result: [null, {"xilffs": "3.5",
"xilrsa": "1.2",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.256
!MESSAGE XSCT Command: [hsi get_property REPOSITORY [hsi get_sw_cores -filter {NAME == "xilffs" && VERSION == "3.5"  && TYPE == "LIBRARY"}]], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.263
!MESSAGE XSCT command with result: [hsi get_property REPOSITORY [hsi get_sw_cores -filter {NAME == "xilffs" && VERSION == "3.5"  && TYPE == "LIBRARY"}]], Result: [null, D:/Xilinx/SDK/2016.4/data\embeddedsw\lib\sw_services\xilffs_v3_5]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.281
!MESSAGE XSCT Command: [hsi get_property REPOSITORY [hsi get_sw_cores -filter {NAME == "xilffs" && VERSION == "3.5"  && TYPE == "LIBRARY"}]], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.287
!MESSAGE XSCT command with result: [hsi get_property REPOSITORY [hsi get_sw_cores -filter {NAME == "xilffs" && VERSION == "3.5"  && TYPE == "LIBRARY"}]], Result: [null, D:/Xilinx/SDK/2016.4/data\embeddedsw\lib\sw_services\xilffs_v3_5]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.302
!MESSAGE XSCT Command: [hsi get_property REPOSITORY [hsi get_sw_cores -filter {NAME == "xilrsa" && VERSION == "1.2"  && TYPE == "LIBRARY"}]], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.308
!MESSAGE XSCT command with result: [hsi get_property REPOSITORY [hsi get_sw_cores -filter {NAME == "xilrsa" && VERSION == "1.2"  && TYPE == "LIBRARY"}]], Result: [null, D:/Xilinx/SDK/2016.4/data\embeddedsw\lib\sw_services\xilrsa_v1_2]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.317
!MESSAGE XSCT Command: [hsi get_property REPOSITORY [hsi get_sw_cores -filter {NAME == "xilrsa" && VERSION == "1.2"  && TYPE == "LIBRARY"}]], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.324
!MESSAGE XSCT command with result: [hsi get_property REPOSITORY [hsi get_sw_cores -filter {NAME == "xilrsa" && VERSION == "1.2"  && TYPE == "LIBRARY"}]], Result: [null, D:/Xilinx/SDK/2016.4/data\embeddedsw\lib\sw_services\xilrsa_v1_2]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.413
!MESSAGE XSCT Command: [::hsi::utils::closesw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:05:12.426
!MESSAGE XSCT command with result: [::hsi::utils::closesw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/fsbl_bsp/system.mss], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:39:16.627
!MESSAGE XSCT Command: [::hsi::utils::get_design_properties -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:39:16.634
!MESSAGE XSCT command with result: [::hsi::utils::get_design_properties -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, {"device": "7z035",
"family": "zynq",
"timestamp": "Fri Mar 27 14:56:01 2020",
"vivado_version": "2016.4",
"part": "xc7z035ffg676-2",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:39:16.648
!MESSAGE XSCT Command: [::hsi::utils::get_all_periphs -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-11 17:39:16.665
!MESSAGE XSCT command with result: [::hsi::utils::get_all_periphs -json D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, {"axi_ad9361": {"hier_name": "axi_ad9361",
"type": "axi_ad9361",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_adc_dma": {"hier_name": "axi_ad9361_adc_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_dma": {"hier_name": "axi_ad9361_dac_dma",
"type": "axi_dmac",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_ad9361_dac_fifo": {"hier_name": "axi_ad9361_dac_fifo",
"type": "util_rfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_cpu_interconnect": {"hier_name": "axi_cpu_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp0_interconnect": {"hier_name": "axi_hp0_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp1_interconnect": {"hier_name": "axi_hp1_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_hp2_interconnect": {"hier_name": "axi_hp2_interconnect",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"axi_quad_spi_0": {"hier_name": "axi_quad_spi_0",
"type": "axi_quad_spi",
"version": "3.2",
"ip_type": "MEMORY_CNTLR",
},
"sys_concat_intc": {"hier_name": "sys_concat_intc",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"sys_ps7": {"hier_name": "sys_ps7",
"type": "processing_system7",
"version": "5.5",
"ip_type": "",
},
"sys_rstgen": {"hier_name": "sys_rstgen",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_fifo": {"hier_name": "util_ad9361_adc_fifo",
"type": "util_wfifo",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_adc_pack": {"hier_name": "util_ad9361_adc_pack",
"type": "util_cpack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_dac_upack": {"hier_name": "util_ad9361_dac_upack",
"type": "util_upack",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk": {"hier_name": "util_ad9361_divclk",
"type": "util_clkdiv",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_reset": {"hier_name": "util_ad9361_divclk_reset",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel": {"hier_name": "util_ad9361_divclk_sel",
"type": "util_reduced_logic",
"version": "2.0",
"ip_type": "PERIPHERAL",
},
"util_ad9361_divclk_sel_concat": {"hier_name": "util_ad9361_divclk_sel_concat",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"util_ad9361_tdd_sync": {"hier_name": "util_ad9361_tdd_sync",
"type": "util_tdd_sync",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"ps7_clockc_0": {"hier_name": "ps7_clockc_0",
"type": "ps7_clockc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_uart_0": {"hier_name": "ps7_uart_0",
"type": "ps7_uart",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pl310_0": {"hier_name": "ps7_pl310_0",
"type": "ps7_pl310",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pmu_0": {"hier_name": "ps7_pmu_0",
"type": "ps7_pmu",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_0": {"hier_name": "ps7_qspi_0",
"type": "ps7_qspi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_linear_0": {"hier_name": "ps7_qspi_linear_0",
"type": "ps7_qspi_linear",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_axi_interconnect_0": {"hier_name": "ps7_axi_interconnect_0",
"type": "ps7_axi_interconnect",
"version": "1.00.a",
"ip_type": "BUS",
},
"ps7_cortexa9_0": {"hier_name": "ps7_cortexa9_0",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_cortexa9_1": {"hier_name": "ps7_cortexa9_1",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_ddr_0": {"hier_name": "ps7_ddr_0",
"type": "ps7_ddr",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ethernet_0": {"hier_name": "ps7_ethernet_0",
"type": "ps7_ethernet",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_0": {"hier_name": "ps7_spi_0",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_spi_1": {"hier_name": "ps7_spi_1",
"type": "ps7_spi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpio_0": {"hier_name": "ps7_gpio_0",
"type": "ps7_gpio",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ddrc_0": {"hier_name": "ps7_ddrc_0",
"type": "ps7_ddrc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dev_cfg_0": {"hier_name": "ps7_dev_cfg_0",
"type": "ps7_dev_cfg",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_xadc_0": {"hier_name": "ps7_xadc_0",
"type": "ps7_xadc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ocmc_0": {"hier_name": "ps7_ocmc_0",
"type": "ps7_ocmc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_coresight_comp_0": {"hier_name": "ps7_coresight_comp_0",
"type": "ps7_coresight_comp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpv_0": {"hier_name": "ps7_gpv_0",
"type": "ps7_gpv",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuc_0": {"hier_name": "ps7_scuc_0",
"type": "ps7_scuc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_globaltimer_0": {"hier_name": "ps7_globaltimer_0",
"type": "ps7_globaltimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_intc_dist_0": {"hier_name": "ps7_intc_dist_0",
"type": "ps7_intc_dist",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_l2cachec_0": {"hier_name": "ps7_l2cachec_0",
"type": "ps7_l2cachec",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_s": {"hier_name": "ps7_dma_s",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_iop_bus_config_0": {"hier_name": "ps7_iop_bus_config_0",
"type": "ps7_iop_bus_config",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ram_0": {"hier_name": "ps7_ram_0",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ram_1": {"hier_name": "ps7_ram_1",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_scugic_0": {"hier_name": "ps7_scugic_0",
"type": "ps7_scugic",
"version": "1.00.a",
"ip_type": "INTERRUPT_CNTLR",
},
"ps7_scutimer_0": {"hier_name": "ps7_scutimer_0",
"type": "ps7_scutimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuwdt_0": {"hier_name": "ps7_scuwdt_0",
"type": "ps7_scuwdt",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_slcr_0": {"hier_name": "ps7_slcr_0",
"type": "ps7_slcr",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_ns": {"hier_name": "ps7_dma_ns",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_0": {"hier_name": "ps7_afi_0",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_1": {"hier_name": "ps7_afi_1",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_2": {"hier_name": "ps7_afi_2",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_3": {"hier_name": "ps7_afi_3",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_m_axi_gp0": {"hier_name": "ps7_m_axi_gp0",
"type": "ps7_m_axi_gp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 1 0 2020-05-11 17:48:50.547
!MESSAGE Executed Webtalk command
!SESSION 2020-05-12 14:37:03.722 -----------------------------------------------
eclipse.buildId=2016.4
java.version=1.8.0_66
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -data D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk

!ENTRY org.eclipse.ui 2 0 2020-05-12 14:37:05.402
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2020-05-12 14:37:05.402
!MESSAGE Commands should really have a category: plug-in='com.xilinx.sdk.appwiz', id='com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences', categoryId='com.xilinx.sdk.app.commands.category'

!ENTRY org.eclipse.ui 2 0 2020-05-12 14:37:05.952
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2020-05-12 14:37:05.952
!MESSAGE Commands should really have a category: plug-in='com.xilinx.sdk.appwiz', id='com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences', categoryId='com.xilinx.sdk.app.commands.category'

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 14:37:13.169
!MESSAGE XSCT Command: [set sdk::sdk_chan tcfchan#0], Thread: Thread-17

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 14:37:13.177
!MESSAGE XSCT Command: [::hsi::utils::init_repo], Thread: Worker-1

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 14:37:13.182
!MESSAGE XSCT command with result: [set sdk::sdk_chan tcfchan#0], Result: [null, tcfchan#0]. Thread: Thread-17

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 14:37:13.187
!MESSAGE XSCT Command: [setws D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk], Thread: Thread-17

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 14:37:13.453
!MESSAGE XSCT command with result: [::hsi::utils::init_repo], Result: [null, ]. Thread: Worker-1

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 14:37:13.458
!MESSAGE XSCT command with result: [setws D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk], Result: [null, ]. Thread: Thread-17

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 14:37:13.480
!MESSAGE XSCT Command: [::hsi::utils::openhw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Thread: Worker-6

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 14:37:15.247
!MESSAGE XSCT command with result: [::hsi::utils::openhw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_0/system.hdf], Result: [null, ]. Thread: Worker-6

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 14:37:15.252
!MESSAGE XSCT Command: [::hsi::utils::openhw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 14:37:17.320
!MESSAGE XSCT command with result: [::hsi::utils::openhw D:/xiaoE/R75_Z7035_0320/fmcomms2_zed.sdk/system_top_hw_platform_2/system.hdf], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 16:18:23.397
!MESSAGE XSCT Command: [enable_info_messages], Thread: Worker-10

!ENTRY com.xilinx.sdk.utils 0 0 2020-05-12 16:18:23.403
!MESSAGE XSCT command with result: [enable_info_messages], Result: [null, ]. Thread: Worker-10
